const path = require('path');
const CompressionPlugin = require('compression-webpack-plugin');
// const CopyWebpackPlugin = require('copy-webpack-plugin');

function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  //路径前缀
  publicPath: '/bdsplatform',
  lintOnSave: false,
  productionSourceMap: false,
  chainWebpack: (config) => {
    //忽略的打包文件
    config.externals({
      'vue': 'Vue',
      'vue-router': 'VueRouter',
      'vuex': 'Vuex',
      'axios': 'axios',
      'element-ui': 'ELEMENT'
    });
    const entry = config.entry('app');
    entry.add('babel-polyfill').end();
    entry.add('classlist-polyfill').end();
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end();
    // 去除CSS压缩插件, 防止某些语法报错
    config.plugins.delete('optimize-css');
    config.plugin('compression').use(
      new CompressionPlugin({
        cache: false, // 取消缓存
        test: /\.js$|\.html$|\.css$/,
        threshold: 10240, // 超过10KB的压缩
        deleteOriginalAssets: false// 保留源文件
      })
    );
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [
        path.resolve(__dirname, 'src/assets/less/variables.less'),
        path.resolve(__dirname, 'src/assets/less/mixins.less'),
        path.resolve(__dirname, 'src/assets/global.less')
      ]
    }
  },
  css: {
    extract: { ignoreOrder: true }
  },
  //开发模式反向代理配置，生产模式请使用Nginx部署并配置反向代理
  devServer: {
    port: 1888,
    proxy: {
      '/bdsPlatformApi/vdm-websocket': {
        target: 'http://192.168.57.112:20418', // bd-check本地服务地址
        pathRewrite: {
          '^/bdsPlatformApi/vdm-websocket': '/'
        },
      //   onProxyRes(proxyRes, req) {
      //     const realUrl = req.url|| ''; // 真实请求网址
      //     console.log('-> realUrl', realUrl);
      //     proxyRes.headers['A-Real-Url'] = realUrl; // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
      //   }
      },
      '/bdsPlatformApi': {
        //本地服务接口地址
        // target: 'http://10.10.10.87:20413', // 广州内网地址
        // target: 'http://10.10.10.97:8060', // 兴旺地址
        // target: 'http://59.41.7.83:20413', // 广州外网地址 需要屏蔽pathRewrite设置
        // target: 'http://192.168.1.105:30001', // 北京地址 需要屏蔽pathRewrite设置
        target: 'http://117.118.3.219:20002', // 北京外网地址(研发环境)
        ws: true,
        // pathRewrite: {
        //   '^/bdsPlatformApi': '/'
        // },
        // onProxyRes(proxyRes, req) {
        //   const realUrl = 'http://10.10.10.87:20413' + req.url|| ''; // 真实请求网址
        //   console.log(realUrl); // 在终端显示
        //   proxyRes.headers['A-Real-Url'] = realUrl; // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        // }
      }
    }
  },
  configureWebpack: {
    devtool: process.env.NODE_ENV === 'development' ? 'source-map' : false,
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    // 青柿集成代码
    // plugins: [
    //   new CopyWebpackPlugin([
    //       { from: 'node_modules/@liveqing/liveplayer/dist/component/crossdomain.xml'},
    //       { from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer-lib.min.js', to: 'js/'},
    //       { from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer.swf'}
    //   ])
    // ],
    module: {
      rules: [
        // 配置读取 *.md 文件的规则
        {
          test: /\.md$/,
          use: [
            { loader: 'html-loader' },
            {
              loader: 'markdown-loader',
              options: {}
            }
          ]
        }
      ]
    }
  }
};
