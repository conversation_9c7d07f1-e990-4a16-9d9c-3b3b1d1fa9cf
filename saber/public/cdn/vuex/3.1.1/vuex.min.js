/**
 * vuex v3.1.1
 * (c) 2019 Evan You
 * @license MIT
 */
!function (t, e) { "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : (t = t || self).Vuex = e() }(this, function () { "use strict"; var t = ("undefined" != typeof window ? window : "undefined" != typeof global ? global : {}).__VUE_DEVTOOLS_GLOBAL_HOOK__; function e (t, e) { Object.keys(t).forEach(function (n) { return e(t[n], n) }) } var n = function (t, e) { this.runtime = e, this._children = Object.create(null), this._rawModule = t; var n = t.state; this.state = ("function" == typeof n ? n() : n) || {} }, o = { namespaced: { configurable: !0 } }; o.namespaced.get = function () { return !!this._rawModule.namespaced }, n.prototype.addChild = function (t, e) { this._children[t] = e }, n.prototype.removeChild = function (t) { delete this._children[t] }, n.prototype.getChild = function (t) { return this._children[t] }, n.prototype.update = function (t) { this._rawModule.namespaced = t.namespaced, t.actions && (this._rawModule.actions = t.actions), t.mutations && (this._rawModule.mutations = t.mutations), t.getters && (this._rawModule.getters = t.getters) }, n.prototype.forEachChild = function (t) { e(this._children, t) }, n.prototype.forEachGetter = function (t) { this._rawModule.getters && e(this._rawModule.getters, t) }, n.prototype.forEachAction = function (t) { this._rawModule.actions && e(this._rawModule.actions, t) }, n.prototype.forEachMutation = function (t) { this._rawModule.mutations && e(this._rawModule.mutations, t) }, Object.defineProperties(n.prototype, o); var i, r = function (t) { this.register([], t, !1) }; r.prototype.get = function (t) { return t.reduce(function (t, e) { return t.getChild(e) }, this.root) }, r.prototype.getNamespace = function (t) { var e = this.root; return t.reduce(function (t, n) { return t + ((e = e.getChild(n)).namespaced ? n + "/" : "") }, "") }, r.prototype.update = function (t) { !function t (e, n, o) { n.update(o); if (o.modules) for (var i in o.modules) { if (!n.getChild(i)) return; t(e.concat(i), n.getChild(i), o.modules[i]) } }([], this.root, t) }, r.prototype.register = function (t, o, i) { var r = this; void 0 === i && (i = !0); var s = new n(o, i); 0 === t.length ? this.root = s : this.get(t.slice(0, -1)).addChild(t[t.length - 1], s); o.modules && e(o.modules, function (e, n) { r.register(t.concat(n), e, i) }) }, r.prototype.unregister = function (t) { var e = this.get(t.slice(0, -1)), n = t[t.length - 1]; e.getChild(n).runtime && e.removeChild(n) }; var s = function (e) { var n = this; void 0 === e && (e = {}), !i && "undefined" != typeof window && window.Vue && d(window.Vue); var o = e.plugins; void 0 === o && (o = []); var s = e.strict; void 0 === s && (s = !1), this._committing = !1, this._actions = Object.create(null), this._actionSubscribers = [], this._mutations = Object.create(null), this._wrappedGetters = Object.create(null), this._modules = new r(e), this._modulesNamespaceMap = Object.create(null), this._subscribers = [], this._watcherVM = new i; var a = this, c = this.dispatch, u = this.commit; this.dispatch = function (t, e) { return c.call(a, t, e) }, this.commit = function (t, e, n) { return u.call(a, t, e, n) }, this.strict = s; var h = this._modules.root.state; p(this, h, [], this._modules.root), f(this, h), o.forEach(function (t) { return t(n) }), (void 0 !== e.devtools ? e.devtools : i.config.devtools) && function (e) { t && (e._devtoolHook = t, t.emit("vuex:init", e), t.on("vuex:travel-to-state", function (t) { e.replaceState(t) }), e.subscribe(function (e, n) { t.emit("vuex:mutation", e, n) })) }(this) }, a = { state: { configurable: !0 } }; function c (t, e) { return e.indexOf(t) < 0 && e.push(t), function () { var n = e.indexOf(t); n > -1 && e.splice(n, 1) } } function u (t, e) { t._actions = Object.create(null), t._mutations = Object.create(null), t._wrappedGetters = Object.create(null), t._modulesNamespaceMap = Object.create(null); var n = t.state; p(t, n, [], t._modules.root, !0), f(t, n, e) } function f (t, n, o) { var r = t._vm; t.getters = {}; var s = t._wrappedGetters, a = {}; e(s, function (e, n) { a[n] = function (t, e) { return function () { return t(e) } }(e, t), Object.defineProperty(t.getters, n, { get: function () { return t._vm[n] }, enumerable: !0 }) }); var c = i.config.silent; i.config.silent = !0, t._vm = new i({ data: { $$state: n }, computed: a }), i.config.silent = c, t.strict && function (t) { t._vm.$watch(function () { return this._data.$$state }, function () { }, { deep: !0, sync: !0 }) }(t), r && (o && t._withCommit(function () { r._data.$$state = null }), i.nextTick(function () { return r.$destroy() })) } function p (t, e, n, o, r) { var s = !n.length, a = t._modules.getNamespace(n); if (o.namespaced && (t._modulesNamespaceMap[a] = o), !s && !r) { var c = h(e, n.slice(0, -1)), u = n[n.length - 1]; t._withCommit(function () { i.set(c, u, o.state) }) } var f = o.context = function (t, e, n) { var o = "" === e, i = { dispatch: o ? t.dispatch : function (n, o, i) { var r = l(n, o, i), s = r.payload, a = r.options, c = r.type; return a && a.root || (c = e + c), t.dispatch(c, s) }, commit: o ? t.commit : function (n, o, i) { var r = l(n, o, i), s = r.payload, a = r.options, c = r.type; a && a.root || (c = e + c), t.commit(c, s, a) } }; return Object.defineProperties(i, { getters: { get: o ? function () { return t.getters } : function () { return function (t, e) { var n = {}, o = e.length; return Object.keys(t.getters).forEach(function (i) { if (i.slice(0, o) === e) { var r = i.slice(o); Object.defineProperty(n, r, { get: function () { return t.getters[i] }, enumerable: !0 }) } }), n }(t, e) } }, state: { get: function () { return h(t.state, n) } } }), i }(t, a, n); o.forEachMutation(function (e, n) { !function (t, e, n, o) { (t._mutations[e] || (t._mutations[e] = [])).push(function (e) { n.call(t, o.state, e) }) }(t, a + n, e, f) }), o.forEachAction(function (e, n) { var o = e.root ? n : a + n, i = e.handler || e; !function (t, e, n, o) { (t._actions[e] || (t._actions[e] = [])).push(function (e, i) { var r, s = n.call(t, { dispatch: o.dispatch, commit: o.commit, getters: o.getters, state: o.state, rootGetters: t.getters, rootState: t.state }, e, i); return (r = s) && "function" == typeof r.then || (s = Promise.resolve(s)), t._devtoolHook ? s.catch(function (e) { throw t._devtoolHook.emit("vuex:error", e), e }) : s }) }(t, o, i, f) }), o.forEachGetter(function (e, n) { !function (t, e, n, o) { if (t._wrappedGetters[e]) return; t._wrappedGetters[e] = function (t) { return n(o.state, o.getters, t.state, t.getters) } }(t, a + n, e, f) }), o.forEachChild(function (o, i) { p(t, e, n.concat(i), o, r) }) } function h (t, e) { return e.length ? e.reduce(function (t, e) { return t[e] }, t) : t } function l (t, e, n) { var o; return null !== (o = t) && "object" == typeof o && t.type && (n = e, e = t, t = t.type), { type: t, payload: e, options: n } } function d (t) { i && t === i || function (t) { if (Number(t.version.split(".")[0]) >= 2) t.mixin({ beforeCreate: n }); else { var e = t.prototype._init; t.prototype._init = function (t) { void 0 === t && (t = {}), t.init = t.init ? [n].concat(t.init) : n, e.call(this, t) } } function n () { var t = this.$options; t.store ? this.$store = "function" == typeof t.store ? t.store() : t.store : t.parent && t.parent.$store && (this.$store = t.parent.$store) } }(i = t) } a.state.get = function () { return this._vm._data.$$state }, a.state.set = function (t) { }, s.prototype.commit = function (t, e, n) { var o = this, i = l(t, e, n), r = i.type, s = i.payload, a = { type: r, payload: s }, c = this._mutations[r]; c && (this._withCommit(function () { c.forEach(function (t) { t(s) }) }), this._subscribers.forEach(function (t) { return t(a, o.state) })) }, s.prototype.dispatch = function (t, e) { var n = this, o = l(t, e), i = o.type, r = o.payload, s = { type: i, payload: r }, a = this._actions[i]; if (a) { try { this._actionSubscribers.filter(function (t) { return t.before }).forEach(function (t) { return t.before(s, n.state) }) } catch (t) { } return (a.length > 1 ? Promise.all(a.map(function (t) { return t(r) })) : a[0](r)).then(function (t) { try { n._actionSubscribers.filter(function (t) { return t.after }).forEach(function (t) { return t.after(s, n.state) }) } catch (t) { } return t }) } }, s.prototype.subscribe = function (t) { return c(t, this._subscribers) }, s.prototype.subscribeAction = function (t) { return c("function" == typeof t ? { before: t } : t, this._actionSubscribers) }, s.prototype.watch = function (t, e, n) { var o = this; return this._watcherVM.$watch(function () { return t(o.state, o.getters) }, e, n) }, s.prototype.replaceState = function (t) { var e = this; this._withCommit(function () { e._vm._data.$$state = t }) }, s.prototype.registerModule = function (t, e, n) { void 0 === n && (n = {}), "string" == typeof t && (t = [t]), this._modules.register(t, e), p(this, this.state, t, this._modules.get(t), n.preserveState), f(this, this.state) }, s.prototype.unregisterModule = function (t) { var e = this; "string" == typeof t && (t = [t]), this._modules.unregister(t), this._withCommit(function () { var n = h(e.state, t.slice(0, -1)); i.delete(n, t[t.length - 1]) }), u(this) }, s.prototype.hotUpdate = function (t) { this._modules.update(t), u(this, !0) }, s.prototype._withCommit = function (t) { var e = this._committing; this._committing = !0, t(), this._committing = e }, Object.defineProperties(s.prototype, a); var m = b(function (t, e) { var n = {}; return g(e).forEach(function (e) { var o = e.key, i = e.val; n[o] = function () { var e = this.$store.state, n = this.$store.getters; if (t) { var o = w(this.$store, "mapState", t); if (!o) return; e = o.context.state, n = o.context.getters } return "function" == typeof i ? i.call(this, e, n) : e[i] }, n[o].vuex = !0 }), n }), v = b(function (t, e) { var n = {}; return g(e).forEach(function (e) { var o = e.key, i = e.val; n[o] = function () { for (var e = [], n = arguments.length; n--;)e[n] = arguments[n]; var o = this.$store.commit; if (t) { var r = w(this.$store, "mapMutations", t); if (!r) return; o = r.context.commit } return "function" == typeof i ? i.apply(this, [o].concat(e)) : o.apply(this.$store, [i].concat(e)) } }), n }), _ = b(function (t, e) { var n = {}; return g(e).forEach(function (e) { var o = e.key, i = e.val; i = t + i, n[o] = function () { if (!t || w(this.$store, "mapGetters", t)) return this.$store.getters[i] }, n[o].vuex = !0 }), n }), y = b(function (t, e) { var n = {}; return g(e).forEach(function (e) { var o = e.key, i = e.val; n[o] = function () { for (var e = [], n = arguments.length; n--;)e[n] = arguments[n]; var o = this.$store.dispatch; if (t) { var r = w(this.$store, "mapActions", t); if (!r) return; o = r.context.dispatch } return "function" == typeof i ? i.apply(this, [o].concat(e)) : o.apply(this.$store, [i].concat(e)) } }), n }); function g (t) { return Array.isArray(t) ? t.map(function (t) { return { key: t, val: t } }) : Object.keys(t).map(function (e) { return { key: e, val: t[e] } }) } function b (t) { return function (e, n) { return "string" != typeof e ? (n = e, e = "") : "/" !== e.charAt(e.length - 1) && (e += "/"), t(e, n) } } function w (t, e, n) { return t._modulesNamespaceMap[n] } return { Store: s, install: d, version: "3.1.1", mapState: m, mapMutations: v, mapGetters: _, mapActions: y, createNamespacedHelpers: function (t) { return { mapState: m.bind(null, t), mapGetters: _.bind(null, t), mapMutations: v.bind(null, t), mapActions: y.bind(null, t) } } } });