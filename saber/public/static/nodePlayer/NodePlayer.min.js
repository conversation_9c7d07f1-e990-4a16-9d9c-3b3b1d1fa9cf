var Module=typeof Module!=="undefined"?Module:{};const NP_LOGLEVEL={NONE:0,ERROR:1,INFO:2,DEBUG:3};typeof Module.logLevel=="undefined"&&(Module.logLevel=NP_LOGLEVEL.INFO);const logTime=()=>{let e=new Date;return e.toLocaleDateString()+" "+e.toLocaleTimeString([],{hour12:!1})},NP_ERROR=(...e)=>{Module.logLevel<NP_LOGLEVEL.ERROR||console.error(logTime(),"[ERROR]",...e)},NP_INFO=(...e)=>{Module.logLevel<NP_LOGLEVEL.INFO||console.log(logTime(),"[INFO]",...e)},NP_DEBUG=(...e)=>{Module.logLevel<NP_LOGLEVEL.DEBUG||console.log(logTime(),"[DEBUG]",...e)};Module.print=NP_INFO,Module.printErr=NP_ERROR;var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];function warnOnce(text){if(!warnOnce.shown)warnOnce.shown={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;err(text)}}var tempRet0=0;var setTempRet0=function(value){tempRet0=value};var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}function getCFunc(ident){var func=Module["_"+ident];assert(func,"Cannot call unknown function "+ident+", make sure it is exported");return func}function ccall(ident,returnType,argTypes,args,opts){var toC={"string":function(str){var ret=0;if(str!==null&&str!==undefined&&str!==0){var len=(str.length<<2)+1;ret=stackAlloc(len);stringToUTF8(str,ret,len)}return ret},"array":function(arr){var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string")return UTF8ToString(ret);if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret}function cwrap(ident,returnType,argTypes,opts){argTypes=argTypes||[];var numericArgs=argTypes.every(function(type){return type==="number"});var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return function(){return ccall(ident,returnType,argTypes,arguments,opts)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATEXIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){runtimeExited=true}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){{if(Module["onAbort"]){Module["onAbort"](what)}}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile="NodePlayer.min.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmMemory=Module["asm"]["Ha"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module["asm"]["ib"];addOnInit(Module["asm"]["Ia"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync();return{}}var ASM_CONSTS={200269:function($0,$1){var np=NP[$0];var sl=SL[$1];function emitState(){np.emit("audioState",sl.audioContext.state=="running",sl.audioContext.state)}function checkState(){if(sl.audioContext.state!="running"){emitState()}}if(np&&sl&&sl.audioContext){sl.audioContext.resume().then(emitState);setTimeout(checkState,1e3)}},200606:function($0,$1,$2){var self=NP[$0];self.emit("click",$1,$2)},200657:function($0,$1){var self=NP[$0];self.emit("videoFrame",$1)},200709:function($0,$1,$2,$3){var self=NP[$0];self.emit("videoInfo",$1,$2,UTF8ToString($3))},200782:function($0){NP[$0]={}},200799:function($0){var self=NP[$0];if(self&&self.ve&&self.ve.getContext("webgl")&&self.ve.getContext("webgl").getExtension("WEBGL_lose_context")){self.ve.getContext("webgl").getExtension("WEBGL_lose_context").loseContext()}},201022:function($0){var self=NP[$0];self.emit("buffer","empty")},201075:function($0,$1,$2){var self=NP[$0];self.emit("metadata",new Uint8Array(Module.HEAPU8.buffer,$1,$2).slice(0,$2));return self.isVod},201199:function(){NP={};NB={};if(typeof npAllReady=="function"){npAllReady()}if(typeof nbAllReady=="function"){nbAllReady()}window.nbAllReadyFlag=true;window.npAllReadyFlag=true},201393:function($0){var self=NP[$0];self.emit("buffer","buffering")},201450:function($0){var self=NP[$0];self.emit("buffer","full")},201502:function($0,$1,$2,$3){var self=NP[$0];self.emit("videoSei",new Uint8Array(Module.HEAPU8.buffer,$1,$2),$3)},201598:function($0,$1,$2,$3){var self=NP[$0];self.emit("audioInfo",$1,$2,UTF8ToString($3))},201671:function($0,$1,$2,$3){var self=NP[$0];self.emit("videoInfo",$1,$2,UTF8ToString($3))},201744:function($0,$1){var self=NP[$0];self.emit("videoFrame",$1)},201796:function($0,$1,$2,$3,$4){var self=NP[$0];if($2>0){var data=new Uint8Array(Module.HEAPU8.buffer,$1,$2);self.loader.send(data)}if($4>0){var data=new Uint8Array(Module.HEAPU8.buffer,$3,$4);self.loader.send(data)}},202013:function($0,$1,$2){var self=NP[$0];self.emit("metadata",new Uint8Array(Module.HEAPU8.buffer,$1,$2).slice(0,$2))},202118:function($0){var self=NP[$0];self.emit("buffer","empty")},202171:function($0,$1,$2,$3,$4,$5,$6){var self=NP[$0];self.emit("stats",{"buf":$1,"fps":$2,"abps":$3,"vbps":$4,"abuf":$5,"ts":$6})},202290:function($0,$1){let self=NB[$0];self.ws=WS.sockets[$1]},202339:function($0){let self=NB[$0];self.emit("start")},202382:function($0){let self=NB[$0];self.emit("stop")},202424:function($0){let self=NB[$0];self.emit("error","websocket error")},202486:function($0,$1){if(typeof SL=="undefined"){SL={}}SL[$0]={};NP[$1].sl=$1;if(window.workletAudioEngine){return 1024}else if(window.activeAudioEngine){return 4800}else{return 4096}},202684:function($0){if(typeof SL[$0]=="object"){delete SL[$0]}},202739:function($0,$1,$2,$3,$4,$5){var AudioContext=window.webkitAudioContext||window.AudioContext;var sl=SL[$0];sl.audioContext=new AudioContext({sampleRate:48e3});sl.gainNode=sl.audioContext.createGain();if(window.activeAudioEngine){var interval=1e3*$1/sl.audioContext.sampleRate;sl.startTime=0;sl.timer=setInterval(function(){var ret=dynCall("ii",$3,[$0]);var audioSource=sl.audioContext.createBufferSource();var audioBuffer=sl.audioContext.createBuffer($2,$1,sl.audioContext.sampleRate);var channelData0=audioBuffer.getChannelData(0);var channelData1=audioBuffer.getChannelData(1);if(channelData0&&channelData1){if(ret==$1){channelData0.set(Module.HEAPF32.subarray($4>>2,($4>>2)+$1));channelData1.set(Module.HEAPF32.subarray($5>>2,($5>>2)+$1));if(sl.startTime<sl.audioContext.currentTime){sl.startTime=sl.audioContext.currentTime}audioSource.buffer=audioBuffer;audioSource.connect(sl.gainNode);audioSource.start(sl.startTime);sl.startTime+=audioBuffer.duration}else{channelData0.fill(0);channelData1.fill(0)}}},interval)}else if(window.workletAudioEngine){sl.audioContext.audioWorklet.addModule(NodePlayer.WAB()).then(()=>{sl.workletProcessorNode=new AudioWorkletNode(sl.audioContext,"wa-processor",{outputChannelCount:[$2]});sl.workletProcessorNode.connect(sl.gainNode);sl.workletProcessorNode.port.postMessage({"message":"init","bufferSize":$1});sl.workletProcessorNode.port.onmessage=function(e){if(sl.workletProcessorNode){var ret=dynCall("ii",$3,[$0]);if(ret==$1){var samples0=new Float32Array(Module.HEAPU8.buffer,$4,$1).slice(0,$1);var samples1=new Float32Array(Module.HEAPU8.buffer,$5,$1).slice(0,$1);sl.workletProcessorNode.port.postMessage({"message":"data","buffer":[samples0,samples1]})}else{sl.workletProcessorNode.port.postMessage({"message":"zero"})}}}})}else{sl.scriptProcessorNode=sl.audioContext.createScriptProcessor($1,0,$2);sl.scriptProcessorNode.onaudioprocess=function(e){if(sl.audioContext){var ret=dynCall("ii",$3,[$0]);var channelData0=e.outputBuffer.getChannelData(0);var channelData1=e.outputBuffer.getChannelData(1);if(channelData0&&channelData1){if(ret==$1){channelData0.set(Module.HEAPF32.subarray($4>>2,($4>>2)+$1));channelData1.set(Module.HEAPF32.subarray($5>>2,($5>>2)+$1))}else{channelData0.fill(0);channelData1.fill(0)}}}};sl.scriptProcessorNode.connect(sl.gainNode)}sl.gainNode.connect(sl.audioContext.destination);sl.audioContext.resume();return sl.audioContext.sampleRate},205352:function($0){var sl=SL[$0];if(sl.gainNode!=undefined){sl.gainNode.disconnect();sl.gainNode=undefined}if(sl.scriptProcessorNode!=undefined){sl.scriptProcessorNode.disconnect();sl.scriptProcessorNode=undefined}if(sl.audioContext!=undefined){sl.audioContext.close();sl.audioContext=undefined}if(sl.timer!=undefined){clearInterval(sl.timer)}return 0},205734:function($0,$1){var sl=SL[$0];sl.gainNode.gain.value=$1},205784:function($0){let np=NP[$0];return np.width},205822:function($0){let np=NP[$0];return np.height},205861:function($0,$1,$2,$3,$4,$5){let np=NP[$0];let metadata={};metadata.id=1;metadata.type="video";metadata.timescale=1e3;metadata.duration=0;metadata.avcc=Module.HEAPU8.subarray($1,$1+$2);metadata.codecWidth=$3;metadata.codecHeight=$4;metadata.codec=$5;let metabox=NodePlayer.MP4.generateInitSegment(metadata);np.mse.isAvc=metadata.codec==512;np.mse.appendBuffer(metabox.buffer);np.mse.sequenceNumber=0;np.cacheTrack=null;np.timeInit=false;if(np.isDOC){np.veCtx=np.ve.getContext("2d");np.lastScaleMode=np.scaleMode}},206413:function($0){let np=NP[$0];np.isEmitInfo=false;np.decoder=null;np.cacheTrack=null},206498:function($0,$1,$2,$3,$4,$5){let np=NP[$0];let data=Module.HEAPU8.subarray($1,$1+$2).slice(0,$2);let bytes=$2;let cts=$3;let dts=$4;let flags=$5;if(np.vi.buffered.length>1){np.mse.removeBuffer(np.vi.buffered.start(0),np.vi.buffered.end(0));np.timeInit=false}if(np.vi.drop&&dts-np.cacheTrack.dts>1e3){np.vi.drop=false;np.cacheTrack={}}else if(np.cacheTrack&&dts>np.cacheTrack.dts){let mdatBytes=8+np.cacheTrack.size;let mdatbox=new Uint8Array(mdatBytes);mdatbox[0]=mdatBytes>>>24&255;mdatbox[1]=mdatBytes>>>16&255;mdatbox[2]=mdatBytes>>>8&255;mdatbox[3]=mdatBytes&255;mdatbox.set(NodePlayer.MP4.types.mdat,4);mdatbox.set(np.cacheTrack.data,8);np.cacheTrack.duration=dts-np.cacheTrack.dts;let moofbox=NodePlayer.MP4.moof(np.cacheTrack,np.cacheTrack.dts);let result=new Uint8Array(moofbox.byteLength+mdatbox.byteLength);result.set(moofbox,0);result.set(mdatbox,moofbox.byteLength);np.mse.appendBuffer(result.buffer)}else{np.timeInit=false;np.cacheTrack={}}np.cacheTrack.id=1;np.cacheTrack.sn=++np.mse.sequenceNumber;np.cacheTrack.size=bytes;np.cacheTrack.dts=dts;np.cacheTrack.cts=cts;np.cacheTrack.data=data;np.cacheTrack.flags={};np.cacheTrack.flags.isLeading=0;np.cacheTrack.flags.dependsOn=flags?2:1;np.cacheTrack.flags.isDependedOn=flags?1:0;np.cacheTrack.flags.hasRedundancy=0;np.cacheTrack.flags.isNonSync=flags?0:1;if(!np.timeInit&&np.vi.buffered.length==1){np.timeInit=true;np.vi.currentTime=np.vi.buffered.end(0)}if(!np.isEmitInfo&&np.vi.videoWidth>0&&np.vi.videoHeight>0){var codecName=np.mse.isAvc?"h264":"hevc";NP_INFO("got video info codec="+codecName+" w="+np.vi.videoWidth+" h="+np.vi.videoHeight+" f=mse");np.emit("videoInfo",np.vi.videoWidth,np.vi.videoHeight,codecName);np.isEmitInfo=true}if(np.isDOC){let viewX=0;let viewY=0;let viewWidth=np.ve.width;let viewHeight=np.ve.height;let videoWidth=np.vi.videoWidth;let videoHeight=np.vi.videoHeight;if(np.scaleMode>0){let dW=np.ve.width/videoWidth;let dH=np.ve.height/videoHeight;if(np.scaleMode==1){let ratio=Math.min(dW,dH);viewWidth=videoWidth*ratio;viewHeight=videoHeight*ratio;viewY=(np.ve.height-viewHeight)/2}else{let ratio=Math.max(dW,dH);viewWidth=videoWidth*ratio;viewHeight=videoHeight*ratio;viewX=(np.ve.width-viewWidth)/2}}if(np.lastScaleMode!=np.scaleMode){np.veCtx.fillStyle="#000000";np.veCtx.fillRect(0,0,np.ve.width,np.ve.height);np.lastScaleMode=np.scaleMode}np.veCtx.drawImage(np.vi,viewX,viewY,viewWidth,viewHeight)}},209230:function($0,$1){let np=NP[$0];np.vi.drop=$1;if(np.vi.buffered.length>0){if(np.vi.buffered.end(0)-np.vi.currentTime>1){np.vi.currentTime=np.vi.buffered.end(0)}}},209401:function($0,$1,$2,$3,$4,$5){let np=NP[$0];let codecStr=UTF8ToString($1);let codecName=codecStr.startsWith("avc")?"h264":"hevc";let avcC=Module.HEAPU8.subarray($2,$2+$3);let width=$4;let height=$5;let scaleMode=np.scaleMode;np.render=new NodePlayer.WGL("webgl2",np.ve);np.decoder=new VideoDecoder({output:function(frame){if(!np.isEmitInfo){np.isEmitInfo=true;NP_INFO("got video info codec="+codecName+" w="+frame.codedWidth+" h="+frame.codedHeight+" f=wcs");np.emit("videoInfo",frame.codedWidth,frame.codedHeight,codecName)}let viewX=0;let viewY=0;let viewWidth=np.ve.width;let viewHeight=np.ve.height;if(np.scaleMode>0){let dW=np.ve.width/frame.codedWidth;let dH=np.ve.height/frame.codedHeight;if(np.scaleMode==1){let ratio=Math.min(dW,dH);viewWidth=frame.codedWidth*ratio;viewHeight=frame.codedHeight*ratio;viewY=(np.ve.height-viewHeight)/2}else{let ratio=Math.max(dW,dH);viewWidth=frame.codedWidth*ratio;viewHeight=frame.codedHeight*ratio;viewX=(np.ve.width-viewWidth)/2}}if(scaleMode!=np.scaleMode){scaleMode=np.scaleMode}np.render.draw(frame,viewX,viewY,viewWidth,viewHeight)},error:function(e){NP_INFO("WebCodecs VideoDecoder error",e);np.decoder=null;np.emit("error",e)}});np.decoder.configure({codec:codecStr,codedWidth:width,codedHeight:height,description:avcC})},210836:function($0){let np=NP[$0];np.isEmitInfo=false;if(np.render){np.render.clear()}if(np.decoder){np.decoder.close();np.decoder=null}},210976:function($0,$1,$2,$3,$4){let np=NP[$0];let decoder=np.decoder;let subData=Module.HEAPU8.subarray($1,$1+$2);let chunk=new EncodedVideoChunk({data:subData,timestamp:$3,type:$4==1?"key":"delta"});decoder.decode(chunk)}};function gas(){var jsString=atob("VW5hdXRob3JpemVkIQ==");var lengthBytes=lengthBytesUTF8(jsString)+1;var stringOnWasmHeap=_malloc(lengthBytes);stringToUTF8(jsString,stringOnWasmHeap,lengthBytes+1);return stringOnWasmHeap}function gds(){var jsString=document.domain;var lengthBytes=lengthBytesUTF8(jsString)+1;var stringOnWasmHeap=_malloc(lengthBytes);stringToUTF8(jsString,stringOnWasmHeap,lengthBytes+1);return stringOnWasmHeap}function ges(){var jsString=atob("RW5kIG9mIHRoZSB0cmlhbCE=");var lengthBytes=lengthBytesUTF8(jsString)+1;var stringOnWasmHeap=_malloc(lengthBytes);stringToUTF8(jsString,stringOnWasmHeap,lengthBytes+1);return stringOnWasmHeap}function get_a_str(){var jsString=atob("VW5hdXRob3JpemVkIQ==");var lengthBytes=lengthBytesUTF8(jsString)+1;var stringOnWasmHeap=_malloc(lengthBytes);stringToUTF8(jsString,stringOnWasmHeap,lengthBytes+1);return stringOnWasmHeap}function get_audio_state(render_sl){var self=SL[render_sl];return self&&self.audioContext&&self.audioContext.state=="suspended"}function get_do_str(){var jsString=document.domain;var lengthBytes=lengthBytesUTF8(jsString)+1;var stringOnWasmHeap=_malloc(lengthBytes);stringToUTF8(jsString,stringOnWasmHeap,lengthBytes+1);return stringOnWasmHeap}function get_e_str(){var jsString=atob("RW5kIG9mIHRoZSB0cmlhbCE=");var lengthBytes=lengthBytesUTF8(jsString)+1;var stringOnWasmHeap=_malloc(lengthBytes);stringToUTF8(jsString,stringOnWasmHeap,lengthBytes+1);return stringOnWasmHeap}function get_is_mse(np){var npjs=NP[np];return npjs.isMSE}function get_is_wcs(np){var npjs=NP[np];return npjs.isWCS}function get_js_dir(){var jsString=scriptDirectory;var lengthBytes=lengthBytesUTF8(jsString)+1;var js_dir=_malloc(lengthBytes);stringToUTF8(jsString,js_dir,lengthBytes+1);return js_dir}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){(function(){dynCall_v.call(null,func)})()}else{(function(a1){dynCall_vi.apply(null,[func,a1])})(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}function dynCallLegacy(sig,ptr,args){var f=Module["dynCall_"+sig];return args&&args.length?f.apply(null,[ptr].concat(args)):f.call(null,ptr)}function getWasmTableEntry(funcPtr){return wasmTable.get(funcPtr)}function dynCall(sig,ptr,args){return dynCallLegacy(sig,ptr,args)}function handleException(e){if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function ___syscall_fcntl64(fd,cmd,varargs){SYSCALLS.varargs=varargs;return 0}function ___syscall_open(path,flags,varargs){SYSCALLS.varargs=varargs}function _abort(){abort("")}function _clock(){if(_clock.start===undefined)_clock.start=Date.now();return(Date.now()-_clock.start)*(1e6/1e3)|0}var readAsmConstArgsArray=[];function readAsmConstArgs(sigPtr,buf){readAsmConstArgsArray.length=0;var ch;buf>>=2;while(ch=HEAPU8[sigPtr++]){var readAsmConstArgsDouble=ch<105;if(readAsmConstArgsDouble&&buf&1)buf++;readAsmConstArgsArray.push(readAsmConstArgsDouble?HEAPF64[buf++>>1]:HEAP32[buf]);++buf}return readAsmConstArgsArray}function _emscripten_asm_const_int(code,sigPtr,argbuf){var args=readAsmConstArgs(sigPtr,argbuf);return ASM_CONSTS[code].apply(null,args)}function _emscripten_set_main_loop_timing(mode,value){Browser.mainLoop.timingMode=mode;Browser.mainLoop.timingValue=value;if(!Browser.mainLoop.func){return 1}if(!Browser.mainLoop.running){Browser.mainLoop.running=true}if(mode==0){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setTimeout(){var timeUntilNextTick=Math.max(0,Browser.mainLoop.tickStartTime+value-_emscripten_get_now())|0;setTimeout(Browser.mainLoop.runner,timeUntilNextTick)};Browser.mainLoop.method="timeout"}else if(mode==1){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_rAF(){Browser.requestAnimationFrame(Browser.mainLoop.runner)};Browser.mainLoop.method="rAF"}else if(mode==2){if(typeof setImmediate==="undefined"){var setImmediates=[];var emscriptenMainLoopMessageId="setimmediate";var Browser_setImmediate_messageHandler=function(event){if(event.data===emscriptenMainLoopMessageId||event.data.target===emscriptenMainLoopMessageId){event.stopPropagation();setImmediates.shift()()}};addEventListener("message",Browser_setImmediate_messageHandler,true);setImmediate=function Browser_emulated_setImmediate(func){setImmediates.push(func);if(ENVIRONMENT_IS_WORKER){if(Module["setImmediates"]===undefined)Module["setImmediates"]=[];Module["setImmediates"].push(func);postMessage({target:emscriptenMainLoopMessageId})}else postMessage(emscriptenMainLoopMessageId,"*")}}Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setImmediate(){setImmediate(Browser.mainLoop.runner)};Browser.mainLoop.method="immediate"}return 0}var _emscripten_get_now;_emscripten_get_now=function(){return performance.now()};function _exit(status){exit(status)}function maybeExit(){if(!keepRuntimeAlive()){try{_exit(EXITSTATUS)}catch(e){handleException(e)}}}function setMainLoop(browserIterationFunc,fps,simulateInfiniteLoop,arg,noSetTiming){assert(!Browser.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters.");Browser.mainLoop.func=browserIterationFunc;Browser.mainLoop.arg=arg;var thisMainLoopId=Browser.mainLoop.currentlyRunningMainloop;function checkIsRunning(){if(thisMainLoopId<Browser.mainLoop.currentlyRunningMainloop){maybeExit();return false}return true}Browser.mainLoop.running=false;Browser.mainLoop.runner=function Browser_mainLoop_runner(){if(ABORT)return;if(Browser.mainLoop.queue.length>0){var start=Date.now();var blocker=Browser.mainLoop.queue.shift();blocker.func(blocker.arg);if(Browser.mainLoop.remainingBlockers){var remaining=Browser.mainLoop.remainingBlockers;var next=remaining%1==0?remaining-1:Math.floor(remaining);if(blocker.counted){Browser.mainLoop.remainingBlockers=next}else{next=next+.5;Browser.mainLoop.remainingBlockers=(8*remaining+next)/9}}out('main loop blocker "'+blocker.name+'" took '+(Date.now()-start)+" ms");Browser.mainLoop.updateStatus();if(!checkIsRunning())return;setTimeout(Browser.mainLoop.runner,0);return}if(!checkIsRunning())return;Browser.mainLoop.currentFrameNumber=Browser.mainLoop.currentFrameNumber+1|0;if(Browser.mainLoop.timingMode==1&&Browser.mainLoop.timingValue>1&&Browser.mainLoop.currentFrameNumber%Browser.mainLoop.timingValue!=0){Browser.mainLoop.scheduler();return}else if(Browser.mainLoop.timingMode==0){Browser.mainLoop.tickStartTime=_emscripten_get_now()}Browser.mainLoop.runIter(browserIterationFunc);if(!checkIsRunning())return;if(typeof SDL==="object"&&SDL.audio&&SDL.audio.queueNewAudioData)SDL.audio.queueNewAudioData();Browser.mainLoop.scheduler()};if(!noSetTiming){if(fps&&fps>0)_emscripten_set_main_loop_timing(0,1e3/fps);else _emscripten_set_main_loop_timing(1,1);Browser.mainLoop.scheduler()}if(simulateInfiniteLoop){throw"unwind"}}function callUserCallback(func,synchronous){if(runtimeExited||ABORT){return}if(synchronous){func();return}try{func()}catch(e){handleException(e)}}function safeSetTimeout(func,timeout){return setTimeout(function(){callUserCallback(func)},timeout)}var Browser={mainLoop:{running:false,scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){Browser.mainLoop.scheduler=null;Browser.mainLoop.currentlyRunningMainloop++},resume:function(){Browser.mainLoop.currentlyRunningMainloop++;var timingMode=Browser.mainLoop.timingMode;var timingValue=Browser.mainLoop.timingValue;var func=Browser.mainLoop.func;Browser.mainLoop.func=null;setMainLoop(func,0,false,Browser.mainLoop.arg,true);_emscripten_set_main_loop_timing(timingMode,timingValue);Browser.mainLoop.scheduler()},updateStatus:function(){if(Module["setStatus"]){var message=Module["statusMessage"]||"Please wait...";var remaining=Browser.mainLoop.remainingBlockers;var expected=Browser.mainLoop.expectedBlockers;if(remaining){if(remaining<expected){Module["setStatus"](message+" ("+(expected-remaining)+"/"+expected+")")}else{Module["setStatus"](message)}}else{Module["setStatus"]("")}}},runIter:function(func){if(ABORT)return;if(Module["preMainLoop"]){var preRet=Module["preMainLoop"]();if(preRet===false){return}}callUserCallback(func);if(Module["postMainLoop"])Module["postMainLoop"]()}},isFullscreen:false,pointerLock:false,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(!Module["preloadPlugins"])Module["preloadPlugins"]=[];if(Browser.initted)return;Browser.initted=true;try{new Blob;Browser.hasBlobConstructor=true}catch(e){Browser.hasBlobConstructor=false;out("warning: no blob constructor, cannot create blobs with mimetypes")}Browser.BlobBuilder=typeof MozBlobBuilder!="undefined"?MozBlobBuilder:typeof WebKitBlobBuilder!="undefined"?WebKitBlobBuilder:!Browser.hasBlobConstructor?out("warning: no BlobBuilder"):null;Browser.URLObject=typeof window!="undefined"?window.URL?window.URL:window.webkitURL:undefined;if(!Module.noImageDecoding&&typeof Browser.URLObject==="undefined"){out("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available.");Module.noImageDecoding=true}var imagePlugin={};imagePlugin["canHandle"]=function imagePlugin_canHandle(name){return!Module.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(name)};imagePlugin["handle"]=function imagePlugin_handle(byteArray,name,onload,onerror){var b=null;if(Browser.hasBlobConstructor){try{b=new Blob([byteArray],{type:Browser.getMimetype(name)});if(b.size!==byteArray.length){b=new Blob([new Uint8Array(byteArray).buffer],{type:Browser.getMimetype(name)})}}catch(e){warnOnce("Blob constructor present but fails: "+e+"; falling back to blob builder")}}if(!b){var bb=new Browser.BlobBuilder;bb.append(new Uint8Array(byteArray).buffer);b=bb.getBlob()}var url=Browser.URLObject.createObjectURL(b);var img=new Image;img.onload=function img_onload(){assert(img.complete,"Image "+name+" could not be decoded");var canvas=document.createElement("canvas");canvas.width=img.width;canvas.height=img.height;var ctx=canvas.getContext("2d");ctx.drawImage(img,0,0);Module["preloadedImages"][name]=canvas;Browser.URLObject.revokeObjectURL(url);if(onload)onload(byteArray)};img.onerror=function img_onerror(event){out("Image "+url+" could not be decoded");if(onerror)onerror()};img.src=url};Module["preloadPlugins"].push(imagePlugin);var audioPlugin={};audioPlugin["canHandle"]=function audioPlugin_canHandle(name){return!Module.noAudioDecoding&&name.substr(-4)in{".ogg":1,".wav":1,".mp3":1}};audioPlugin["handle"]=function audioPlugin_handle(byteArray,name,onload,onerror){var done=false;function finish(audio){if(done)return;done=true;Module["preloadedAudios"][name]=audio;if(onload)onload(byteArray)}function fail(){if(done)return;done=true;Module["preloadedAudios"][name]=new Audio;if(onerror)onerror()}if(Browser.hasBlobConstructor){try{var b=new Blob([byteArray],{type:Browser.getMimetype(name)})}catch(e){return fail()}var url=Browser.URLObject.createObjectURL(b);var audio=new Audio;audio.addEventListener("canplaythrough",function(){finish(audio)},false);audio.onerror=function audio_onerror(event){if(done)return;out("warning: browser could not fully decode audio "+name+", trying slower base64 approach");function encode64(data){var BASE="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";var PAD="=";var ret="";var leftchar=0;var leftbits=0;for(var i=0;i<data.length;i++){leftchar=leftchar<<8|data[i];leftbits+=8;while(leftbits>=6){var curr=leftchar>>leftbits-6&63;leftbits-=6;ret+=BASE[curr]}}if(leftbits==2){ret+=BASE[(leftchar&3)<<4];ret+=PAD+PAD}else if(leftbits==4){ret+=BASE[(leftchar&15)<<2];ret+=PAD}return ret}audio.src="data:audio/x-"+name.substr(-3)+";base64,"+encode64(byteArray);finish(audio)};audio.src=url;safeSetTimeout(function(){finish(audio)},1e4)}else{return fail()}};Module["preloadPlugins"].push(audioPlugin);function pointerLockChange(){Browser.pointerLock=document["pointerLockElement"]===Module["canvas"]||document["mozPointerLockElement"]===Module["canvas"]||document["webkitPointerLockElement"]===Module["canvas"]||document["msPointerLockElement"]===Module["canvas"]}var canvas=Module["canvas"];if(canvas){canvas.requestPointerLock=canvas["requestPointerLock"]||canvas["mozRequestPointerLock"]||canvas["webkitRequestPointerLock"]||canvas["msRequestPointerLock"]||function(){};canvas.exitPointerLock=document["exitPointerLock"]||document["mozExitPointerLock"]||document["webkitExitPointerLock"]||document["msExitPointerLock"]||function(){};canvas.exitPointerLock=canvas.exitPointerLock.bind(document);document.addEventListener("pointerlockchange",pointerLockChange,false);document.addEventListener("mozpointerlockchange",pointerLockChange,false);document.addEventListener("webkitpointerlockchange",pointerLockChange,false);document.addEventListener("mspointerlockchange",pointerLockChange,false);if(Module["elementPointerLock"]){canvas.addEventListener("click",function(ev){if(!Browser.pointerLock&&Module["canvas"].requestPointerLock){Module["canvas"].requestPointerLock();ev.preventDefault()}},false)}}},createContext:function(canvas,useWebGL,setInModule,webGLContextAttributes){if(useWebGL&&Module.ctx&&canvas==Module.canvas)return Module.ctx;var ctx;var contextHandle;if(useWebGL){var contextAttributes={antialias:false,alpha:false,majorVersion:1};if(webGLContextAttributes){for(var attribute in webGLContextAttributes){contextAttributes[attribute]=webGLContextAttributes[attribute]}}if(typeof GL!=="undefined"){contextHandle=GL.createContext(canvas,contextAttributes);if(contextHandle){ctx=GL.getContext(contextHandle).GLctx}}}else{ctx=canvas.getContext("2d")}if(!ctx)return null;if(setInModule){if(!useWebGL)assert(typeof GLctx==="undefined","cannot set in module if GLctx is used, but we are a non-GL context that would replace it");Module.ctx=ctx;if(useWebGL)GL.makeContextCurrent(contextHandle);Module.useWebGL=useWebGL;Browser.moduleContextCreatedCallbacks.forEach(function(callback){callback()});Browser.init()}return ctx},destroyContext:function(canvas,useWebGL,setInModule){},fullscreenHandlersInstalled:false,lockPointer:undefined,resizeCanvas:undefined,requestFullscreen:function(lockPointer,resizeCanvas){Browser.lockPointer=lockPointer;Browser.resizeCanvas=resizeCanvas;if(typeof Browser.lockPointer==="undefined")Browser.lockPointer=true;if(typeof Browser.resizeCanvas==="undefined")Browser.resizeCanvas=false;var canvas=Module["canvas"];function fullscreenChange(){Browser.isFullscreen=false;var canvasContainer=canvas.parentNode;if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvasContainer){canvas.exitFullscreen=Browser.exitFullscreen;if(Browser.lockPointer)canvas.requestPointerLock();Browser.isFullscreen=true;if(Browser.resizeCanvas){Browser.setFullscreenCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}else{canvasContainer.parentNode.insertBefore(canvas,canvasContainer);canvasContainer.parentNode.removeChild(canvasContainer);if(Browser.resizeCanvas){Browser.setWindowedCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}if(Module["onFullScreen"])Module["onFullScreen"](Browser.isFullscreen);if(Module["onFullscreen"])Module["onFullscreen"](Browser.isFullscreen)}if(!Browser.fullscreenHandlersInstalled){Browser.fullscreenHandlersInstalled=true;document.addEventListener("fullscreenchange",fullscreenChange,false);document.addEventListener("mozfullscreenchange",fullscreenChange,false);document.addEventListener("webkitfullscreenchange",fullscreenChange,false);document.addEventListener("MSFullscreenChange",fullscreenChange,false)}var canvasContainer=document.createElement("div");canvas.parentNode.insertBefore(canvasContainer,canvas);canvasContainer.appendChild(canvas);canvasContainer.requestFullscreen=canvasContainer["requestFullscreen"]||canvasContainer["mozRequestFullScreen"]||canvasContainer["msRequestFullscreen"]||(canvasContainer["webkitRequestFullscreen"]?function(){canvasContainer["webkitRequestFullscreen"](Element["ALLOW_KEYBOARD_INPUT"])}:null)||(canvasContainer["webkitRequestFullScreen"]?function(){canvasContainer["webkitRequestFullScreen"](Element["ALLOW_KEYBOARD_INPUT"])}:null);canvasContainer.requestFullscreen()},exitFullscreen:function(){if(!Browser.isFullscreen){return false}var CFS=document["exitFullscreen"]||document["cancelFullScreen"]||document["mozCancelFullScreen"]||document["msExitFullscreen"]||document["webkitCancelFullScreen"]||function(){};CFS.apply(document,[]);return true},nextRAF:0,fakeRequestAnimationFrame:function(func){var now=Date.now();if(Browser.nextRAF===0){Browser.nextRAF=now+1e3/60}else{while(now+2>=Browser.nextRAF){Browser.nextRAF+=1e3/60}}var delay=Math.max(Browser.nextRAF-now,0);setTimeout(func,delay)},requestAnimationFrame:function(func){if(typeof requestAnimationFrame==="function"){requestAnimationFrame(func);return}var RAF=Browser.fakeRequestAnimationFrame;RAF(func)},safeSetTimeout:function(func){return safeSetTimeout(func)},safeRequestAnimationFrame:function(func){return Browser.requestAnimationFrame(function(){callUserCallback(func)})},getMimetype:function(name){return{"jpg":"image/jpeg","jpeg":"image/jpeg","png":"image/png","bmp":"image/bmp","ogg":"audio/ogg","wav":"audio/wav","mp3":"audio/mpeg"}[name.substr(name.lastIndexOf(".")+1)]},getUserMedia:function(func){if(!window.getUserMedia){window.getUserMedia=navigator["getUserMedia"]||navigator["mozGetUserMedia"]}window.getUserMedia(func)},getMovementX:function(event){return event["movementX"]||event["mozMovementX"]||event["webkitMovementX"]||0},getMovementY:function(event){return event["movementY"]||event["mozMovementY"]||event["webkitMovementY"]||0},getMouseWheelDelta:function(event){var delta=0;switch(event.type){case"DOMMouseScroll":delta=event.detail/3;break;case"mousewheel":delta=event.wheelDelta/120;break;case"wheel":delta=event.deltaY;switch(event.deltaMode){case 0:delta/=100;break;case 1:delta/=3;break;case 2:delta*=80;break;default:throw"unrecognized mouse wheel delta mode: "+event.deltaMode}break;default:throw"unrecognized mouse wheel event: "+event.type}return delta},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(event){if(Browser.pointerLock){if(event.type!="mousemove"&&"mozMovementX"in event){Browser.mouseMovementX=Browser.mouseMovementY=0}else{Browser.mouseMovementX=Browser.getMovementX(event);Browser.mouseMovementY=Browser.getMovementY(event)}if(typeof SDL!="undefined"){Browser.mouseX=SDL.mouseX+Browser.mouseMovementX;Browser.mouseY=SDL.mouseY+Browser.mouseMovementY}else{Browser.mouseX+=Browser.mouseMovementX;Browser.mouseY+=Browser.mouseMovementY}}else{var rect=Module["canvas"].getBoundingClientRect();var cw=Module["canvas"].width;var ch=Module["canvas"].height;var scrollX=typeof window.scrollX!=="undefined"?window.scrollX:window.pageXOffset;var scrollY=typeof window.scrollY!=="undefined"?window.scrollY:window.pageYOffset;if(event.type==="touchstart"||event.type==="touchend"||event.type==="touchmove"){var touch=event.touch;if(touch===undefined){return}var adjustedX=touch.pageX-(scrollX+rect.left);var adjustedY=touch.pageY-(scrollY+rect.top);adjustedX=adjustedX*(cw/rect.width);adjustedY=adjustedY*(ch/rect.height);var coords={x:adjustedX,y:adjustedY};if(event.type==="touchstart"){Browser.lastTouches[touch.identifier]=coords;Browser.touches[touch.identifier]=coords}else if(event.type==="touchend"||event.type==="touchmove"){var last=Browser.touches[touch.identifier];if(!last)last=coords;Browser.lastTouches[touch.identifier]=last;Browser.touches[touch.identifier]=coords}return}var x=event.pageX-(scrollX+rect.left);var y=event.pageY-(scrollY+rect.top);x=x*(cw/rect.width);y=y*(ch/rect.height);Browser.mouseMovementX=x-Browser.mouseX;Browser.mouseMovementY=y-Browser.mouseY;Browser.mouseX=x;Browser.mouseY=y}},resizeListeners:[],updateResizeListeners:function(){var canvas=Module["canvas"];Browser.resizeListeners.forEach(function(listener){listener(canvas.width,canvas.height)})},setCanvasSize:function(width,height,noUpdates){var canvas=Module["canvas"];Browser.updateCanvasDimensions(canvas,width,height);if(!noUpdates)Browser.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullscreenCanvasSize:function(){if(typeof SDL!="undefined"){var flags=HEAPU32[SDL.screen>>2];flags=flags|8388608;HEAP32[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},setWindowedCanvasSize:function(){if(typeof SDL!="undefined"){var flags=HEAPU32[SDL.screen>>2];flags=flags&~8388608;HEAP32[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},updateCanvasDimensions:function(canvas,wNative,hNative){if(wNative&&hNative){canvas.widthNative=wNative;canvas.heightNative=hNative}else{wNative=canvas.widthNative;hNative=canvas.heightNative}var w=wNative;var h=hNative;if(Module["forcedAspectRatio"]&&Module["forcedAspectRatio"]>0){if(w/h<Module["forcedAspectRatio"]){w=Math.round(h*Module["forcedAspectRatio"])}else{h=Math.round(w/Module["forcedAspectRatio"])}}if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvas.parentNode&&typeof screen!="undefined"){var factor=Math.min(screen.width/w,screen.height/h);w=Math.round(w*factor);h=Math.round(h*factor)}if(Browser.resizeCanvas){if(canvas.width!=w)canvas.width=w;if(canvas.height!=h)canvas.height=h;if(typeof canvas.style!="undefined"){canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}else{if(canvas.width!=wNative)canvas.width=wNative;if(canvas.height!=hNative)canvas.height=hNative;if(typeof canvas.style!="undefined"){if(w!=wNative||h!=hNative){canvas.style.setProperty("width",w+"px","important");canvas.style.setProperty("height",h+"px","important")}else{canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}}}};function _emscripten_call_worker(id,funcName,data,size,callback,arg){funcName=UTF8ToString(funcName);var info=Browser.workers[id];var callbackId=-1;if(callback){callbackId=info.callbacks.length;info.callbacks.push({func:function(a1,a2,a3){dynCall_viii.apply(null,[callback,a1,a2,a3])},arg:arg});info.awaited++}var transferObject={"funcName":funcName,"callbackId":callbackId,"data":data?new Uint8Array(HEAPU8.subarray(data,data+size)):0};if(data){info.worker.postMessage(transferObject,[transferObject.data.buffer])}else{info.worker.postMessage(transferObject)}}function _emscripten_clear_interval(id){clearInterval(id)}function _emscripten_create_worker(url){url=UTF8ToString(url);var id=Browser.workers.length;var info={worker:new Worker(url),callbacks:[],awaited:0,buffer:0,bufferSize:0};info.worker.onmessage=function info_worker_onmessage(msg){if(ABORT)return;var info=Browser.workers[id];if(!info)return;var callbackId=msg.data["callbackId"];var callbackInfo=info.callbacks[callbackId];if(!callbackInfo)return;if(msg.data["finalResponse"]){info.awaited--;info.callbacks[callbackId]=null}var data=msg.data["data"];if(data){if(!data.byteLength)data=new Uint8Array(data);if(!info.buffer||info.bufferSize<data.length){if(info.buffer)_free(info.buffer);info.bufferSize=data.length;info.buffer=_malloc(data.length)}HEAPU8.set(data,info.buffer);callbackInfo.func(info.buffer,data.length,callbackInfo.arg)}else{callbackInfo.func(0,0,callbackInfo.arg)}};Browser.workers.push(info);return id}function _emscripten_destroy_worker(id){var info=Browser.workers[id];info.worker.terminate();if(info.buffer)_free(info.buffer);Browser.workers[id]=null}var JSEvents={inEventHandler:0,removeAllEventListeners:function(){for(var i=JSEvents.eventHandlers.length-1;i>=0;--i){JSEvents._removeHandler(i)}JSEvents.eventHandlers=[];JSEvents.deferredCalls=[]},registerRemoveEventListeners:function(){if(!JSEvents.removeEventListenersRegistered){__ATEXIT__.push(JSEvents.removeAllEventListeners);JSEvents.removeEventListenersRegistered=true}},deferredCalls:[],deferCall:function(targetFunction,precedence,argsList){function arraysHaveEqualContent(arrA,arrB){if(arrA.length!=arrB.length)return false;for(var i in arrA){if(arrA[i]!=arrB[i])return false}return true}for(var i in JSEvents.deferredCalls){var call=JSEvents.deferredCalls[i];if(call.targetFunction==targetFunction&&arraysHaveEqualContent(call.argsList,argsList)){return}}JSEvents.deferredCalls.push({targetFunction:targetFunction,precedence:precedence,argsList:argsList});JSEvents.deferredCalls.sort(function(x,y){return x.precedence<y.precedence})},removeDeferredCalls:function(targetFunction){for(var i=0;i<JSEvents.deferredCalls.length;++i){if(JSEvents.deferredCalls[i].targetFunction==targetFunction){JSEvents.deferredCalls.splice(i,1);--i}}},canPerformEventHandlerRequests:function(){return JSEvents.inEventHandler&&JSEvents.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(!JSEvents.canPerformEventHandlerRequests()){return}for(var i=0;i<JSEvents.deferredCalls.length;++i){var call=JSEvents.deferredCalls[i];JSEvents.deferredCalls.splice(i,1);--i;call.targetFunction.apply(null,call.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(target,eventTypeString){for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==target&&(!eventTypeString||eventTypeString==JSEvents.eventHandlers[i].eventTypeString)){JSEvents._removeHandler(i--)}}},_removeHandler:function(i){var h=JSEvents.eventHandlers[i];h.target.removeEventListener(h.eventTypeString,h.eventListenerFunc,h.useCapture);JSEvents.eventHandlers.splice(i,1)},registerOrRemoveHandler:function(eventHandler){var jsEventHandler=function jsEventHandler(event){++JSEvents.inEventHandler;JSEvents.currentEventHandler=eventHandler;JSEvents.runDeferredCalls();eventHandler.handlerFunc(event);JSEvents.runDeferredCalls();--JSEvents.inEventHandler};if(eventHandler.callbackfunc){eventHandler.eventListenerFunc=jsEventHandler;eventHandler.target.addEventListener(eventHandler.eventTypeString,jsEventHandler,eventHandler.useCapture);JSEvents.eventHandlers.push(eventHandler);JSEvents.registerRemoveEventListeners()}else{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==eventHandler.target&&JSEvents.eventHandlers[i].eventTypeString==eventHandler.eventTypeString){JSEvents._removeHandler(i--)}}}},getNodeNameForTarget:function(target){if(!target)return"";if(target==window)return"#window";if(target==screen)return"#screen";return target&&target.nodeName?target.nodeName:""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};function maybeCStringToJsString(cString){return cString>2?UTF8ToString(cString):cString}var specialHTMLTargets=[0,document,window];function findEventTarget(target){target=maybeCStringToJsString(target);var domElement=specialHTMLTargets[target]||document.querySelector(target);return domElement}function findCanvasEventTarget(target){return findEventTarget(target)}function _emscripten_get_canvas_element_size(target,width,height){var canvas=findCanvasEventTarget(target);if(!canvas)return-4;HEAP32[width>>2]=canvas.width;HEAP32[height>>2]=canvas.height}function getBoundingClientRect(e){return specialHTMLTargets.indexOf(e)<0?e.getBoundingClientRect():{"left":0,"top":0}}function _emscripten_get_element_css_size(target,width,height){target=findEventTarget(target);if(!target)return-4;var rect=getBoundingClientRect(target);HEAPF64[width>>3]=rect.width;HEAPF64[height>>3]=rect.height;return 0}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}function _emscripten_set_canvas_element_size(target,width,height){var canvas=findCanvasEventTarget(target);if(!canvas)return-4;canvas.width=width;canvas.height=height;return 0}function fillMouseEventData(eventStruct,e,target){HEAPF64[eventStruct>>3]=e.timeStamp;var idx=eventStruct>>2;HEAP32[idx+2]=e.screenX;HEAP32[idx+3]=e.screenY;HEAP32[idx+4]=e.clientX;HEAP32[idx+5]=e.clientY;HEAP32[idx+6]=e.ctrlKey;HEAP32[idx+7]=e.shiftKey;HEAP32[idx+8]=e.altKey;HEAP32[idx+9]=e.metaKey;HEAP16[idx*2+20]=e.button;HEAP16[idx*2+21]=e.buttons;HEAP32[idx+11]=e["movementX"];HEAP32[idx+12]=e["movementY"];var rect=getBoundingClientRect(target);HEAP32[idx+13]=e.clientX-rect.left;HEAP32[idx+14]=e.clientY-rect.top}function registerMouseEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){if(!JSEvents.mouseEvent)JSEvents.mouseEvent=_malloc(72);target=findEventTarget(target);var mouseEventHandlerFunc=function(ev){var e=ev||event;fillMouseEventData(JSEvents.mouseEvent,e,target);if(function(a1,a2,a3){return dynCall_iiii.apply(null,[callbackfunc,a1,a2,a3])}(eventTypeId,JSEvents.mouseEvent,userData))e.preventDefault()};var eventHandler={target:target,allowsDeferredCalls:eventTypeString!="mousemove"&&eventTypeString!="mouseenter"&&eventTypeString!="mouseleave",eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:mouseEventHandlerFunc,useCapture:useCapture};JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_click_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){registerMouseEventCallback(target,userData,useCapture,callbackfunc,4,"click",targetThread);return 0}function _emscripten_set_element_css_size(target,width,height){target=findEventTarget(target);if(!target)return-4;target.style.width=width+"px";target.style.height=height+"px";return 0}function _emscripten_set_interval(cb,msecs,userData){return setInterval(function(){callUserCallback(function(){(function(a1){dynCall_vi.apply(null,[cb,a1])})(userData)})},msecs)}function _emscripten_set_mousedown_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){registerMouseEventCallback(target,userData,useCapture,callbackfunc,5,"mousedown",targetThread);return 0}function registerTouchEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){if(!JSEvents.touchEvent)JSEvents.touchEvent=_malloc(1696);target=findEventTarget(target);var touchEventHandlerFunc=function(e){var t,touches={},et=e.touches;for(var i=0;i<et.length;++i){t=et[i];t.isChanged=t.onTarget=0;touches[t.identifier]=t}for(var i=0;i<e.changedTouches.length;++i){t=e.changedTouches[i];t.isChanged=1;touches[t.identifier]=t}for(var i=0;i<e.targetTouches.length;++i){touches[e.targetTouches[i].identifier].onTarget=1}var touchEvent=JSEvents.touchEvent;HEAPF64[touchEvent>>3]=e.timeStamp;var idx=touchEvent>>2;HEAP32[idx+3]=e.ctrlKey;HEAP32[idx+4]=e.shiftKey;HEAP32[idx+5]=e.altKey;HEAP32[idx+6]=e.metaKey;idx+=7;var targetRect=getBoundingClientRect(target);var numTouches=0;for(var i in touches){var t=touches[i];HEAP32[idx+0]=t.identifier;HEAP32[idx+1]=t.screenX;HEAP32[idx+2]=t.screenY;HEAP32[idx+3]=t.clientX;HEAP32[idx+4]=t.clientY;HEAP32[idx+5]=t.pageX;HEAP32[idx+6]=t.pageY;HEAP32[idx+7]=t.isChanged;HEAP32[idx+8]=t.onTarget;HEAP32[idx+9]=t.clientX-targetRect.left;HEAP32[idx+10]=t.clientY-targetRect.top;idx+=13;if(++numTouches>31){break}}HEAP32[touchEvent+8>>2]=numTouches;if(function(a1,a2,a3){return dynCall_iiii.apply(null,[callbackfunc,a1,a2,a3])}(eventTypeId,touchEvent,userData))e.preventDefault()};var eventHandler={target:target,allowsDeferredCalls:eventTypeString=="touchstart"||eventTypeString=="touchend",eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:touchEventHandlerFunc,useCapture:useCapture};JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_touchstart_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){registerTouchEventCallback(target,userData,useCapture,callbackfunc,22,"touchstart",targetThread);return 0}function __webgl_enable_ANGLE_instanced_arrays(ctx){var ext=ctx.getExtension("ANGLE_instanced_arrays");if(ext){ctx["vertexAttribDivisor"]=function(index,divisor){ext["vertexAttribDivisorANGLE"](index,divisor)};ctx["drawArraysInstanced"]=function(mode,first,count,primcount){ext["drawArraysInstancedANGLE"](mode,first,count,primcount)};ctx["drawElementsInstanced"]=function(mode,count,type,indices,primcount){ext["drawElementsInstancedANGLE"](mode,count,type,indices,primcount)};return 1}}function __webgl_enable_OES_vertex_array_object(ctx){var ext=ctx.getExtension("OES_vertex_array_object");if(ext){ctx["createVertexArray"]=function(){return ext["createVertexArrayOES"]()};ctx["deleteVertexArray"]=function(vao){ext["deleteVertexArrayOES"](vao)};ctx["bindVertexArray"]=function(vao){ext["bindVertexArrayOES"](vao)};ctx["isVertexArray"]=function(vao){return ext["isVertexArrayOES"](vao)};return 1}}function __webgl_enable_WEBGL_draw_buffers(ctx){var ext=ctx.getExtension("WEBGL_draw_buffers");if(ext){ctx["drawBuffers"]=function(n,bufs){ext["drawBuffersWEBGL"](n,bufs)};return 1}}function __webgl_enable_WEBGL_multi_draw(ctx){return!!(ctx.multiDrawWebgl=ctx.getExtension("WEBGL_multi_draw"))}var GL={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],stringCache:{},unpackAlignment:4,recordError:function recordError(errorCode){if(!GL.lastError){GL.lastError=errorCode}},getNewId:function(table){var ret=GL.counter++;for(var i=table.length;i<ret;i++){table[i]=null}return ret},getSource:function(shader,count,string,length){var source="";for(var i=0;i<count;++i){var len=length?HEAP32[length+i*4>>2]:-1;source+=UTF8ToString(HEAP32[string+i*4>>2],len<0?undefined:len)}return source},createContext:function(canvas,webGLContextAttributes){if(!canvas.getContextSafariWebGL2Fixed){canvas.getContextSafariWebGL2Fixed=canvas.getContext;canvas.getContext=function(ver,attrs){var gl=canvas.getContextSafariWebGL2Fixed(ver,attrs);return ver=="webgl"==gl instanceof WebGLRenderingContext?gl:null}}var ctx=canvas.getContext("webgl",webGLContextAttributes);if(!ctx)return 0;var handle=GL.registerContext(ctx,webGLContextAttributes);return handle},registerContext:function(ctx,webGLContextAttributes){var handle=GL.getNewId(GL.contexts);var context={handle:handle,attributes:webGLContextAttributes,version:webGLContextAttributes.majorVersion,GLctx:ctx};if(ctx.canvas)ctx.canvas.GLctxObject=context;GL.contexts[handle]=context;if(typeof webGLContextAttributes.enableExtensionsByDefault==="undefined"||webGLContextAttributes.enableExtensionsByDefault){GL.initExtensions(context)}return handle},makeContextCurrent:function(contextHandle){GL.currentContext=GL.contexts[contextHandle];Module.ctx=GLctx=GL.currentContext&&GL.currentContext.GLctx;return!(contextHandle&&!GLctx)},getContext:function(contextHandle){return GL.contexts[contextHandle]},deleteContext:function(contextHandle){if(GL.currentContext===GL.contexts[contextHandle])GL.currentContext=null;if(typeof JSEvents==="object")JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas);if(GL.contexts[contextHandle]&&GL.contexts[contextHandle].GLctx.canvas)GL.contexts[contextHandle].GLctx.canvas.GLctxObject=undefined;GL.contexts[contextHandle]=null},initExtensions:function(context){if(!context)context=GL.currentContext;if(context.initExtensionsDone)return;context.initExtensionsDone=true;var GLctx=context.GLctx;__webgl_enable_ANGLE_instanced_arrays(GLctx);__webgl_enable_OES_vertex_array_object(GLctx);__webgl_enable_WEBGL_draw_buffers(GLctx);{GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query")}__webgl_enable_WEBGL_multi_draw(GLctx);var exts=GLctx.getSupportedExtensions()||[];exts.forEach(function(ext){if(!ext.includes("lose_context")&&!ext.includes("debug")){GLctx.getExtension(ext)}})}};var __emscripten_webgl_power_preferences=["default","low-power","high-performance"];function _emscripten_webgl_do_create_context(target,attributes){var a=attributes>>2;var powerPreference=HEAP32[a+(24>>2)];var contextAttributes={"alpha":!!HEAP32[a+(0>>2)],"depth":!!HEAP32[a+(4>>2)],"stencil":!!HEAP32[a+(8>>2)],"antialias":!!HEAP32[a+(12>>2)],"premultipliedAlpha":!!HEAP32[a+(16>>2)],"preserveDrawingBuffer":!!HEAP32[a+(20>>2)],"powerPreference":__emscripten_webgl_power_preferences[powerPreference],"failIfMajorPerformanceCaveat":!!HEAP32[a+(28>>2)],majorVersion:HEAP32[a+(32>>2)],minorVersion:HEAP32[a+(36>>2)],enableExtensionsByDefault:HEAP32[a+(40>>2)],explicitSwapControl:HEAP32[a+(44>>2)],proxyContextToMainThread:HEAP32[a+(48>>2)],renderViaOffscreenBackBuffer:HEAP32[a+(52>>2)]};var canvas=findCanvasEventTarget(target);if(!canvas){return 0}if(contextAttributes.explicitSwapControl){return 0}var contextHandle=GL.createContext(canvas,contextAttributes);return contextHandle}function _emscripten_webgl_create_context(a0,a1){return _emscripten_webgl_do_create_context(a0,a1)}function _emscripten_webgl_do_get_current_context(){return GL.currentContext?GL.currentContext.handle:0}function _emscripten_webgl_get_current_context(){return _emscripten_webgl_do_get_current_context()}Module["_emscripten_webgl_get_current_context"]=_emscripten_webgl_get_current_context;function _emscripten_webgl_make_context_current(contextHandle){var success=GL.makeContextCurrent(contextHandle);return success?0:-5}Module["_emscripten_webgl_make_context_current"]=_emscripten_webgl_make_context_current;function _emscripten_webgl_destroy_context(contextHandle){if(GL.currentContext==contextHandle)GL.currentContext=0;GL.deleteContext(contextHandle)}function _emscripten_webgl_init_context_attributes(attributes){var a=attributes>>2;for(var i=0;i<56>>2;++i){HEAP32[a+i]=0}HEAP32[a+(0>>2)]=HEAP32[a+(4>>2)]=HEAP32[a+(12>>2)]=HEAP32[a+(16>>2)]=HEAP32[a+(32>>2)]=HEAP32[a+(40>>2)]=1}var WS={sockets:[null],socketEvent:null};function _emscripten_websocket_close(socketId,code,reason){var socket=WS.sockets[socketId];if(!socket){return-3}var reasonStr=reason?UTF8ToString(reason):undefined;if(reason)socket.close(code||undefined,UTF8ToString(reason));else if(code)socket.close(code);else socket.close();return 0}function _emscripten_websocket_delete(socketId){var socket=WS.sockets[socketId];if(!socket){return-3}socket.onopen=socket.onerror=socket.onclose=socket.onmessage=null;delete WS.sockets[socket];return 0}function _emscripten_websocket_new(createAttributes){if(typeof WebSocket==="undefined"){return-1}if(!createAttributes){return-5}var createAttrs=createAttributes>>2;var url=UTF8ToString(HEAP32[createAttrs]);var protocols=HEAP32[createAttrs+1];var socket=protocols?new WebSocket(url,UTF8ToString(protocols).split(",")):new WebSocket(url);socket.binaryType="arraybuffer";var socketId=WS.sockets.length;WS.sockets[socketId]=socket;return socketId}function _emscripten_websocket_send_binary(socketId,binaryData,dataLength){var socket=WS.sockets[socketId];if(!socket){return-3}socket.send(HEAPU8.subarray(binaryData,binaryData+dataLength));return 0}function _emscripten_websocket_set_onclose_callback_on_thread(socketId,userData,callbackFunc,thread){if(!WS.socketEvent)WS.socketEvent=_malloc(1024);var socket=WS.sockets[socketId];if(!socket){return-3}socket.onclose=function(e){HEAPU32[WS.socketEvent>>2]=socketId;HEAPU32[WS.socketEvent+4>>2]=e.wasClean;HEAPU32[WS.socketEvent+8>>2]=e.code;stringToUTF8(e.reason,WS.socketEvent+10,512);(function(a1,a2,a3){return dynCall_iiii.apply(null,[callbackFunc,a1,a2,a3])})(0,WS.socketEvent,userData)};return 0}function _emscripten_websocket_set_onerror_callback_on_thread(socketId,userData,callbackFunc,thread){if(!WS.socketEvent)WS.socketEvent=_malloc(1024);var socket=WS.sockets[socketId];if(!socket){return-3}socket.onerror=function(e){HEAPU32[WS.socketEvent>>2]=socketId;(function(a1,a2,a3){return dynCall_iiii.apply(null,[callbackFunc,a1,a2,a3])})(0,WS.socketEvent,userData)};return 0}function _emscripten_websocket_set_onopen_callback_on_thread(socketId,userData,callbackFunc,thread){if(!WS.socketEvent)WS.socketEvent=_malloc(1024);var socket=WS.sockets[socketId];if(!socket){return-3}socket.onopen=function(e){HEAPU32[WS.socketEvent>>2]=socketId;(function(a1,a2,a3){return dynCall_iiii.apply(null,[callbackFunc,a1,a2,a3])})(0,WS.socketEvent,userData)};return 0}var ENV={};function getExecutableName(){return thisProgram||"./this.program"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator==="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+"="+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}function _environ_get(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAP32[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAP32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAP32[penviron_buf_size>>2]=bufSize;return 0}function _fd_close(fd){return 0}function _fd_fdstat_get(fd,pbuf){var type=fd==1||fd==2?2:abort();HEAP8[pbuf>>0]=type;return 0}function _fd_read(fd,iov,iovcnt,pnum){var stream=SYSCALLS.getStreamFromFD(fd);var num=SYSCALLS.doReadv(stream,iov,iovcnt);HEAP32[pnum>>2]=num;return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov>>2];var len=HEAP32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function _gettimeofday(ptr){var now=Date.now();HEAP32[ptr>>2]=now/1e3|0;HEAP32[ptr+4>>2]=now%1e3*1e3|0;return 0}function _glActiveTexture(x0){GLctx["activeTexture"](x0)}function _glAttachShader(program,shader){GLctx.attachShader(GL.programs[program],GL.shaders[shader])}function _glBindBuffer(target,buffer){GLctx.bindBuffer(target,GL.buffers[buffer])}function _glBindTexture(target,texture){GLctx.bindTexture(target,GL.textures[texture])}function _glBufferData(target,size,data,usage){GLctx.bufferData(target,data?HEAPU8.subarray(data,data+size):size,usage)}function _glClear(x0){GLctx["clear"](x0)}function _glClearColor(x0,x1,x2,x3){GLctx["clearColor"](x0,x1,x2,x3)}function _glCompileShader(shader){GLctx.compileShader(GL.shaders[shader])}function _glCreateProgram(){var id=GL.getNewId(GL.programs);var program=GLctx.createProgram();program.name=id;program.maxUniformLength=program.maxAttributeLength=program.maxUniformBlockNameLength=0;program.uniformIdCounter=1;GL.programs[id]=program;return id}function _glCreateShader(shaderType){var id=GL.getNewId(GL.shaders);GL.shaders[id]=GLctx.createShader(shaderType);return id}function _glDeleteProgram(id){if(!id)return;var program=GL.programs[id];if(!program){GL.recordError(1281);return}GLctx.deleteProgram(program);program.name=0;GL.programs[id]=null}function _glDeleteShader(id){if(!id)return;var shader=GL.shaders[id];if(!shader){GL.recordError(1281);return}GLctx.deleteShader(shader);GL.shaders[id]=null}function _glDeleteTextures(n,textures){for(var i=0;i<n;i++){var id=HEAP32[textures+i*4>>2];var texture=GL.textures[id];if(!texture)continue;GLctx.deleteTexture(texture);texture.name=0;GL.textures[id]=null}}function _glDrawArrays(mode,first,count){GLctx.drawArrays(mode,first,count)}function _glEnableVertexAttribArray(index){GLctx.enableVertexAttribArray(index)}function __glGenObject(n,buffers,createFunction,objectTable){for(var i=0;i<n;i++){var buffer=GLctx[createFunction]();var id=buffer&&GL.getNewId(objectTable);if(buffer){buffer.name=id;objectTable[id]=buffer}else{GL.recordError(1282)}HEAP32[buffers+i*4>>2]=id}}function _glGenBuffers(n,buffers){__glGenObject(n,buffers,"createBuffer",GL.buffers)}function _glGenTextures(n,textures){__glGenObject(n,textures,"createTexture",GL.textures)}function _glGetAttribLocation(program,name){return GLctx.getAttribLocation(GL.programs[program],UTF8ToString(name))}function _glGetError(){var error=GLctx.getError()||GL.lastError;GL.lastError=0;return error}function _glGetProgramInfoLog(program,maxLength,length,infoLog){var log=GLctx.getProgramInfoLog(GL.programs[program]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>2]=numBytesWrittenExclNull}function _glGetProgramiv(program,pname,p){if(!p){GL.recordError(1281);return}if(program>=GL.counter){GL.recordError(1281);return}program=GL.programs[program];if(pname==35716){var log=GLctx.getProgramInfoLog(program);if(log===null)log="(unknown error)";HEAP32[p>>2]=log.length+1}else if(pname==35719){if(!program.maxUniformLength){for(var i=0;i<GLctx.getProgramParameter(program,35718);++i){program.maxUniformLength=Math.max(program.maxUniformLength,GLctx.getActiveUniform(program,i).name.length+1)}}HEAP32[p>>2]=program.maxUniformLength}else if(pname==35722){if(!program.maxAttributeLength){for(var i=0;i<GLctx.getProgramParameter(program,35721);++i){program.maxAttributeLength=Math.max(program.maxAttributeLength,GLctx.getActiveAttrib(program,i).name.length+1)}}HEAP32[p>>2]=program.maxAttributeLength}else if(pname==35381){if(!program.maxUniformBlockNameLength){for(var i=0;i<GLctx.getProgramParameter(program,35382);++i){program.maxUniformBlockNameLength=Math.max(program.maxUniformBlockNameLength,GLctx.getActiveUniformBlockName(program,i).length+1)}}HEAP32[p>>2]=program.maxUniformBlockNameLength}else{HEAP32[p>>2]=GLctx.getProgramParameter(program,pname)}}function _glGetShaderInfoLog(shader,maxLength,length,infoLog){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>2]=numBytesWrittenExclNull}function _glGetShaderiv(shader,pname,p){if(!p){GL.recordError(1281);return}if(pname==35716){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var logLength=log?log.length+1:0;HEAP32[p>>2]=logLength}else if(pname==35720){var source=GLctx.getShaderSource(GL.shaders[shader]);var sourceLength=source?source.length+1:0;HEAP32[p>>2]=sourceLength}else{HEAP32[p>>2]=GLctx.getShaderParameter(GL.shaders[shader],pname)}}function jstoi_q(str){return parseInt(str)}function webglGetLeftBracePos(name){return name.slice(-1)=="]"&&name.lastIndexOf("[")}function webglPrepareUniformLocationsBeforeFirstUse(program){var uniformLocsById=program.uniformLocsById,uniformSizeAndIdsByName=program.uniformSizeAndIdsByName,i,j;if(!uniformLocsById){program.uniformLocsById=uniformLocsById={};program.uniformArrayNamesById={};for(i=0;i<GLctx.getProgramParameter(program,35718);++i){var u=GLctx.getActiveUniform(program,i);var nm=u.name;var sz=u.size;var lb=webglGetLeftBracePos(nm);var arrayName=lb>0?nm.slice(0,lb):nm;var id=program.uniformIdCounter;program.uniformIdCounter+=sz;uniformSizeAndIdsByName[arrayName]=[sz,id];for(j=0;j<sz;++j){uniformLocsById[id]=j;program.uniformArrayNamesById[id++]=arrayName}}}}function _glGetUniformLocation(program,name){name=UTF8ToString(name);if(program=GL.programs[program]){webglPrepareUniformLocationsBeforeFirstUse(program);var uniformLocsById=program.uniformLocsById;var arrayIndex=0;var uniformBaseName=name;var leftBrace=webglGetLeftBracePos(name);if(leftBrace>0){arrayIndex=jstoi_q(name.slice(leftBrace+1))>>>0;uniformBaseName=name.slice(0,leftBrace)}var sizeAndId=program.uniformSizeAndIdsByName[uniformBaseName];if(sizeAndId&&arrayIndex<sizeAndId[0]){arrayIndex+=sizeAndId[1];if(uniformLocsById[arrayIndex]=uniformLocsById[arrayIndex]||GLctx.getUniformLocation(program,name)){return arrayIndex}}}else{GL.recordError(1281)}return-1}function _glLinkProgram(program){program=GL.programs[program];GLctx.linkProgram(program);program.uniformLocsById=0;program.uniformSizeAndIdsByName={}}function _glPixelStorei(pname,param){if(pname==3317){GL.unpackAlignment=param}GLctx.pixelStorei(pname,param)}function _glShaderSource(shader,count,string,length){var source=GL.getSource(shader,count,string,length);GLctx.shaderSource(GL.shaders[shader],source)}function computeUnpackAlignedImageSize(width,height,sizePerPixel,alignment){function roundedToNextMultipleOf(x,y){return x+y-1&-y}var plainRowSize=width*sizePerPixel;var alignedRowSize=roundedToNextMultipleOf(plainRowSize,alignment);return height*alignedRowSize}function __colorChannelsInGlTextureFormat(format){var colorChannels={5:3,6:4,8:2,29502:3,29504:4};return colorChannels[format-6402]||1}function heapObjectForWebGLType(type){type-=5120;if(type==1)return HEAPU8;if(type==4)return HEAP32;if(type==6)return HEAPF32;if(type==5||type==28922)return HEAPU32;return HEAPU16}function heapAccessShiftForWebGLHeap(heap){return 31-Math.clz32(heap.BYTES_PER_ELEMENT)}function emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat){var heap=heapObjectForWebGLType(type);var shift=heapAccessShiftForWebGLHeap(heap);var byteSize=1<<shift;var sizePerPixel=__colorChannelsInGlTextureFormat(format)*byteSize;var bytes=computeUnpackAlignedImageSize(width,height,sizePerPixel,GL.unpackAlignment);return heap.subarray(pixels>>shift,pixels+bytes>>shift)}function _glTexImage2D(target,level,internalFormat,width,height,border,format,type,pixels){GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixels?emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat):null)}function _glTexParameterf(x0,x1,x2){GLctx["texParameterf"](x0,x1,x2)}function webglGetUniformLocation(location){var p=GLctx.currentProgram;if(p){var webglLoc=p.uniformLocsById[location];if(typeof webglLoc==="number"){p.uniformLocsById[location]=webglLoc=GLctx.getUniformLocation(p,p.uniformArrayNamesById[location]+(webglLoc>0?"["+webglLoc+"]":""))}return webglLoc}else{GL.recordError(1282)}}function _glUniform1i(location,v0){GLctx.uniform1i(webglGetUniformLocation(location),v0)}function _glUseProgram(program){program=GL.programs[program];GLctx.useProgram(program);GLctx.currentProgram=program}function _glVertexAttribPointer(index,size,type,normalized,stride,ptr){GLctx.vertexAttribPointer(index,size,type,!!normalized,stride,ptr)}function _glViewport(x0,x1,x2,x3){GLctx["viewport"](x0,x1,x2,x3)}function _openAudioEncoder(t,o){let e=NB[t];e.at=e.as.getAudioTracks()[0],e.ap=new MediaStreamTrackProcessor(e.at),e.ar=e.ap.readable.getReader(),e.bk=!1,e.ae=new AudioEncoder({output:function(r,i){if(e.bk)return;if(i.decoderConfig&&i.decoderConfig.description){let l=new Uint8Array(i.decoderConfig.description),f=_malloc(i.decoderConfig.description.byteLength);HEAPU8.set(l,f),getWasmTableEntry(o)(t,f,i.decoderConfig.description.byteLength,BigInt(r.timestamp),0),Module._free(f)}let a=new Uint8Array(r.byteLength),n=_malloc(r.byteLength);r.copyTo(a),HEAPU8.set(a,n),e.bk=getWasmTableEntry(o)(t,n,r.byteLength,BigInt(r.timestamp),1)<0,Module._free(n)},error:function(r){e.emit("error",r)}});let d={codec:e.audioCodec,bitrate:e.audioBitrate,numberOfChannels:e.audioChannels,sampleRate:e.audioSampleRate};NP_INFO("open audio encoder",d),e.audioPromise=e.ae.configure(d)}function _openVideoEncoder(t,o){let e=NB[t];e.vt=e.vs.getVideoTracks()[0],e.tp=new MediaStreamTrackProcessor(e.vt),e.vr=e.tp.readable.getReader(),e.fc=0,e.bk=!1,e.ve=new VideoEncoder({output:function(r,i){if(e.bk)return;if(i.decoderConfig&&i.decoderConfig.description){let f=new Uint8Array(i.decoderConfig.description);var n=Module._malloc(i.decoderConfig.description.byteLength);HEAPU8.set(f,n),getWasmTableEntry(o)(t,n,i.decoderConfig.description.byteLength,BigInt(r.timestamp),2),Module._free(n)}let a=new Uint8Array(r.byteLength);var n=Module._malloc(r.byteLength);r.copyTo(a),HEAPU8.set(a,n);let l=r.type==="key"?3:4;e.bk=getWasmTableEntry(o)(t,n,r.byteLength,BigInt(r.timestamp),l)<0,Module._free(n)},error:function(r){e.emit("error",r)}});let d={codec:e.videoCodec,bitrate:e.videoBitrate,framerate:e.videoFps,width:e.videoWidth,height:e.videoHeight};e.videoPromise=e.ve.configure(d),NP_INFO("open video encoder",d)}function _readAudioEncoder(t){let o=NB[t],e=o.ar,d=o.ae;e.read().then(r=>{if(r.done||!o.isStart){r.value.close(),d.close();return}d.encode(r.value),r.value.close(),_readAudioEncoder(t)})}function _readVideoEncoder(t){let o=NB[t],e=o.vr,d=o.ve;e.read().then(r=>{if(r.done||!o.isStart){r.value.close(),d.close();return}const i=o.fc++%o.videoKeyInt==0;d.encode(r.value,{keyFrame:i}),r.value.close(),_readVideoEncoder(t)})}function _setTempRet0(val){setTempRet0(val)}function _time(ptr){var ret=Date.now()/1e3|0;if(ptr){HEAP32[ptr>>2]=ret}return ret}Module["requestFullscreen"]=function Module_requestFullscreen(lockPointer,resizeCanvas){Browser.requestFullscreen(lockPointer,resizeCanvas)};Module["requestAnimationFrame"]=function Module_requestAnimationFrame(func){Browser.requestAnimationFrame(func)};Module["setCanvasSize"]=function Module_setCanvasSize(width,height,noUpdates){Browser.setCanvasSize(width,height,noUpdates)};Module["pauseMainLoop"]=function Module_pauseMainLoop(){Browser.mainLoop.pause()};Module["resumeMainLoop"]=function Module_resumeMainLoop(){Browser.mainLoop.resume()};Module["getUserMedia"]=function Module_getUserMedia(){Browser.getUserMedia()};Module["createContext"]=function Module_createContext(canvas,useWebGL,setInModule,webGLContextAttributes){return Browser.createContext(canvas,useWebGL,setInModule,webGLContextAttributes)};var GLctx;var asmLibraryArg={"N":___syscall_fcntl64,"L":___syscall_open,"a":_abort,"Q":_clock,"b":_emscripten_asm_const_int,"f":_emscripten_call_worker,"F":_emscripten_clear_interval,"wa":_emscripten_create_worker,"M":_emscripten_destroy_worker,"D":_emscripten_get_canvas_element_size,"E":_emscripten_get_element_css_size,"H":_emscripten_memcpy_big,"I":_emscripten_resize_heap,"g":_emscripten_set_canvas_element_size,"Ca":_emscripten_set_click_callback_on_thread,"ya":_emscripten_set_element_css_size,"o":_emscripten_set_interval,"Ea":_emscripten_set_mousedown_callback_on_thread,"Da":_emscripten_set_touchstart_callback_on_thread,"za":_emscripten_webgl_create_context,"s":_emscripten_webgl_destroy_context,"Ba":_emscripten_webgl_init_context_attributes,"c":_emscripten_webgl_make_context_current,"ja":_emscripten_websocket_close,"ka":_emscripten_websocket_delete,"ta":_emscripten_websocket_new,"C":_emscripten_websocket_send_binary,"ra":_emscripten_websocket_set_onclose_callback_on_thread,"qa":_emscripten_websocket_set_onerror_callback_on_thread,"sa":_emscripten_websocket_set_onopen_callback_on_thread,"J":_environ_get,"K":_environ_sizes_get,"q":_fd_close,"O":_fd_fdstat_get,"P":_fd_read,"G":_fd_seek,"p":_fd_write,"ia":gas,"ua":gds,"ha":ges,"pa":get_a_str,"Y":get_audio_state,"Aa":get_do_str,"fa":get_e_str,"Fa":get_is_mse,"Ga":get_is_wcs,"xa":get_js_dir,"va":_gettimeofday,"R":_glActiveTexture,"u":_glAttachShader,"m":_glBindBuffer,"e":_glBindTexture,"l":_glBufferData,"v":_glClear,"w":_glClearColor,"ca":_glCompileShader,"V":_glCreateProgram,"ea":_glCreateShader,"S":_glDeleteProgram,"n":_glDeleteShader,"$":_glDeleteTextures,"Z":_glDrawArrays,"x":_glEnableVertexAttribArray,"aa":_glGenBuffers,"X":_glGenTextures,"z":_glGetAttribLocation,"ga":_glGetError,"T":_glGetProgramInfoLog,"t":_glGetProgramiv,"ba":_glGetShaderInfoLog,"B":_glGetShaderiv,"k":_glGetUniformLocation,"U":_glLinkProgram,"W":_glPixelStorei,"da":_glShaderSource,"i":_glTexImage2D,"d":_glTexParameterf,"j":_glUniform1i,"A":_glUseProgram,"y":_glVertexAttribPointer,"_":_glViewport,"ma":_openAudioEncoder,"oa":_openVideoEncoder,"la":_readAudioEncoder,"na":_readVideoEncoder,"h":_setTempRet0,"r":_time};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["Ia"]).apply(null,arguments)};var _ng=Module["_ng"]=function(){return(_ng=Module["_ng"]=Module["asm"]["Ja"]).apply(null,arguments)};var _nc=Module["_nc"]=function(){return(_nc=Module["_nc"]=Module["asm"]["Ka"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["La"]).apply(null,arguments)};var _nd=Module["_nd"]=function(){return(_nd=Module["_nd"]=Module["asm"]["Ma"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["Na"]).apply(null,arguments)};var _ns=Module["_ns"]=function(){return(_ns=Module["_ns"]=Module["asm"]["Oa"]).apply(null,arguments)};var _np=Module["_np"]=function(){return(_np=Module["_np"]=Module["asm"]["Pa"]).apply(null,arguments)};var _nu=Module["_nu"]=function(){return(_nu=Module["_nu"]=Module["asm"]["Qa"]).apply(null,arguments)};var _nv=Module["_nv"]=function(){return(_nv=Module["_nv"]=Module["asm"]["Ra"]).apply(null,arguments)};var _nr=Module["_nr"]=function(){return(_nr=Module["_nr"]=Module["asm"]["Sa"]).apply(null,arguments)};var _ni=Module["_ni"]=function(){return(_ni=Module["_ni"]=Module["asm"]["Ta"]).apply(null,arguments)};var _nm=Module["_nm"]=function(){return(_nm=Module["_nm"]=Module["asm"]["Ua"]).apply(null,arguments)};var _nsl=Module["_nsl"]=function(){return(_nsl=Module["_nsl"]=Module["asm"]["Va"]).apply(null,arguments)};var _ncl=Module["_ncl"]=function(){return(_ncl=Module["_ncl"]=Module["asm"]["Wa"]).apply(null,arguments)};var _nb=Module["_nb"]=function(){return(_nb=Module["_nb"]=Module["asm"]["Xa"]).apply(null,arguments)};var _nk=Module["_nk"]=function(){return(_nk=Module["_nk"]=Module["asm"]["Ya"]).apply(null,arguments)};var _nsf=Module["_nsf"]=function(){return(_nsf=Module["_nsf"]=Module["asm"]["Za"]).apply(null,arguments)};var _ne=Module["_ne"]=function(){return(_ne=Module["_ne"]=Module["asm"]["_a"]).apply(null,arguments)};var _ny=Module["_ny"]=function(){return(_ny=Module["_ny"]=Module["asm"]["$a"]).apply(null,arguments)};var _nar=Module["_nar"]=function(){return(_nar=Module["_nar"]=Module["asm"]["ab"]).apply(null,arguments)};var _nor=Module["_nor"]=function(){return(_nor=Module["_nor"]=Module["asm"]["bb"]).apply(null,arguments)};var _nse=Module["_nse"]=function(){return(_nse=Module["_nse"]=Module["asm"]["cb"]).apply(null,arguments)};var _ncv=Module["_ncv"]=function(){return(_ncv=Module["_ncv"]=Module["asm"]["db"]).apply(null,arguments)};var _nuw=Module["_nuw"]=function(){return(_nuw=Module["_nuw"]=Module["asm"]["eb"]).apply(null,arguments)};var _nea=Module["_nea"]=function(){return(_nea=Module["_nea"]=Module["asm"]["fb"]).apply(null,arguments)};var _nev=Module["_nev"]=function(){return(_nev=Module["_nev"]=Module["asm"]["gb"]).apply(null,arguments)};var _main=Module["_main"]=function(){return(_main=Module["_main"]=Module["asm"]["hb"]).apply(null,arguments)};var _bc=Module["_bc"]=function(){return(_bc=Module["_bc"]=Module["asm"]["jb"]).apply(null,arguments)};var _bd=Module["_bd"]=function(){return(_bd=Module["_bd"]=Module["asm"]["kb"]).apply(null,arguments)};var _bs=Module["_bs"]=function(){return(_bs=Module["_bs"]=Module["asm"]["lb"]).apply(null,arguments)};var _bp=Module["_bp"]=function(){return(_bp=Module["_bp"]=Module["asm"]["mb"]).apply(null,arguments)};var _ba=Module["_ba"]=function(){return(_ba=Module["_ba"]=Module["asm"]["Xa"]).apply(null,arguments)};var _bv=Module["_bv"]=function(){return(_bv=Module["_bv"]=Module["asm"]["ob"]).apply(null,arguments)};var _by=Module["_by"]=function(){return(_by=Module["_by"]=Module["asm"]["pb"]).apply(null,arguments)};var _be=Module["_be"]=function(){return(_be=Module["_be"]=Module["asm"]["qb"]).apply(null,arguments)};var stackSave=Module["stackSave"]=function(){return(stackSave=Module["stackSave"]=Module["asm"]["rb"]).apply(null,arguments)};var stackRestore=Module["stackRestore"]=function(){return(stackRestore=Module["stackRestore"]=Module["asm"]["sb"]).apply(null,arguments)};var stackAlloc=Module["stackAlloc"]=function(){return(stackAlloc=Module["stackAlloc"]=Module["asm"]["tb"]).apply(null,arguments)};var dynCall_iiiiiiii=Module["dynCall_iiiiiiii"]=function(){return(dynCall_iiiiiiii=Module["dynCall_iiiiiiii"]=Module["asm"]["ub"]).apply(null,arguments)};var dynCall_iiii=Module["dynCall_iiii"]=function(){return(dynCall_iiii=Module["dynCall_iiii"]=Module["asm"]["vb"]).apply(null,arguments)};var dynCall_viii=Module["dynCall_viii"]=function(){return(dynCall_viii=Module["dynCall_viii"]=Module["asm"]["wb"]).apply(null,arguments)};var dynCall_iiiii=Module["dynCall_iiiii"]=function(){return(dynCall_iiiii=Module["dynCall_iiiii"]=Module["asm"]["xb"]).apply(null,arguments)};var dynCall_iiiiii=Module["dynCall_iiiiii"]=function(){return(dynCall_iiiiii=Module["dynCall_iiiiii"]=Module["asm"]["yb"]).apply(null,arguments)};var dynCall_vi=Module["dynCall_vi"]=function(){return(dynCall_vi=Module["dynCall_vi"]=Module["asm"]["zb"]).apply(null,arguments)};var dynCall_iiiiji=Module["dynCall_iiiiji"]=function(){return(dynCall_iiiiji=Module["dynCall_iiiiji"]=Module["asm"]["Ab"]).apply(null,arguments)};var dynCall_ii=Module["dynCall_ii"]=function(){return(dynCall_ii=Module["dynCall_ii"]=Module["asm"]["Bb"]).apply(null,arguments)};var dynCall_vii=Module["dynCall_vii"]=function(){return(dynCall_vii=Module["dynCall_vii"]=Module["asm"]["Cb"]).apply(null,arguments)};var dynCall_iii=Module["dynCall_iii"]=function(){return(dynCall_iii=Module["dynCall_iii"]=Module["asm"]["Db"]).apply(null,arguments)};var dynCall_iid=Module["dynCall_iid"]=function(){return(dynCall_iid=Module["dynCall_iid"]=Module["asm"]["Eb"]).apply(null,arguments)};var dynCall_iidii=Module["dynCall_iidii"]=function(){return(dynCall_iidii=Module["dynCall_iidii"]=Module["asm"]["Fb"]).apply(null,arguments)};var dynCall_viiii=Module["dynCall_viiii"]=function(){return(dynCall_viiii=Module["dynCall_viiii"]=Module["asm"]["Gb"]).apply(null,arguments)};var dynCall_viiiii=Module["dynCall_viiiii"]=function(){return(dynCall_viiiii=Module["dynCall_viiiii"]=Module["asm"]["Hb"]).apply(null,arguments)};var dynCall_viiiiiifi=Module["dynCall_viiiiiifi"]=function(){return(dynCall_viiiiiifi=Module["dynCall_viiiiiifi"]=Module["asm"]["Ib"]).apply(null,arguments)};var dynCall_iiiiiii=Module["dynCall_iiiiiii"]=function(){return(dynCall_iiiiiii=Module["dynCall_iiiiiii"]=Module["asm"]["Jb"]).apply(null,arguments)};var dynCall_v=Module["dynCall_v"]=function(){return(dynCall_v=Module["dynCall_v"]=Module["asm"]["Kb"]).apply(null,arguments)};var dynCall_viiiiii=Module["dynCall_viiiiii"]=function(){return(dynCall_viiiiii=Module["dynCall_viiiiii"]=Module["asm"]["Lb"]).apply(null,arguments)};var dynCall_viiiiiiiii=Module["dynCall_viiiiiiiii"]=function(){return(dynCall_viiiiiiiii=Module["dynCall_viiiiiiiii"]=Module["asm"]["Mb"]).apply(null,arguments)};var dynCall_viiiiiiii=Module["dynCall_viiiiiiii"]=function(){return(dynCall_viiiiiiii=Module["dynCall_viiiiiiii"]=Module["asm"]["Nb"]).apply(null,arguments)};var dynCall_viiiifii=Module["dynCall_viiiifii"]=function(){return(dynCall_viiiifii=Module["dynCall_viiiifii"]=Module["asm"]["Ob"]).apply(null,arguments)};var dynCall_fii=Module["dynCall_fii"]=function(){return(dynCall_fii=Module["dynCall_fii"]=Module["asm"]["Pb"]).apply(null,arguments)};var dynCall_viiiiiiiiii=Module["dynCall_viiiiiiiiii"]=function(){return(dynCall_viiiiiiiiii=Module["dynCall_viiiiiiiiii"]=Module["asm"]["Qb"]).apply(null,arguments)};var dynCall_viifi=Module["dynCall_viifi"]=function(){return(dynCall_viifi=Module["dynCall_viifi"]=Module["asm"]["Rb"]).apply(null,arguments)};var dynCall_fiii=Module["dynCall_fiii"]=function(){return(dynCall_fiii=Module["dynCall_fiii"]=Module["asm"]["Sb"]).apply(null,arguments)};var dynCall_viidi=Module["dynCall_viidi"]=function(){return(dynCall_viidi=Module["dynCall_viidi"]=Module["asm"]["Tb"]).apply(null,arguments)};var dynCall_dd=Module["dynCall_dd"]=function(){return(dynCall_dd=Module["dynCall_dd"]=Module["asm"]["Ub"]).apply(null,arguments)};var dynCall_viiiiiii=Module["dynCall_viiiiiii"]=function(){return(dynCall_viiiiiii=Module["dynCall_viiiiiii"]=Module["asm"]["Vb"]).apply(null,arguments)};var dynCall_viiijj=Module["dynCall_viiijj"]=function(){return(dynCall_viiijj=Module["dynCall_viiijj"]=Module["asm"]["Wb"]).apply(null,arguments)};var dynCall_iiiiiiidiiddii=Module["dynCall_iiiiiiidiiddii"]=function(){return(dynCall_iiiiiiidiiddii=Module["dynCall_iiiiiiidiiddii"]=Module["asm"]["Xb"]).apply(null,arguments)};var dynCall_jij=Module["dynCall_jij"]=function(){return(dynCall_jij=Module["dynCall_jij"]=Module["asm"]["Yb"]).apply(null,arguments)};var dynCall_jii=Module["dynCall_jii"]=function(){return(dynCall_jii=Module["dynCall_jii"]=Module["asm"]["Zb"]).apply(null,arguments)};var dynCall_jiji=Module["dynCall_jiji"]=function(){return(dynCall_jiji=Module["dynCall_jiji"]=Module["asm"]["_b"]).apply(null,arguments)};var dynCall_iidiiii=Module["dynCall_iidiiii"]=function(){return(dynCall_iidiiii=Module["dynCall_iidiiii"]=Module["asm"]["$b"]).apply(null,arguments)};var _ff_h264_cabac_tables=Module["_ff_h264_cabac_tables"]=108956;var calledRun;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}var calledMain=false;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function callMain(args){var entryFunction=Module["_main"];var argc=0;var argv=0;try{var ret=entryFunction(argc,argv);exit(ret,true);return ret}catch(e){return handleException(e)}finally{calledMain=true}}function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();if(shouldRunNow)callMain(args);postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;function exit(status,implicit){EXITSTATUS=status;if(keepRuntimeAlive()){}else{exitRuntime()}procExit(status)}function procExit(code){EXITSTATUS=code;if(!keepRuntimeAlive()){if(Module["onExit"])Module["onExit"](code);ABORT=true}quit_(code,new ExitStatus(code))}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}var shouldRunNow=true;if(Module["noInitialRun"])shouldRunNow=false;run();(function(){null;const E={bc:cwrap("bc","number",["number"]),bd:cwrap("bd","number",["number"]),bs:cwrap("bs","number",["number","string"]),bp:cwrap("bp","number",["number"]),ba:cwrap("ba","number",["number","number","number","number","number"]),bv:cwrap("bv","number",["number","number","number","number","number","number","number"]),be:cwrap("be","number",["number","array","number"]),by:cwrap("by","number",["number","string"])};class w extends g{constructor(){super();this.ctx=E.bc(),this.isStart=!1,NB[this.ctx]=this}release(){this.ctx&&(this.stop(),E.bd(this.ctx),delete NB[this.ctx],this.ctx=void 0)}async listDevices(){try{return await navigator.mediaDevices.enumerateDevices()}catch(A){this.emit("error","listDevices error, "+A)}}async setAudioSource(A){if(this.isStart)return;let e={audio:{}};e.audio.deviceId=A?{exact:A}:void 0,NB[this.ctx].as=await window.navigator.mediaDevices.getUserMedia(e)}async setVideoSource(A,e){if(this.isStart)return;let t=document.getElementById(A),r={audio:!1,video:{width:1920,height:1080,frameRate:30}};if(t.nodeName==="VIDEO"&&e==="desktop"){r.video.deviceId=e?{exact:e}:void 0;let s=await window.navigator.mediaDevices.getDisplayMedia({video:!0});t.srcObject=s,NB[this.ctx].vs=s}else if(t.nodeName==="VIDEO"){r.video.deviceId=e?{exact:e}:void 0;let s=await window.navigator.mediaDevices.getUserMedia(r);t.srcObject=s,NB[this.ctx].vs=s}else if(t.nodeName==="CANVAS"){let s=t.captureStream(r.frameRate);NB[this.ctx].vs=s}}setAudioConfig(A,e,t,r){let s;A.indexOf("mp4a.")===0?(e=Math.max(96e3,e),e=Math.min(192e3,e),s=10):A==="opus"&&(r=48e3,s=13),this.audioCodec=A,this.audioBitrate=e,this.audioChannels=t,this.audioSampleRate=r,E.ba(this.ctx,s,e,t,r)}setVideoConfig(A,e,t,r,s,o){let a;A.indexOf("avc1.")===0?a=7:A.indexOf("vp8")===0?A=10:A.indexOf("vp09.")===0?A=11:A.indexOf("hev1.")===0&&(A=12),this.videoCodec=A,this.videoWidth=e,this.videoHeight=t,this.videoFps=r,this.videoKeyInt=s,this.videoBitrate=o,E.bv(this.ctx,a,e,t,r,s,o)}setCryptoKey(A){E.by(this.ctx,A)}start(A){return this.isStart?-1:(this.isStart=!0,E.bs(this.ctx,A))}stop(){return this.isStart?(this.isStart=!1,E.bp(this.ctx)):-1}sendSEI(A){return E.be(this.ctx,A,A.length)}static load(A){window.nbAllReadyFlag?A():setTimeout(w.load.bind(this,A),100)}static asyncLoad(){return new Promise((A,e)=>{w.load(()=>{A()})})}}const h={nc:cwrap("nc","number",["number"]),nd:cwrap("nd","number",["number"]),ns:cwrap("ns","number",["number","string"]),np:cwrap("np","number",["number"]),nu:cwrap("nu","number",["number","number"]),ni:cwrap("ni","number",["number","array","number"]),ng:cwrap("ng","string"),nv:cwrap("nv","number",["number","string","number","boolean"]),nm:cwrap("nm","number",["number","number"]),nb:cwrap("nb","number",["number","number"]),nk:cwrap("nk","number",["number","number"]),ne:cwrap("ne","number",["number","number"]),nr:cwrap("nr","number",["number","number","number","number"]),ny:cwrap("ny","number",["number","string"]),nor:cwrap("nor","number",["number","number","number"]),nar:cwrap("nar","number",["number"]),nse:cwrap("nse","number",["number","number"]),ncv:cwrap("ncv","number",["number"]),nuw:cwrap("nuw","number",["number","string"]),nea:cwrap("nea","number",["number","number"]),nev:cwrap("nev","number",["number","number"]),nsl:cwrap("nsl","number",["number","number","number","number"]),ncl:cwrap("ncl","number",["number","number","number","number"]),nsf:cwrap("nsf","number",["number"])};document.addEventListener("fullscreenchange",n=>{n.target.ctx&&h.nor(n.target.ctx)});class m extends g{constructor(){super();this.ism=/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent),this.ctx=h.nc(this.ism),this.dpr=window.devicePixelRatio||1,this.version=h.ng(),this.isStart=!1,this.isVod=!1,this.isMSE=!1,this.isWCS=!1,this.isDOC=!1,this.hasAudio=!0,this.duration=0,this.volume=1,this.eventFlags=0,this.screenCount=0,this.connTimeout=0,this.connTimer=null,this.screenshotImage1=null,this.screenshotImage2=null,this.headers={},this.streamLoader=new R,this.streamLoader.on("data",A=>{h.ni(this.ctx,A,A.length)<0&&this.stop()}),this.streamLoader.on("open",()=>{this.emit("start"),h.ns(this.ctx,this.url)!==0&&this.stop()}),this.streamLoader.on("close",()=>{}),this.streamLoader.on("error",A=>{this.emit("error",A),this.stop()}),NP[this.ctx].streamLoader=this.streamLoader,NP[this.ctx].emit=this.emit.bind(this),NP[this.ctx].do=document?document.domain:location.hostname,this.on("buffer",A=>{this.connTimeout>0&&(this._clearTimeout(),A==="empty"&&(this.connTimer=setTimeout(()=>{this.emit("timeout")},this.connTimeout*1e3)))}),this.on("metadata",A=>{var e=u.parseScriptData(A.buffer,0,A.length);e.onMetaData&&(NP[this.ctx].width=e.width,NP[this.ctx].height=e.height),e.onMetaData&&e.onMetaData.duration>0&&(NP[this.ctx].isVod=this.isVod=!0,NP[this.ctx].duration=this.duration=Math.floor(e.onMetaData.duration*1e3))}),this.ism&&(this.wakeLock=new S)}on(A,e){super.on(A,e),A==="videoSei"?this.eventFlags|=1:A==="videoFrame"&&(this.eventFlags|=2),h.nse(this.ctx,this.eventFlags)}setTimeout(A){A>0?this.connTimeout=A:this._clearTimeout()}_clearTimeout(){this.connTimer!=null&&(clearTimeout(this.connTimer),this.connTimer=null)}addHeaders(A){this.headers=A}setView(A,e){if(this.ve=document.getElementById(A),this.ve.ctx=this.ctx,NP[this.ctx].ve=this.ve,this.isMSE){if(this.ve.nodeName==="VIDEO"?this.vi=this.ve:(this.vi=document.createElement("video"),this.ve.width!==300&&this.ve.height!==150&&(this.vi.width=this.ve.width,this.vi.height=this.ve.height),this.vi.style.cssText=this.ve.style.cssText),this.isDOC){if(this.ve.nodeName!=="CANVAS"){this.emit("error","setView id must be a canvas");return}}else this.vi.id=A,this.ve.parentNode.replaceChild(this.vi,this.ve);this.vi.onwaiting=(()=>{this.emit("mseWaiting",this.vi.id)}),this.vi.ontimeupdate=(()=>{this.emit("mseTimeupdate",this.vi.id)}),this.vi.ctx=this.ctx,NP[this.ctx].vi=this.vi,NP[this.ctx].isMSE=this.isMSE,NP[this.ctx].isDOC=this.isDOC}else NP[this.ctx].isWCS=this.isWCS;return h.nv(this.ctx,"#"+A,this.dpr,e)}resizeView(A,e){this.isMSE||h.nr(this.ctx,A,e,0)}setKeepScreenOn(){this.isKeepOn=!0}setScaleMode(A){return NP[this.ctx].scaleMode=A,h.nm(this.ctx,A)}setScaleLevel(A,e,t){return h.nsl(this.ctx,A,e,t)}setCanvasScaleLevel(A,e,t){return h.ncl(this.ctx,A,e,t)}setBufferTime(A){return h.nb(this.ctx,A)}skipLoopFilter(A){return h.nk(this.ctx,A)}skipErrorFrame(){h.nsf(this.ctx)}setVolume(A){this.volume=A,h.ne(this.ctx,A)}setCryptoKey(A){h.ny(this.ctx,A)}enableAudio(A){h.nea(this.ctx,A)}enableVideo(A){h.nev(this.ctx,A)}audioResume(){h.nar(this.ctx)}launchIntoFullscreen(A){A.requestFullscreen?A.requestFullscreen():A.mozRequestFullScreen?A.mozRequestFullScreen():A.webkitRequestFullscreen?A.webkitRequestFullscreen():A.msRequestFullscreen&&A.msRequestFullscreen()}fullscreen(){this.isMSE?this.launchIntoFullscreen(this.vi):this.launchIntoFullscreen(this.ve)}fullWebview(A){this.ve.tmpcss=this.ve.style.cssText,this.ve.style.cssText="width:100%;height:100%;position:absolute;top: 0;left: 0;z-index: -1;",h.nor(this.ctx,A)}exitFullWebview(){this.ve.tmpcss===void 0||this.ve.tmpcss===""||(this.ve.style.cssText=this.ve.tmpcss,h.nor(this.ctx,0))}onResize(A,e){h.nor(this.ctx,A,e)}screenshot(A,e,t){if(!(this.isMSE&&!this.isDOC))if(this.isStart&&this.screenCount++<30){if(this.screenshotImage1==null){this.screenshotImage1=this.ve.toDataURL("image/"+e,t),setTimeout(()=>{this.screenshot(A,e,t)},100);return}if(this.screenshotImage2==null&&(this.screenshotImage2=this.ve.toDataURL("image/"+e,t)),this.screenshotImage1.length===this.screenshotImage2.length){this.screenshotImage2=null,setTimeout(()=>{this.screenshot(A,e,t)},100);return}const r=document.createElement("a");r.href=this.screenshotImage1.length>this.screenshotImage2.length?this.screenshotImage1:this.screenshotImage2,r.download=A,r.click(),this.screenCount=0,this.screenshotImage1=null,this.screenshotImage2=null}else this.screenCount=0,this.screenshotImage1=null,this.screenshotImage2=null,this.emit("error","screenshot error")}start(A){if(typeof A!="string"){NP_ERROR("url invalid parameter");return}if(this.isStart){NP_DEBUG("The instance has started, ignore it");return}if(!this.ctx){NP_ERROR("Player has been released");return}NP_INFO("call start play url:",A),this.isMSE?(this.mse=new v(this.vi),this.mse.on("error",e=>{NP_ERROR("MSE on error",e),this.stop()}),this.mse.start(),NP[this.ctx].mse=this.mse):this.ism&&this.isKeepOn&&this.wakeLock.enable(),this.url=A,this.setVolume(this.volume),this.audioResume(),this.streamLoader.start(this.url,this.headers),this.isStart=!0}stop(){if(!this.isStart){NP_DEBUG("The instance has not started yet, ignore it");return}NP_INFO("call stop play url:",this.url),this.isMSE?(this.mse.stop(),this.mse.removeAllListeners("error"),this.mse=null,delete NP[this.ctx].mse):this.ism&&this.isKeepOn&&this.wakeLock.disable(),this.isStart=!1,this.streamLoader.stop(),this._clearTimeout(),h.np(this.ctx),this.emit("stop")}pause(A){this.isStart&&this.isVod&&(h.nu(this.ctx,A),this.isPause=A)}clearView(){h.ncv(this.ctx)}release(A){!this.isMSE&&this.ctx&&(this.stop(),h.nd(this.ctx,A)),delete NP[this.ctx],_free(this.buffer),this.ctx=null,this.streamLoader=null,this.wakeLock=null}useWorker(A){h.nuw(this.ctx,A)}useWCS(){this.isWCS="VideoEncoder"in window,this.isWCS?NP_INFO("NodePlayer.js use WCS"):NP_INFO("WCS is not supported or experimental-web-platform-features not enabled")}useMSE(A){this.isDOC=A,this.isMSE=v.isSupported('video/mp4; codecs="avc1.64002A"'),this.isMSE?NP_INFO("NodePlayer.js use MSE"):NP_INFO("MSE is not supported")}static activeAudioEngine(A){window.activeAudioEngine=A,A&&NP_INFO("NodePlayer.js use activeAudioEngine")}static workletAudioEngine(A){window.workletAudioEngine=A&P(),window.workletAudioEngine&&NP_INFO("NodePlayer.js use workletAudioEngine")}static debug(A){A?Module.logLevel=NP_LOGLEVEL.INFO:Module.logLevel=NP_LOGLEVEL.NONE}static load(A){window.npAllReadyFlag?A():setTimeout(m.load.bind(this,A),100)}static asyncLoad(){return new Promise((A,e)=>{m.load(()=>{A()})})}}class D{async start(A,e){this.isStart=!0,this.fetchAbortController=new AbortController;try{let t=this.fetchAbortController.signal,r=await fetch(A,{signal:t,headers:e});if(!r.ok){this.isStart=!1,this.emit("error",{code:-2,msg:"fetch open url error, status code is "+r.status});return}for(this.emit("open"),this.reader=r.body.getReader();this.isStart;){let s=await this.reader.read();if(s.done){this.emit("close");break}this.emit("data",s.value)}}catch(t){this.isAbort||this.emit("error",{code:-3,msg:"fetch data error, "+t.message})}this.isStart=!1,this.isAbort=!1,this.reader=null,this.fetchAbortController=null}async stop(){this.isStart=!1;try{this.reader?await this.reader.cancel():this.fetchAbortController&&(this.isAbort=!0,this.fetchAbortController.abort())}catch(A){this.emit("error",{code:-4,msg:"fetch stop error, "+A.message})}}postMessage(){}send(){}}function Q(){class n{async start(t){this.isStart=!0,this.fetchAbortController=new AbortController;try{let r=this.fetchAbortController.signal,s={};Object.assign(s,t.headers);let o=await fetch(t.url,{signal:r,headers:s});if(!o.ok){this.isStart=!1,self.postMessage({func:"error",arg:{code:-2,msg:"open url error, status code is "+o.status}});return}self.postMessage({func:"open"});let a=o.body.getReader();for(;this.isStart;){let l=await a.read();if(l.done){self.postMessage({func:"close"});break}self.postMessage({func:"data",arg:l.value})}}catch(r){this.fetchAbortController&&self.postMessage({func:"error",arg:{code:-3,msg:"fetch data error, "+r.message}})}this.isStart=!1}stop(){this.isStart=!1,this.fetchAbortController&&(this.fetchAbortController.abort(),this.fetchAbortController=null)}send(t){}}const A=new n;onmessage=(e=>{const{func:t,arg:r}=e.data;switch(t){case"start":A.start(r);break;case"stop":A.stop();break;case"send":A.send(r);break}})}class H{start(A){let e=A.replace(/^rtmp:|^rtsp:/,"ws:");e===A&&(e=A.replace(/^rtmps:/,"wss:")),this.socket=new WebSocket(e),this.socket.binaryType="arraybuffer",this.socket.onopen=(()=>{this.emit("open")}),this.socket.onclose=(()=>{this.emit("close")}),this.socket.onerror=(t=>{this.emit("error",{code:-3,msg:t})}),this.socket.onmessage=(t=>{try{const r=new Uint8Array(t.data);this.emit("data",r)}catch(r){this.emit("error",{code:-2,msg:r.message})}})}postMessage(){}stop(){this.socket.close(),this.socket=null}send(A){this.socket.send(A)}}class R extends g{constructor(){super();this.loader=null}getFuncBody(A){return A.trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1]}checkResponse(){return typeof window.ReadableStream=="function"&&typeof window.Response=="function"}checkResponseBody(){return Object.prototype.hasOwnProperty.call(window.Response.prototype,"body")}start(A,e){if(this.url=A,this.loader==null)if(/^ws{1,2}:|^rtmp:|^rtmps:|^rtsp:/i.test(A))this.loader=new H,this.loader.emit=this.emit.bind(this),this.loader.terminate=this.loader.stop,this.loader.start(A);else if(this.checkResponse()&&this.checkResponseBody())this.loader=new D,this.loader.emit=this.emit.bind(this),this.loader.terminate=this.loader.stop,this.loader.start(A,e);else if(this.checkResponse()){const t=this.getFuncBody(Q.toString()),r=new Blob([t],{type:"text/javascript"});this.workerURL=URL.createObjectURL(r),this.loader=new Worker(this.workerURL),this.loader.onmessage=(s=>{const{func:o,arg:a}=s.data;this.emit(o,a)}),this.loader.postMessage({func:"start",arg:{url:A,headers:e}})}else this.emit("error",{code:-4,msg:"No loader is available."});else this.emit("error",{code:-1,msg:"WebLoader has started."})}stop(){this.loader!=null?(this.loader.postMessage({func:"stop"}),this.loader.terminate(),this.loader=null):this.emit("error",{code:-1,msg:"Webloader has not been started yet."})}send(A){this.loader.send(A)}}const y="data:video/mp4;base64,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",j="data:video/webm;base64,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",I=()=>"wakeLock"in navigator&&window.navigator.userAgent.indexOf("Samsung")===-1;class S{constructor(){if(I()){NP_DEBUG("keepScreenOn native implementation"),this._wakeLock=null;const A=()=>{this._wakeLock!==null&&document.visibilityState==="visible"&&this.enable()};document.addEventListener("visibilitychange",A),document.addEventListener("fullscreenchange",A)}else NP_DEBUG("keepScreenOn simulation implementation"),this.noSleepVideo=document.createElement("video"),this.noSleepVideo.setAttribute("playsinline",""),this._addSourceToVideo(this.noSleepVideo,"webm",j),this._addSourceToVideo(this.noSleepVideo,"mp4",y),Object.assign(this.noSleepVideo.style,{position:"absolute",left:"-100%",top:"-100%"}),this.noSleepVideo.addEventListener("timeupdate",()=>{this.noSleepVideo.currentTime>4&&(this.noSleepVideo.currentTime=1)})}_addSourceToVideo(A,e,t){var r=document.createElement("source");r.src=t,r.type=`video/${e}`,A.appendChild(r)}enable(){if(I())return navigator.wakeLock.request("screen").then(A=>{this._wakeLock=A,NP_DEBUG("Wake Lock active."),this._wakeLock.addEventListener("release",()=>{NP_DEBUG("Wake Lock released.")})}).catch(A=>{NP_DEBUG(`${A.name}, ${A.message}`)});document.querySelector("body").append(this.noSleepVideo),this.noSleepVideo.play()}disable(){if(I())this._wakeLock&&this._wakeLock.release(),this._wakeLock=null;else{this.noSleepVideo.pause();try{this.noSleepVideo.parentNode&&this.noSleepVideo.parentNode.removeChild(this.noSleepVideo)}catch(A){NP_DEBUG("Did not unmount video - likely nosleep was never enabled")}}}}function g(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}g.EventEmitter=g,g.prototype._events=void 0,g.prototype._maxListeners=void 0,g.defaultMaxListeners=10,g.prototype.setMaxListeners=function(n){if(!G(n)||n<0||isNaN(n))throw TypeError("n must be a positive number");return this._maxListeners=n,this},g.prototype.emit=function(n){let A,e,t,r,s,o;if(this._events||(this._events={}),n==="error"&&(!this._events.error||C(this._events.error)&&!this._events.error.length)){if(A=arguments[1],A instanceof Error)throw A;{const a=new Error('Uncaught, unspecified "error" event. ('+A+")");throw a.context=A,a}}if(e=this._events[n],F(e))return!1;if(B(e))switch(arguments.length){case 1:e.call(this);break;case 2:e.call(this,arguments[1]);break;case 3:e.call(this,arguments[1],arguments[2]);break;default:r=Array.prototype.slice.call(arguments,1),e.apply(this,r)}else if(C(e))for(r=Array.prototype.slice.call(arguments,1),o=e.slice(),t=o.length,s=0;s<t;s++)o[s].apply(this,r);return!0},g.prototype.addListener=function(n,A){let e;if(!B(A))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",n,B(A.listener)?A.listener:A),this._events[n]?C(this._events[n])?this._events[n].push(A):this._events[n]=[this._events[n],A]:this._events[n]=A,C(this._events[n])&&!this._events[n].warned&&(F(this._maxListeners)?e=g.defaultMaxListeners:e=this._maxListeners,e&&e>0&&this._events[n].length>e&&(this._events[n].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[n].length),typeof console.trace=="function"&&console.trace())),this},g.prototype.on=g.prototype.addListener,g.prototype.once=function(n,A){if(!B(A))throw TypeError("listener must be a function");let e=!1;function t(){this.removeListener(n,t),e||(e=!0,A.apply(this,arguments))}return t.listener=A,this.on(n,t),this},g.prototype.removeListener=function(n,A){let e,t,r,s;if(!B(A))throw TypeError("listener must be a function");if(!this._events||!this._events[n])return this;if(e=this._events[n],r=e.length,t=-1,e===A||B(e.listener)&&e.listener===A)delete this._events[n],this._events.removeListener&&this.emit("removeListener",n,A);else if(C(e)){for(s=r;s-- >0;)if(e[s]===A||e[s].listener&&e[s].listener===A){t=s;break}if(t<0)return this;e.length===1?(e.length=0,delete this._events[n]):e.splice(t,1),this._events.removeListener&&this.emit("removeListener",n,A)}return this},g.prototype.removeAllListeners=function(n){let A,e;if(!this._events)return this;if(!this._events.removeListener)return arguments.length===0?this._events={}:this._events[n]&&delete this._events[n],this;if(arguments.length===0){for(A in this._events)A!=="removeListener"&&this.removeAllListeners(A);return this.removeAllListeners("removeListener"),this._events={},this}if(e=this._events[n],B(e))this.removeListener(n,e);else if(e)for(;e.length;)this.removeListener(n,e[e.length-1]);return delete this._events[n],this},g.prototype.listeners=function(n){let A;return!this._events||!this._events[n]?A=[]:B(this._events[n])?A=[this._events[n]]:A=this._events[n].slice(),A},g.prototype.listenerCount=function(n){if(this._events){const A=this._events[n];if(B(A))return 1;if(A)return A.length}return 0},g.listenerCount=function(n,A){return n.listenerCount(A)};function B(n){return typeof n=="function"}function G(n){return typeof n=="number"}function C(n){return typeof n=="object"&&n!==null}function F(n){return n==="undefine"}function p(n,A,e){const t=n;if(A+e<t.length){for(;e--;)if((t[++A]&192)!=128)return!1;return!0}else return!1}function b(n){const A=[],e=n;let t=0;const r=n.length;for(;t<r;){if(e[t]<128){A.push(String.fromCharCode(e[t])),++t;continue}else if(!(e[t]<192)){if(e[t]<224){if(p(e,t,1)){const s=(e[t]&31)<<6|e[t+1]&63;if(s>=128){A.push(String.fromCharCode(s&65535)),t+=2;continue}}}else if(e[t]<240){if(p(e,t,2)){const s=(e[t]&15)<<12|(e[t+1]&63)<<6|e[t+2]&63;if(s>=2048&&(s&63488)!=55296){A.push(String.fromCharCode(s&65535)),t+=3;continue}}}else if(e[t]<248&&p(e,t,3)){let s=(e[t]&7)<<18|(e[t+1]&63)<<12|(e[t+2]&63)<<6|e[t+3]&63;if(s>65536&&s<1114112){s-=65536,A.push(String.fromCharCode(s>>>10|55296)),A.push(String.fromCharCode(s&1023|56320)),t+=4;continue}}}A.push(String.fromCharCode(65533)),++t}return A.join("")}const d=function(){const n=new ArrayBuffer(2);return new DataView(n).setInt16(0,256,!0),new Int16Array(n)[0]===256}();class u{static parseScriptData(A,e,t){const r={};try{const s=u.parseValue(A,e,t),o=u.parseValue(A,e+s.size,t-s.size);r[s.data]=o.data}catch(s){NP_ERROR("AMF",s.toString())}return r}static parseObject(A,e,t){if(t<3)throw new IllegalStateException("Data not enough when parse ScriptDataObject");const r=u.parseString(A,e,t),s=u.parseValue(A,e+r.size,t-r.size),o=s.objectEnd;return{data:{name:r.data,value:s.data},size:r.size+s.size,objectEnd:o}}static parseVariable(A,e,t){return u.parseObject(A,e,t)}static parseString(A,e,t){if(t<2)throw new IllegalStateException("Data not enough when parse String");const s=new DataView(A,e,t).getUint16(0,!d);let o;return s>0?o=b(new Uint8Array(A,e+2,s)):o="",{data:o,size:2+s}}static parseLongString(A,e,t){if(t<4)throw new IllegalStateException("Data not enough when parse LongString");const s=new DataView(A,e,t).getUint32(0,!d);let o;return s>0?o=b(new Uint8Array(A,e+4,s)):o="",{data:o,size:4+s}}static parseDate(A,e,t){if(t<10)throw new IllegalStateException("Data size invalid when parse Date");const r=new DataView(A,e,t);let s=r.getFloat64(0,!d);return s+=r.getInt16(8,!d)*60*1e3,{data:new Date(s),size:8+2}}static parseValue(A,e,t){if(t<1)throw new IllegalStateException("Data not enough when parse Value");const r=new DataView(A,e,t);let s=1;const o=r.getUint8(0);let a,l=!1;try{switch(o){case 0:a=r.getFloat64(1,!d),s+=8;break;case 1:{a=!!r.getUint8(1),s+=1;break}case 2:{const x=u.parseString(A,e+1,t-1);a=x.data,s+=x.size;break}case 3:{a={};let x=0;for((r.getUint32(t-4,!d)&16777215)==9&&(x=3);s<t-4;){const c=u.parseObject(A,e+s,t-s-x);if(c.objectEnd)break;a[c.data.name]=c.data.value,s+=c.size}s<=t-3&&(r.getUint32(s-1,!d)&16777215)===9&&(s+=3);break}case 8:{a={},s+=4;let x=0;for((r.getUint32(t-4,!d)&16777215)==9&&(x=3);s<t-8;){const c=u.parseVariable(A,e+s,t-s-x);if(c.objectEnd)break;a[c.data.name]=c.data.value,s+=c.size}s<=t-3&&(r.getUint32(s-1,!d)&16777215)===9&&(s+=3);break}case 9:a=void 0,s=1,l=!0;break;case 10:{a=[];const x=r.getUint32(1,!d);s+=4;for(let c=0;c<x;c++){const f=u.parseValue(A,e+s,t-s);a.push(f.data),s+=f.size}break}case 11:{const x=u.parseDate(A,e+1,t-1);a=x.data,s+=x.size;break}case 12:{const x=u.parseString(A,e+1,t-1);a=x.data,s+=x.size;break}default:s=t,NP_ERROR("AMF","Unsupported AMF value type "+o)}}catch(x){NP_ERROR("AMF",x.toString())}return{data:a,size:s,objectEnd:l}}}class v extends g{constructor(A){super();this.codecs='video/mp4; codecs="avc1.64002A"',this.hvcCodecs='video/mp4; codecs="hev1.1.6.L123.b0"',this.isAvc=!0,this.mediaSource=new window.MediaSource,A.src=window.URL.createObjectURL(this.mediaSource),this.video=A,this.mediaSource.addEventListener("sourceopen",()=>{this.emit("sourceopen")}),this.mediaSource.addEventListener("sourceclose",()=>{this.emit("sourceclose")})}get state(){return this.mediaSource.readyState}get duration(){return this.mediaSource.duration}set duration(A){this.mediaSource.duration=A}appendBuffer(A){if(this.sourceBuffer==null){let t=this.isAvc?this.codecs:this.hvcCodecs;this.sourceBuffer=this.mediaSource.addSourceBuffer(t),this.sourceBuffer.addEventListener("error",r=>{this.emit("error",{type:"sourceBuffer",error:r})})}const e=this.sourceBuffer;if(e.updating===!1&&this.state==="open")return e.appendBuffer(A),!0;if(this.state==="closed")this.emit("error",{type:"sourceBuffer",error:new Error("mediaSource is not attached to video or mediaSource is closed")});else if(this.state==="ended")this.emit("error",{type:"sourceBuffer",error:new Error("mediaSource is closed")});else return e.updating===!0&&this.emit("warn",{type:"sourceBuffer",error:new Error("mediaSource is busy")}),!1}start(){this.video.muted=!0,this.video.play()}stop(){this.state==="open"&&(this.sourceBuffer&&this.sourceBuffer.abort(),this.endOfStream())}removeBuffer(A,e){if(this.state==="open")try{this.sourceBuffer.remove(A,e)}catch(t){}}endOfStream(){this.state==="open"&&this.mediaSource.endOfStream()}static isSupported(A){return window.MediaSource&&window.MediaSource.isTypeSupported(A)}}class i{static init(){i.types={avc1:[],avcC:[],hvc1:[],hvcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]};for(const e in i.types)i.types.hasOwnProperty(e)&&(i.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);const A=i.constants={};A.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),A.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),A.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),A.STSC=A.STCO=A.STTS,A.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),A.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),A.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),A.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),A.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),A.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(A){let e=8,t=null,r=Array.prototype.slice.call(arguments,1),s=r.length;for(let a=0;a<s;a++)e+=r[a].byteLength;t=new Uint8Array(e),t[0]=e>>>24&255,t[1]=e>>>16&255,t[2]=e>>>8&255,t[3]=e&255,t.set(A,4);let o=8;for(let a=0;a<s;a++)t.set(r[a],o),o+=r[a].byteLength;return t}static generateInitSegment(A){let e=i.box(i.types.ftyp,i.constants.FTYP),t=i.moov(A),r=new Uint8Array(e.byteLength+t.byteLength);return r.set(e,0),r.set(t,e.byteLength),r}static moov(A){let e=i.mvhd(A.timescale,A.duration),t=i.trak(A),r=i.mvex(A);return i.box(i.types.moov,e,t,r)}static mvhd(A,e){return i.box(i.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,A>>>24&255,A>>>16&255,A>>>8&255,A&255,e>>>24&255,e>>>16&255,e>>>8&255,e&255,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(A){return i.box(i.types.trak,i.tkhd(A),i.mdia(A))}static tkhd(A){let e=A.id,t=A.duration,r=A.codecWidth,s=A.codecHeight;return i.box(i.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,e&255,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,t&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,r>>>8&255,r&255,0,0,s>>>8&255,s&255,0,0]))}static mdia(A){return i.box(i.types.mdia,i.mdhd(A),i.hdlr(A),i.minf(A))}static mdhd(A){let e=A.timescale,t=A.duration;return i.box(i.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,e&255,t>>>24&255,t>>>16&255,t>>>8&255,t&255,85,196,0,0]))}static hdlr(A){let e=null;return A.type==="audio"?e=i.constants.HDLR_AUDIO:e=i.constants.HDLR_VIDEO,i.box(i.types.hdlr,e)}static minf(A){let e=null;return A.type==="audio"?e=i.box(i.types.smhd,i.constants.SMHD):e=i.box(i.types.vmhd,i.constants.VMHD),i.box(i.types.minf,e,i.dinf(),i.stbl(A))}static dinf(){return i.box(i.types.dinf,i.box(i.types.dref,i.constants.DREF))}static stbl(A){return i.box(i.types.stbl,i.stsd(A),i.box(i.types.stts,i.constants.STTS),i.box(i.types.stsc,i.constants.STSC),i.box(i.types.stsz,i.constants.STSZ),i.box(i.types.stco,i.constants.STCO))}static stsd(A){return A.type==="audio"?i.box(i.types.stsd,i.constants.STSD_PREFIX,i.mp4a(A)):A.codec==512?i.box(i.types.stsd,i.constants.STSD_PREFIX,i.avc1(A)):i.box(i.types.stsd,i.constants.STSD_PREFIX,i.hvc1(A))}static mp4a(A){let e=A.channelCount,t=A.audioSampleRate,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e,0,16,0,0,0,0,t>>>8&255,t&255,0,0]);return i.box(i.types.mp4a,r,i.esds(A))}static esds(A){let e=A.config||[],t=e.length,r=new Uint8Array([0,0,0,0,3,23+t,0,1,0,4,15+t,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([t]).concat(e).concat([6,1,2]));return i.box(i.types.esds,r)}static avc1(A){let e=A.avcc,t=A.codecWidth;const r=A.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t>>>8&255,t&255,r>>>8&255,r&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return i.box(i.types.avc1,s,i.box(i.types.avcC,e))}static hvc1(A){let e=A.avcc,t=A.codecWidth;const r=A.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t>>>8&255,t&255,r>>>8&255,r&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return i.box(i.types.hvc1,s,i.box(i.types.hvcC,e))}static mvex(A){return i.box(i.types.mvex,i.trex(A))}static trex(A){let e=A.id,t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,e&255,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return i.box(i.types.trex,t)}static moof(A,e){return i.box(i.types.moof,i.mfhd(A.sn),i.traf(A,e))}static mfhd(A){let e=new Uint8Array([0,0,0,0,A>>>24&255,A>>>16&255,A>>>8&255,A&255]);return i.box(i.types.mfhd,e)}static traf(A,e){let t=A.id,r=i.box(i.types.tfhd,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,t&255])),s=i.box(i.types.tfdt,new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,e&255])),o=i.sdtp(A),a=i.trun(A,o.byteLength+16+16+8+16+8+8);return i.box(i.types.traf,r,s,a,o)}static sdtp(A){let e=new Uint8Array(4+1),t=A.flags;return e[4]=t.isLeading<<6|t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy,i.box(i.types.sdtp,e)}static trun(A,e){let t=12+16,r=new Uint8Array(t);e+=8+t,r.set([0,0,15,1,0,0,0,1,e>>>24&255,e>>>16&255,e>>>8&255,e&255],0);let s=A.duration,o=A.size,a=A.flags,l=A.cts;return r.set([s>>>24&255,s>>>16&255,s>>>8&255,s&255,o>>>24&255,o>>>16&255,o>>>8&255,o&255,a.isLeading<<2|a.dependsOn,a.isDependedOn<<6|a.hasRedundancy<<4|a.isNonSync,0,0,l>>>24&255,l>>>16&255,l>>>8&255,l&255],12),i.box(i.types.trun,r)}static mdat(A){return i.box(i.types.mdat,A)}}i.init();function P(){let n=new OfflineAudioContext(1,1,44100);return Boolean(n.audioWorklet&&typeof n.audioWorklet.addModule=="function")}function O(){class n extends AudioWorkletProcessor{constructor(){super();this.state=0,this.start=!0,this.samplesArray0=[],this.samplesArray1=[],this.offset=0,this.bufferSize=1024,this.port.onmessage=(e=>{e.data.message=="init"?this.bufferSize=e.data.bufferSize:e.data.message=="stop"?this.start=!1:e.data.message=="data"?(this.samplesArray0.push(e.data.buffer[0]),this.samplesArray1.push(e.data.buffer[1])):e.data.message=="zero"})}process(e,t,r){const s=t[0],o=s[0],a=s[1];if(this.offset==0&&this.port.postMessage({message:"beep"}),this.state==0&&(this.state=1),this.state==1&&this.samplesArray0.length==4&&this.samplesArray1.length==4&&(this.state=2),this.state==2){let l=this.samplesArray0[0],x=this.samplesArray1[0];for(let c=0;c<o.length;c++)o[c]=l[c+this.offset];for(let c=0;c<a.length;c++)a[c]=x[c+this.offset]}else o.fill(0),a.fill(0);return this.offset+=128,this.offset==this.bufferSize&&(this.offset=0,this.state==2&&(this.samplesArray0.shift(),this.samplesArray1.shift()),this.samplesArray0.length==0&&this.samplesArray1.length==0&&(this.state=0)),this.start}}registerProcessor("wa-processor",n)}function L(n){return n.trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1]}function Y(){const n=L(O.toString()),A=new Blob([n],{type:"text/javascript"});return URL.createObjectURL(A)}class N{constructor(A,e){const t=`\n        attribute vec2 xy;\n    \n        varying highp vec2 uv;\n    \n        void main(void) {\n          gl_Position = vec4(xy, 0.0, 1.0);\n          // Map vertex coordinates (-1 to +1) to UV coordinates (0 to 1).\n          // UV coordinates are Y-flipped relative to vertex coordinates.\n          uv = vec2((1.0 + xy.x) / 2.0, (1.0 - xy.y) / 2.0);\n        }\n      `,r=`\n        varying highp vec2 uv;\n\n        uniform sampler2D texture;\n\n        void main(void) {\n          gl_FragColor = texture2D(texture, uv);\n        }\n      `;this.canvas=e;const s=this.ctx=e.getContext(A),o=s.createShader(s.VERTEX_SHADER);if(s.shaderSource(o,t),s.compileShader(o),!s.getShaderParameter(o,s.COMPILE_STATUS))throw s.getShaderInfoLog(o);const a=s.createShader(s.FRAGMENT_SHADER);if(s.shaderSource(a,r),s.compileShader(a),!s.getShaderParameter(a,s.COMPILE_STATUS))throw s.getShaderInfoLog(a);const l=s.createProgram();if(s.attachShader(l,o),s.attachShader(l,a),s.linkProgram(l),!s.getProgramParameter(l,s.LINK_STATUS))throw s.getProgramInfoLog(l);s.useProgram(l);const x=s.createBuffer();s.bindBuffer(s.ARRAY_BUFFER,x),s.bufferData(s.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),s.STATIC_DRAW);const c=s.getAttribLocation(l,"xy");s.vertexAttribPointer(c,2,s.FLOAT,!1,0,0),s.enableVertexAttribArray(c);const f=s.createTexture();s.bindTexture(s.TEXTURE_2D,f),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.NEAREST),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.NEAREST),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE)}draw(A,e,t,r,s){this.canvas.width=A.displayWidth,this.canvas.height=A.displayHeight;const o=this.ctx;o.texImage2D(o.TEXTURE_2D,0,o.RGBA,o.RGBA,o.UNSIGNED_BYTE,A),A.close(),o.viewport(0,0,o.drawingBufferWidth,o.drawingBufferHeight),o.clearColor(0,0,0,1),o.clear(o.COLOR_BUFFER_BIT),o.drawArrays(o.TRIANGLE_FAN,0,4)}clear(){const A=this.ctx;A.clearColor(0,0,0,1),A.clear(A.COLOR_BUFFER_BIT)}}m.WAB=Y,m.MP4=i,m.AMF=u,m.WGL=N,typeof module!="undefined"&&typeof module.exports!="undefined"?module.exports={NodePlayer:m,NodePublisher:w}:(window.NodePlayer=m,window.NodePublisher=w)})();
