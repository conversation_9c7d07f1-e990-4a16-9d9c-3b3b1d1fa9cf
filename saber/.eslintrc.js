module.exports = {
  root: true,
  env: {
    node: true
  },
  'extends': [
    'plugin:vue/essential',
    'eslint:recommended'
  ],
  rules: {
    'no-undef': 'off',
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    "no-multi-spaces": "warn",// 不能用多余的空格
    "semi":  ["error", "always"], // 代码需要分号结尾
    "no-unused-vars": "warn", // 未使用的变量与方法，仅警告提示，提交不受限制
    'indent': [2, 2], // 缩进风格
    "no-trailing-spaces": "error", // 一行结束后面不要有空格
    "vue/html-end-tags": "error", // 结束标签
    "vue/html-indent": "error", // 统一缩进
    "vue/html-quotes": "error", // html双引号
    "vue/html-self-closing": "error", // 标签自闭合
    "vue/max-attributes-per-line": "error", // 标签属性换行
    "vue/mustache-interpolation-spacing": "error", // 绑定标签内容需要间隔
    "vue/name-property-casing": "error", // 每个文件需要name值采用大驼峰
    "vue/no-multi-spaces": "error", // 模板不允许有多余的空格
    "vue/prop-name-casing": "error", // props使用小驼峰
    "vue/require-default-prop": "error", // props需要默认值
    "vue/require-prop-types": "error", // 在props中需要类型定义
    "vue/v-bind-style": "error", // v-bind简写
    "vue/v-on-style": "error", // v-on事件简写
    // recommended
    "vue/attributes-order": "error", // 属性顺序
    "vue/no-v-html": "error", // 禁用v-html
    "vue/order-in-components": "error", // 内部组件属性声明顺序
    "vue/this-in-template": "error", // 模板禁用this
    "vue/html-closing-bracket-newline": ["warn", {
      "singleline": "never",
      "multiline": "always"
    }]
  },
  parserOptions: {
    parser: 'babel-eslint'
  }
}
