{"ModelOptions": [{"value": "设置参数", "label": "设置参数"}, {"value": "查询参数", "label": "查询参数"}], "InputContent": {"设置参数": {"Basic": {"stringInput": [{"title": "终端心跳发送间隔(秒)", "body": [{"name": "终端心跳发送间隔(秒)", "key": "heart_beat", "param_id": "0x0001", "type": "integer"}]}, {"title": "TCP设置", "body": [{"name": "TCP消息应答超时时间(秒)", "key": "tcp_time_out", "param_id": "0x0002", "type": "integer"}, {"name": "TCP消息重传次数", "key": "tcp_retrans", "param_id": "0x0003", "type": "integer"}]}, {"title": "UDP设置", "body": [{"name": "UDP消息应答超时时间(秒)", "key": "udp_time_out", "param_id": "0x0004", "type": "integer"}, {"name": "UDP消息重传次数", "key": "udp_retrans", "param_id": "0x0005", "type": "integer"}]}, {"title": "SMS设置", "body": [{"name": "SMS消息应答超时时间(秒)", "key": "sms_time_out", "param_id": "0x0006", "type": "integer"}, {"name": "SMS消息重传次数", "key": "sms_retrans", "param_id": "0x0007", "type": "integer"}]}, {"title": "服务器设置", "body": [{"name": "主服务器APN", "key": "main_server_apn", "param_id": "0x0010", "type": "string"}, {"name": "拨号用户名", "key": "main_server_username", "param_id": "0x0011", "type": "string"}, {"name": "拨号密码", "key": "main_server_password", "param_id": "0x0012", "type": "string"}, {"name": "IP地址域名", "key": "main_server_ips", "param_id": "0x0013", "type": "string"}, {"name": "从服务器IP地址", "key": "slave_server_ips", "param_id": "38", "type": "string"}, {"name": "备份服务器APN", "key": "spare_server_apn", "param_id": "0x0014", "type": "string"}, {"name": "拨号用户名", "key": "spare_server_username", "param_id": "0x0015", "type": "string"}, {"name": "拨号密码", "key": "spare_server_password", "param_id": "0x0016", "type": "string"}, {"name": "IP地址域名", "key": "spare_server_ips", "param_id": "0x0017", "type": "string"}, {"name": "服务器TCP端口", "key": "server_tcp_port", "param_id": "0x0018", "type": "integer"}, {"name": "服务器UDP端口", "key": "server_udp_port", "param_id": "0x0019", "type": "integer"}]}, {"title": "驾驶员未登录设置", "body": [{"name": "驾驶员未登录汇报时间间隔(秒)", "key": "loc_time_in_log_out", "param_id": "0x0022", "type": "integer"}, {"name": "驾驶员未登录汇报距离间隔(米)", "key": "loc_step_in_log_out", "param_id": "0x002d", "type": "integer"}]}, {"title": "缺省时间汇报设置", "body": [{"name": "缺省时间汇报间隔(秒)", "key": "loc_time_default", "param_id": "0x0029", "type": "integer"}, {"name": "缺省距离汇报间隔(米)", "key": "loc_step_default", "param_id": "0x002c", "type": "integer"}]}, {"title": "休眠时汇报时间间隔(秒)", "body": [{"name": "休眠时汇报时间间隔(秒)", "key": "loc_time_in_sleep", "param_id": "0x0027", "type": "integer"}]}, {"title": "拐点补传角度(小于180度)", "body": [{"name": "拐点补传角度(小于180度)", "key": "vertex_add_angle", "param_id": "0x0030", "type": "integer"}]}, {"title": "电子围栏半径设置(米)", "body": [{"name": "电子围栏半径设置(米)", "key": "ele_fence_radius", "param_id": "0x0031", "type": "integer"}]}, {"title": "车辆里程表读数(1/10km)", "body": [{"name": "车辆里程表读数(1/10km)", "key": "mileage_read", "param_id": "0x0080", "type": "integer"}]}, {"title": "车辆所在的省域ID", "body": [{"name": "车辆所在的省域ID", "key": "province_id", "param_id": "0x0081", "type": "integer"}]}, {"title": "车辆所在的市域ID", "body": [{"name": "车辆所在的市域ID", "key": "city_id", "param_id": "0x0082", "type": "integer"}]}, {"title": "监管目标标识", "body": [{"name": "监管目标标识(监管车辆时填写公安交通管理部门颁发的机动车号牌;铁路货车填写车厢编号;集装箱时填写集装箱编号;人员填写人员编号(系统唯一);其他类型监管目标时填写终端标识号(终端ID);默认值为终端ID)", "key": "licence_plate", "param_id": "0x0083", "type": "other"}]}, {"title": "监管目标类型", "body": [{"name": "监管目标类型(蓝色:1,黄色:2,黑色:3,白色:4,绿色:5,其它颜色:9,铁路货车:10,集装箱:11,人员定位:12,外派:13,访客:14,其他:15,农黄:91,农绿:92,黄绿:93,渐变绿:94)", "key": "car_color", "param_id": "0x0084", "type": "integer"}]}, {"title": "超速告警预警差值(1/10km/h)", "body": [{"name": "超速告警预警差值(1/10km/h)", "key": "overspeed_warn_value", "param_id": "0x005b", "type": "integer"}]}], "radioData": [{"key": "loc_rept_strategy", "name": "位置汇报策略", "data": [{"value": 1, "name": "定时汇报"}, {"value": 2, "name": "定距汇报"}, {"value": 3, "name": "定时和定距汇报"}], "param_id": "0x0020", "type": "integer"}, {"key": "loc_rept_plan", "name": "位置汇报方案", "data": [{"value": 1, "name": "根据ACC状态"}, {"value": 2, "name": "先判断登录状态，若登录再根据ACC状态"}], "param_id": "0x0021", "type": "integer"}], "timeData": [{"name": "设置禁行时段", "key": "illegalDuration", "value": {"startTime": "", "endTime": ""}, "param_id": "0x0032"}], "mixData": [{"key": "gnss_mode", "name": "GNSS定位模式", "checkBody": [{"name": "启用GPS", "key": "gnss_mode_GPS"}, {"name": "启用北斗定位", "key": "gnss_mode_bd"}, {"name": "启用GLONASS定位", "key": "gnss_mode_GLONASS"}, {"name": "启用Galileo定位", "key": "gnss_mode_Galileo"}], "param_id": "0x0090", "type": "integer", "style": "checkbox"}, {"key": "gnss_rate", "name": "GNSS波特率", "data": [{"value": 0, "name": "4800"}, {"value": 1, "name": "9600"}, {"value": 2, "name": "19200"}, {"value": 3, "name": "38400"}, {"value": 4, "name": "57600"}, {"value": 5, "name": "115200"}], "param_id": "0x0091", "type": "integer", "style": "radio"}, {"key": "gnss_frequency", "name": "GNSS定位数据输出频率", "data": [{"value": 0, "name": "500ms"}, {"value": 1, "name": "1000ms(默认值)"}, {"value": 2, "name": "2000ms"}, {"value": 3, "name": "3000ms"}, {"value": 4, "name": "4000ms"}], "param_id": "0x0092", "type": "integer", "style": "radio"}, {"title": "GNSS定位数据采集频率(秒)", "body": [{"name": "GNSS定位数据采集频率(秒)", "key": "gnss_gather", "param_id": "0x0093", "type": "integer"}], "style": "input"}, {"key": "gnss_upload", "name": "GNSS定位数据上传方式", "data": [{"value": 0, "name": "本地存储，不上传(默认值)"}, {"value": 1, "name": "按时间间隔上传"}, {"value": 2, "name": "按距离间隔上传"}, {"value": 11, "name": "按累计时间上传,达到传输时间后自动停止上传"}, {"value": 12, "name": "按累计距离上传,达到距离后自动停止上传"}, {"value": 13, "name": "按累计条数上传,达到上传条数后自动停止上传"}], "param_id": "0x0094", "type": "integer", "style": "radio"}, {"title": "GNSS定位数据上传设置", "body": [{"name": "GNSS定位数据上传设置", "key": "gnss_upload_set", "param_id": "0x0095", "type": "integer"}], "special": [{"value": 1, "name": "单位为秒"}, {"value": 2, "name": "单位为米"}, {"value": 11, "name": "单位为秒"}, {"value": 12, "name": "单位为米"}, {"value": 13, "name": "单位为条"}], "style": "input"}, {"title": "终端休眠等待时长参数", "param_id": "0xF005", "content": [{"name": "模式:", "key": "mode", "param_id": "0xF005", "type": "integer", "style": "radio", "params": [{"value": 0, "name": "根据ACC判断"}, {"value": 1, "name": "定时休眠"}]}, {"name": "延迟休眠时间（分钟）", "key": "minutes", "param_id": "0x0095", "type": "integer", "style": "input"}, {"name": "休眠开始时间", "key": "start_time", "param_id": "0x0095", "type": "integer", "style": "timePicker", "fuction": true}, {"name": "休眠结束时间", "key": "end_time", "param_id": "0x0095", "type": "integer", "style": "timePicker", "fuction": true}]}, {"title": "平台控制终端远程重启", "content": [{"name": "平台控制终端远程重启:", "key": "cmd", "param_id": "0xF005", "type": "integer", "style": "radio", "params": [{"value": 4, "name": "终端复位"}, {"value": 5, "name": "终端恢复出厂设置"}]}], "unQuery": true, "post": "terminalcontrol"}]}, "Alarm": {"stringInput": [{"title": "疲劳驾驶设置", "body": [{"name": "连续驾驶时间阈值(秒)", "key": "dur_of_driving", "param_id": "0x0057", "type": "integer"}, {"name": "当天累计驾驶时间阈值(秒)", "key": "t_time_of_dri_today", "param_id": "0x0058", "type": "integer"}, {"name": "最小休息时间(秒)", "key": "min_rest_time", "param_id": "0x0059", "type": "integer"}, {"name": "疲劳驾驶预警差值", "key": "fatigue_dri_warn_value", "param_id": "92", "type": "integer"}]}, {"title": "超速设置", "body": [{"name": "最高速度(公里/小时)", "key": "max_speed", "param_id": "0x0055", "type": "integer"}, {"name": "超速持续时间(秒)", "key": "dur_of_overspeed", "param_id": "0x0056", "type": "integer"}]}, {"title": "告警多媒体设置", "body": [{"name": "告警时发送文本SMS", "key": "alarm_text_switch", "param_id": "0x0051", "type": "integer"}, {"name": "告警时拍摄照片", "key": "alarm_shoot_switch", "param_id": "0x0052", "type": "integer"}, {"name": "告警时拍摄存储", "key": "alarm_photosave_flag", "param_id": "0x0053", "type": "integer"}]}, {"title": "紧急告警汇报设置", "body": [{"name": "紧急告警时汇报时间间隔(秒)", "key": "loc_time_in_em_alarm", "param_id": "0x0028", "type": "integer"}, {"name": "紧急告警时汇报距离间隔(米)", "key": "loc_step_in_em_alarm", "param_id": "0x002f", "type": "integer"}]}, {"title": "告警屏蔽字", "body": [{"name": "告警屏蔽字", "key": "alarm_mask_word", "param_id": "0x0050", "type": "integer"}]}, {"title": "最长停车时间(秒)", "body": [{"name": "最长停车时间(秒)", "key": "max_stop_time", "param_id": "0x005a", "type": "integer"}]}]}, "Phone": {"stringInput": [{"title": "通话时间设置", "body": [{"name": "每次最长通话是时间(秒)", "key": "max_talk_time_once", "param_id": "0x0046", "type": "integer"}, {"name": "当月最长通话时间(秒)", "key": "max_talk_time_month", "param_id": "0x0047", "type": "integer"}]}, {"title": "复位电话号码", "body": [{"name": "复位电话号码", "key": "reset_tel", "param_id": "0x0041", "type": "string"}]}, {"title": "恢复出厂设置电话号码", "body": [{"name": "恢复出厂设置电话号码", "key": "restore_tel", "param_id": "0x0042", "type": "string"}]}, {"title": "监控平台SMS电话号码", "body": [{"name": "监控平台SMS电话号码", "key": "server_smstel", "param_id": "0x0043", "type": "string"}]}, {"title": "终端接收SMS文本电话号码", "body": [{"name": "终端接收SMS文本电话号码", "key": "ter_sms_text_alarm_tel", "param_id": "0x0044", "type": "string"}]}, {"title": "监听电话号码", "body": [{"name": "监听电话号码", "key": "monitor_tel", "param_id": "0x0048", "type": "string"}]}, {"title": "监管平台特权短信号码", "body": [{"name": "监管平台特权短信号码", "key": "server_own_text_tel", "param_id": "0x0049", "type": "string"}]}], "radioData": [{"key": "answer_phone_strategy", "name": "接听设置", "data": [{"value": 1, "name": "自动接听"}, {"value": 2, "name": "点火自动接听，熄火手动接听"}], "param_id": "0x0045", "type": "integer"}]}, "Photo": {"stringInput": [{"title": "图像/视频质量(1-10,1最好)", "body": [{"name": "图像/视频质量(1-10,1最好)", "key": "definition", "param_id": "0x0070", "type": "integer"}]}, {"title": "亮度(0-255)", "body": [{"name": "亮度(0-255)", "key": "brightness", "param_id": "0x0071", "type": "integer"}]}, {"title": "对比度(0-127)", "body": [{"name": "对比度(0-127)", "key": "contrast", "param_id": "0x0072", "type": "integer"}]}, {"title": "饱和度(0-127)", "body": [{"name": "饱和度(0-127)", "key": "saturation", "param_id": "0x0073", "type": "integer"}]}, {"title": "色度(0-255)", "body": [{"name": "色度(0-255)", "key": "chroma", "param_id": "0x0074", "type": "integer"}]}], "checkData": [{"title": "定时拍照时间间隔设置", "body": [{"title": "摄像头索引", "checkBody": [{"name": "摄像头1", "key": "time_photo_on_chan_1"}, {"name": "摄像头2", "key": "time_photo_on_chan_2"}, {"name": "摄像头3", "key": "time_photo_on_chan_3"}, {"name": "摄像头4", "key": "time_photo_on_chan_4"}], "inputBody": [{"name": "拍照时间间隔(秒)", "key": "time_interval"}], "param_id": "0x0064"}]}]}, "Update": {"stringInput": [{"title": "终端远程升级", "body": [{"name": "文件名", "key": "file_name", "type": "string"}, {"name": "文件路径", "key": "path", "type": "string"}, {"name": "用户名", "key": "user", "type": "string"}, {"name": "密码", "key": "pass_word", "type": "string"}, {"name": "ftp服务器ip", "key": "ftp_ip", "type": "string"}, {"name": "ftp服务器端口", "key": "tcp_port", "type": "string"}]}]}}}, "inputSet": {"machine": {"input": [{"title": "", "body": [{"name": "终端SIM卡ICCID号", "key": "iccid"}, {"name": "终端类型", "key": "ter_type"}, {"name": "制造商id", "key": "vendor_id"}, {"name": "终端型号", "key": "model"}, {"name": "序列号", "key": "ter_id"}, {"name": "终端本机号码", "key": "phone"}, {"name": "国际移动设备识别码", "key": "imei"}, {"name": "终端硬件版本号", "key": "hw_version"}, {"name": "终端固件版本号", "key": "fw_version"}, {"name": "GNSS模块属性", "key": "gnss_attr"}, {"name": "通信模块属性", "key": "comm_attr"}]}]}}}