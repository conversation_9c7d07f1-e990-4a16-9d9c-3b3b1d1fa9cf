.mainContainer {
    display: block;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}
@media screen and (min-width: 1152px) {
    .mainContainer {
        display: block;
        width: 1152px;
        margin-left: auto;
        margin-right: auto;
    }
}

.video-container {
    position: relative;
    margin-top: 8px;
}

.video-container:before {
    display: block;
    content: "";
    width: 100%;
    padding-bottom: 56.25%;
}

.video-container > div {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.video-container video {
    width: 100%;
    height: 100%;
}

.urlInput {
    display: block;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 8px;
    margin-bottom: 8px;
}

.centeredVideo {
    display: block;
    width: 100%;
    height: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: auto;
}

.controls {
    display: block;
    width: 100%;
    text-align: left;
    margin-left: auto;
    margin-right: auto;
    margin-top: 8px;
    margin-bottom: 10px;
}

.logcatBox {
    border-color: #CCCCCC;
    font-size: 11px;
    font-family: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, monospace;
    display: block;
    width: 100%;
    text-align: left;
    margin-left: auto;
    margin-right: auto;
}

.url-input , .options {
    font-size: 13px;
}

.url-input {
    display: flex;
}

.url-input label {
    flex: initial;
}

.url-input input {
    flex: auto;
    margin-left: 8px;
}

.url-input button {
    flex: initial;
    margin-left: 8px;
}

.options {
    margin-top: 5px;
}

.hidden {
    display: none;
}
