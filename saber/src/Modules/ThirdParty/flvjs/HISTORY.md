# flvjs修改

### V1.5.0.1
> 日期：2021年1月21日
##### 版本说明
* tutorial
  * 如何加载flvjs项目
     ![Image text](./images/新增子package.json.png)
  * 安装依赖
    * cd src/Modules/ThirdParty/flvjs
    * npm install
  * 开发和编译
    ![Image text](./images/开发和编译flvjs.png)
  * 成功启动开发版本
    ![Image text](./images/成功启动开发版本.png)
  * 调试界面
    ![Image text](./images/调试界面.png)
* fork from 【flvjs V1.5.0】
* g711 compatible
  * compatible G.711 A-law logarithmic PCM
  * compatible G711u µ-law logarithmic PCM
  * add g711.js
  * add pcm-player.js
  * update flv-demuxer.js
* egg-server compatible for vue project
  * update .eslintrc.json
    * add <code>"root": true</code>
* generate documentation
  * npm run genDoc
  * 文档首页 ./dist/Docs/Gen/index.html
  
### V1.5.0.2
> 日期：2021年2月7日
* aac compatible for no-AAC-sequence-header stream
  * 如果一个流没有发送aac头文件，那么将会用rtmp的默认协议来解析
    * 参考Adobe Flash Video File Format Specification Version 10.1 文档第71页
