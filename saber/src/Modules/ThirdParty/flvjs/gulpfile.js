/*
 * Copyright (C) 2016 Bilibili. All Rights Reserved.
 *
 * <AUTHOR> qian <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

'use strict';

const del = require('del');
const gulp = require('gulp');
const rename = require('gulp-rename');
const uglify = require('gulp-uglify');
const eslint = require('gulp-eslint');
const sourcemaps = require('gulp-sourcemaps');
const babelify = require('babelify');
const browserify = require('browserify');
const watchify = require('watchify');
const buffer = require('vinyl-buffer');
const source = require('vinyl-source-stream');
const child_process = require('child_process');
const os = require('os');
const rimraf = require('rimraf');

const browserSync = require('browser-sync').create();

function doWatchify() {
    let customOpts = {
        entries: 'src/index.js',
        standalone: 'flvjs',
        debug: true,
        transform: ['babelify', 'browserify-versionify'],
        plugin: ['browserify-derequire']
    };

    let opts = Object.assign({}, watchify.args, customOpts);
    let b = watchify(browserify(opts));

    b.on('update', function () {
        return doBundle(b).on('end', browserSync.reload.bind(browserSync));
    });
    b.on('log', console.log.bind(console));

    return b;
}

function doBundle(b) {
    return b.bundle()
        .on('error', console.error.bind(console))
        .pipe(source('flv.js'))
        .pipe(buffer())
        .pipe(sourcemaps.init({loadMaps: true}))
        .pipe(sourcemaps.write('./'))
        .pipe(gulp.dest('./dist/'));
}

function doLint(paths, exit) {
    return gulp.src(paths)
        .pipe(eslint({
            configFilePath: './eslintrc.json'
        }))
        .pipe(eslint.format())
        .pipe(exit ? eslint.failAfterError() : eslint.result(function () {}));
}

function streamToPromise(stream) {
    return new Promise(function (resolve, reject) {
        stream.on('finish', resolve);
        stream.on('end', resolve);
        stream.on('error', reject);
    });
}

gulp.task('default', ['clean', 'lint', 'build']);
gulp.task('release', ['clean', 'lint', 'build', 'minimize']);

gulp.task('watch', ['clean'], function () {
    let gulpWatcher = gulp.watch(['gulpfile.js', 'src/**/*.js']);

    gulpWatcher.on('change', function (e) {
        if (e.type === 'changed' || e.type === 'added') {
            return doLint(e.path, false);
        }
    });

    return doBundle(doWatchify()).on('end', function () {
        browserSync.init({
            server: {
                baseDir: './'
            },
            port: 8000,
            open: false
        });
        require('opn')('http://localhost:8000/demo/index.html');
    });
});

gulp.task('clean', function () {
    return del([
        'dist/*'
    ]);
});

gulp.task('lint', function () {
    return doLint(['gulpfile.js', 'src/**/*.js'], true);
});

gulp.task('build', ['clean', 'lint'], function () {
    let b = browserify({
        entries: 'src/index.js',
        standalone: 'flvjs',
        debug: true,
        transform: ['babelify', 'browserify-versionify'],
        plugin: ['browserify-derequire']
    });

    return doBundle(b);
});

gulp.task('minimize', ['lint', 'build'], function () {
    let options = {
        sourceMap: true,
        sourceMapIncludeSources: true,
        sourceMapRoot: './src/',
        mangle: true,
        compress: {
            sequences: true,
            dead_code: true,
            conditionals: true,
            booleans: true,
            unused: true,
            if_return: true,
            join_vars: true
        }
    };

    return gulp.src('dist/flv.js')
        .pipe(rename({extname: '.min.js'}))
        .pipe(sourcemaps.init({loadMaps: true}))
            .pipe(uglify(options))
            .on('error', console.error.bind(console))
        .pipe(sourcemaps.write('./'))
        .pipe(gulp.dest('./dist/'));
});

// 删除所有文档
gulp.task('cleanAllDocumentation', function (done) {
    let files = ['dist/Docs/Gen'];
    files.forEach(function (file) {
        rimraf.sync(file);
    });
    done();
});
// 生成所有文档
gulp.task('generateAllDocumentation', function (cb) {
    let envPathSeperator = os.platform() === 'win32' ? ';' : ':';
    return new Promise(function (resolve, reject) {
        child_process.exec('jsdoc --configure jsdocConfig.json', {
            env: {
                PATH: process.env.PATH + envPathSeperator + 'node_modules/.bin'
            }
        }, function (error, stdout, stderr) {
            if (error) {
                console.log(stderr);
                return reject(error);
            } else {
                let stream = gulp.src('images/**').pipe(gulp.dest('dist/Docs/Gen/images'));
                return streamToPromise(stream).then(resolve);
            }
        });
    });
});
// 更新所有文档
// gulp.task('updateAllDocumentation', gulp.series('cleanAllDocumentation', 'generateAllDocumentation'));//gulp4.x
gulp.task('updateAllDocumentation', ['cleanAllDocumentation', 'generateAllDocumentation']);//gulp3.x
