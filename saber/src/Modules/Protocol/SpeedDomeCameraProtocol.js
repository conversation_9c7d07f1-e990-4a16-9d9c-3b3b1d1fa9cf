import SharedProtocol from './SharedProtocol'
import $ from 'jquery'

'use strict';

/**
 * 快球控制协议
 * @exports SpeedDomeCameraProtocol
 * @alias SpeedDomeCameraProtocol（快球控制协议）
 */
var SpeedDomeCameraProtocol = {};

/**
 * 控制云台转动
 * @param {Object} requiredData
 * @param {String} requiredData.channelID 通道20位编号
 * @param {String} requiredData.command 控制命令 { left, right, up, down, upleft, upright, downleft,downright,zoomin, zoomout, stop }
 * @param {Number} [requiredData.speed] 控制速度 0~255，默认为129,可以不填
 */
SpeedDomeCameraProtocol.panTileZoomControl = function (requiredData) {
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/ptz/control',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var panTileZoomControlPromise = SpeedDomeCameraProtocol.panTileZoomControl(requiredData);
            panTileZoomControlPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 控制云台焦点和光圈
 * @param {Object} requiredData
 * @param {String} requiredData.channelID 通道20位编号
 * @param {String} requiredData.command { near拉近 far拉远 light变亮  darken变暗 stop停止}发送一次变化以后需要stop才能停止。
 * @param {Number} [requiredData.speed] 控制速度 0~255，默认为129,可以不填
 */
SpeedDomeCameraProtocol.nearFarLightDarkenControl = function (requiredData) {
  return window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/fi/control',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var nearFarLightDarkenPromise = SpeedDomeCameraProtocol.nearFarLightDarkenControl(requiredData);
            nearFarLightDarkenPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 控制云台拉框放大
 * @param {Object} requiredData
 * @param {String} requiredData.channelID 通道20位编号
 * @param {Number} requiredData.length 播放窗口长度像素值
 * @param {Number} requiredData.width 播放窗口宽度像素值
 * @param {Number} requiredData.midpointx 拉框中心的横坐标像素值
 * @param {Number} requiredData.midpointy 拉框中心的纵坐标像素值
 * @param {Number} requiredData.lengthx 拉框x方向像素值
 * @param {Number} requiredData.lengthy 拉框y方向像素值
 */
SpeedDomeCameraProtocol.dragZoomoutControl = function (requiredData) {
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/dragzoomout/control',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var dragZoomoutControlPromise = SpeedDomeCameraProtocol.dragZoomoutControl(requiredData);
            dragZoomoutControlPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 控制云台拉框缩小
 * @param {Object} requiredData
 * @param {String} requiredData.channelID 通道20位编号
 * @param {Number} requiredData.length 播放窗口长度像素值
 * @param {Number} requiredData.width 播放窗口宽度像素值
 * @param {Number} requiredData.midpointx 拉框中心的横坐标像素值
 * @param {Number} requiredData.midpointy 拉框中心的纵坐标像素值
 * @param {Number} requiredData.lengthx 拉框x方向像素值
 * @param {Number} requiredData.lengthy 拉框y方向像素值
 */
SpeedDomeCameraProtocol.dragZoominControl = function (requiredData) {
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/dragzoomin/control',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        // console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var dragZoominControlPromise = SpeedDomeCameraProtocol.dragZoominControl(requiredData);
            dragZoominControlPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });
  });

};

/**
 * 连接状态查询
 * @description 连接状态查询（查看当前是否已登录已连接状态，如果没有登录需先登录）
 * @param {Object} requiredData
 * @param {String} requiredData.manufacturer 厂家如Dahua
 */
SpeedDomeCameraProtocol.checkSdkConnectionState = function (requiredData) {
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/sdk/connection/state',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var checkSdkConnectionStatePromise = SpeedDomeCameraProtocol.checkSdkConnectionState(requiredData);
            checkSdkConnectionStatePromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        } else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 登录SDK
 * @description 登录SDK（同时只允许登录一个设备，如需登录其他设备需要先登出）
 * @param {Object} requiredData
 * @param {String} requiredData.manufacturer 厂家。如Dahua
 * @param {String} requiredData.ip 设备ip。如*************
 * @param {String} requiredData.user 用户名。如admin
 * @param {String} requiredData.password 密码。如admin123
 */
SpeedDomeCameraProtocol.sdkLogin = function (requiredData) {
  console.log('使用下列参数登录SDK：', requiredData);
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/sdk/login',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log('登录结果为：', json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var sdkLoginPromise = SpeedDomeCameraProtocol.sdkLogin(requiredData);
            sdkLoginPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 登出SDK
 * @param {Object} requiredData
 * @param {String} requiredData.manufacturer 厂家。如Dahua
 */
SpeedDomeCameraProtocol.sdkLogout = function (requiredData) {
  // console.log('登出SDK：');
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/sdk/logout',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var sdkLogoutPromise = SpeedDomeCameraProtocol.sdkLogout(requiredData);
            sdkLogoutPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });
};

/**
 * 精确控制球机转动
 * @param {Object} requiredData
 * @param {String} requiredData.manufacturer 厂家。如Dahua
 * @param {String} requiredData.channel 通道号。如果直接连接的摄像头，通道号就是0；如果连接的是NVR，通道号就是该摄像头在NVR里的序号。现阶段直接连接的摄像头，如果后续需要连接NVR，则数据库里需要该字段
 * @param {String} requiredData.pan 水平方向角度。水平方向角度（0~3600）
 * @param {String} requiredData.tilt 垂直方向角度。垂直方向角度（0~900）
 * @param {String} requiredData.zoom 倍率。变倍（0~4）
 */
SpeedDomeCameraProtocol.sdkPrecisePanTileZoomControl = function (requiredData) {
  console.log('精确控制球机转动:');
  console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/sdk/exact/goto',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var sdkPrecisePanTileZoomControlPromise = SpeedDomeCameraProtocol.sdkPrecisePanTileZoomControl(requiredData);
            sdkPrecisePanTileZoomControlPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 云台状态查询
 * @param {Object} requiredData
 * @param {String} requiredData.manufacturer 厂家。如Dahua
 * @param {String} requiredData.channel 通道号。如果直接连接的摄像头，通道号就是0；如果连接的是NVR，通道号就是该摄像头在NVR里的序号。现阶段直接连接的摄像头，如果后续需要连接NVR，则数据库里需要该字段
 */
SpeedDomeCameraProtocol.sdkQueryLocation = function (requiredData) {
  // console.log('云台状态查询:');
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/sdk/query/location',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        // console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var sdkQueryLocationPromise = SpeedDomeCameraProtocol.sdkQueryLocation(requiredData);
            sdkQueryLocationPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 自动移动到指定经纬度
 * @param requiredData
 * @param {Number} requiredData.lng 经度
 * @param {Number} requiredData.lat 纬度
 *
 * @description 不用指定摄像头，由服务自动计算
 */
SpeedDomeCameraProtocol.autoFocusOnWorldCoordinate = function (requiredData) {
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/video-analysis-service/Mall/CameraLinkage',
      type: 'POST',
      async: true,
      dataType: 'JSON',
      contentType: 'application/json',
      data: JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var autoFocusOnWorldCoordinatePromise = SpeedDomeCameraProtocol.autoFocusOnWorldCoordinate(requiredData);
            autoFocusOnWorldCoordinatePromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });
};

/**
 * 拉框球机放大缩小
 * @description 鼠标从左上到右下拉框，视频放大；鼠标从右下到左上拉框，视频缩小。sdk非GB28181
 * @param {Object} requiredData
 * @param {String} requiredData.manufacturer 厂家。如Haikang
 * @param {String} requiredData.channel 20位id
 * @param {String} requiredData.width 宽度
 * @param {String} requiredData.height 高度
 * @param {String} requiredData.beginX 开始位置X
 * @param {String} requiredData.beginY 开始位置Y
 * @param {String} requiredData.endX 结束位置X
 * @param {String} requiredData.endY 结束位置Y
 */
SpeedDomeCameraProtocol.zoomInZoomOutWithSdk = function (requiredData) {
  console.log('拉框球机放大缩小：', requiredData);
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v05/sdk/fast/goto',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var zoomInZoomOutWithSdkPromise = SpeedDomeCameraProtocol.zoomInZoomOutWithSdk(requiredData);
            zoomInZoomOutWithSdkPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

export default SpeedDomeCameraProtocol;
