import defaultValue from '../Core/defaultValue'
import defined from '../Core/defined'
import SharedProtocol from './SharedProtocol'
import ResourceUtil from '../Util/ResourceUtil'
import $ from 'jquery'

'use strict';

/**
 * 流媒体协议
 * @exports MediaServiceProtocol
 * @alias MediaServiceProtocol
 */
var MediaServiceProtocol = {};

/**
 * 获取获取所有可用的摄像头的id，name,从缓存中读token
 * @description 获取ChannelID, DeviceID, ChannelName, 其中ChannelID是唯一的，ChannelName可能不唯一
 *
 * @param requiredData 无需传入，可为{}
 * @returns {window.Promise} 数据包含
 */
MediaServiceProtocol.getAllChannelList = function (requiredData) {
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/channel/all',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        if(json.code === 0){
          var data = json.data;
          if(data.ChannelCount > 0){
            resolve(data.OnlineList);
          }else{
            resolve([]);
          }
        }else if (json.code === -2){
          // reject(json.msg);
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var refreshPromise = SharedProtocol.refreshToken();
            refreshPromise.then(function () {
              var getAllChannelListPromise = MediaServiceProtocol.getAllChannelList(requiredData);
              getAllChannelListPromise.then(function (json) {
                resolve(json);
              }).catch(function (msg) {
                reject(msg);
              });
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }

      }
    });

  });

};

/**
 * 开始点播
 *
 * @description
 *  RTMP 为VideoPlayerRTMP播放器专用，
 *  RTSP 为VideoPlayerJanus播放器播放RTSP over udp（ip camera或GB28181实时流）
 *  RTP 为VideoPlayerJanus播放器播放RTSP over tcp时的折中方案，此时，流媒体会将RTP推送到指定的端口号上
 * @param {Object} requiredData
 * @param {String} [streamType=RTMP] RTMP/RTSP/RTP
 * @return {Promise}
 */
MediaServiceProtocol.startChannel = function (requiredData, streamType) {
  // console.log('单路点播：');
  // console.log(requiredData);
  streamType = defaultValue(streamType, 'RTMP');
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/stream/start',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      data: requiredData,
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        // console.log('单路点播回调：');
        // console.log(json);
        if(json.code === 0){
          let videoUrl;
          console.log(json.data.StreamList)
          console.log(streamType)
          if(json.data.StreamList.length > 0){
            if(streamType === 'RTMP'){
              videoUrl = json.data.StreamList[0].RTMP;
            }else if(streamType === 'RTSP'){
              videoUrl = json.data.StreamList[0].RTSP;
            }else if(streamType === 'RTP'){
              videoUrl = json.data.StreamList[0].RTP;
            }else if(streamType === 'FLV'){
              videoUrl = json.data.StreamList[0].FLV;
              resolve(videoUrl);
            }else if(streamType === 'WS_FLV'){
              videoUrl = json.data.StreamList[0].WS_FLV;
              resolve(videoUrl);
            }
            videoUrl = ResourceUtil.urlTranslate(videoUrl);
            resolve(videoUrl);
          }else{
            reject('点播失败,没有可用的流!');
          }
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var startChannelPromise = MediaServiceProtocol.startChannel(requiredData);
            startChannelPromise.then(function (videoUrl) {
              resolve(videoUrl);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 开始点播（janus播放的假函数）
 * @deprecated
 * @see MediaServiceProtocol#startChannel
 * @description
 *  这是一个替代startChannel的函数，使用janus的时候无需点播gb28181平台，为了代码一致性增加了这个假的接口函数
 * @return {Promise}
 */
MediaServiceProtocol.startChannelJanusFake = function () {
  return new window.Promise((resolve, reject)=>{
    resolve('janusFake');
});
};

/**
 * 停止直播地址，避免占用过多的流媒体资源(以后有流媒体自己维护)
 * @param requiredData
 * @param {String} requiredData.channelID
 * @deprecated
 */
MediaServiceProtocol.stopLiveChannel = function (requiredData) {
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/stream/stop',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        // console.log('停止直播回调', json);
        if(json.code === 0){
          resolve('停止成功');
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var stopLiveChannelPromise = MediaServiceProtocol.stopLiveChannel(requiredData);
            stopLiveChannelPromise.then(function (json) {
              resolve('停止成功');
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 同时点播多路视频
 * @param {Object} requiredData
 * @param {Array.<String>} requiredData.channelID 20位ID
 */
MediaServiceProtocol.startMultipleChannel = function (requiredData) {
  console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    if(!defined(requiredData) || !defined(requiredData.channelID) || requiredData.channelID.length === 0){
      reject('no valid channelIDs');
      return;
    }
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/stream/multi',
      type: 'POST',
      async: true,
      dataType: 'JSON',
      contentType: 'application/json',
      data: JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          if(json.data !== null){
            resolve(json.data.StreamList);
          }else{
            resolve([]);
          }
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var startMultipleChannelPromise = MediaServiceProtocol.startMultipleChannel(requiredData);
            startMultipleChannelPromise.then(function (StreamList) {
              resolve(StreamList);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 开始回放单路视频
 *
 * @description 开始时间必须小于结束时间，注意区分'YYYY-MM-DDTHH:mm:ss'与'YYYY-MM-DDThh:mm:ss'，前者为24小时制，后者为12小时制
 * @param {Object} requiredData
 * @param {String} requiredData.channelID
 * @param {String} requiredData.starttime 形如"'YYYY-MM-DDTHH:mm:ss'"
 * @param {String} requiredData.endtime 形如"'YYYY-MM-DDTHH:mm:ss'"
 */
MediaServiceProtocol.startPlayback = function (requiredData) {
  // console.log('开启单路回放：');
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/playback/start',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        // console.log(json);
        if(json.code === 0){
          var rtmpUrl = json.data;
          resolve(rtmpUrl);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var startPlaybackPromise = MediaServiceProtocol.startPlayback(requiredData);
            startPlaybackPromise.then(function (rtmpUrl) {
              resolve(rtmpUrl);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 停止正在回放的视频流，避免占用过多的流媒体资源
 * @param requiredData
 * @param {String} requiredData.streamID
 */
MediaServiceProtocol.stopPlaybackStream = function (requiredData) {
  console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/playback/stop',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          resolve('停止成功');
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var stopPlaybackStreamPromise = MediaServiceProtocol.stopPlaybackStream(requiredData);
            stopPlaybackStreamPromise.then(function (json) {
              resolve('停止成功');
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 分段视频回放
 * @param requiredData
 */
MediaServiceProtocol.startPlaybackSplit = function (requiredData) {
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/playback/split',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var startPlaybackSplitPromise = MediaServiceProtocol.startPlaybackSplit(requiredData);
            startPlaybackSplitPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });
};

/**
 * 控制回放视频流
 * @param requiredData
 * @param {String} requiredData.command 控制命令"play","pause","scale"
 * @param {String} requiredData.scale 回放速度，0.5,2
 * @param {String|Number} [requiredData.range] 播放跳转 视频时间戳
 */
MediaServiceProtocol.controlPlaybackStream = function (requiredData) {
  // console.log('控制回放视频流', requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/playback/control',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var controlPlaybackStreamPromise = MediaServiceProtocol.controlPlaybackStream(requiredData);
            controlPlaybackStreamPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 录像下载
 * @param requiredData
 * @param {String} requiredData.channelID 设备序列号，20位国际编码
 * @param {String} requiredData.starttime 开始时间，形如2018-01-24T10:00:00
 * @param {String} requiredData.endtime 结束时间，形如2018-01-24T11:00:00
 * @param {Number} requiredData.download_Speed 下载倍速,默认值: 4
 */
MediaServiceProtocol.downloadPlayback = function (requiredData) {
  console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v02/playback/download',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var downloadPlaybackPromise = MediaServiceProtocol.downloadPlayback(requiredData);
            downloadPlaybackPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 获取录像回放，视频下载的信息
 * @param requiredData
 * @param {String} requiredData.streamid 回放/下载ID
 */
MediaServiceProtocol.getDownloadPlaybackInformation = function (requiredData) {
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v04/playback/download/information',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        // console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var getDownloadPlaybackInformationPromise = MediaServiceProtocol.getDownloadPlaybackInformation(requiredData);
            getDownloadPlaybackInformationPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

/**
 * 测试摄像头是否能用
 * @param requiredData 20位唯一识别ID
 */
MediaServiceProtocol.testCam = function (requiredData) {
  console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v04/camera/check/number?channelID=' + requiredData,
      type: 'GET',
      async: true,
      dataType: 'JSON',
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        console.log(json);
        if(json.code === 0){
          resolve(json);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var testCamPromise = MediaServiceProtocol.testCam(requiredData);
            testCamPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json);
        }
      }
    });

  });

};

/**
 * 获取视频缩略图
 * @param requiredData
 * @param {String} requiredData.channelID
 */
MediaServiceProtocol.getVideoThumbnailUrl = function (requiredData) {
  // console.log(requiredData);
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getBackendIp() + '/restAPI/media-service/api/media/v1/channel/capture',
      type: 'GET',
      async: true,
      dataType: 'JSON',
      // contentType: 'application/json',
      data: requiredData, //JSON.stringify(requiredData),
      beforeSend: function(request) {
        request.setRequestHeader('access-token', SharedProtocol.getToken());
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(XMLHttpRequest);
      },
      success: function (json) {
        // console.log(json);
        if(json.code === 0){
          resolve(json.data);
        }else if(json.code === -2){
          var refreshPromise = SharedProtocol.refreshToken();
          refreshPromise.then(function () {
            var getVideoThumbnailUrlPromise = MediaServiceProtocol.getVideoThumbnailUrl(requiredData);
            getVideoThumbnailUrlPromise.then(function (json) {
              resolve(json);
            }).catch(function (msg) {
              reject(msg);
            });
          }).catch(function (msg) {
            reject(msg);
          });
        }else{
          reject(json.msg);
        }
      }
    });

  });

};

export default MediaServiceProtocol;
