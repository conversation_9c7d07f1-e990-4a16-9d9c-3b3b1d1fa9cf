import defaultValue from '../Core/defaultValue'
import defined from '../Core/defined'
import SharedProtocol from './SharedProtocol'
import $ from 'jquery'

'use strict';

/**
 * 文件服务器协议
 * @exports FileProtocol
 * @alias FileProtocol（文件服务器协议）
 */
var FileProtocol = {};

/**
 * 上传jpg图片流
 * @description 无需token
 *
 * @param {String} stream base64字符串
 * @returns {window.Promise}
 * @description 异步将bas64更换为文件服务器的一个url
 *
 */
FileProtocol.UploadJpgStream = function (stream) {
  var type = 'image/jpeg'; // 定义图片类型（canvas转的图片一般都是png，也可以指定其他类型）
  var conversions = base64ToBlob(stream, type); // 调用base64转image图片方法生成Blob
  var imgData = new FormData(); // 新建一个formData对象
  imgData.append('file', conversions, 'targetImg.jpg'); // 这里就是base64转化后的图片文件，append到formData里就可以上传了。
  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getFileServiceIp() + '/group1/upload',
      type: 'POST',
      async: true,
      contentType: false,
      data: imgData,
      processData: false,
      cache: false,
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(errorThrown);
      },
      success: function (json) {
        // console.log(json);
        if(json.code === 0){
          resolve(json.data);
        } else{
          reject(json.msg);
        }

      }
    });

  });

};

/**
 * 上传png图片流
 * @description 无需token
 *
 * @param {String} stream base64字符串
 * @returns {Promise.<String>}
 * @description 异步将bas64更换为文件服务器的一个url
 *
 */
FileProtocol.UploadPngStream = function (stream) {
  var type = 'image/png'; // 定义图片类型（canvas转的图片一般都是png，也可以指定其他类型）
  var conversions = base64ToBlob(stream, type); // 调用base64转图片方法生成Blob
  var imgData = new FormData(); // 新建一个formData对象
  imgData.append('file', conversions, 'targetImg.png'); // 这里就是base64转化后的图片文件，append到formData里就可以上传了。

  return new window.Promise((resolve, reject)=>{
    $.ajax({
      url: SharedProtocol.getFileServiceIp() + '/fdfs/upload/image/sample',
      type: 'POST',
      async: true,
      contentType: false,
      data: imgData,
      processData: false,
      cache: false,
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        reject(errorThrown);
      },
      success: function (json) {
        // console.log(json);
        if(json.code === 0){
          resolve(json.data);
        } else{
          reject(json.msg);
        }

      }
    });

  });

};

/**
 * base64转为Blob
 * @description 这里的base64是指可以塞到img中src的字符串
 * @param {String} urlData 'data:image/jpeg;base64,'+ base64编码的jpg字节
 * @param {String} type
 * @example var conversions = base64ToBlob('data:image/jpeg;base64,' + '.........', 'image/jpeg');
 */
function base64ToBlob(urlData, type) {
  var arr = urlData.split(',');
  var mime = arr[0].match(/:(.*?);/)[1] || type;
  // 去掉url的头，并转化为byte
  var bytes = window.atob(arr[1]);
  // 处理异常,将ascii码小于0的转换为大于0
  var ab = new ArrayBuffer(bytes.length);
  // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
  var ia = new Uint8Array(ab);
  for (var i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], {type: mime});
}

export default FileProtocol;
