'use strict';

import defined from '../Core/defined'
import configUri from '../Global/configUri'
import axios from 'axios';

/**
 * 获取后台的IP
 * @return {undefined|String}
 */
function getBackendIp() {
  return configUri.globalBackend;
}

/**
 * 获取文件服务器IP
 * @return {undefined|String}
 */
function getFileServiceIp() {
  return configUri.globalFileService;
}

/**
 * 获取后台RabbitMQ的地址
 * @return {undefined|String}
 */
function getBackendRabbitMQ() {
  return configUri.globalRabbitMQ
}

/**
 * 获取token
 * @return {undefined|String}
 */
function getToken() {
  let userConfig = getUserConfig();
  return userConfig.token;
}

/**
 * 将用户信息入缓存
 * @param {Object} userConfig
 * @param {String} userConfig.userId
 * @param {Number|String} userConfig.organizationId
 * @param {String} userConfig.username
 * @param {String} userConfig.email
 * @param {String} userConfig.mobile
 * @param {String} userConfig.userAccount
 * @param {String} userConfig.userAvatar
 * @param {Boolean} userConfig.isAdmin
 * @param {String} userConfig.token
 * @param {String} [userConfig.version] 版本号，升级用
 * @param {String} [userConfig.indexDBVersion] indexDB版本号，升级用
 */
function saveUserConfig(userConfig) {
  userConfig.isValid = undefined; // 关键字不保存在缓存中避免不必要的bug
  localStorage.setItem('userConfig', JSON.stringify(userConfig))
}

/**
 * 清除用户登录信息缓存
 */
function cleanUserConfig() {
  localStorage.removeItem('userConfig');
}

/**
 * 获取用户信息
 * @description 如果有效会返回一个isValid
 * @return {Object}
 */
function getUserConfig() {
  let userConfig = {
    isValid: false
  };
  let jsonString = localStorage.getItem('userConfig')
  if(jsonString){
    userConfig = JSON.parse(jsonString);
    userConfig.isValid = true
  }
  return userConfig;
}

/**
 * 获取后台网关中注册的微服务的前缀
 * @param value system/label/event/videoAnalysis
 * @return {*}
 */
function getMiniServiceName(value) {
  switch (value){
    case 'system':
      return '/restAPI/system-management';
    case 'label':
      return '/restAPI/label-manage-service';
    case 'event':
      return '/restAPI/event-manage-service';
    case 'videoAnalysis': // 视频集成框架
      return '/restAPI/video-analysis-service';
  }
  return value;
}

/**
 * 公用协议token
 * @exports SharedProtocol
 * @alias SharedProtocol（公用协议token）
 */
const SharedProtocol = {
  getMiniServiceName: getMiniServiceName,
  getBackendIp: getBackendIp,
  getFileServiceIp: getFileServiceIp,
};

export default SharedProtocol;
