import ResourceUtil from "../Util/ResourceUtil";

let globalBackend = window.globalBackend; // 后台IP地址
let globalFileService = window.globalFileService; // 文件服务器IP地址

// 外网访问需要ip映射
if(window.globalDefaultHostName){
  if(window.location.hostname !== window.globalDefaultHostName){
    globalBackend = ResourceUtil.urlTranslate(globalBackend);
    globalFileService = ResourceUtil.urlTranslate(globalFileService);
  }
}

export default {
  globalBackend: globalBackend,
  globalFileService: globalFileService,
}
