.lux-VideoPlayerToolBarForPlayback-wrapper {
    /*display: inline-block;*/
    /*height: 100%;*/
    /*width: 130px;*/
    background-color: unset;
    /*过度动画动画*/
    --lux-VideoPlayerToolBarForPlayback-transitionDefault: all linear 500ms;

    --lux-VideoPlayerToolBarForPlayback-rectButtonWidth: 20px;
    /*pointer-events: all;*/
}

/*九宫格控制*/
.lux-VideoPlayerToolBarForPlayback-baseControlGroup{
    position: absolute;
    left: 2px;
    right: 2px;
    bottom: 2px;
    height: var(--lux-VideoPlayerToolBarForPlayback-rectButtonWidth);
    background-color: rgba(0, 0, 0, 0.647058823529412);
    border: none;
    pointer-events: all;
    transition: var(--lux-VideoPlayerToolBarForPlayback-transitionDefault);
    opacity: 0;
}
.lux-VideoPlayerToolBarForPlayback-baseControlGroup:hover{
    opacity: 1;
}

.lux-VideoPlayerToolBarForPlayback-baseControlGroup button{
    width: var(--lux-VideoPlayerToolBarForPlayback-rectButtonWidth);
    height: var(--lux-VideoPlayerToolBarForPlayback-rectButtonWidth);
    border: 0;
    pointer-events: all;
    background-color: unset;
    background-size: 80% 80%;
    background-position: center;
    background-repeat: no-repeat;
}
.lux-VideoPlayerToolBarForPlayback-baseControlGroup button:hover{
    border-radius: 10px;
    border: 1px solid #444;
    box-shadow: 0 0 8px #fff;
    border-color: #ea4;
    outline: none;
}

.lux-VideoPlayerToolBarForPlayback-baseControlGroup-textDom{
    display: inline-block;
    height: var(--lux-VideoPlayerToolBarForPlayback-rectButtonWidth);
    border: 0;
    pointer-events: none;
    background-color: unset;
    color: #ffffff;
    float: right;
    text-align: center;
    vertical-align: middle;
    line-height: var(--lux-VideoPlayerToolBarForPlayback-rectButtonWidth);
    margin: 0;
    padding: 1px 8px;
}
