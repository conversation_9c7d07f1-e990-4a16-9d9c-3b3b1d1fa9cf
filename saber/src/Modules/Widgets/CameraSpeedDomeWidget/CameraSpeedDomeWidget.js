import defined from '../../Core/defined'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import DeveloperError from '../../Core/DeveloperError'
import knockout from '../../ThirdParty/knockout'
import getElement from '../../Widgets/getElement'
import CameraSpeedDomeWidgetViewModel from './CameraSpeedDomeWidgetViewModel'

'use strict';

/**
 * 快球控制
 *
 * @alias CameraSpeedDomeWidget
 *
 * @param {Element|String} container 控件容器
 * @param options
 * @param {VideoPlayerRTMP|VideoPlayerJanus} options.videoPlayer
 *
 * @exception {DeveloperError} container is required.
 *
 * @constructor
 */
function CameraSpeedDomeWidget(container, options) {
  //>>includeStart('debug', pragmas.debug);
  if (!defined(container)) {
    throw new DeveloperError('container is required.');
  }
  //>>includeEnd('debug');

  container = getElement(container);

  var viewModel = new CameraSpeedDomeWidgetViewModel(options);

  /**
   * 包裹层
   * @type {HTMLDivElement}
   */
  var wrapper = document.createElement('div');
  wrapper.className = 'lux-CameraSpeedDomeWidget-wrapper';
  wrapper.setAttribute('data-bind', 'visible: show && biggerThanSize');
  container.appendChild(wrapper);

  /**
   * 基本控制组，九宫格
   * @type {HTMLDivElement}
   */
  var baseControlGroup = document.createElement('div');
  baseControlGroup.className = 'lux-CameraSpeedDomeWidget-baseControlGroup';
  baseControlGroup.setAttribute('data-bind', 'visible: baseControlGroupVisible');
  baseControlGroup.innerHTML =
    // '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-leftTopItem"><button data-bind="click: panTiltFunction.bind($data, \'upleft\')">左上</button></div>' +
    '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-middleTopItem"><button data-bind="click: panTiltFunction.bind($data, \'up\')">上</button></div>' +
    // '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-rightTopItem"><button data-bind="click: panTiltFunction.bind($data, \'upright\')">右上</button></div>' +
    '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-leftMiddleItem"><button data-bind="click: panTiltFunction.bind($data, \'left\')">左</button></div>' +
    '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-middleMiddleItem"><button data-bind="click: panTiltFunction.bind($data, \'stop\')">停止</button></div>' +
    '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-rightMiddleItem"><button data-bind="click: panTiltFunction.bind($data, \'right\')">右</button></div>' +
    // '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-leftBottomItem"><button data-bind="click: panTiltFunction.bind($data, \'downleft\')">左下</button></div>' +
    '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-middleBottomItem"><button data-bind="click: panTiltFunction.bind($data, \'down\')">下</button></div>' +
    // '<div class="lux-CameraSpeedDomeWidget-baseControlGroup-rightBottomItem"><button data-bind="click: panTiltFunction.bind($data, \'downright\')">右下</button></div>' +
    ''
  ;
  wrapper.appendChild(baseControlGroup);

  knockout.applyBindings(viewModel, wrapper);

  this._viewModel = viewModel;
  this._wrapper = wrapper;
  this._container = container;

}

defineProperties(CameraSpeedDomeWidget.prototype, {
  /**
   * Gets the parent container.
   * @memberof CameraSpeedDomeWidget.prototype
   *
   * @type {Element}
   */
  container : {
    get : function() {
      return this._container;
    }
  },

  /**
   * Gets the view model.
   * @memberof CameraSpeedDomeWidget.prototype
   *
   * @type {CameraSpeedDomeWidgetViewModel}
   */
  viewModel : {
    get : function() {
      return this._viewModel;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof CameraSpeedDomeWidget.prototype
   *
   * @type {Element}
   */
  wrapper : {
    get : function() {
      return this._wrapper;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof CameraSpeedDomeWidget.prototype
   *
   * @type {Element}
   */
  show : {
    get : function() {
      return this._viewModel.show;
    },
    set : function(value) {
      this._viewModel.show = value;
    }
  }

});

/**
 * 订阅球机转动事件
 * @param {Function} callback
 */
CameraSpeedDomeWidget.prototype.subscribeAllControlEvent = function(callback) {
  this._viewModel.subscribeAllControlEvent(callback);
};

/**
 *  禁止zoom球机控制鼠标事件
 */
CameraSpeedDomeWidget.prototype.forbidMouseZoomEvent = function () {
  this._viewModel.forbidMouseZoomEvent();
};

/**
 *  取消禁止zoom球机控制鼠标事件
 */
CameraSpeedDomeWidget.prototype.cancelForbidMouseZoomEvent = function () {
  this._viewModel.cancelForbidMouseZoomEvent();
};

/**
 * @returns {Boolean} true if the object has been destroyed, false otherwise.
 */
CameraSpeedDomeWidget.prototype.isDestroyed = function() {
  return false;
};

/**
 * Destroys the widget.  Should be called if permanently
 * removing the widget from layout.
 */
CameraSpeedDomeWidget.prototype.destroy = function() {
  knockout.cleanNode(this._wrapper);
  this._container.removeChild(this._wrapper);
  return destroyObject(this);
};

export default CameraSpeedDomeWidget;
