'use strict';
import defined from '../../Core/defined';
import defineProperties from '../../Core/defineProperties';
import DeveloperError from '../../Core/DeveloperError';
import getElement from '../../Widgets/getElement';
import StringUtil from '../../Util/StringUtil';

/**
 * VideoPlayerIndexWidget
 * @alias VideoPlayerIndexWidget
 *
 * @param {Object} options
 * @param {VideoPlayerFLV} options.videoPlayer
 *
 * @exception {DeveloperError} videoPlayerGallery is required.
 *
 * @constructor
 */
function VideoPlayerIndexWidget (options) {
  if (!defined(options.videoPlayer)) {
    throw new DeveloperError('videoPlayer is required.');
  }

  this._videoPlayer = options.videoPlayer;
  this._init();
}

defineProperties(VideoPlayerIndexWidget.prototype, {
  /**
   * Gets the wrapper.
   * @memberof VideoPlayerToolBar.prototype
   *
   * @type {Element}
   */
  show: {
    get: function () {
      return this._nameDom.style.display === 'block';
    },
    set: function (value) {
      if (value) {
        this._nameDom.style.display = 'block';
      } else {
        this._nameDom.style.display = 'none';
      }
    }
  }
});

/**
 * 初始化
 * @private
 */
VideoPlayerIndexWidget.prototype._init = function () {
  let indexDom = document.createElement('div');
  indexDom.innerText = this._videoPlayer.getIndexOfParentGallery() + 1;
  indexDom.className = 'xh-VideoPlayerIndexWidget-indexDom';
  getElement(this._videoPlayer._container).appendChild(indexDom);
  this._nameDom = indexDom;
  this.show = false;
};
export default VideoPlayerIndexWidget;
