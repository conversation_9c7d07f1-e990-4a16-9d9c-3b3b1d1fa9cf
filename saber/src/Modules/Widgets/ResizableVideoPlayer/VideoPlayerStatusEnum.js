'use strict';

/**
 * 播放器状态(枚举类)
 * @alias VideoPlayerStatusEnum（播放器状态枚举类）
 * @exports VideoPlayerStatusEnum
 */
var VideoPlayerStatusEnum = {
  /**
   * 直播中
   *
   * @type {Number}
   * @constant
   */
  LIVE: 0,
  /**
   * 直播加载中
   *
   * @type {Number}
   * @constant
   */
  LIVE_LOADING: 1,
  /**
   * 直播重连中
   *
   * @type {Number}
   * @constant
   */
  LIVE_RESUME: 2,
  /**
   * 直播切换视频
   *
   * @type {Number}
   * @constant
   */
  LIVE_SWITCH: 3,
  /**
   * 直播点播失败
   *
   * @type {Number}
   * @constant
   */
  LIVE_FAIL: 4,
  /**
   * 直播结束
   *
   * @type {Number}
   * @constant
   */
  LIVE_END: 5,
  /**
   * 回放中
   *
   * @type {Number}
   * @constant
   */
  PLAY_BACK: 10,
  /**
   * 回放流加载中
   *
   * @type {Number}
   * @constant
   */
  PLAY_BACK_LOADING: 11,
  /**
   * 回放流重连中
   *
   * @type {Number}
   * @constant
   */
  PLAY_BACK_RESUME: 12,
  /**
   * 回放流切换视频
   *
   * @type {Number}
   * @constant
   */
  PLAY_BACK_SWITCH: 13,
  /**
   * 回放流点播失败
   *
   * @type {Number}
   * @constant
   */
  PLAY_BACK_FAIL: 14,
  /**
   * 回放流结束
   *
   * @type {Number}
   * @constant
   */
  PLAY_BACK_END: 15,
  /**
   * 初始化时请求用户拖拽
   *
   * @type {Number}
   * @constant
   */
  INIT_PLEASE_DRAG: 100,
  /**
   * 该浏览器不支持WebRTC
   *
   * @type {Number}
   * @constant
   */
  NO_WEBRTC_SUPPORT: 200,
  /**
   * 没有连接上WebRTC的服务器
   *
   * @type {Number}
   * @constant
   */
  NO_CONNECT_WEBRTC_SERVER: 201,
  /**
   * 音频监听
   *
   * @type {Number}
   * @constant
   */
  PLAY_AUDIO: 202
};

export default VideoPlayerStatusEnum;
