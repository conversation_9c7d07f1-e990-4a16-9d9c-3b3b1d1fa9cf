import defineProperties from '../../Core/defineProperties'
import knockout from '../../ThirdParty/knockout'
import createCommand from '../createCommand'

'use strict';

/**
 * RTMP播放器viewModel
 * @alias VideoPlayerRTMPViewModel（RTMP播放器viewModel）
 * @param options
 * @constructor
 */
function VideoPlayerRTMPViewModel(options) {
  /**
   * Gets or sets whether the full screen mode are currently shown.  This property is observable.
   * @type {Boolean}
   * @default false
   */
  this.showFullScreen = false;

  this.thumbnailUrl = './Images/alarmman.png';

  this.showThumbnail = false;

  this.showFlashDragOverlay = false;

  this.highlightFlashDragOverlay = false;

  this.highlightActiveOverlay = false;

  /**
   * 是否加载完毕
   * @type {boolean}
   * @default false
   */
  this.loaded = false;

  var that = this;

  this._toggleFullScreen = createCommand(function() {
    that.showFullScreen = !that.showFullScreen;
  });

  this._toggleFlashDragOverlay = createCommand(function() {
    that.showFlashDragOverlay = true;
  });

  knockout.track(this, ['showFullScreen', 'thumbnailUrl', 'showThumbnail', 'showFlashDragOverlay', 'highlightFlashDragOverlay', 'highlightActiveOverlay',
    'loaded'
  ]);

  // 播放器加载成功后叠加拖拽层和canvas
  knockout.getObservable(this, 'loaded').subscribe(function(loaded) {
    // console.log('flash加载完毕');
    if(loaded === true){
      that.showFlashDragOverlay = true;
    }
  });

}

defineProperties(VideoPlayerRTMPViewModel.prototype, {
  /**
   * 最大化视频切换.
   * @memberof VideoPlayerRTMPViewModel.prototype
   *
   * @type {Command}
   */
  toggleFullScreen : {
    get : function() {
      return this._toggleFullScreen;
    }
  },

  /**
   * 显示用于拖拽flash的叠加层.
   * @memberof VideoPlayerRTMPViewModel.prototype
   *
   * @type {Command}
   */
  toggleFlashDragOverlay : {
    get : function() {
      return this._toggleFlashDragOverlay;
    }
  }

});

export default VideoPlayerRTMPViewModel;
