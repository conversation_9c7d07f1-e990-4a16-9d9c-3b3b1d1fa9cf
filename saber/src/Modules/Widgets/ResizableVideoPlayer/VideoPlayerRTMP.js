import defaultValue from '../../Core/defaultValue'
import defined from '../../Core/defined'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import DeveloperError from '../../Core/DeveloperError'
import Queue from '../../Core/Queue'
import knockout from '../../ThirdParty/knockout'
import getElement from '../../Widgets/getElement'
import StringUtil from '../../Util/StringUtil'

import MediaServiceProtocol from '../../Protocol/MediaServiceProtocol'
import ShowTipsWidget from '../../Widgets/ShowTipsWidget/ShowTipsWidget'
import VideoPlayerToolBar from '../../Widgets/VideoPlayerToolBar/VideoPlayerToolBar'
import VideoPlayerStatusEnum from './VideoPlayerStatusEnum'
import VideoPlayerStatusEnumHelper from './VideoPlayerStatusEnumHelper'
import VideoPlayerRTMPViewModel from './VideoPlayerRTMPViewModel'

'use strict';

/**
 * VideoPlayerRTMP（RTMP播放器chplayer）
 * @alias VideoPlayerRTMP（RTMP播放器chplayer）
 *
 * @description RTMP播放器使用chplayer
 *
 * @see http://www.chplayer.com/
 * @param {Object} options
 * @param {VideoPlayerGallery} options.parentGallery 构造此播放器类VideoPlayerGallery
 * @param {String} [options.id] chplayer使用的id
 * @param {String} [options.url='rtmp'] 初始化的视频流地址
 * @param {String|Element} options.container 容器ID或容器元素
 *
 * @constructor
 */
function VideoPlayerRTMP(options) {
    //>>includeStart('debug', pragmas.debug);
    if(!defined(options)){
        options = {};
    }
    if (!defined(options.container)) {
        throw new DeveloperError('options.container is required.');
    }
    //>>includeEnd('debug');

    var that = this;
    var viewModel = new VideoPlayerRTMPViewModel(options);

    var container = getElement(options.container);

    var wrapper = document.createElement('div');
    wrapper.className = 'lux-resizable-video-player';
    wrapper.setAttribute('data-bind', 'css: { "lux-resizable-video-player-fullScreen" : showFullScreen}');
    wrapper.setAttribute('draggable', 'true');
    container.appendChild(wrapper);

    this._videoElementId = defaultValue(options.id, StringUtil.generateLongUid());// 会表现为父框架的<code>id+'0x0'
    var videoElement = document.createElement('div'); // 注意chplayer不能为video标签
    videoElement.className = 'lux-resizable-video-element';
    // videoElement.setAttribute('data-bind', 'attr: { poster: thumbnailUrl }');
    videoElement.setAttribute('id', this._videoElementId);
    wrapper.appendChild(videoElement);

    // 一个叠加层，用于绘制嫌疑人框
    var videoElementCanvas = document.createElement('canvas');
    videoElementCanvas.className = 'lux-video-player-flash-overlay';
    videoElementCanvas.setAttribute('draggable', 'false');
    videoElementCanvas.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
    wrapper.appendChild(videoElementCanvas);

    // 一个叠加层，用于高亮摄像头，不能操作
    var videoElementHighlightOverlay = document.createElement('div');
    videoElementHighlightOverlay.className = 'lux-video-player-active-overlay';
    videoElementHighlightOverlay.setAttribute('draggable', 'true');
    videoElementHighlightOverlay.setAttribute('data-bind', 'css: { "lux-video-player-active-overlay-visible" : showFlashDragOverlay, "lux-video-player-active-overlay-highlight" : highlightActiveOverlay}');
    wrapper.appendChild(videoElementHighlightOverlay);

    // 一个叠加层，用于操作div，比如拖拽，点击选中
    var videoElementOverlay = document.createElement('div');
    videoElementOverlay.className = 'lux-video-player-flash-overlay';
    videoElementOverlay.setAttribute('draggable', 'true');
    videoElementOverlay.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
    wrapper.appendChild(videoElementOverlay);

    // 一个叠加层，用于操作显示错误信息
    var videoElementErrorOverlay = document.createElement('div');
    videoElementErrorOverlay.className = 'lux-video-player-error-overlay';
    wrapper.appendChild(videoElementErrorOverlay);
    this._showTipsWidget = new ShowTipsWidget({
        container: videoElementErrorOverlay
    });

    // 加载中
    var videoElementLoadingIndicator = document.createElement('div');
    videoElementLoadingIndicator.className = 'loadingIndicator';
    videoElementLoadingIndicator.style.display = 'none';
    wrapper.appendChild(videoElementLoadingIndicator);

    // 球机的控制控件
    var videoSpeedDomeControl = document.createElement('div');
    videoSpeedDomeControl.className = 'lux-video-player-videoSpeedDomeControl';
    wrapper.appendChild(videoSpeedDomeControl);

    // 视频播放器工具栏
    var videoPlayerToolBarContainer = document.createElement('div');
    videoPlayerToolBarContainer.className = 'lux-video-player-videoPlayerToolBar';
    wrapper.appendChild(videoPlayerToolBarContainer);

    knockout.applyBindings(viewModel, wrapper);

    this._parentGallery = options.parentGallery;// 整个父框架对象的指针传进来
    this._viewModel = viewModel;
    this._videoElement = videoElement;
    this._wrapper = wrapper;
    this._container = container;
    this._drawCanvas = videoElementCanvas;
    this._drawCanvasCtx = this._drawCanvas.getContext('2d');
    this._videoElementOverlay = videoElementOverlay;
    this._videoElementLoadingIndicator = videoElementLoadingIndicator;
    this._videoUrl = undefined;

    // 初始化时可以默认为'rtmp'，指定为rtmp可以避免chplayer报错
    if(options.url === undefined || options.url === null || options.url === ''){
        /**
         * FIXME
         */
        this._videoUrl = 'rtmp';
        // this._videoUrl = 'm3u8';
    }else {
        this._videoUrl = options.url;
    }

    this._channelID = undefined;//options.channelID;
    this._playbackStreamCurrentID = undefined;// 正在使用的回放视频流ID
    this._playbackStreamIDQueue = new Queue();// 队列用于删除
    this._pauseOverlayVisible = false;// 暂停叠加层是否可见

    this._playerName = 'player' + this._videoElementId;
    this._loadedHandlerName = 'loadedHandler' + this._videoElementId;
    this._videoObject = {
        container: '#' + this._videoElementId, //容器的ID或className
        variable: this._playerName,
        volume: 0, //默认音量
        autoplay: true, //是否自动播放
        loadedHandler: this._loadedHandlerName, //当播放器加载后执行的函数
        loop: false, //是否循环播放
        live: true, //是否是直播
        seek: 0, //默认需要跳转的时间
        // drag: 'start', //在flashplayer情况下是否需要支持拖动进度
        // poster: this._viewModel.thumbnailUrl, //封面图片地址
        video: [this._videoUrl, '', '标清', 0],
        loaded: this._loadedHandlerName
        // html5m3u8:true
    };

    // chplayer必须在window域下使用
    window[this._playerName] = new window.chplayer(this._videoObject);
    this._chplayer = window[this._playerName];
    window[this._loadedHandlerName] = loadedHandler;
    this.loaded = false;// flash是否加载完毕
    this._isValidStream = false;// 当前播放的是否是有效视频流

    this._chplayer.time = NaN;//时间设为空
    this.videoPlayerStatus = VideoPlayerStatusEnum.INIT_PLEASE_DRAG;// 默认
    // this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;

    function loadedHandler() {
        that.loaded = true;
        that._viewModel.loaded = true;

        setInterval(function () {
            if(!that.checkStreamValid()){
                if(that._lastChplayerTime > 0){
                    if(VideoPlayerStatusEnumHelper.isLiveStatus(that.videoPlayerStatus)){
                        if(that.hasValidChannelId === true){
                            // that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_RESUME;
                            // that._currentErrorMsg = '实时视频重连中...';
                            setTimeout(function () {
                                that.startReconnect();
                            }, 1000);
                        }
                    }else if(VideoPlayerStatusEnumHelper.isPlaybackStatus(that.videoPlayerStatus)){
                        that.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK_END;
                    }
                    that.showError();
                }else{
                    // that.drawDanmuError('无效视频');
                    that.showError('无有效视频流');
                }

                that._lastChplayerTime = undefined;
                that._isValidStream = false;
            } else{
                that._lastChplayerTime = that._chplayer.time;
                that.hideError('无有效视频流');
                that._isValidStream = true;
            }
        }, 1000);

    }

    render();
    function render() {
        // console.log('resize');
        that.resizeVideo();
        setTimeout(function () {
            render();
        }, 3000);
        // requestAnimationFrame(render);
    }

    videoElementOverlay.addEventListener('dragstart', function(event) {
        // console.log('dragstart');
        // console.log(event);
        var data = {
            videoUrl: that.videoUrl,
            thumbnailUrl: that.thumbnailUrl,
            // cameraID: '',
            origin: 'VIDEO',
            videoElementId: that._videoElementId,
            channelID: that._channelID
        };
        event.dataTransfer.setData('Text', JSON.stringify(data));
    }, false);
    videoElementOverlay.addEventListener('drop', function(event) {
        event.preventDefault();
        console.log('drop');
        console.log(event);
        console.log(event.dataTransfer.getData('Text'));
        /**
         * FIXME 需要校验数据，避免从别的地方拖入的脏数据
         */
        var data;
        try{
            data = JSON.parse(event.dataTransfer.getData('Text'));
        }catch (e){
            console.log(e);
        }
        console.log(data);
        if(defined(data)){
            if(data.origin === 'BIM'){
                // 获取视频流地址并替换
                if(data.channelID !== undefined ){
                    that.reloadChannelID({channelID: data.channelID});
                }
            }else if (data.origin === 'VIDEO'){
                // 交换同一个gallery的两个视频
                if(defined(that._parentGallery)){
                    if(that.getParentGalleryId(data.videoElementId) === that.getParentGalleryId(that._videoElementId)) {
                        var position1 = that.getPositionOfParentGallery(data.videoElementId);
                        var position2 = that.getPositionOfParentGallery(that._videoElementId);
                        // 只有同一个视频集合图库才能交换播放器
                        if(defined(position1) && defined(position2)){
                            that._parentGallery.switchTwoPlayers(position1, position2);
                        }
                    }
                }else if(defined(data.channelID)){
                    that.reloadChannelID(data);
                } else{// 重载视频
                    that.reload(data);
                }
            }else if(data.origin === 'JSTREE'){
                // 获取jstree中的数据中的视频流地址并替换
                var obj = data.data;
                if(defined(obj) && StringUtil.leftContainRight(obj.type, 'camera')){
                    if(defined(obj.id)){
                        // console.log(obj);
                        if(StringUtil.leftContainRight(obj.type, 'cameraSpeedDome')){
                            that.reloadChannelID({
                                channelID: StringUtil.getLastSliceString(obj.id, 20),
                                callback: function () {
                                    that.videoPlayerToolBarVisible = true;
                                    that.cameraSpeedDomeWidgetVisible = true;
                                }
                            });
                        }else{
                            that.reloadChannelID({
                                channelID: StringUtil.getLastSliceString(obj.id, 20),
                                callback: function () {
                                    that.videoPlayerToolBarVisible = true;
                                }
                            });
                        }
                    }
                }
            }else if(data.origin === 'LIST'){//FIXME
              that.reloadChannelID({
                channelID: StringUtil.getLastSliceString(data.channelID, 20),
                callback: function () {
                  that.videoPlayerToolBarVisible = true;
                  setTimeout(function () {
                    that.clearStream();
                  }, 30 * 60 * 1000);
                }
              });
            }
        }
    }, false);
    videoElementOverlay.addEventListener('dragenter', function(event) {
        // console.log('dragenter')
        event.preventDefault();
    }, false);
    videoElementOverlay.addEventListener('dragover', function(event) {
        event.preventDefault();
        // console.log('dragover');
    }, false);
    videoElementOverlay.addEventListener('dragleave', function(event){
        event.preventDefault();
        // console.log('dragleave');
    });
    videoElementOverlay.addEventListener('dragend', function(event){
        event.preventDefault();
        // console.log('dragend');
    });

    /**
     *  设置隐藏属性和改变可见属性的事件的名称，即chrome不同网页间的切换
     */
    var hidden, visibilityChange;
    if (typeof document.hidden !== 'undefined') { // Opera 12.10 and Firefox 18 and later support
        hidden = 'hidden';
        visibilityChange = 'visibilitychange';
    } else if (typeof document.msHidden !== 'undefined') {
        hidden = 'msHidden';
        visibilityChange = 'msvisibilitychange';
    } else if (typeof document.webkitHidden !== 'undefined') {
        hidden = 'webkitHidden';
        visibilityChange = 'webkitvisibilitychange';
    }
    /**
     * 如果页面是隐藏状态，则暂停视频；如果页面是展示状态，则播放视频
     */
    function handleVisibilityChange() {
        if (document[hidden]) {
            // 不再后台暂停
            // that.pause();
        } else {
            // FIXME 是否需要重载
            // that.reload();
        }
    }
    document.addEventListener(visibilityChange, handleVisibilityChange, false);

    /**
     * 是否在主屏幕
     * @type {boolean}
     */
    this.isBigVideoPlayer = false;
    knockout.track(this, ['isBigVideoPlayer']);

    /**
     * 视频播放器工具栏
     * @private
     * @type {VideoPlayerToolBar}
     */
    this._videoPlayerToolBar = new VideoPlayerToolBar(videoPlayerToolBarContainer, {
        videoPlayer: this
    });

}

defineProperties(VideoPlayerRTMP.prototype, {
    /**
     * Gets the parent container.
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Element}
     */
    container : {
        get : function() {
            return this._container;
        }
    },

    /**
     * Gets the view model.
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {VideoPlayerRTMP}
     */
    viewModel : {
        get : function() {
            return this._viewModel;
        }
    },

    /**
     * Gets the wrapper.
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Element}
     */
    wrapper : {
        get : function() {
            return this._wrapper;
        }
    },

    /**
     * 缩略图地址
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {String}
     */
    thumbnailUrl: {
        get: function () {
            return this._viewModel.thumbnailUrl;
        },
        set : function(value) {
            this._viewModel.thumbnailUrl = value;
        }
    },

    /**
     * 视频地址
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {String}
     */
    videoUrl: {
        get: function () {
            return this._videoUrl;
        },
        set : function(value) {
            this._videoUrl = value;
        }
    },

    /**
     * 通道号
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {String}
     */
    channelID: {
        get: function () {
            return this._channelID;
        },
        set : function(value) {
            this._channelID = value;
        }
    },

    /**
     * 这个播放器的id
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {String}
     */
    videoElementId: {
        get: function () {
            return this._videoElementId;
        },
        set : function(value) {
            this._videoElementId = value;
        }
    },

    /**
     * 高亮播放器
     * @description 红色
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Boolean}
     */
    highlight: {
        get: function () {
            return this._viewModel.highlightActiveOverlay;
        },
        set : function(value) {
            this._viewModel.highlightActiveOverlay = value;
        }
    },

    /**
     * 选中摄像头播放器
     * @description 蓝色
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Boolean}
     */
    select: {
        get: function () {
            return this._viewModel.highlightFlashDragOverlay;
        },
        set : function(value) {
            this._viewModel.highlightFlashDragOverlay = value;
        }
    },

    /**
     * 唯一一个可以触发鼠标事件的操作层
     * @description 蓝色
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Element}
     */
    videoElementOverlay: {
        get: function () {
            return this._videoElementOverlay;
        }
    },

    /**
     * 视频播放器工具栏
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {VideoPlayerToolBar}
     */
    videoPlayerToolBar: {
        get: function () {
            return this._videoPlayerToolBar;
        }
    },

    /**
     * 视频播放器工具栏是否可见
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Boolean}
     */
    videoPlayerToolBarVisible: {
        get: function () {
            return this._videoPlayerToolBar.show;
        },
        set:function (value) {
            this._videoPlayerToolBar.show = value;
        }
    },

    /**
     * 是否有有效的channelID
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Boolean}
     */
    hasValidChannelId: {
        get: function () {
            return this._channelID !== undefined && this._channelID !== 'rtmp';
        }
    },

    /**
     * 详情信息
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Element}
     */
    cameraObject: {
        get: function () {
            if(this.hasValidChannelId){
                return this._parentGallery.channelIdIndexCollection.get(this._channelID).cameraObject;
            }else{
                return undefined;
            }
        }
    },

    /**
     * 画布
     * @memberof VideoPlayerRTMP.prototype
     *
     * @type {Element}
     */
    drawCanvas: {
        get: function () {
            return this._drawCanvas;
        }
    },

    /**
     * 画布ctx
     * @memberof VideoPlayerRTMP.prototype
     */
    drawCanvasCtx: {
        get: function () {
            return this._drawCanvasCtx;
        }
    }

});

/**
 * 获取某个channelID的摄像头流媒体地址并播放，然后停掉回放的流
 * @description 会调用reload()
 * @param {Object} options
 * @param {String} [options.channelID] 摄像头通道号
 * @param {Function} [options.callback] 回调函数
 * @see VideoPlayerRTMP#reloadChannelIdAndUrl
 */
VideoPlayerRTMP.prototype.reloadChannelID = function (options){
    console.log('reloadChannelID:');
    console.log(options);
    // 如果正在播放别的channel，将channel停掉，在这里处理是为了确保每点开一路新的流都会关闭一个旧的
    this.stopPlayBackStream();

    options = defaultValue(options, {});

    if(defined(options.channelID)){
        // FIXME 20190401
        if(this.checkStreamValid() === true){
            this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
            this.clearStream();
        }else{
            this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
        }

        this._channelID = options.channelID;
    }else{
        // FIXME 20190401
        if(this.checkStreamValid() === true){
            this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
            this.clearStream();
        }else{
            this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
        }
    }

    if(!defined(this._channelID)){
        return;
    }

    var that = this;

    var startChannelPromise = MediaServiceProtocol.startChannel({
        channelID: this._channelID
    });
    startChannelPromise.then(function (json) {
        if(defined(options) && defined(options.callback)){
            that.reload({
                videoUrl: json,
                callback: options.callback
            });
        } else {
            that.reload({
                videoUrl: json
            });
        }
    }).catch(function (msg) {
        that.drawDanmuError('点播' + that._channelID + '错误：' + msg);

        // FIXME 20190401
        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
        // that.showErrorTips('点播' + that._channelID + '错误：' + msg);
        that._currentErrorMsg = '点播' + that._channelID + '错误：' + msg;
    });

};

/**
 * 同时替换channelID和Url
 *
 * @description 此函数是为了在请求到多路视频是可以直接塞入数据，只允许VideoPlayerRTMPGallery调用
 * @param options
 * @param {String} options.channelID
 * @param {String} options.videoUrl
 * @see VideoPlayerRTMP#reloadChannelID
 */
VideoPlayerRTMP.prototype.reloadChannelIdAndUrl = function (options){
    options = defaultValue(options, {});
    this.stopPlayBackStream();

    if(this.checkStreamValid() === true){
        this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
        this.clearStream();
    }else{
        this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
    }

    this._channelID = options.channelID;
    this.reload({
        videoUrl: options.videoUrl
    });
};

/**
 * 重载视频
 *
 * @description 最基础的切换视频源的方法，其余的根据其他参数生成的方法都会调用此方法
 * @param {Object} [options] 当只是需要刷新视频时无需设置此属性时
 * @param {String} options.videoUrl 视频直播的地址。如果为<code>'rtmp'<code>，属于清空视频流的请求
 * @param {String} options.thumbnailUrl 缩略图地址
 * @param {String} [options.isSpeedDomeCamera=false] 是不是快球摄像头
 * @param {Function} [options.callback] 回调函数
 */
VideoPlayerRTMP.prototype.reload = function (options) {
    var that = this;
    // 若是没加载完成，等待加载完成后再接收视频流
    if(this.loaded === false){
        setTimeout(function () {
            that.reload(options);
        }, 300);
        return;
    }

    options = defaultValue(options, {});
    if(defined(options.thumbnailUrl)){
        this.thumbnailUrl = options.thumbnailUrl;
    }
    if(defined(options.videoUrl)){
        this._videoUrl = options.videoUrl;
    }
    var videoUrl = this._videoUrl;

    if(this._chplayer == null) {
        return;
    }

    var newVideoObject = {
        autoplay: true, //是否自动播放
        video: videoUrl
        // html5m3u8: true
    };

    console.log(videoUrl);
    this._chplayer.newVideo(newVideoObject);

    // 约定的将视频流清除的url
    if(videoUrl === 'rtmp'){
        this._chplayer.time = NaN;
    }

    // console.log('是否会进入hidePauseOverlay呢？');
    if(defined(options.videoUrl) && options.videoUrl !== 'rtmp'){
        // console.log('是!');
        this.hidePauseOverlay();
    }else{
        // console.log('否!');
    }

    this.cameraSpeedDomeWidgetVisible = false;
    this.videoPlayerToolBarVisible = false;

    if(defined(options.callback)){
        options.callback();
    }
};

/**
 * 暂停视频
 */
VideoPlayerRTMP.prototype.pause = function () {
    if(this._chplayer !== undefined || this._chplayer !== null){
        try{
            this._chplayer.pause();
        }catch (e){
            console.log(e);
        }
        // FIXME 不是每个暂停都需要显示隐藏图层
        // this.showPauseOverlay();
    }
};

/**
 * 播放视频
 */
VideoPlayerRTMP.prototype.play = function () {
    if(this._chplayer !== undefined || this._chplayer !== null){
        try{
            this._chplayer.play();
        }catch (e){
            console.log(e);
        }

        this.hidePauseOverlay();
    }
};

/**
 * 是否有效视频流
 * @return {Boolean}
 */
VideoPlayerRTMP.prototype.checkStreamValid = function () {
    if(defined(this._chplayer)){
        if(!isNaN(this._chplayer.time)){
            return true;
        }
    }
    return false;
};

/**
 * 是否有效视频地址
 * @return {Boolean}
 */
VideoPlayerRTMP.prototype.checkVideoUrlValid = function () {
    return defined(this._videoUrl) && this._videoUrl !== 'rtmp';
};

/**
 * 清空视频流
 * @description chplayer在停止流后理论上会吐出NaN，因此在人工黑屏时可以同样将时间设置为NaN，简化代码，此代码仅在chplayer下有效
 */
VideoPlayerRTMP.prototype.clearStream = function () {
    this.reload({
        videoUrl: 'rtmp'
    });
    // this._chplayer.time = NaN;//这行移动到加载reload里
};

/**
 * 获取该播放器在父节点中的位置
 * @param {String} [videoElementId=this._videoElementId]
 * @example videoPlayerRTMP.getPositionOfParentGallery(videoPlayerRTMP._videoElementId);
 * @returns {String|undefined}
 */
VideoPlayerRTMP.prototype.getPositionOfParentGallery = function(videoElementId) {
    videoElementId = defaultValue(videoElementId, this._videoElementId);
    if(defined(videoElementId)){
        return videoElementId.slice(-3);
    }else{
        return undefined;
    }
};

/**
 * 获取该播放器在父节点中的编号，从1开始
 * @returns {Number|undefined}
 */
VideoPlayerRTMP.prototype.getIndexOfParentGallery = function() {
    var position = this.getPositionOfParentGallery(this._videoElementId);
    var positionArray = position.split('x', 2);
    var verticalIndex = parseInt(positionArray[0]);
    var horizontalIndex = parseInt(positionArray[1]);
    var verticalNumber = parseInt(this._parentGallery._verticalNumber);
    return verticalIndex * verticalNumber + horizontalIndex + 1;
};

/**
 * 获取父框架的标识
 * @param {String} [videoElementId=this._videoElementId]
 */
VideoPlayerRTMP.prototype.getParentGalleryId = function(videoElementId) {
    /**
     * FIXME 关键操作，可能需要正则
     */
    videoElementId = defaultValue(videoElementId, this._videoElementId);
    return videoElementId.slice(0, -3);
};

/**
 * 通过弹幕的形式绘制错误信息
 * @description 使用弹幕的方式将后台的错误信息直接打印，方便调试
 * @param {String} msg
 * @deprecated
 */
VideoPlayerRTMP.prototype.drawDanmuError = function(msg){
    console.warn('弹幕信息为：', msg);

    // var danmuObj = {
    //     list: [
    //         {
    //             type: 'text', //说明是文本
    //             text: msg, //文本内容
    //             fontColor: '#FFFFFF',
    //             fontSize: 14,
    //             fontFamily: '"Microsoft YaHei", YaHei, "微软雅黑", SimHei, "黑体",Arial',
    //             lineHeight: 30,
    //             alpha: 1, //透明度
    //             paddingLeft: 10, //左边距离
    //             paddingRight: 10, //右边距离
    //             paddingTop: 0,
    //             paddingBottom: 0,
    //             marginLeft: 0,
    //             marginRight: 0,
    //             marginTop: 0,
    //             marginBottom: 0,
    //             backgroundColor: '#000000',
    //             backAlpha: 0.5,
    //             backRadius: 30 //背景圆角弧度
    //         }
    //     ],
    //     x: '100%', //x轴坐标
    //     y: '50%', //y轴坐标
    //     //position:[2,1,0],//位置[x轴对齐方式（0=左，1=中，2=右），y轴对齐方式（0=上，1=中，2=下），x轴偏移量（不填写或null则自动判断，第一个值为0=紧贴左边，1=中间对齐，2=贴合右边），y轴偏移量（不填写或null则自动判断，0=紧贴上方，1=中间对齐，2=紧贴下方）]
    //     alpha: 1,
    //     //backgroundColor:'#FFFFFF',
    //     backAlpha: 0.8,
    //     backRadius: 30 //背景圆角弧度
    // };
    // var danmu = this._chplayer.addElement(danmuObj);
    // var danmuS = this._chplayer.getElement(danmu);
    // var obj = {
    //     element: danmu,
    //     parameter: 'x',
    //     static: true, //是否禁止其它属性，true=是，即当x(y)(alpha)变化时，y(x)(x,y)在播放器尺寸变化时不允许变化
    //     effect: 'None.easeOut',
    //     start: null,
    //     end: -danmuS['width'],
    //     speed: 10,
    //     overStop: true,
    //     pauseStop: true,
    //     callBack: 'deleteChild'
    // };
    // var danmuAnimate = this._chplayer.animate(obj);
};

/**
 * 展示错误，一致存在
 * @param {String} msg
 */
VideoPlayerRTMP.prototype.showError = function(msg){
    // console.log('显示的错误信息为：');
    // console.log(msg);

    if(this._pauseOverlayVisible === true){
        console.warn('如果暂停了就先不打印错误了');
        return;
    }

    // console.log(this.videoPlayerStatus);

    // FIXME 20190401
    if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_LOADING){
        msg = '实时视频加载中...';
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_RESUME){
        // msg = '实时视频重连中...';
        msg = this._currentErrorMsg;
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_SWITCH){
        msg = '实时视频切换中...';
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_FAIL){
        // msg = '实时视频错误';
        // console.log(this._lastErrorMsg, this._currentErrorMsg);
        console.warn('实时视频错误');
        msg = this._currentErrorMsg;
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_END){
        msg = '无实时视频';
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_LOADING){
        msg = '历史视频加载中...';
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_RESUME){
        msg = '历史视频重连中...';
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_SWITCH){
        msg = '历史视频切换中...';
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_FAIL){
        // msg = '历史视频错误';
        msg = this._currentErrorMsg;
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_END){
        msg = '历史视频播放结束';
    }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.INIT_PLEASE_DRAG){
        msg = '拖拽加载';
    }
    // console.log('showError:', msg, '。当前的状态是：', this.videoPlayerStatus);

    if(this._lastErrorMsg === msg){
        return;
    }
    this._lastErrorMsg = msg;

    // FIXME 不显示yln 2020/10/14
    // this._showTipsWidget.show(msg);
};

/**
 * 展示错误信息，出现后又消失
 * @param {String} msg
 */
VideoPlayerRTMP.prototype.showErrorTips = function(msg){
    if(this._pauseOverlayVisible === true){
        return;
    }
    if(this._lastErrorMsg === msg){
        return;
    }
    this._lastErrorMsg = msg;
    console.log(msg);

    var that = this;
    this._showTipsWidget.show(msg);
    setTimeout(function () {
        that._showTipsWidget.hide();
    }, 2000);
};

/**
 * 隐藏指定的错误信息
 */
VideoPlayerRTMP.prototype.hideError = function(msg){
    // console.log('隐藏的错误信息为：');
    // console.log(msg);

    // FIXME 20190401
    // msg = '';
    // if(defined(msg) && this._lastErrorMsg !== msg){
    //     return;
    // }

    if(VideoPlayerStatusEnumHelper.isLiveStatus(this.videoPlayerStatus)){
        this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE;
    }else if(VideoPlayerStatusEnumHelper.isPlaybackStatus(this.videoPlayerStatus)){
        this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK;
    }

    if(this._showTipsWidget._visible === true){
        this._lastErrorMsg = '';
        this._showTipsWidget.hide();
    }
};

/**
 * 开始断线重连
 * @description 视频流有时会因为某些原因而临时断线，这时需要一个重新连接的尝试机制，这里初始化参数，并会用以指数增长的形式主动请求若干次通过reconnect函数，达到重连的目的
 * @see VideoPlayerRTMP#reconnect
 */
VideoPlayerRTMP.prototype.startReconnect = function () {
    var that = this;
    console.log('视频流断线重连函数开始了' + that._videoUrl);
    // 初始化若干参数
    this.reconnectInterval = 1000;
    this.maxReconnectInterval = 30000;
    this.reconnectDecay = 1.5;
    this.timeoutInterval = 2000;
    this.maxReconnectAttempts = null;
    this.reconnectAttempts = 0;

    // 首先排除主动黑屏的情况
    if (this._videoUrl === 'rtmp') {
        that._isValidStream = false;
        // this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_END;
    } else {
        var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
        setTimeout(function() {
            that.reconnectAttempts++;
            if(that._isValidStream === false){
                // console.log('断线重连：' + that._videoUrl + '第' + that.reconnectAttempts);
                // FIXME

                that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_RESUME;
                // that._currentErrorMsg = '实时视频重连中...';
                that._currentErrorMsg = '断线重连：' + that._videoUrl + '第' + that.reconnectAttempts;
                that.reload();
                that.reconnect();
            }
        }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
    }
};

/**
 * 断线重连
 * @description 视频流有时会因为某些原因而临时断线，需要一个重新连接的尝试机制
 * @see VideoPlayerRTMP#startReconnect
 */
VideoPlayerRTMP.prototype.reconnect = function () {
    var that = this;

    // 首先排除主动黑屏的情况
    if (this._videoUrl === 'rtmp') {
        that._isValidStream = false;
    } else {
        var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
        setTimeout(function() {
            that.reconnectAttempts++;
            if(that._isValidStream === false){
                console.log('断线重连：' + that._videoUrl + '第' + that.reconnectAttempts);
                that.reload();
                that.reconnect();
            }
        }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
    }

};

/**
 * 视频截屏
 * @description 得到的stream可以直接放到<img>的src中
 * @param {Number} [quality=100] 截屏质量1到100都为有效值，100最高
 */
VideoPlayerRTMP.prototype.printScreen = function(quality){
    var jpgQuality = defaultValue(quality, 100);
    var stream = this._chplayer.printScreen(jpgQuality);
    return stream;
};

/**
 * 显示暂停叠加层
 * @see VideoPlayerRTMP#pause
 * @description 这个层的主要意义在于回放暂停是视频流会中断，播放器会在若干秒后黑屏，因此需要一个临时的截图叠加层来遮盖黑屏
 */
VideoPlayerRTMP.prototype.showPauseOverlay = function(){
    // FIXME 20190318
    // console.log('生成图层');
    this._chplayer.showPauseOverlay();
    this._pauseOverlayVisible = true;
};

/**
 * 隐藏暂停叠加层
 * @see VideoPlayerRTMP#reload
 * @see VideoPlayerRTMP#play
 */
VideoPlayerRTMP.prototype.hidePauseOverlay = function(){
    // FIXME 20190124
    // console.log('隐藏图层');
    // if(this._pauseOverlayVisible === true){
    //     this._chplayer.hidePauseOverlay();
    //     this._pauseOverlayVisible = false;
    // }

    this._chplayer.hidePauseOverlay();
    this._pauseOverlayVisible = false;

};

/**
 * 选中此播放器（针对父集合）
 * @see VideoPlayerRTMPGallery#getSelectedPlayer
 * @description 选中当前的播放器，并且将父集合中其他所有的播放器设置为非选中的状态
 */
VideoPlayerRTMP.prototype.focusCurrentPlayerOfParentGallery = function(){
    if(this.select === true){
        this.select = false;
    }else{
        if(defined(this._parentGallery)){
            var selectedPlayer = this._parentGallery.getSelectedPlayer();
            if(defined(selectedPlayer)){
                selectedPlayer.select = false;
            }
            this.select = true;
        }
    }
};

/**
 * 订阅channelId变化事件
 * @param callback
 */
VideoPlayerRTMP.prototype.subscribeChannelIdChangeEvent = function (callback){
    knockout.track(this, ['_channelID']);
    knockout.getObservable(this, '_channelID').subscribe(function(newValue) {
        callback(newValue);
    });
};

/**
 * 订阅videoUrl变化事件
 * @param callback
 */
VideoPlayerRTMP.prototype.subscribeVideoUrlChangeEvent = function (callback){
    knockout.track(this, ['_videoUrl']);
    knockout.getObservable(this, '_videoUrl').subscribe(function(newValue) {
        callback(newValue);
    });
};

/**
 * 重新设置canvas的大小并刷新画布
 * @description 设置width和height后会自动清空画布
 */
VideoPlayerRTMP.prototype.resizeCanvas = function () {
    this._drawCanvas.width = this._drawCanvas.clientWidth;
    this._drawCanvas.height = this._drawCanvas.clientHeight;
};

/**
 * 调整Video的大小
 */
VideoPlayerRTMP.prototype.resizeVideo = function(){
    this._chplayer.resize();
};

/**
 * Returns true if this object was destroyed; otherwise, false.
 * <br /><br />
 * If this object was destroyed, it should not be used; calling any function other than
 *
 * @returns {Boolean} <code>true</code> if this object was destroyed; otherwise, <code>false</code>.
 *
 * @see VideoPlayerRTMP#destroy
 */
VideoPlayerRTMP.prototype.isDestroyed = function() {
    return false;
};

/**
 * Removes and destroys all created by this instance.
 */
VideoPlayerRTMP.prototype.destroy = function() {
    // console.log(this, 'destroy');
    // if (FeatureDetection.supportsPointerEvents()) {
    //     document.removeEventListener('pointerdown', this._closeDropDown, true);
    // } else {
    //     document.removeEventListener('mousedown', this._closeDropDown, true);
    //     document.removeEventListener('touchstart', this._closeDropDown, true);
    // }
    // this.clearStream()
    // this._chplayer.clear();
    knockout.cleanNode(this._wrapper);
    this._container.removeChild(this._wrapper);

    // return destroyObject(this);
    return true
};

export default VideoPlayerRTMP;
