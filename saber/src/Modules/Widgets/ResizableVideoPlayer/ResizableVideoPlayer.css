/*边缘光基本样式*/
.lux-resizable-video-player {
  position: relative;
  width: 100%;
  height: 100%;
  /*transition: all 1s linear 0s;*/
}

.lux-resizable-video-player:hover {
  box-shadow: 0 0 8px #ffffff;
  border-color: #eeaa44;
  outline: none;
}

.lux-resizable-video-player:hover {
  box-shadow: 0 0 8px #ffffff;
  border-color: #eeaa44;
  outline: none;
}

.lux-resizable-video-player:active {
  border-color: #eeaa44;
  box-shadow: 0 0 16px #ffffff;
}

/*全屏*/
.lux-resizable-video-player-fullScreen {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  background: black;
}

.lux-resizable-video-player {
  /* background: url("../../../assets/images/map.png") no-repeat center center,
              linear-gradient(135deg, #116ab6, #2693e0); */
  /* background: no-repeat center center,
              linear-gradient(135deg, #a8a8a8, #888888); */
  /* background-size: cover, 100%; */
  background: url("/bdsplatform/favicon.png") no-repeat center center,
              linear-gradient(135deg, #a8a8a8, #888888);
  background-size: 35%, 100%;
}

/*video元素*/
.lux-resizable-video-element {
  /* position: absolute; */
  width: 100%;
  height: 100%;
}

.lux-resizable-video-element-noPointerEvent {
  pointer-events: none;
}

/*缩略图元素*/
.lux-resizable-thumbnail-element {
  position: absolute;
  width: 100%;
  height: 100%;
}

/*垂直布局container属性,能将div在垂直方向三等分*/
.lux-video-player-column-flex-container {
  display: flex;
  flex-direction: column;
}

/*.lux-video-player-column-flex-container:hover {*/
/*box-shadow: 0 0 8px #fff;*/
/*border-color: #ea4;*/
/*outline: none;*/
/*}*/

/*垂直布局item属性*/
.lux-video-player-column-flex-item {
  flex-grow: 1;
  flex-shrink: 1;
  transition: all 100ms linear 0s;
}

.lux-video-player-column-flex-item-hidden {
  flex: 0;
  height: 0;
  width: 0;
  transition: all 250ms linear 0s;
}

/*水平布局container属性,能将div在水平方向三等分*/
.lux-video-player-row-flex-container {
  display: flex;
  flex-direction: row;
}

/*水平布局item属性*/
.lux-video-player-row-flex-item {
  flex-grow: 1;
  flex-shrink: 1;
  transition: all 100ms linear 0s;
}

.lux-video-player-row-flex-item-hidden {
  flex: 0;
  height: 0;
  width: 0;
  transition: all 250ms linear 0s;
}

/*在flash播放器上再叠加一层div的样式，用于操作*/
.lux-video-player-flash-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  pointer-events: none;
  /*width: calc(100% - 4px);*/
  /*height: calc(100% - 4px);*/
  width: calc(100%);
  height: calc(100%);
  border: 0px solid red;
  /*transition:250ms border ease-in-out;*/
  transition: 250ms border linear;
}

.lux-video-player-flash-overlay-visible {
  pointer-events: auto;
}

.lux-video-player-flash-overlay-invisible {
  pointer-events: none;
}

.lux-video-player-flash-overlay-highlight {
  /*transition:250ms border ease-in-out, 250ms width ease-in-out;*/
  transition: 250ms border linear, 250ms width linear;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  border: 2px solid red;
}

/*错误信息*/
.lux-video-player-error-overlay {
  position: absolute;
  top: calc(50% - 15px);
  left: 0;
  right: 0;
  color: darkred
}

/*只用于显示摄像头高亮，不能点击*/
.lux-video-player-active-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  pointer-events: none;
  /*width: calc(100% - 4px);*/
  /*height: calc(100% - 4px);*/
  width: calc(100%);
  height: calc(100%);
  /*border: 0px solid #56a80f;*/
  /*transition:250ms border ease-in-out;*/
}

.lux-video-player-active-overlay-visible {
  pointer-events: none;
}

.lux-video-player-activet-overlay-invisible {
  pointer-events: none;
}

.lux-video-player-active-overlay-highlight {
  /*transition:250ms border ease-in-out, 250ms width ease-in-out;*/
  /*width: calc(100% - 4px);*/
  /*height: calc(100% - 4px);*/
  /* border: 2px solid #d43900; */
}

.lux-video-player-videoSpeedDomeControl {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;
}

.lux-video-player-videoPlayerToolBar {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  top: 0;
  z-index: 11;
  pointer-events: none;
}
