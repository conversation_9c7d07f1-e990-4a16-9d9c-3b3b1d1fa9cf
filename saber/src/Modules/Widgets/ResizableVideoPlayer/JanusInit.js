import defaultValue from '../../Core/defaultValue'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import DeveloperError from '../../Core/DeveloperError'
import knockout from '../../ThirdParty/knockout'
import SharedProtocol from '../../Protocol/SharedProtocol'
import VideoPlayerStatusEnum from './VideoPlayerStatusEnum'

import Janus from  '../../ThirdParty/webRTC/janus-es6'

'use strict';

/**
 * JanusInit
 * @alias JanusInit（webRTC播放器janus初始化）
 *
 * @description WebRTC 连接，独立出来这个对象是为了减少连接后台的次数
 *
 * @see https://github.com/meetecho/janus-gateway
 *
 * @param {Array|String} [options.debug=[]] 'all'或true 会打印所有关键字trace, debug, vdebug, log, warn, error. 默认不打印任何东西
 *
 * @constructor
 */
function JanusInit(options) {
  options = defaultValue(options, {});
  //>>includeStart('debug', pragmas.debug);
  //>>includeEnd('debug');

  /**
   * 初始化成功
   * @type {boolean}
   */
  this.success = false;

  /**
   * 错误
   * @type {boolean}
   */
  this.error = false;

  /**
   * 已销毁
   * @type {boolean}
   */
  this.destroyed = false;

  this._janus = undefined;

  /**
   * debug等级
   */
  this._debug = defaultValue(options.debug, []);

  knockout.track(this, ['success', 'error', 'destroyed'
  ]);

  if(!Janus.isWebrtcSupported()) {
    console.error('no support webrtc');
    this.error = {
      videoPlayerStatus: VideoPlayerStatusEnum.NO_WEBRTC_SUPPORT
    };
    return;
  }

  this._init();
}

defineProperties(JanusInit.prototype, {
  /**
   * janus对象
   * @memberof JanusInit.prototype
   * @type {Object|undefined}
   */
  janus: {
    get: function () {
      if(this.success === true){
        return this._janus;
      }else{
        throw new DeveloperError('Please subscribe success flag to make sure janus is ready to use.');
      }
    }
  }
});

/**
 * 初始化
 * @private
 */
JanusInit.prototype._init = function () {
  var janusGatewayServerIp = SharedProtocol.getWebRTCJanus();
  // var janusGatewayServerIp = 'localhost';
  // var janusGatewayServerIp = '*************';
  // var janusGatewayServerIp = '*************';
  // console.log(janusGatewayServerIp);
  var server = [
    'http://' + janusGatewayServerIp + '/janus'
    // 'http://' + janusGatewayServerIp + ':8088/janus'
    // 'ws://' + janusGatewayServerIp + ':8188',
  ];

  var that = this;

  Janus.init({
    debug: that._debug,
    // debug: 'all',
    callback: function() {
      // Create session
      that._janus = new Janus({
        server: server,
        success: function() {
          console.log('success');
          that.success = true;
        },
        error: function(error) {
          Janus.error(error);
          console.log('error');
          that.error = {
            videoPlayerStatus: VideoPlayerStatusEnum.NO_CONNECT_WEBRTC_SERVER
          };
          that.success = false;
          setTimeout(function () {
            console.log('尝试重连webRTC服务器...');
            that._init();
          }, 5000);
        },
        destroyed: function() {
          console.log('destroyed');
        }
      });
    }});

};

/**
 * Returns true if this object was destroyed; otherwise, false.
 * <br /><br />
 * If this object was destroyed, it should not be used; calling any function other than
 *
 * @returns {Boolean} <code>true</code> if this object was destroyed; otherwise, <code>false</code>.
 *
 * @see JanusInit#destroy
 */
JanusInit.prototype.isDestroyed = function() {
  return false;
};

/**
 * Removes and destroys all created by this instance.
 */
JanusInit.prototype.destroy = function() {
  return destroyObject(this);
};

export default JanusInit;
