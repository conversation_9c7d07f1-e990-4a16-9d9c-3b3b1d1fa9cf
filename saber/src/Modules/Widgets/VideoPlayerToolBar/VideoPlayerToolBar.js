import defaultValue from '../../Core/defaultValue';
import defined from '../../Core/defined';
import defineProperties from '../../Core/defineProperties';
import destroyObject from '../../Core/destroyObject';
import DeveloperError from '../../Core/DeveloperError';
import getElement from '../../Widgets/getElement';
import knockout from '../../ThirdParty/knockout';
import VideoPlayerToolBarViewModel from './VideoPlayerToolBarViewModel';
import {sendPhotoMsg, sendLocationMsg} from '@/api/center/instruction';
import download from '@/utils/download/download';
import { Notification } from 'element-ui';
import moment from 'moment';
import StreamTypeEnum from '@/enumerate/video/streamType';
import DataTypeEnum from '@/enumerate/video/dataType';

/**
 * VideoPlayerToolBar
 *
 * @alias VideoPlayerToolBar（视频播放器工具栏）
 *
 * @param {Element|String} container 控件容器
 * @param options
 * @param {VideoPlayerRTMP} options.videoPlayer
 *
 * @exception {DeveloperError} container is required.
 *
 * @constructor
 */
function VideoPlayerToolBar (container, options) {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(container)) {
    throw new DeveloperError('container is required.');
  }
  // >>includeEnd('debug');

  container = getElement(container);
  this._videoPlayer = options.videoPlayer;

  var viewModel = new VideoPlayerToolBarViewModel(options);

  /**
   * 包裹层
   * @type {HTMLDivElement}
   */
  var wrapper = document.createElement('div');
  wrapper.className = 'lux-VideoPlayerToolBar-wrapper';
  wrapper.setAttribute('data-bind', 'visible: show & hasAtLeastOneButton');
  container.appendChild(wrapper);

  var baseControlGroup = document.createElement('div');
  baseControlGroup.className = 'lux-VideoPlayerToolBar-baseControlGroup';
  baseControlGroup.innerHTML = '';
  wrapper.appendChild(baseControlGroup);

  knockout.applyBindings(viewModel, wrapper);

  this._viewModel = viewModel;
  this._wrapper = wrapper;
  this._container = container;
  this._baseControlGroup = baseControlGroup;
  this._videoPlayer = options.videoPlayer;

  // 新增若干个默认的按钮
  // 关闭静音
  this.closeMutedButton = this.addButton('/bdsplatform/static/images/icons/audioCloseWhite.svg', () => {
    this._videoPlayer.setFLVMuted(false);
    this.MutedButton.style.display = 'inline-block';
    this.closeMutedButton.style.display = 'none';
  }, '关闭静音');

  // 开启静音
  this.MutedButton = this.addButton('/bdsplatform/static/images/icons/audioOpenWhite.svg', () => {
    this._videoPlayer.setFLVMuted(true);
    this.closeMutedButton.style.display = 'inline-block';
    this.MutedButton.style.display = 'none';
  }, '开启静音');
  this.closeMutedButton.style.display = 'none';
  // 关闭视频
  this.addButton('/bdsplatform/static/images/icons/videoCloseWhite.svg', () => {
    this._videoPlayer.stopLiveChannel();
    this._videoPlayer.clearStream();
    // 初始化开启声音 todo
    this.MutedButton.style.display = 'inline-block';
    this.closeMutedButton.style.display = 'none';
  }, '关闭视频');
  // 当前为音视频模式，点击开启监听模式
  this.avButton = this.addButton('/bdsplatform/static/images/icons/video.svg', () => {
    this._videoPlayer.switchToMonitor();
    this.avButton.style.display = 'none';
    this.monitorButton.style.display = 'inline-block';
    // 开启声音
    this.MutedButton.style.display = 'inline-block';
    this.closeMutedButton.style.display = 'none';
  }, '当前为音视频模式，点击开启监听模式');
  // 当前为监听模式，点击开启音视频模式
  this.monitorButton = this.addButton('/bdsplatform/static/images/icons/monitor.svg', () => {
    this._videoPlayer.switchToVideoAudio();
    this.avButton.style.display = 'inline-block';
    this.monitorButton.style.display = 'none';
    // 开启声音
    this.MutedButton.style.display = 'inline-block';
    this.closeMutedButton.style.display = 'none';
  }, '当前为监听模式，点击开启音视频模式');
  if (this._videoPlayer.dataType === DataTypeEnum.VIDEO_AUDIO) {
    this.monitorButton.style.display = 'none';
  } else {
    this.avButton.style.display = 'none';
  }
  // 视频拍照
  this._takePhotoButton = this.addButton('/bdsplatform/static/images/icons/videoScreenshotWhite.svg', () => {
    if (!defined(this._videoPlayer.channelID)) {
      return;
    }
    let tempArray = this._videoPlayer.channelID.split('_');
    let licencePlate = tempArray[0];
    let channel = parseInt(tempArray[1]);
    let param = {
      licencePlate: licencePlate,
      channel: channel,
      vehicleId: this._videoPlayer.vehicleId
    };
    sendPhotoMsg(param).then(res => {
      this._videoPlayer.drawDanmuError('拍照成功！');
      Notification.success({
        title: `${licencePlate}通道${channel}拍照成功！`
      });
    });
  }, '视频拍照');

  // 下载画面截屏
  this.addButton('/bdsplatform/static/images/icons/videoDownloadWhite.svg', () => {
    if (!defined(this._videoPlayer.channelID)) {
      return;
    }
    let tempArray = this._videoPlayer.channelID.split('_');
    let licencePlate = tempArray[0];
    let channel = parseInt(tempArray[1]);
    let base64 = this._videoPlayer.getPrintScreenBase64Jpg();
    download(base64, `${licencePlate}通道${channel}视频截图${moment().format('YYYYMMDDHHmmss')}.jpg`);
    Notification.success({
      title: `${licencePlate}通道${channel}视频截图下载到本地！`
    });
  }, '下载画面截屏');

  // 定位
  // this.addButton('/bdsplatform/static/images/icons/vehicleLocation.svg', () => {
  //   if (!defined(this._videoPlayer.channelID)) {
  //     return;
  //   }
  //   let tempArray = this._videoPlayer.channelID.split('_');
  //   let licencePlate = tempArray[0];
  //   let param = {
  //     licencePlate: licencePlate
  //   };
  //   sendLocationMsg(param).then(res => {
  //     this._videoPlayer.drawDanmuError('定位成功！');
  //   });
  // }, '发起定位');

  // 重新点播视频
  this.addButton('/bdsplatform/static/images/icons/videoReloadWhite.svg', () => {
    this._videoPlayer.reloadChannelID({
      channelID: this._videoPlayer.channelID,
      vehicleId: this._videoPlayer.vehicleId,
      customData: {
        'VEHICLETREE': {
          treeNodeKey: this._videoPlayer.treeNodeKey
        }
      },
    });
  }, '重新点播视频');

  // 重新加载视频
  this.addButton('/bdsplatform/static/images/icons/videoRefreshWhite.svg', () => {
    this._videoPlayer.reload();
  }, '重新加载视频');

  // 子码流和主码流切换 这个项目的终端不能切换码流 不放这个按钮了
  this.mainStreamButton = this.addButton('/bdsplatform/static/images/icons/streamMain.svg', () => {
    this._videoPlayer.switchToSubStream();
    this.mainStreamButton.style.display = 'none';
    this.subStreamButton.style.display = 'inline-block';
  }, '当前为主码流，点击后切换为子码流');
  this.subStreamButton = this.addButton('/bdsplatform/static/images/icons/streamSub.svg', () => {
    this._videoPlayer.switchToMainStream();
    this.mainStreamButton.style.display = 'inline-block';
    this.subStreamButton.style.display = 'none';
  }, '当前为子码流，点击后切换为主码流');
  if (this._videoPlayer.streamType === StreamTypeEnum.MAIN_STREAM) {
    this.subStreamButton.style.display = 'none';
  } else {
    this.mainStreamButton.style.display = 'none';
  }

  // 重新点播视频
  this.addButton('/bdsplatform/static/images/icons/videoContinuePush.svg', () => {
    let localConfig = JSON.parse(localStorage.getItem(`localUserConfig`));
    this._videoPlayer.pushTime = ((localConfig && localConfig.videoLiveTime) ? localConfig.videoLiveTime : 180) * 60;
  }, '推流延时');
  this._pushTimeTextDom = this.addTextDom(0, () => {}, 0);

  this._bpsTextDom = this.addTextDom('0bps', () => {}, 'bps');

}

defineProperties(VideoPlayerToolBar.prototype, {
  /**
   * Gets the parent container.
   * @memberof VideoPlayerToolBar.prototype
   *
   * @type {Element}
   */
  container: {
    get: function () {
      return this._container;
    }
  },

  /**
   * Gets the view model.
   * @memberof VideoPlayerToolBar.prototype
   *
   * @type {VideoPlayerToolBarViewModel}
   */
  viewModel: {
    get: function () {
      return this._viewModel;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof VideoPlayerToolBar.prototype
   *
   * @type {Element}
   */
  wrapper: {
    get: function () {
      return this._wrapper;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof VideoPlayerToolBar.prototype
   *
   * @type {Element}
   */
  show: {
    get: function () {
      return this._viewModel.show;
    },
    set: function (value) {
      this._viewModel.show = value;
    }
  },

  /**
   * 播放器
   * @memberof VideoPlayerToolBar.prototype
   *
   * @type {VideoPlayerRTMP}
   */
  videoPlayer: {
    get: function () {
      return this._videoPlayer;
    }
  },

  /**
   * 拍照按钮是否可见.
   * @memberof VideoPlayerToolBar.prototype
   *
   * @type {Element}
   */
  takePhotoButtonVisible: {
    get: function () {
      return this._takePhotoButton.style.display === 'inline-block';
    },
    set: function (value) {
      if (value) {
        this._takePhotoButton.style.display = 'inline-block';
      } else {
        this._takePhotoButton.style.display = 'none';
      }
    }
  },
  /**
   * bps.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {String}
   */
  bpsTextDomText: {
    get: function () {
      return this._bpsTextDom.innerText;
    },
    set: function (value) {
      if (value) {
        this._bpsTextDom.innerText = value;
      }
    }
  },

  /**
   * 推流剩余时间
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {String}
   */
  pushTimeTextDomText: {
    get: function () {
      return this._pushTimeTextDom.innerText;
    },
    set: function (value) {
      if (value && value > 0) {
        let m = parseInt(value / 60 % 60);
        m = m < 10 ? '0' + m : m;
        let s = parseInt(value % 60);
        s = s < 10 ? '0' + s : s;
        this._pushTimeTextDom.innerText = m + ':' + s;
      } else {
      }
    }
  }

});

/**
 * 新增按钮
 * @param {String} imgUrl 按钮图片的地址
 * @param {Function} callback
 * @param {String} tips 提示
 */
VideoPlayerToolBar.prototype.addButton = function (imgUrl, callback, tips) {
  var newButton = document.createElement('button');
  newButton.style.backgroundImage = 'url("' + imgUrl + '")';
  this._baseControlGroup.appendChild(newButton);
  var that = this;
  newButton.addEventListener('click', function (event) {
    callback(that, event);
  });
  this._viewModel.hasAtLeastOneButton = true;
  newButton.title = tips;
  return newButton;
};

/**
 * 初始化视频播放器切换按钮.
 */
VideoPlayerToolBar.prototype.initializeToggleButton = function () {
  // 初始化为音视频模式
  this.avButton.style.display = 'inline-block';
  this.monitorButton.style.display = 'none';
  // 初始化为主码流模式
  this.subStreamButton.style.display = 'none';
  this.mainStreamButton.style.display = 'inline-block';
  // 初始化开启声音
  this.MutedButton.style.display = 'inline-block';
  this.closeMutedButton.style.display = 'none';
};

/**
 * 新增文本
 * @param text
 * @param callback
 * @param tips
 * @return {HTMLDivElement}
 */
VideoPlayerToolBar.prototype.addTextDom = function (text, callback, tips) {
  let newDom = document.createElement('div');
  newDom.className = 'lux-VideoPlayerToolBar-baseControlGroup-textDom';
  newDom.innerText = text;
  this._baseControlGroup.appendChild(newDom);
  newDom.title = tips;
  return newDom;
};

/**
 * 订阅球机转动事件
 * @param {Function} callback
 */
VideoPlayerToolBar.prototype.subscribeAllControlEvent = function (callback) {
  this._viewModel.subscribeAllControlEvent(callback);
};

/**
 *  禁止zoom球机控制鼠标事件
 */
VideoPlayerToolBar.prototype.forbidMouseZoomEvent = function () {
  this._viewModel.forbidMouseZoomEvent();
};

/**
 *  取消禁止zoom球机控制鼠标事件
 */
VideoPlayerToolBar.prototype.cancelForbidMouseZoomEvent = function () {
  this._viewModel.cancelForbidMouseZoomEvent();
};

/**
 * @returns {Boolean} true if the object has been destroyed, false otherwise.
 */
VideoPlayerToolBar.prototype.isDestroyed = function () {
  return false;
};

/**
 * Destroys the widget.  Should be called if permanently
 * removing the widget from layout.
 */
VideoPlayerToolBar.prototype.destroy = function () {
  knockout.cleanNode(this._wrapper);
  this._container.removeChild(this._wrapper);
  return destroyObject(this);
};

export default VideoPlayerToolBar;
