import defaultValue from '../../Core/defaultValue';
import defined from '../../Core/defined';
import defineProperties from '../../Core/defineProperties';
import DeveloperError from '../../Core/DeveloperError';
import FeatureDetection from '../../Core/FeatureDetection';
import AssociativeArray from '../../Core/AssociativeArray';
import knockout from '../../ThirdParty/knockout';
import getElement from '../../Widgets/getElement';
import StringUtil from '../../Util/StringUtil';
import IndexCollection from '../../DataSource/IndexCollection';
import ShowTipsWidget from '../ShowTipsWidget/ShowTipsWidget';
import VideoPlayerRTMP from '../ResizableVideoPlayer/VideoPlayerRTMP';
import VideoPlayerJanus from '../ResizableVideoPlayer/VideoPlayerJanus';
import VideoPlayerFLV from '../ResizableVideoPlayer/VideoPlayerFLV';
import VideoPlayerEasyPlayer from '../ResizableVideoPlayer/VideoPlayerEasyPlayer';
import VideoPlayerHtml5 from '../ResizableVideoPlayer/VideoPlayerHtml5';
import JanusInit from '../ResizableVideoPlayer/JanusInit';
import CameraTypeEnum from '../../Enumerate/Camera/CameraTypeEnum';
import Fullscreen from '@/Modules/Core/Fullscreen';
import VideoPlayerNodePlayer from '@/Modules/Widgets/ResizableVideoPlayer/VideoPlayerNodePlayer'

/**
 * VideoPlayerGallery
 *
 * @alias VideoPlayerGallery(视频播放器集合)
 * @constructor
 *
 * @param options
 * @param {String} options.container 父元素的id
 * @param {String} [options.defaultMode={}]
 * @param {String} [options.maxVideoCount=8] 最大使用的视频数
 * @param {Boolean} [options.subscribeDoubleClickFullScreenEvent=true] 是否要使用双击事件
 * @param {Boolean} [options.subscribeClickSwitchPlayerEvent=false] 是否要使用单击事件
 * @param {Boolean} [options.showAllCameraSpeedDomeWidget=false] 是否要显示所有的球机控件
 * @param {String} [options.playerType='VideoPlayerRTMP'] 播放器类型VideoPlayerRTMP/VideoPlayerRTSP/VideoPlayerRTP/VideoPlayerFLV/VideoPlayerHtml5
 * @param {String} [options.forceUseVideoPlayer] 强制使用的播放器类型播放器类型VideoPlayerWebRTCStreamer(仅在playerType=VideoPlayerRTSP时有效)/VideoPlayerEasyPlayer(仅在playerType=VideoPlayerFLV时有效)
 * @param {Boolean} [options.ignoreGb28181ForJanusWebRTC=false] 是否在使用janus的webRTC时不去调用GB28181的接口（可节约时间，但需配置）
 * @param {IndexCollection} [options.indexCollection] 内部索引，为了是模块更独立
 * @param {String} [options.videoElementIdPrefix='video'] 前缀区分，在有多个页面生成播放器时使用
 * @param {String} [options.videoPlayerElementIdPrefix='videoPlayer'] 前缀区分
 * @param {Boolean} [options.subStream=true] 是否使用子码流，默认启用，节约带宽（仅在playerType=VideoPlayerRTSP时有效）
 * @param {Boolean} [options.defaultStream='第三码流'] 可选项为'第三码流' '辅码流' '主码流'，暂时只支持海康和大华的摄像头（仅在playerType=VideoPlayerRTSP时有效）
 * @param {Number} [options.defaultStreamType] 默认的码流类型 码流类型  0：主码流，1：子码流
 * @param {Boolean} [options.subscribeClickHighlightPlayerEvent=false] 是否要使用单击事件触发高亮播放器
 * @param {Boolean} [options.forPlayback=false] 是否要使用单击事件触发高亮播放器
 *
 * @exception {DeveloperError} options.container is required.
 */
function VideoPlayerGallery (options) {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(options)) {
    options = {};
  }
  if (!defined(options.container)) {
    throw new DeveloperError('options.container is required.');
  }
  // >>includeEnd('debug');

  this._indexCollection = defaultValue(options.indexCollection, new IndexCollection());
  this._playerType = defaultValue(options.playerType, 'VideoPlayerRTMP');
  this._forceUseVideoPlayer = options.forceUseVideoPlayer;
  this._ignoreGb28181ForJanusWebRTC = defaultValue(options.ignoreGb28181ForJanusWebRTC, false);
  this._subscribeDoubleClickFullScreenEvent = defaultValue(options.subscribeDoubleClickFullScreenEvent, true);
  this._forPlayback = defaultValue(options.forPlayback, false);

  var that = this;
  this.fullScreenVisible = false;// 是否有播放器正在处于全屏状态

  this._customSetup = options.customSetup;

  this._videoPlayers = new AssociativeArray();

  this._maxVideoCount = defaultValue(options.maxVideoCount, 8);

  /**
   * 父容器
   * @type {Element}
   * @private
   */
  this._container = getElement(options.container);
  this._container.classList.add('lux-VideoPlayerGallery-wrapper');

  /**
   * 分屏模式的具体参数
   * @type {Object}
   * @private
   */
  this._modeArray = this._getAllTemplates();

  this._defaultMode = defaultValue(options.defaultMode, this._modeArray[0]);
  this._mode = this._defaultMode;

  const uid = StringUtil.generateShortUid(); // id加上唯一的标识符
  this._videoPlayerIdPrefix = 'videoPlayer' + uid;// id前缀，chplayer需要用id来定位播放器
  this._videoElementIdPrefix = defaultValue(options.videoElementIdPrefix, 'videoElement' + uid);// 每个播放器布局位置的元素的id前缀
  this._videoPlayerElementIdPrefix = defaultValue(options.videoElementIdPrefix, 'videoPlayerElement' + uid);// 每个用于设置的播放器元素（如videoPlayerRTMP的container）的id前缀
  this._videoPlayerPositionClassNamePrefix = 'videoPlayer-position';// 子视频位置类名的前缀
  this._uid = uid;

  this._initPlayer(options);
  this._initTemplate();
  this._initBigContainer();
  this.bigVideoPlayer.isBigVideoPlayer = true;

  this._bigVideoChangeData = null;
  this._switchTemplateData = null;

  knockout.track(this, ['fullScreenVisible', '_modeArray']);

  this._clickFuncSubscribeClickSwitchPlayerEvent = function (e) {
    var element = e.target;
    var videoPlayerArray = that._videoPlayers.values;
    for (var i = 0; i < that._maxVideoCount; i++) {
      if (videoPlayerArray[i].container.contains(element)) {
        if (videoPlayerArray[i].container.className === 'videoPlayerSmall') {
          that._bigVideoChangeData = {// @see VideoPlayerGallery#_bigVideoChangeData
            bigIndex: that._bigIndex,
            smallIndex: i
          };

          var currentContainer = getElement(that._videoElementIdPrefix + i);
          var currentContainerClassName = currentContainer.className;
          var bigIndex = that._bigIndex;
          var bigContainer = getElement(that._videoElementIdPrefix + bigIndex);
          var bigContainerClassName = bigContainer.className;
          // console.log('bigContainerClassName:', bigContainerClassName, ',', 'currentContainerClassName:', currentContainerClassName);

          currentContainer.className = bigContainerClassName;
          bigContainer.className = currentContainerClassName;

          videoPlayerArray[i].container.className = 'videoPlayerBig';
          videoPlayerArray[bigIndex].container.className = 'videoPlayerSmall';

          that._bigContainer = currentContainer;
          that._bigIndex = i;

          videoPlayerArray[i].isBigVideoPlayer = true;
          videoPlayerArray[bigIndex].isBigVideoPlayer = false;
        }
      }
    }
  };
  this._clickFuncSubscribeClickHighlightPlayerEvent = function (e) {
    var element = e.target;
    var videoPlayerArray = that._videoPlayers.values;
    var lastHighlightedVideoPlayerIndex = that._highlightedVideoPlayerIndex;
    var newHighlightedVideoPlayerIndex;
    for (var i = 0; i < that._maxVideoCount; i++) {
      let videoPlayer = videoPlayerArray[i];
      if (videoPlayer) {
        if (videoPlayer.container && videoPlayer.container.contains(element)) {
          videoPlayer.highlight = true;
          newHighlightedVideoPlayerIndex = i;
        } else {
          videoPlayer.highlight = false;
        }
      }
    }
    // console.log(newHighlightedVideoPlayerIndex, lastHighlightedVideoPlayerIndex)
    if (!defined(newHighlightedVideoPlayerIndex) && defined(lastHighlightedVideoPlayerIndex)) {
      videoPlayerArray[lastHighlightedVideoPlayerIndex].highlight = true;
    } else if (defined(newHighlightedVideoPlayerIndex)) {
      that._highlightedVideoPlayerIndex = newHighlightedVideoPlayerIndex;
    }
  };

  // 鼠标事件
  if (FeatureDetection.supportsPointerEvents()) {
    // 双击全屏事件
    if (this.subscribeDoubleClickFullScreenEvent === true) {
      // document.addEventListener('dblclick', this._toggleFullScreen, false);
    }
    // 单击交换位置
    this.subscribeClickSwitchPlayerEvent = defaultValue(options.subscribeClickSwitchPlayerEvent, false);
    if (this.subscribeClickSwitchPlayerEvent === true) {
      document.addEventListener('click', this._clickFuncSubscribeClickSwitchPlayerEvent, false);
    }
    // 单击高亮播放器
    this.subscribeClickHighlightPlayerEvent = defaultValue(options.subscribeClickHighlightPlayerEvent, false);
    if (this.subscribeClickHighlightPlayerEvent === true) {
      this._videoPlayers.values[0].highlight = true;
      this._highlightedVideoPlayerIndex = 0;
      document.addEventListener('click', this._clickFuncSubscribeClickHighlightPlayerEvent, false);
    }
  }

  this._showAllCameraSpeedDomeWidget = defaultValue(options.showAllCameraSpeedDomeWidget, false);
  if (this._showAllCameraSpeedDomeWidget === true) {
    this.setAllCameraSpeedDomeWidgetVisible();
  }

  var errorTipsElement = document.createElement('div');
  errorTipsElement.style = 'position: absolute; top: 48%; left: 0; right: 0; pointer-events: none; color: red; z-index: 999999999999999';
  document.body.appendChild(errorTipsElement);
  this._errorTipsElement = errorTipsElement;

  this._initShowTipsWidgets();
}

defineProperties(VideoPlayerGallery.prototype, {
  /**
   * Gets the parent container.
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Element}
   */
  container: {
    get: function () {
      return this._container;
    }
  },

  /**
   * 模式
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {String}
   */
  mode: {
    get: function () {
      return this._mode;
    }
  },

  /**
   * 模式
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {String|undefined}
   */
  fullScreenPosition: {
    get: function () {
      return this._fullScreenPosition;
    }
  },

  /**
   * 当前屏数
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Number}
   */
  currentPlayerCount: {
    get: function () {
      if (this._mode.name === '1big7small-leftTop') {
        return 8;
      } else if (this._mode.name === '3big4small-leftTop') {
        return 7;
      } else if (this._mode.name === '1big5small-leftTop') {
        return 6;
      } else if (this._mode.name === '4balance-leftTop') {
        return 4;
      } else if (this._mode.name === '2TopBottom-Top') {
        return 2;
      } else if (this._mode.name === '8balanceTopBottom-Top') {
        return 8;
      } else if (this._mode.name === '1big-full') {
        return 1;
      } else if (this._mode.name === '5leftRight-left') {
        return 5;
      } else if (this._mode.name === '9balance-leftTop') {
        return 9;
      } else if (this._mode.name === '16balance-leftTop') {
        return 16;
      }
    }
  },

  /**
   * 是否是单屏
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Number}
   */
  isSingleScreen: {
    get: function () {
      return this.fullScreenVisible === true || this.mode === '1x1';
    }
  },

  /**
   * 大的主屏幕的元素
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Element}
   */
  bigContainer: {
    get: function () {
      if (defined(this._bigContainer)) {
        return this._bigContainer;
      } else {
        return getElement(this._videoElementIdPrefix + 0);
      }
    }
  },

  /**
   * 大的主屏幕的index
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Element}
   */
  bigIndex: {
    get: function () {
      if (defined(this._bigIndex)) {
        return this._bigIndex;
      } else {
        return 0;
      }
    }
  },

  /**
   * 主屏幕的videoPlayer
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Element}
   */
  bigVideoPlayer: {
    get: function () {
      return this._videoPlayers.get(this._bigIndex);
    }
  },

  /**
   * 摄像头索引
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Element}
   */
  channelIdIndexCollection: {
    get: function () {
      return this._indexCollection.cameraAssociativeArray;
    }
  },

  /**
   * 最大视频数
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {Number}
   */
  maxVideoCount: {
    get: function () {
      return this._maxVideoCount;
    }
  },

  /**
   * 播放器的类型
   * @memberof VideoPlayerGallery.prototype
   *
   * @type {String}
   */
  playerType: {
    get: function () {
      return this._playerType;
    }
  }
});

/**
 * 获取全部布局模板参数
 * @private
 * @return {Array.<Object>}
 */
VideoPlayerGallery.prototype._getAllTemplates = function () {
  var modeArray = [
    {
      videoNumber: 8,
      templateName: 'videoPlayer-template-1big7small-leftTop',
      name: '1big7small-leftTop',
      displayName: '8分屏'
    },
    {
      videoNumber: 7,
      templateName: 'videoPlayer-template-3big4small-leftTop',
      name: '3big4small-leftTop',
      displayName: '7分屏'
    },
    {
      videoNumber: 6,
      templateName: 'videoPlayer-template-1big5small-leftTop',
      name: '1big5small-leftTop',
      displayName: '6分屏'
    },
    {
      videoNumber: 4,
      templateName: 'videoPlayer-template-4balance-leftTop',
      name: '4balance-leftTop',
      displayName: '4分屏'
    },
    {
      videoNumber: 2,
      templateName: 'videoPlayer-template-2TopBottom-Top',
      name: '2TopBottom-Top',
      displayName: '上下2分屏'
    },
    {
      videoNumber: 8,
      templateName: 'videoPlayer-template-8balanceTopBottom-Top',
      name: '8balanceTopBottom-Top',
      displayName: '上下8分屏'
    },
    {
      videoNumber: 1,
      templateName: 'videoPlayer-template-1big-full',
      name: '1big-full',
      displayName: '单主屏'
    },
    {
      videoNumber: 5,
      templateName: 'videoPlayer-template-5leftRight-left',
      name: '5leftRight-left',
      displayName: '左右5分屏'
    },
    {
      videoNumber: 9,
      templateName: 'videoPlayer-template-9balance-leftTop',
      name: '9balance-leftTop',
      displayName: '9分屏'
    },
    {
      videoNumber: 16,
      templateName: 'videoPlayer-template-16balance-leftTop',
      name: '16balance-leftTop',
      displayName: '16分屏'
    }
  ];
  return modeArray;
};

/**
 * 初始化播放器
 * @param options
 * @private
 */
VideoPlayerGallery.prototype._initPlayer = function (options) {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(this._mode)) {
    throw new DeveloperError('this._mode is required.');
  }
  if (!defined(this._container)) {
    throw new DeveloperError('this._container is required.');
  }
  if (!defined(this._videoElementIdPrefix)) {
    throw new DeveloperError('this._videoElementIdPrefix is required.');
  }
  if (!defined(this._videoPlayerElementIdPrefix)) {
    throw new DeveloperError('this._videoPlayerElementIdPrefix is required.');
  }
  if (!defined(this._videoPlayerIdPrefix)) {
    throw new DeveloperError('this._videoPlayerIdPrefix is required.');
  }
  // >>includeEnd('debug');

  // init janus
  if ((this._playerType === 'VideoPlayerRTSP' && this._forceUseVideoPlayer !== 'VideoPlayerWebRTCStreamer') ||
    this._playerType === 'VideoPlayerRTP') {
    this._janusInit = new JanusInit();
  }

  for (var i = 0; i < this._maxVideoCount; i++) {
    var videoContainer = this._container;
    // 播放器的位置容器
    var videoElement = document.createElement('div');
    videoElement.id = this._videoElementIdPrefix + i;
    // 播放器的直接承载容器
    var videoPlayerElement = document.createElement('div');
    videoPlayerElement.id = this._videoPlayerElementIdPrefix + i;
    videoElement.appendChild(videoPlayerElement);
    videoContainer.appendChild(videoElement);
    if (i === 0) {
      videoPlayerElement.className = 'videoPlayerBig';
    } else {
      videoPlayerElement.className = 'videoPlayerSmall';
    }

    if (this._playerType === 'VideoPlayerRTMP') {
      // 这里采用flash播放器
      let videoPlayerRTMP = new VideoPlayerRTMP({
        container: videoPlayerElement,
        id: this._videoPlayerIdPrefix + i,
        parentGallery: this
      });
      videoPlayerRTMP._index = i;
      this._videoPlayers.set(i, videoPlayerRTMP);
    } else if (this._playerType === 'VideoPlayerRTSP') {
      if (defined(this._forceUseVideoPlayer) && this._forceUseVideoPlayer === 'VideoPlayerWebRTCStreamer') {
        // var videoPlayerWebRTCStreamer = new VideoPlayerWebRTCStreamer({
        //     container: videoPlayerElement,
        //     id: this._videoPlayerIdPrefix + i,
        //     parentGallery: this,
        //     protocol: 'RTSP'
        // });
        // videoPlayerWebRTCStreamer._index = i;
        // this._videoPlayers.set(i, videoPlayerWebRTCStreamer);
      } else {
        // 这里采用webRTC-janus播放器
        let videoPlayerJanusRTSP = new VideoPlayerJanus({
          container: videoPlayerElement,
          id: this._videoPlayerIdPrefix + i,
          parentGallery: this,
          janusInit: this._janusInit,
          protocol: 'RTSP',
          ignoreGb28181ForWebRTC: this._ignoreGb28181ForJanusWebRTC,
          defaultStream: options.defaultStream
        });
        videoPlayerJanusRTSP._index = i;
        this._videoPlayers.set(i, videoPlayerJanusRTSP);
      }
    } else if (this._playerType === 'VideoPlayerRTP') {
      // 这里采用webRTC-janus播放器
      let videoPlayerJanusRTP = new VideoPlayerJanus({
        container: videoPlayerElement,
        id: this._videoPlayerIdPrefix + i,
        parentGallery: this,
        janusInit: this._janusInit,
        protocol: 'RTP'
        // protocol: 'AUTO',
      });
      videoPlayerJanusRTP._index = i;
      this._videoPlayers.set(i, videoPlayerJanusRTP);
    } else if (this._playerType === 'VideoPlayerFLV') {
      if (defined(this._forceUseVideoPlayer) && this._forceUseVideoPlayer === 'VideoPlayerEasyPlayer') {
        // 这里采用EasyPlayerWasm技术播放器
        let videoPlayerEasyPlayer = new VideoPlayerEasyPlayer({
          container: videoPlayerElement,
          id: this._videoPlayerIdPrefix + i,
          parentGallery: this,
          protocol: 'WS_FLV'
        });
        videoPlayerEasyPlayer._index = i;
        this._videoPlayers.set(i, videoPlayerEasyPlayer);
      } else {
        // 这里采用bilibiliFLV播放器
        let videoPlayerFLV = new VideoPlayerFLV({
          container: videoPlayerElement,
          id: this._videoPlayerIdPrefix + i,
          parentGallery: this,
          protocol: 'WS_FLV',
          defaultStreamType: options.defaultStreamType,
          subscribeDoubleClickFullScreenEvent: this._subscribeDoubleClickFullScreenEvent,
          forPlayback: this._forPlayback,
          customSetup: this._customSetup,
          index: i
        });
        this._videoPlayers.set(i, videoPlayerFLV);
      }
    } else if (this._playerType === 'VideoPlayerNodePlayer') {
      // 这里采用NodePlayer播放器
      let videoPlayerNodePlayer = new VideoPlayerNodePlayer({
        container: videoPlayerElement,
        id: this._videoPlayerIdPrefix + i,
        parentGallery: this,
        protocol: 'WS_FLV',
        defaultStreamType: options.defaultStreamType,
        subscribeDoubleClickFullScreenEvent: this._subscribeDoubleClickFullScreenEvent,
        forPlayback: this._forPlayback,
        customSetup: this._customSetup,
        index: i
      });
      this._videoPlayers.set(i, videoPlayerNodePlayer);
    } else if (this._playerType === 'VideoPlayerHtml5') {
      let videoPlayerHtml5 = new VideoPlayerHtml5({
        container: videoPlayerElement,
        id: this._videoPlayerIdPrefix + i,
        parentGallery: this
      });
      videoPlayerHtml5._index = i;
      this._videoPlayers.set(i, videoPlayerHtml5);
    }
  }
};

/**
 * 初始化较大的主视频的参数
 * @private
 */
VideoPlayerGallery.prototype._initBigContainer = function () {
  var bigIndex = 0;
  var bigContainer = getElement(this._videoElementIdPrefix + bigIndex);
  this._bigContainer = bigContainer;
  this._bigIndex = bigIndex;
};

/**
 * 初始化布局模板
 * @private
 * @description 模式命名规则示例：1big5small-leftTop 1big5small为布局名 leftTop为主屏
 *
 * @exception {DeveloperError} this._container is required.
 * @exception {DeveloperError} this._videoElementIdPrefix is required.
 * @exception {DeveloperError} this._mode is required.
 */
VideoPlayerGallery.prototype._initTemplate = function () {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(this._container)) {
    throw new DeveloperError('this._container is required.');
  }
  if (!defined(this._videoElementIdPrefix)) {
    throw new DeveloperError('this._videoElementIdPrefix is required.');
  }
  if (!defined(this._mode)) {
    throw new DeveloperError('this._mode is required.');
  }
  // >>includeEnd('debug');
  this._container.classList.add(this._mode.templateName);
  var i;
  if (this._mode.name === '1big7small-leftTop') {
    for (i = 0; i < 8; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column3-row3');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left3-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left3-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left3-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 4).classList.add('videoPlayer-left3-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 5).classList.add('videoPlayer-left2-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 6).classList.add('videoPlayer-left1-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 7).classList.add('videoPlayer-left0-top3-column1-row1');
    this._initCollapsedVideoPlay(8);
  } else if (this._mode.name === '3big4small-leftTop') {
    for (i = 0; i < 7; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column2-row2');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left2-top0-column2-row2');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left0-top2-column2-row2');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left2-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 4).classList.add('videoPlayer-left3-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 5).classList.add('videoPlayer-left2-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 6).classList.add('videoPlayer-left3-top3-column1-row1');
    this._initCollapsedVideoPlay(7);
  } else if (this._mode.name === '1big5small-leftTop') {
    for (i = 0; i < 6; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column2-row2');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left2-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left2-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left2-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 4).classList.add('videoPlayer-left1-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 5).classList.add('videoPlayer-left0-top2-column1-row1');
    this._initCollapsedVideoPlay(6);
  } else if (this._mode.name === '4balance-leftTop') {
    for (i = 0; i < 4; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left1-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left1-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left0-top1-column1-row1');
    this._initCollapsedVideoPlay(4);
  } else if (this._mode.name === '2TopBottom-Top') {
    for (i = 0; i < 2; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left0-top1-column1-row1');
    this._initCollapsedVideoPlay(2);
  } else if (this._mode.name === '8balanceTopBottom-Top') {
    for (i = 0; i < 8; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left1-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left0-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left1-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 4).classList.add('videoPlayer-left0-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 5).classList.add('videoPlayer-left1-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 6).classList.add('videoPlayer-left0-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 7).classList.add('videoPlayer-left1-top3-column1-row1');
    this._initCollapsedVideoPlay(8);
  } else if (this._mode.name === '1big-full') {
    for (i = 0; i < 1; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column1-row1');
    this._initCollapsedVideoPlay(1);
  } else if (this._mode.name === '5leftRight-left') {
    for (i = 0; i < 5; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left1-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left2-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left3-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 4).classList.add('videoPlayer-left4-top0-column1-row1');
    this._initCollapsedVideoPlay(5);
  } else if (this._mode.name === '9balance-leftTop') {
    for (i = 0; i < 9; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left1-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left2-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left2-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 4).classList.add('videoPlayer-left1-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 5).classList.add('videoPlayer-left0-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 6).classList.add('videoPlayer-left0-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 7).classList.add('videoPlayer-left1-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 8).classList.add('videoPlayer-left2-top2-column1-row1');
    this._initCollapsedVideoPlay(9);
  } else if (this._mode.name === '16balance-leftTop') {
    for (i = 0; i < 16; i++) {
      getElement(this._videoElementIdPrefix + i).className = (this._videoPlayerPositionClassNamePrefix + i);
    }
    getElement(this._videoElementIdPrefix + 0).classList.add('videoPlayer-left0-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 1).classList.add('videoPlayer-left1-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 2).classList.add('videoPlayer-left2-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 3).classList.add('videoPlayer-left3-top0-column1-row1');
    getElement(this._videoElementIdPrefix + 4).classList.add('videoPlayer-left3-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 5).classList.add('videoPlayer-left2-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 6).classList.add('videoPlayer-left1-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 7).classList.add('videoPlayer-left0-top1-column1-row1');
    getElement(this._videoElementIdPrefix + 8).classList.add('videoPlayer-left0-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 9).classList.add('videoPlayer-left1-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 10).classList.add('videoPlayer-left2-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 11).classList.add('videoPlayer-left3-top2-column1-row1');
    getElement(this._videoElementIdPrefix + 12).classList.add('videoPlayer-left3-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 13).classList.add('videoPlayer-left2-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 14).classList.add('videoPlayer-left1-top3-column1-row1');
    getElement(this._videoElementIdPrefix + 15).classList.add('videoPlayer-left0-top3-column1-row1');
    this._initCollapsedVideoPlay(16);
  }
};

/**
 * 切换视频布局模板
 * @exception {DeveloperError} this._container is required.
 * @exception {DeveloperError} this._videoElementIdPrefix is required.
 * @exception {DeveloperError} this._mode is required.
 * @private
 */
VideoPlayerGallery.prototype._switchTemplate = function () {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(this._container)) {
    throw new DeveloperError('this._container is required.');
  }
  if (!defined(this._videoElementIdPrefix)) {
    throw new DeveloperError('this._videoElementIdPrefix is required.');
  }
  if (!defined(this._mode)) {
    throw new DeveloperError('this._mode is required.');
  }
  // >>includeEnd('debug');
  var videoPlayerPositionClassNamePrefix = this._videoPlayerPositionClassNamePrefix;
  this._container.classList.add(this._mode.templateName);
  if (this._mode.name === '1big7small-leftTop') {
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column3-row3' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left3-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left3-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left3-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 4)[0].className = 'videoPlayer-left3-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 4);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 5)[0].className = 'videoPlayer-left2-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 5);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 6)[0].className = 'videoPlayer-left1-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 6);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 7)[0].className = 'videoPlayer-left0-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 7);
    this._setCollapsedVideoPlay(8);
  } else if (this._mode.name === '3big4small-leftTop') {
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column2-row2' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left2-top0-column2-row2' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left0-top2-column2-row2' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left2-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 4)[0].className = 'videoPlayer-left3-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 4);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 5)[0].className = 'videoPlayer-left2-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 5);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 6)[0].className = 'videoPlayer-left3-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 6);
    this._setCollapsedVideoPlay(7);
  } else if (this._mode.name === '1big5small-leftTop') {
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column2-row2' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left2-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left2-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left2-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 4)[0].className = 'videoPlayer-left1-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 4);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 5)[0].className = 'videoPlayer-left0-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 5);
    this._setCollapsedVideoPlay(6);
  } else if (this._mode.name === '4balance-leftTop') {
    this._container.classList.add(this._mode.templateName);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left1-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left1-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left0-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._setCollapsedVideoPlay(4);
  } else if (this._mode.name === '2TopBottom-Top') {
    this._container.classList.add(this._mode.templateName);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left0-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._setCollapsedVideoPlay(2);
  } else if (this._mode.name === '8balanceTopBottom-Top') {
    this._container.classList.add(this._mode.templateName);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left1-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left0-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left1-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 4)[0].className = 'videoPlayer-left0-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 4);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 5)[0].className = 'videoPlayer-left1-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 5);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 6)[0].className = 'videoPlayer-left0-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 6);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 7)[0].className = 'videoPlayer-left1-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 7);
    this._setCollapsedVideoPlay(8);
  } else if (this._mode.name === '1big-full') {
    this._container.classList.add(this._mode.templateName);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._setCollapsedVideoPlay(1);
  } else if (this._mode.name === '5leftRight-left') {
    this._container.classList.add(this._mode.templateName);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left1-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left2-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left3-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 4)[0].className = 'videoPlayer-left4-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 4);
    this._setCollapsedVideoPlay(5);
  } else if (this._mode.name === '9balance-leftTop') {
    this._container.classList.add(this._mode.templateName);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left1-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left2-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left2-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 4)[0].className = 'videoPlayer-left1-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 4);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 5)[0].className = 'videoPlayer-left0-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 5);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 6)[0].className = 'videoPlayer-left0-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 6);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 7)[0].className = 'videoPlayer-left1-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 7);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 8)[0].className = 'videoPlayer-left2-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 8);
    this._setCollapsedVideoPlay(9);
  } else if (this._mode.name === '16balance-leftTop') {
    this._container.classList.add(this._mode.templateName);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 0)[0].className = 'videoPlayer-left0-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 0);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 1)[0].className = 'videoPlayer-left1-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 1);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 2)[0].className = 'videoPlayer-left2-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 2);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 3)[0].className = 'videoPlayer-left3-top0-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 3);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 4)[0].className = 'videoPlayer-left3-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 4);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 5)[0].className = 'videoPlayer-left2-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 5);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 6)[0].className = 'videoPlayer-left1-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 6);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 7)[0].className = 'videoPlayer-left0-top1-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 7);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 8)[0].className = 'videoPlayer-left0-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 8);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 9)[0].className = 'videoPlayer-left1-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 9);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 10)[0].className = 'videoPlayer-left2-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 10);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 11)[0].className = 'videoPlayer-left3-top2-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 11);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 12)[0].className = 'videoPlayer-left3-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 12);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 13)[0].className = 'videoPlayer-left2-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 13);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 14)[0].className = 'videoPlayer-left1-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 14);
    this._container.getElementsByClassName(videoPlayerPositionClassNamePrefix + 15)[0].className = 'videoPlayer-left0-top3-column1-row1' + ' ' + (videoPlayerPositionClassNamePrefix + 15);
    this._setCollapsedVideoPlay(16);
  }

  this._switchTemplateData = {// @see VideoPlayerGallery#subscribeVideoUrlChangeEvent
    templateName: this._mode.templateName
  };
};

/**
 * 初始化默认所有播放器都加载球机控制器
 */
VideoPlayerGallery.prototype.setAllCameraSpeedDomeWidgetVisible = function () {
  for (var i = 0; i < this._maxVideoCount; i++) {
    var videoPlayer = this._videoPlayers.get(i);
    // console.log(videoPlayer);
    videoPlayer.cameraSpeedDomeWidgetVisible = true;
  }
};

/**
 * 禁止所有球机zoom事件
 */
VideoPlayerGallery.prototype.forbidAllMouseZoomEvent = function () {
  for (var i = 0; i < this._maxVideoCount; i++) {
    var videoPlayer = this._videoPlayers.get(i);
    // console.log(videoPlayer);
    videoPlayer.cameraSpeedDomeWidget.forbidMouseZoomEvent();
  }
};

/**
 * 取消禁止所有球机zoom事件
 */
VideoPlayerGallery.prototype.cancelForbidAllMouseZoomEvent = function () {
  for (var i = 0; i < this._maxVideoCount; i++) {
    var videoPlayer = this._videoPlayers.get(i);
    videoPlayer.cameraSpeedDomeWidget.cancelForbidMouseZoomEvent();
  }
};

/**
 * 根据初始化的index获取
 * @description 仅仅用于遍历
 * @param {Number} index 从0到maxVideoCount
 * @returns {VideoPlayerRTMP|undefined}
 */
VideoPlayerGallery.prototype.get = function (index) {
  return this._videoPlayers.get(index);
};

/**
 * 根据channelId获取播放器对象
 * @param {String} channelId 20位唯一id
 * @return {undefined|VideoPlayerRTMP}
 */
VideoPlayerGallery.prototype.getPlayerByChannelId = function (channelId) {
  for (var i = 0; i < this._maxVideoCount; i++) {
    var videoPlayer = this.get(i);
    if (defined(videoPlayer) && videoPlayer.channelID === channelId) {
      return videoPlayer;
    }
  }
  return undefined;
};

/**
 * 清除所有播放器的画布
 * @description 画布是提供给外部的模块用于渲染一些比如嫌疑人人体框的东西
 */
VideoPlayerGallery.prototype.clearDrawCanvasOfAllVideoPlayers = function () {
  for (var i = 0; i < this._maxVideoCount; i++) {
    var videoPlayer = this.get(i);
    if (defined(videoPlayer)) {
      var drawCanvas = videoPlayer.drawCanvas;
      drawCanvas.getContext('2d').clearRect(0, 0, drawCanvas.width, drawCanvas.height);
    }
  }
};

/**
 * 重新设置画布的大小并清除所有播放器的画布
 * @description 画布是提供给外部的模块用于渲染一些比如嫌疑人人体框的东西
 */
VideoPlayerGallery.prototype.resizeAndClearDrawCanvasOfAllVideoPlayers = function () {
  for (var i = 0; i < this._maxVideoCount; i++) {
    var videoPlayer = this.get(i);
    if (defined(videoPlayer)) {
      videoPlayer.resizeCanvas();
    }
  }
};

/**
 * 是不是主视频
 * @param {VideoPlayerRTMP} videoPlayer
 * @returns {Boolean}
 */
VideoPlayerGallery.prototype.isBigVideoPlayer = function (videoPlayer) {
  return videoPlayer._index === this._bigIndex;
};

/**
 * 获取所有的范围内的分屏模式
 * @param {Number} min 最小分屏
 * @param {Number} max 最大分屏
 * @return {Array}
 */
VideoPlayerGallery.prototype.getAllTemplatesWithinRange = function (min, max) {
  var tempArray = this._getAllTemplates();
  var out = [];
  for (var i = 0; i < tempArray.length; i++) {
    if (tempArray[i].videoNumber >= min && tempArray[i].videoNumber <= max) {
      out.push(tempArray[i]);
    }
  }
  return out;
};

/**
 * 将指定channelId的视频切换到主视频播放器上
 * @param {String} channelId 20位唯一id
 */
VideoPlayerGallery.prototype.setBigVideoPlayerByChannelId = function (channelId) {
  var that = this;
  var videoPlayerArray = that._videoPlayers.values;
  for (var i = 0; i < that._maxVideoCount; i++) {
    if (videoPlayerArray[i].channelID === channelId) {
      if (videoPlayerArray[i].container.className === 'videoPlayerSmall') {
        var currentContainer = getElement(that._videoElementIdPrefix + i);
        var currentContainerClassName = currentContainer.className;
        var bigIndex = that._bigIndex;
        var bigContainer = getElement(that._videoElementIdPrefix + bigIndex);
        var bigContainerClassName = bigContainer.className;

        currentContainer.className = bigContainerClassName;
        bigContainer.className = currentContainerClassName;

        videoPlayerArray[i].container.className = 'videoPlayerBig';
        videoPlayerArray[bigIndex].container.className = 'videoPlayerSmall';

        that._bigContainer = currentContainer;
        that._bigIndex = i;
      }
    }
  }
};

/**
 * 点击播放器
 * @param {Event} e
 * @return {undefined|VideoPlayerRTMP}
 */
VideoPlayerGallery.prototype.clickVideoPlayer = function (e) {
  var that = this;
  var element = e.target;
  var videoPlayerArray = that._videoPlayers.values;
  for (var i = 0; i < that._maxVideoCount; i++) {
    if (videoPlayerArray[i].container.contains(element)) {
      return videoPlayerArray[i];
    }
  }
  return undefined;
};

/**
 * 初始化展示错误的控件
 * @private
 */
VideoPlayerGallery.prototype._initShowTipsWidgets = function () {
  var errorTipsElement = document.createElement('div');
  errorTipsElement.style = 'position: absolute; top: 48%; left: 0; right: 0; pointer-events: none; color: red; z-index: 999999999999999';
  this._container.appendChild(errorTipsElement);
  this._errorTipsElement = errorTipsElement;
  this._showTipsWidget = new ShowTipsWidget({
    container: errorTipsElement
  });
};

/**
 * 展示错误
 * @param {String} msg
 * @description 5秒动画时间
 */
VideoPlayerGallery.prototype.showTipsError = function (msg) {
  var message;
  if (!defined(msg)) {
    console.warn('VideoPlayerGallery#showTipsError:缺失错误信息');
    message = '缺失错误信息';
  } else {
    message = msg;
  }
  var that = this;
  this._showTipsWidget.show(message, 'fx2');
  setTimeout(function () {
    that._showTipsWidget.hide('fx3');
  }, 3000);
};

/**
 * 保存播放器的状态
 * @param [projectName=''] 加一个前缀方便区分不同的项目
 *
 * @see VideoPlayerGallery#initLocalStorageIfPossible
 */
VideoPlayerGallery.prototype.saveToLocalStorageIfPossible = function (projectName) {
  var channelIdList = [];
  var i;
  for (i = 0; i < this._mode.videoNumber; i++) {
    var videoPlayer = this.queryPlayerByPositionIndex(i);
    if (defined(videoPlayer)) {
      if (defined(videoPlayer.channelID)) {
        channelIdList.push(videoPlayer.channelID);
      }
    }
  }
  projectName = defaultValue(projectName, '');
  localStorage.setItem(projectName + 'videoPlayerGalleryLastMode', JSON.stringify({
    mode: this._mode,
    channelIdList: channelIdList
  }));
};

/**
 * 通过缓存恢复播放器的状态
 * @description 这个函数只有在click事件后调用才有效
 * @param {String} [projectName=''] 加一个前缀方便区分不同的项目
 * @param {Boolean} [useMode=true] 是否将视频分屏模式模式也恢复出来
 *
 * @see VideoPlayerGallery#saveToLocalStorageIfPossible
 * @returns {Object|undefined}
 */
VideoPlayerGallery.prototype.initLocalStorageIfPossible = function (projectName, useMode) {
  // console.log('VideoPlayerGallery#VideoPlayerGallery-->', projectName);
  projectName = defaultValue(projectName, '');
  useMode = defaultValue(useMode, true);
  var videoPlayerGalleryLastMode;
  var videoPlayerGalleryLastModeString = localStorage.getItem(projectName + 'videoPlayerGalleryLastMode');
  if (videoPlayerGalleryLastModeString !== null) {
    videoPlayerGalleryLastMode = JSON.parse(videoPlayerGalleryLastModeString);
  }

  // console.log('VideoPlayerGallery#initLocalStorageIfPossible', videoPlayerGalleryLastMode);
  if (defined(videoPlayerGalleryLastMode)) {
    if (defined(videoPlayerGalleryLastMode.mode) && useMode) {
      this.switchMode(videoPlayerGalleryLastMode.mode);
    }
    if (defined(videoPlayerGalleryLastMode.channelIdList)) {
      for (var i = 0; i < videoPlayerGalleryLastMode.channelIdList.length; i++) {
        var channelId = videoPlayerGalleryLastMode.channelIdList[i];
        let camera = this._indexCollection.cameraAssociativeArray.get(channelId);
        // console.log('VideoPlayerGallery#initLocalStorageIfPossible#camera', camera);
        if (defined(camera)) {
          let videoPlayer = this._videoPlayers.get(i);
          videoPlayer.reloadChannelID({
            customDatachannelID: camera.busiRealUid,
            callback: function () {
              videoPlayer.videoPlayerToolBarVisible = false;
              videoPlayer.cameraSpeedDomeWidgetVisible = false;
            }
          });
        }
      }
    }
    return videoPlayerGalleryLastMode.mode;
  }
};

/**
 * 获取缓存的上次结束时播放的视频
 * @param [projectName=''] 加一个前缀方便区分不同的项目
 * @return {{mode: Object, channelIdList: Array}|undefined}
 */
VideoPlayerGallery.prototype.getLocalStorageIfPossible = function (projectName) {
  projectName = defaultValue(projectName, '');
  var videoPlayerGalleryLastMode;
  var videoPlayerGalleryLastModeString = localStorage.getItem(projectName + 'videoPlayerGalleryLastMode');
  if (videoPlayerGalleryLastModeString !== null) {
    videoPlayerGalleryLastMode = JSON.parse(videoPlayerGalleryLastModeString);
  }

  if (defined(videoPlayerGalleryLastMode)) {
    return videoPlayerGalleryLastMode;
  }
};

/**
 * 初始化视频
 * @param videoPlayerGalleryLastMode
 * @param {Object} videoPlayerGalleryLastMode.mode
 * @param {Array} videoPlayerGalleryLastMode.channelIdList
 * @param {Object} [options]
 * @param {Boolean} [options.videoPlayerToolBarVisible=false] 是否显示摄像头可选项
 * @param {Boolean} [options.cameraSpeedDomeWidgetVisible=false] 是否显示球机，true时如果摄像头为球机则显示球机控件
 *
 * @return {Object} 模式
 */
VideoPlayerGallery.prototype.initVideoModeAndChannel = function (videoPlayerGalleryLastMode, options) {
  console.log('VideoPlayerGallery#initVideoModeAndChannel', videoPlayerGalleryLastMode);
  options = defaultValue(options, {});
  options.videoPlayerToolBarVisible = defaultValue(options.videoPlayerToolBarVisible, false);
  options.cameraSpeedDomeWidgetVisible = defaultValue(options.cameraSpeedDomeWidgetVisible, false);
  if (defined(videoPlayerGalleryLastMode)) {
    if (defined(videoPlayerGalleryLastMode.mode)) {
      this.switchMode(videoPlayerGalleryLastMode.mode);
    }
    if (defined(videoPlayerGalleryLastMode.channelIdList)) {
      for (var i = 0; i < videoPlayerGalleryLastMode.channelIdList.length; i++) {
        var channelId = videoPlayerGalleryLastMode.channelIdList[i];
        // console.log(channelId)
        // console.log(this._indexCollection.cameraAssociativeArray)
        let camera = this._indexCollection.cameraAssociativeArray.get(channelId);
        console.log('VideoPlayerGallery#initVideoModeAndChannel#camera', camera);
        if (defined(camera)) {
          let videoPlayer = this._videoPlayers.get(i);
          videoPlayer.reloadChannelID({
            channelID: camera.busiRealUid,
            callback: function () {
              // console.log(camera)
              videoPlayer.videoPlayerToolBarVisible = options.videoPlayerToolBarVisible;
              if (camera.cameraType === CameraTypeEnum.SPEED_DOME) {
                videoPlayer.cameraSpeedDomeWidgetVisible = options.cameraSpeedDomeWidgetVisible;
              }
            }
          });
        }
      }
    }
    return videoPlayerGalleryLastMode.mode;
  }
};

/**
 * 分屏模式切换
 * @param {Object} mode 模式
 * @param {String} mode.name 名字
 */
VideoPlayerGallery.prototype.switchMode = function (mode) {
  console.log('切换到：', mode);
  if (!defined(mode) || mode === this._mode) {
    return false;
  }
  this._container.classList.remove(this._mode.templateName);
  this._mode = mode;
  this._switchTemplate();
  return true;
};

/**
 * 根据鼠标事件提取的DOM元素索引对应的播放器
 * @param {Element} element
 * @returns {VideoPlayerRTMP|undefined}
 */
VideoPlayerGallery.prototype.queryPlayerByElement = function (element) {
  for (var i = 0; i < this._videoPlayers.length; i++) {
    var videoPlayer = this._videoPlayers.values[i];
    if (videoPlayer.container.contains(element)) {
      return videoPlayer;
    }
  }
  return undefined;
};

/**
 * 根据鼠标事件提取的DOM元素索引对应的播放器
 * @param {Number} index 0到maxVideoCount
 * @returns {VideoPlayerRTMP|undefined}
 */
VideoPlayerGallery.prototype.queryPlayerByPositionIndex = function (index) {
  var positionElement = this._container.getElementsByClassName(this._videoPlayerPositionClassNamePrefix + index)[0];
  for (var i = 0; i < this._videoPlayers.length; i++) {
    var videoPlayer = this._videoPlayers.values[i];
    if (positionElement.contains(videoPlayer.container)) {
      return videoPlayer;
    }
  }
  return undefined;
};

/**
 * 订阅视频流地址变化事件
 * @param {Function} callback
 * @description [{index: '2', channelID: '4216731562842361784231'}]
 */
VideoPlayerGallery.prototype.subscribeVideoUrlChangeEvent = function (callback) {
  var i;
  for (i = 0; i < this._maxVideoCount; i++) {
    this._videoPlayers.get(i).subscribeVideoUrlChangeEvent(function (videoUrl) { // 任意一个播放器切换视频的时候
      callback(getNewChannelIdList());
    });
  }

  if (this._hasSubscribeSwitchTemplateData !== true) {
    knockout.track(this, ['_switchTemplateData']);
    this._hasSubscribeSwitchTemplateData = true;
  }
  knockout.getObservable(this, '_switchTemplateData').subscribe(function (data) { // 切换播放器的屏幕数的时候
    callback(getNewChannelIdList());
  });

  var that = this;
  function getNewChannelIdList () {
    return that.getPlayerChannelList();
  }
};

/**
 * 订阅视频流通道ID变化事件
 * @param {Function} callback
 * @description [{index: '2', channelID: '4216731562842361784231'}]
 */
VideoPlayerGallery.prototype.subscribeChannelIdChangeEvent = function (callback) {
  var i;
  for (i = 0; i < this._maxVideoCount; i++) {
    this._videoPlayers.get(i).subscribeChannelIdChangeEvent(function (channelID) { // 任意一个播放器切换视频的时候
      callback(getNewChannelIdList());
    });
  }

  if (this._hasSubscribeSwitchTemplateData !== true) {
    knockout.track(this, ['_switchTemplateData']);
    this._hasSubscribeSwitchTemplateData = true;
  }
  knockout.getObservable(this, '_switchTemplateData').subscribe(function (data) { // 切换播放器的屏幕数的时候
    callback(getNewChannelIdList());
  });

  var that = this;
  function getNewChannelIdList () {
    return that.getPlayerChannelList();
  }
};

/**
 * 获取正在播放的每个播放器的通道列表
 */
VideoPlayerGallery.prototype.getPlayerChannelList = function () {
  var i;
  var out = [];
  var currentPlayerCount = this.currentPlayerCount;
  for (i = 0; i < this._maxVideoCount; i++) {
    if (i < currentPlayerCount) {
      let videoPlayer = this._videoPlayers.get(i);
      out.push({
        index: i,
        channelID: videoPlayer._channelID,
        videoUrl: videoPlayer._videoUrl,
        customData: videoPlayer._customData
      });
    } else {
      out.push({
        index: i,
        channelID: undefined,
        videoUrl: undefined,
        customData: undefined
      });
    }
  }

  return out;
};

/**
 * 暂停全部播放器
 */
VideoPlayerGallery.prototype.pauseAll = function () {
  var i;
  for (i = 0; i < this._maxVideoCount; i++) {
    this._videoPlayers.get(i).pause();
  }
};

/**
 * 重新播放全部播放器
 */
VideoPlayerGallery.prototype.playAll = function () {
  var i;
  for (i = 0; i < this._maxVideoCount; i++) {
    this._videoPlayers.get(i).play();
  }
};

/**
 * 清空所有播放器的流
 */
VideoPlayerGallery.prototype.clearAll = function () {
  var i;
  for (i = 0; i < this._maxVideoCount; i++) {
    this._videoPlayers.get(i).clearStream();
  }
};

/**
 * 订阅大屏切换事件
 * @param {Function} callback
 */
VideoPlayerGallery.prototype.subscribeBigVideoChangeEvent = function (callback) {
  if (this._hasSubscribeBigVideoChangeEvent !== true) {
    knockout.track(this, ['_bigVideoChangeData']);
    this._hasSubscribeBigVideoChangeEvent = true;
  }
  knockout.getObservable(this, '_bigVideoChangeData').subscribe(function (newValue) {
    callback(newValue);
  });
};

/**
 * 更新视频的索引
 * @param {Object} [options]
 * @description 一般来说登录后台就要执行这个函数，更新索引从而为拖拽点播服务
 */
VideoPlayerGallery.prototype.updateIndexCameraTree = function (options) {
  this._indexCollection.updateIndexCameraTree(options);
};

/**
 * @private
 * @param {Number} startIndex
 */
VideoPlayerGallery.prototype._setCollapsedVideoPlay = function (startIndex) {
  for (var i = startIndex; i < this._maxVideoCount; i++) {
    this._container.getElementsByClassName(this._videoPlayerPositionClassNamePrefix + i)[0].className = 'lux-VideoPlayerGallery-videoPlayerCollapsed' + ' ' + (this._videoPlayerPositionClassNamePrefix + i);
  }
};

/**
 * @private
 * @param {Number} startIndex
 */
VideoPlayerGallery.prototype._initCollapsedVideoPlay = function (startIndex) {
  for (var i = startIndex; i < this._maxVideoCount; i++) {
    getElement(this._videoElementIdPrefix + i).className = 'lux-VideoPlayerGallery-videoPlayerCollapsed' + ' ' + (this._videoPlayerPositionClassNamePrefix + i);
  }
};

/**
 * 直接替换的方式重载国标20位编号和URL
 * @param {Array} srcArray
 * @param {String} srcArray[].channelID
 * @param {String} srcArray[].videoUrl
 *
 * @example
 * var newArr = [];
 for(i = 0; i < 3; i++){
     newArr.push({
        channelID: streamList[i].channelID,
        videoUrl: streamList[i].RTSP
    });
 }
 VideoPlayerGallery.reloadChannelIdAndUrlList(newArr);
 */
VideoPlayerGallery.prototype.reloadChannelIdAndUrlList = function (srcArray) {
  var that = this;
  for (let i = 0; i < Math.min(srcArray.length, this.maxVideoCount); i++) {
    that._videoPlayers.values[i].reloadChannelIdAndUrl({
      channelID: srcArray[i].channelID,
      videoUrl: srcArray[i].videoUrl
    });
  }
};

/**
 * 按顺序重载视频
 * @param {Array} srcArray
 * @param {String} srcArray[].channelID
 *
 * @example
 * var newArr = [];
 for(i = 0; i < 3; i++){
     newArr.push({
        channelID: streamList[i].channelID
    });
 }
 VideoPlayerGallery.reloadChannelIdAndUrlList(newArr);
 */
VideoPlayerGallery.prototype.reloadChannelIdList = function (srcArray) {
  var that = this;
  for (let i = 0; i < Math.min(srcArray.length, this.maxVideoCount); i++) {
    that._videoPlayers.values[i].reloadChannelID({
      channelID: srcArray[i].channelID
    });
    // console.log('reloadChannelID', srcArray[i].channelID);
  }
};

/**
 * 高亮某些播放器
 * @param {String} channelID 只要是videoPlayer的channelID包含这个字符串的都认为是需要播放的
 */
VideoPlayerGallery.prototype.highlightPlayers = function (channelID) {
  if (channelID === this._channelIDHeightlightTemp) {
    return;
  }
  this._channelIDHeightlightTemp = channelID;
  let length = this._videoPlayers.length;
  for (let i = 0; i < length; i++) {
    let videoPlayer = this._videoPlayers.values[i];
    if (!defined(videoPlayer.channelID) || channelID === '') {
      videoPlayer.highlight = false;
    } else if (videoPlayer.channelID.indexOf(channelID) !== -1) {
      videoPlayer.highlight = true;
    } else {
      videoPlayer.highlight = false;
    }
  }
};

/**
 * 获取高亮的播放器
 * @return {VideoPlayerFLV|undefined}
 */
VideoPlayerGallery.prototype.getHighlightedPlayer = function () {
  if (defined(this._highlightedVideoPlayerIndex)) {
    return this.get(this._highlightedVideoPlayerIndex);
  }
};

/**
 * 获取高亮的播放器的编号
 * @return {Number|undefined}
 */
VideoPlayerGallery.prototype.getHighlightedPlayerIndex = function () {
  return this._highlightedVideoPlayerIndex;
};

/**
 * 订阅鼠标悬浮事件
 * @description 用两个事件来模拟悬浮事件
 * @param {Function} callback
 */
VideoPlayerGallery.prototype.subscribeOnHoverEvent = function (callback) {
  var that = this;
  this._container.addEventListener('mouseover', function (event) {
    event.preventDefault();
    var videoPlayer = that.queryPlayerByElement(event.target);// FIXME
    if (defined(videoPlayer)) {
      callback(event, videoPlayer._channelID);
    } else {
      callback(event, undefined);
    }
  });
  this._container.addEventListener('mouseleave', function (event) {
    event.preventDefault();
    callback(event, undefined);
  });
};

/**
 * 订阅鼠标单击事件
 * @param {Function} callback
 */
VideoPlayerGallery.prototype.subscribeOnClickEvent = function (callback) {
  var that = this;
  this._container.addEventListener('click', function (event) {
    event.preventDefault();
    var videoPlayer = that.queryPlayerByElement(event.target);
    if (defined(videoPlayer)) {
      callback(event, videoPlayer._channelID);
    }
  });
};

/**
 * 在空余的地方加载视频
 * @param {Array.<Object>} srcArray
 * @param {String} srcArray[].channelID 视频主键
 * @param {String} srcArray[].customData
 *
 * @example
 * var newArr = [];
 for(i = 0; i < 3; i++){
     newArr.push(streamList[i].channelID);
 }
 VideoPlayerGallery.reloadChannelIDsIfAvailable(newArr);
 */
VideoPlayerGallery.prototype.reloadChannelIDsIfAvailable = function (srcArray) {
  console.log('reloadChannelIDsIfAvailable', srcArray);
  let currentChannelIDArray = [];
  for (let i = 0; i < this.maxVideoCount; i++) {
    let videoPlayer = this._videoPlayers.values[i];
    if (videoPlayer.channelID) {
      currentChannelIDArray.push(videoPlayer.channelID);
    }
  }
  let newSrcArray = [];
  for (let i = 0; i < srcArray.length; i++) {
    if ((currentChannelIDArray.indexOf(srcArray[i].channelID) === -1)) {
      newSrcArray.push(srcArray[i]);
    }
  }
  // console.log('currentChannelIDArray', JSON.stringify(currentChannelIDArray));
  // console.log('srcArray-->', srcArray);
  // console.log('newSrcArray-->', newSrcArray);
  let index = 0;
  for (let i = 0; i < this.maxVideoCount; i++) {
    let videoPlayer = this._videoPlayers.values[i];
    if (!videoPlayer.channelID) {
      // console.log(`播放器${i}闲置，点播新的通道`);
      // 点播新的视频
      if (index < newSrcArray.length) {
        videoPlayer.reloadChannelID({
          channelID: newSrcArray[index].channelID,
          customData: newSrcArray[index].customData,
          deviceId: newSrcArray[index].deviceId,
          deviceType: newSrcArray[index].deviceType,
          streamType: newSrcArray[index].streamType,
          phone: newSrcArray[index].phone,
          treeCategory: newSrcArray[index].treeCategory
        });
        index++;
      }
    } else {
      let result = srcArray.find(item => {
        return item.channelID.indexOf(videoPlayer.channelID) !== -1;
      });
      // console.log('result-->', result)
      if (!result) {
        // videoPlayer.reloadChannelID();
        // 点播新的视频
        if (index < newSrcArray.length) {
          videoPlayer.reloadChannelID({
            channelID: newSrcArray[index].channelID,
            customData: newSrcArray[index].customData
          });
          index++;
        } else {
          videoPlayer.stopLiveChannel();
          videoPlayer.clearStream();
          // 初始化视频播放器工具栏切换按钮
          videoPlayer.initializePlayerToggleButton();
        }
      }
    }
  }
};

/**
 * 显示播放器的数字
 * @param {Boolean} [flag=true]
 */
VideoPlayerGallery.prototype.showVideoPlayerIndexWidget = function (flag) {
  // console.log('showVideoPlayerIndexWidget', flag);
  flag = defaultValue(flag, true);
  for (let i = 0; i < this.maxVideoCount; i++) {
    let videoPlayer = this._videoPlayers.values[i];
    videoPlayer.videoPlayerIndexWidgetVisible = flag;
  }
};

/**
 * 订阅双击后全屏视频事件
 */
VideoPlayerGallery.prototype.toggleFullscreen = function () {
  if (Fullscreen.supportsFullscreen()) {
    if (!this._hasSubscribeFullscreenEvent) {
      this._hasSubscribeFullscreenEvent = true;
      // 全屏切换
      var that = this;
      var tmpIsFullscreen = knockout.observable(Fullscreen.fullscreen);
      /**
       * Gets whether or not fullscreen mode is active.  This property is observable.
       *
       * @type {Boolean}
       */
      this.isFullscreen = undefined;
      knockout.defineProperty(this, 'isFullscreen', {
        get: function () {
          return tmpIsFullscreen();
        }
      });
      this._fullscreenElement = defaultValue(getElement(this._container), document.body);
      this._callbackFullscreen = function () {
        tmpIsFullscreen(Fullscreen.fullscreen);

        if (Fullscreen.fullscreen) {
          Fullscreen.exitFullscreen();
        } else {
          Fullscreen.requestFullscreen(that._fullscreenElement);
        }
      };
    }

    this._callbackFullscreen();
    // this._fullscreenElement.addEventListener('dblclick', this._callbackFullscreen);
  }
};

/**
 * 停止全部视频
 */
VideoPlayerGallery.prototype.stopAll = function () {
  for (let i = 0; i < this.maxVideoCount; i++) {
    this._videoPlayers.values[i].clearStream();
  }
};

/**
 * 根据treeNodeKey停止单个视频（目前仅用于3分钟自动结束）
 */
VideoPlayerGallery.prototype.stopSingle = function (value) {
  for (let i = 0; i < this.maxVideoCount; i++) {
    if (this._videoPlayers.values[i].treeNodeKey === value.treeNodeKey) {
      this._videoPlayers.values[i].clearStream();
    }
  }
};

/**
 * 根据treeNodeKey设置该视频的播放参数
 */
VideoPlayerGallery.prototype.editVideoParam = function (value) {
  for (let i = 0; i < this.maxVideoCount; i++) {
    if (this._videoPlayers.values[i].treeNodeKey === value.treeNodeKey) {
      this._videoPlayers.values[i].editVideoParam();
    }
  }
};


/**
 * Returns true if this object was destroyed; otherwise, false.
 * <br /><br />
 * If this object was destroyed, it should not be used; calling any function other than
 *
 * @returns {Boolean} <code>true</code> if this object was destroyed; otherwise, <code>false</code>.
 *
 * @see VideoPlayerGallery#destroy
 */
VideoPlayerGallery.prototype.isDestroyed = function () {
  return false;
};

/**
 * Removes and destroys all created by this instance.
 */
VideoPlayerGallery.prototype.destroy = function () {
  for (let i = 0; i < this._maxVideoCount; i++) {
    if (!this._videoPlayers.get(i).isDestroyed()) {
      this._videoPlayers.get(i).destroy();
    }
  }
  this._videoPlayers.removeAll();
};

export default VideoPlayerGallery;
