/*

 ***********************
 *                     *
 *         1           *
 *                     *
 ***********************

*/

.videoPlayer-template-1big-full {
    --columns: 1;
    --rows: 1;
}
/*左上主视频*/
.videoPlayer-left0-top0-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
