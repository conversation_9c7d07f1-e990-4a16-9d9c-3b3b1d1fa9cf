/*

 ***********************   ***********************
 *                     *   *                     *
 *         1           *   *          2          *
 *                     *   *                     *
 ***********************   ***********************

 ***********************   ***********************
 *                     *   *                     *
 *         3           *   *          4          *
 *                     *   *                     *
 ***********************   ***********************

  ***********************   ***********************
 *                     *   *                     *
 *         5           *   *          6          *
 *                     *   *                     *
 ***********************   ***********************

 ***********************   ***********************
 *                     *   *                     *
 *         7           *   *          8          *
 *                     *   *                     *
 ***********************   ***********************

*/

.videoPlayer-template-8balanceTopBottom-Top {
    --columns: 2;
    --rows: 4;
}
/*左上主视频1*/
.videoPlayer-left0-top0-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右上2*/
.videoPlayer-left1-top0-column1-row1{
    left: calc(100% / var(--columns) * 1);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}

/*左上3*/
.videoPlayer-left0-top1-column1-row1{
  left: calc(100% / var(--columns) * 0);
  top: calc(100% / var(--rows) * 1);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
/*右上4*/
.videoPlayer-left1-top1-column1-row1{
  left: calc(100% / var(--columns) * 1);
  top: calc(100% / var(--rows) * 1);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}

/*左下5*/
.videoPlayer-left0-top2-column1-row1{
  left: calc(100% / var(--columns) * 0);
  top: calc(100% / var(--rows) * 2);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
/*右下6*/
.videoPlayer-left1-top2-column1-row1{
  left: calc(100% / var(--columns) * 1);
  top: calc(100% / var(--rows) * 2);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}

/*左下7*/
.videoPlayer-left0-top3-column1-row1{
  left: calc(100% / var(--columns) * 0);
  top: calc(100% / var(--rows) * 3);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
/*右下8*/
.videoPlayer-left1-top3-column1-row1{
  left: calc(100% / var(--columns) * 1);
  top: calc(100% / var(--rows) * 3);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
