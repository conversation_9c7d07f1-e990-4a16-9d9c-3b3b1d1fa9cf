/*

 ***********************   ***********************
 *                     *   *                     *
 *         1           *   *          2          *
 *                     *   *                     *
 ***********************   ***********************

 ***********************   ***********************
 *                     *   *     4    *     5    *
 *         3           *   ***********************
 *                     *   *     6    *     7    *
 ***********************   ***********************

*/
.videoPlayer-template-3big4small-leftTop {
    --columns: 4;
    --rows: 4;
}
/*左上大视频1*/
.videoPlayer-left0-top0-column2-row2{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 2);
    height: calc(100% / var(--rows) * 2);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 2;
    --height: 2;
}
/*右上大视频2*/
.videoPlayer-left2-top0-column2-row2{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 2);
    height: calc(100% / var(--rows) * 2);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 2;
    --height: 2;
}
/*左下大视频3*/
.videoPlayer-left0-top2-column2-row2{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 2);
    height: calc(100% / var(--rows) * 2);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 2;
    --height: 2;
}
/*右下四宫格布局-左上小视频4*/
.videoPlayer-left2-top2-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*下四宫格布局-右上小视频5*/
.videoPlayer-left3-top2-column1-row1{
    left: calc(100% / var(--columns) * 3);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*下四宫格布局-左下小视频6*/
.videoPlayer-left2-top3-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 3);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*下四宫格布局-右下小视频7*/
.videoPlayer-left3-top3-column1-row1{
    left: calc(100% / var(--columns) * 3);
    top: calc(100% / var(--rows) * 3);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
