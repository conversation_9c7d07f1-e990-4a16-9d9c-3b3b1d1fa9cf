'use strict';

const cameraManufactureTypeOptions = [
  {
    value: 1,
    label: '海康'
  },
  {
    value: 2,
    label: '大华'
  }
];

/**
 * 相机厂家(枚举类)
 * @alias CameraManufactureType
 * @exports CameraManufactureType
 */
export default {
  /**
   * 海康
   *
   * @type {Number}
   * @constant
   */
  HAIKANG : 1,

  /**
   * 大华
   *
   * @type {Number}
   * @constant
   */
  DAHUA : 2,

  /**
   * 获取下拉框的选项
   * @return {<{value: Number, label: String}>[]}
   */
  getOptions: function () {
    return cameraManufactureTypeOptions;
  },
  /**
   * 根据键值获取字符串
   * @param {String} value 枚举值
   * @return {string}
   */
  getLabel: function (value) {
    switch (value){
      case 1:
        return '枪机';
      case 2:
        return '球机';
    }
    return '错误值';
  }
}
