/**
 * DictCode
 * @alias Enumerate_DictCode
 */
export default {
  vehicleModel: 1, // 车辆类型
  vehicleUseType: 2, // 行业类型
  vehicleColor: 'licence_color', // 车牌颜色
  vehicleState: 4, // 车辆状态(正常0, 维修1)
  terminalType: 5, // 终端类型
  terminalModel: 6, // 终端型号
  terminalState: 7, // 终端状态(离线0, 在线1)
  driverType: 8, // 司乘类型
  driverVehicleMode: 9, // 驾照类型
  deptType: 10, // 车组类型
  repairRecordType: 11, // 报修类型
  regionType: 12, // 围栏类型
  routeType: 13, // 线路类型
  pointType: 14, // 预设点类型
  repairRecordState: 11, // 告警状态
  licenceColor: 'licence_color', // 车牌颜色，同vehicleColor
  alarmState: 15, // 告警状态
  alarmType: 'alarm_type', // 告警类型
  alarmOrigin: 'alarm_origin', // 告警来源
  reminderWay: 'reminder_way', // 告警提醒方式
  alarmLevel: 'alarm_level', // 告警等级
  removeAlarm: 'remove_alarm', // 接解告警
  eventType: 'event_type', // 多媒体事件类型
  cmdsState: 22, // 命令执行结果
  centerSetting: 23, //  线路告警规则-上报中心设置（驶离0，驶进1，驶离、驶进2）
  inline: 24, // 线路告警规则-进线路设置（给平台0，给驾驶员1）
  outline: 24, // 线路告警规则-出线路设置（给平台0，给驾驶员1）
  fenceType: 25, // 围栏类型
  messageTemplateType: 29, // 预设常用信息类型
  practiceMode: 2, // 行业类型枚举（vehicleUseType）
  fuelType: 27, // 燃料类型（fuelType）
  vehicleModelLevel: 26, // 车辆类型等级（vehicleModelLevel）
  mailType: 31, // 邮件类型
  mailSendType: 32, // 邮件发送类型
  serviceRole: 'service_role', // 服务角色
  serverState: 'server_state', // 服务商处理状态
  teState: 'te_state', // 服务商处理状态
  thirdState: 'third_state', // 第三方处理状态
  companyState: 'company_state', // 企业处理状态
  appealState: 'appeal_state', // 申诉处理状态
  ruleManage: 'rule_manage', // 规则管理
  connectStatus: 'connect_status', // 接驳状态
  licenceColorValue: 'licence_color_value', // 车牌颜色16进制值
  deviceModel: 'device_model',
  deviceType: 'device_type',
  cardType: 'card_type',
  terminalFunctionType: 'terminal_function_type',
  localStandards: 'local_standards',
  package: 'package',
  cardState: 'card_state',
  filingState: 'filing_state',
  busyArea: 'busy_area',
  vehicleType: 'vehicle_type',
  certificateType: 'certificate_type',
  transportType: 'transport_type',
  certificateMediumType: 'certificate_medium_type',
  cameraChannel: 'camera_channel', // 视频通道(规则管理)
  serverMeasures: 'server_measures', // 服务商处理措施
  thirdMeasures: 'third_measures', // 第三方处理措施
  companyMeasures: 'company_measures', // 企业处理措施
  alarmTypeSpecial: 'alarm_type_special', // 告警类型（实时告警首页分类统计专用）
  serviceState: 'service_state', // 服务状态
  operatingStatus: 'operating_status', // 车辆营运状态
  accessMode: 'access_mode', // 车辆接入方式
  operator: 'operator', // 运营商
  connectMode: 'connect_mode', // 车机接入方式
  deviceState: 'device_state', // 设备状态
  roadType: 'road_type', // 道路类型
  alarmDealState: 'alarm_deal_state', // 告警处理状态
  removeAlarmNew: 'remove_alarm', // 解除告警状态(新)
  roadLevel: 'road_level', // 道路级别
  alarmRule: 'alarm_rule', // 规则配置说明的告警类型
  protocolType: 'protocol_type', // 协议类型
  sex: 'sex', // 性别
  instructType: 'instruct_type', // 指令搜索类型
  warnType: 'warn_type', // 乌市告警类型
  targetType: 'target_type', // 目标类别
  bdmType: 'bdm_type', // 北斗设备类型
  bdmWearModel: 'bdm_wear_model', // 北斗穿戴终端型号
  bdmRdssModel: 'bdm_rdss_model', // 短报文终端型号
  bdmTimeModel: 'bdm_time_model', // 北斗授时终端型号
  bdmDetectionModel: 'bdm_detection_model', // 北斗监测终端型号
  bdmTerminalModel: 'bdm_terminal_model', // 北斗定位中端型号
  bdmStatus: 'bdm_status', // 北斗设备状态
  bdmDeviceActivated: 'bdm_device_activated', // 北斗设备激活状态
  bdmWokerIndustry: 'bdm_woker_industry', // 从业类型
  bdmWokerPost: 'bdm_worker_post', // 岗位类型
  // bdmWokerPostUnknow: 'bdm_woker_post_unknow', // 人员管理行业类型未知
  // bdmWokerPostCoal: 'bdm_woker_post_coal', // 人员管理行业类型煤炭
  // bdmWokerPostElectric: 'bdm_woker_post_electric', // 人员管理行业类型电力
  // bdmWokerPostChemical: 'bdm_worker_post_chemical', // 人员管理行业类型化工
  // bdmWokerPostBindTerminalType: 'bdm_worker_bind_terminal_type', // 人员管理终端类型
  bdmIotcardCategory: 'bdm_iotcard_category', // 物联网卡类型
  bdmIotcardOperator: 'bdm_iotcard_operator', // 网络运营商
  bdmIotcardDataPlan: 'bdm_iotcard_data_plan', // 流量套餐
  bdmIotcardStatus: 'bdm_iotcard_status', // 物联网卡状态
  bdmTimeRefSource: 'bdm_time_ref_source', // 授时终端参考源输入
  bdmTimeClockSignal: 'bdm_time_clock_signal', // 授时时钟输出信号
  bdmTimeCategory: 'bdm_time_category', // 北斗授时终端类别
  bdmWearCategory: 'bdm_wear_category', // 北斗穿戴式终端类别
  bdmDetectionCategory: 'bdm_detection_category', // 北斗监测终端类别
  bdmRdssCategory: 'bdm_rdss_category', // 北斗短报文终端类别
  bdmTerminalCategory: 'bdm_terminal_category', // 北斗定位终端类别
  bdmBdcardLevel: 'bdm_bdcard_level', // 北斗卡等级
  bdmDeviceType: 'bdm_device_type', // 终端类别
  bdmCarCategory: 'bdm_car_category', // 车辆信息车辆类型
  bdmTruckCategory: 'bdm_truck_category', // 车辆信息车辆类型
  facilityType: 'facility_type', // 设施类型
  testDeviceType: 'test_device_type', // 入网测试设备类型
  terminalEventType: 'terminal_event_type', // 入网测试设备类型
  checkProcess: 'check_process', // 北斗识别检测进度
  userOperateType: 'user_operate_type', // 用户操作类型
  bdmTerminalApp: 'bdm_terminal_app', // 北斗终端应用方向
  bdmAppSense: 'bdm_app_sense', // 北斗应用场景
  bdmCheckType: 'bdm_check_type', // 北斗应用场景
  bdmGnssMode: 'bdm_gnss_mode', // 旧设备定位模式
  batch: 'batch', // 是否补传
  interfaceDirect: 'interface_direct',
  callInterfaceResult: 'call_interface_result',
  runningStatus: 'running_status',
  onlineStatus: 'online_status',
  bdmTerminalProtocol: 'bdm_terminal_protocol',
  manageStatus: 'manage_status',
  JttProtocol: 'JttProtocol',
  MQTTProtocol: 'MQTTProtocol',
  codeResult: 'code_result', // 赋码结果
  accStatus: 'acc_status',
  boxType: 'box_type',
  accountStatus: 'account_status',
  carriageModel: 'carriage_model',
  shipType: 'ship_type',
  accountStatus1: 'account_status1',
  targetModel: 'target_model', // 监管目标类型
  assetType: 'asset_type', // 资产类型
  ownDeptType: 'own_dept_type', // 归属单位类型
  upgradeCategory: 'upgrade_category', // 升级包软件类型
  bdmDeviceModel: 'bdm_device_model', // 终端型号
  bdmDeviceUsage: 'bdm_device_usage', // 终端用途分类
  industrialSector: 'Industrial_sector', // 产业板块
  inNetType: 'inNetType', // 入网方式
  inNetProvider: 'inNetProvider', // 入网运营商

  // 前端通用布尔枚举
  booleanType: function () {
    return [
      {
        value: true,
        label: '是'
      },
      {
        value: false,
        label: '否'
      }
    ];
  },
  // 搜索栏中的布尔型
  booleanTypeInSearchFilter: function (key) {
    return [
      {
        value: true,
        label: '是',
        labelFunction: (name) => {
          return `${name}(是)`;
        }
      },
      {
        value: false,
        label: '否',
        labelFunction: (name) => {
          return `${name}(否)`;
        }
      }
    ];
  },
  // 前端启用停用数字枚举
  statusType: function () {
    return [
      {
        value: 1,
        label: '启用'
      },
      {
        value: 0,
        label: '停用'
      }
    ];
  }

};
