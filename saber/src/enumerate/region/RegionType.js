const options = [
  {
    value: 1,
    label: '矩形'
  },
  {
    value: 2,
    label: '圆形'
  },
  {
    value: 3,
    label: '多边形'
  },
  {
    value: 4,
    label: '行政区域'
  }
];

/**
 * RegionType
 * @alias Enumerate_RegionType
 */
export default {
  RECT: 1,
  CIRCLE: 2,
  POLYGON: 3,
  DISTRICT: 4,

  /**
   * 获取下拉框的选项
   * @return {Array.<{value: Number, label: String}>}
   */
  getOptions: function () {
    return options;
  },
  /**
   * 根据键值获取字符串
   * @param {Number} value 枚举值
   * @return {String}
   */
  getLabel: function (value) {
    for(let i = 0; i < options.length; i++){
      if(options[i].value === value){
        return options[i].label;
      }
    }
    return '错误值';
  }
};
