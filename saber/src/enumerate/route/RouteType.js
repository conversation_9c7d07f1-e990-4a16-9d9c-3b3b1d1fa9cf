const options = [
  {
    value: 1,
    label: '普通线路'
  },
  {
    value: 2,
    label: '公交线路'
  },
  {
    value: 3,
    label: '客运线路'
  },
  {
    value: 4,
    label: '图层线路'
  }
];

/**
 * RouteType
 * @alias Enumerate_RouteType
 */
export default {
  NORMAL: 1,
  BUS: 2,
  PASSENGER_TRANSPORT: 3,
  LAYER: 4,

  /**
   * 获取下拉框的选项
   * @return {Array.<{value: Number, label: String}>}
   */
  getOptions: function () {
    return options;
  },
  /**
   * 根据键值获取字符串
   * @param {Number} value 枚举值
   * @return {String}
   */
  getLabel: function (value) {
    for(let i = 0; i < options.length; i++){
      if(options[i].value === value){
        return options[i].label;
      }
    }
    return '错误值';
  }
};
