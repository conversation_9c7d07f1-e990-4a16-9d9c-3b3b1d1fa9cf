const options = [
  {
    value: 0,
    label: '无',
    buttonTips: '下载'
  },
  {
    value: 1,
    label: '已传输', // 直到录像上传状态=1，文件上传进度=100时，表示文件全部上传完毕，此时可以下载。
    buttonTips: '下载'
  },
  {
    value: 2,
    label: '正在传输', // 当录像文件开始上传时：录像上传状态=2，文件上传进度显示百分比数值，如果文件url不为空，此时说明文件已经开始上传了一部分
    buttonTips: '传输视频中，点我取消'
  },
  {
    value: 3,
    label: '录像传输被暂停', // 如果录像上传状态=3，表示录像上传途中被暂停
    buttonTips: '传输被暂停，点我重新开始'
  },
  {
    value: 4,
    label: '未开始传输', // 如果录像上传状态=3，表示录像上传途中被暂停
    buttonTips: '下载'
  },
  {
    value: 5,
    label: '上次传输失败', // 30秒内没有传输数据
    buttonTips: '传输失败，即将重新开始'
  },
  {
    value: 11,
    label: '等待传输', // 前端自己定义的临时状态
    buttonTips: '等待传输中，点我取消'
  }
];

/**
 * STREAM_TYPE DOWNLOAD_HISTORY_VIDEO_STATE
 * @alias Enumerate_DOWNLOAD_HISTORY_VIDEO_STATE
 */
export default {
  DEFAULT: 0,
  UPLOADED: 1,
  UPLOADING: 2,
  PAUSE: 3,
  NO_INIT: 4,
  FAIL: 5,
  WAIT: 11,

  /**
   * 获取下拉框的选项
   * @return {Array.<{value: Number, label: String}>}
   */
  getOptions: function () {
    return options;
  },
  /**
   * 根据键值获取字符串
   * @param {Number} value 枚举值
   * @return {String}
   */
  getLabel: function (value) {
    for (let i = 0; i < options.length; i++) {
      if (value === options[i].value) {
        return options[i].label;
      }
    }
    return '';
  },
  /**
   * 根据键值获取字符串
   * @param {Number} value 枚举值
   * @return {String}
   */
  getButtonTips: function (value) {
    for (let i = 0; i < options.length; i++) {
      if (value === options[i].value) {
        return options[i].buttonTips;
      }
    }
    return '下载';
  }
};
