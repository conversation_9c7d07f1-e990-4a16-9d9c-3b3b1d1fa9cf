const options = [
  {
    value: 0,
    label: '主码流'
  },
  {
    value: 1,
    label: '子码流'
  }
];

/**
 * STREAM_TYPE
 * @alias Enumerate_STREAM_TYPE
 */
export default {
  MAIN_STREAM: 0,
  SUB_STREAM: 1,

  /**
   * 获取下拉框的选项
   * @return {Array.<{value: Number, label: String}>}
   */
  getOptions: function () {
    return options;
  },
  /**
   * 根据键值获取字符串
   * @param {String} value 枚举值
   * @return {String}
   */
  getLabel: function (value) {
    for (let i = 0; i < options.length; i++) {
      if (value === options[i].value) {
        return options[i].label;
      }
    }
    return '错误值';
  }
};
