import Layout from '@/page/index/'

export default [{
  path: '/wel',
  component: Layout,
  redirect: '/wel/index',
  children: [{
    path: 'index',
    name: '首页',
    meta: {
      i18n: 'dashboard',
      keepAlive: true
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/monitoring/main/index')
  }]
}, {
  path: '/info',
  component: Layout,
  redirect: '/info/index',
  children: [{
    path: 'index',
    name: '个人信息',
    meta: {
      i18n: 'info'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/system/userinfo')
  }]
}, {
  path: '/center/instructDetails',
  component: Layout,
  redirect: '/center/instructDetails/index',
  children: [{
    path: 'index',
    name: '指令任务下发详情',
    meta: {
      i18n: 'instructDetails'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/center/instructDetails/index')
  }]
}]
