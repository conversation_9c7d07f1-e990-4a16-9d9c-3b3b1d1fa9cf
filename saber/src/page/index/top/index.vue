<template>
  <div class="avue-top">
    <div class="top-bar__logo">
      <img
        class="logo-img"
        :src="require('@/assets/images/home-logo.png')"
        alt=""
      >
    </div>
    <div class="top-bar__left">
      <div class="project-name">
        <span :title="projectName + version">{{ projectName }}</span>
      </div>
    </div>
    <div class="top-bar__right">
      <div class="top-bar__right__item time-item">
        <p class="top-bar__time2">{{ time2 }}</p>
        <p class="top-bar__time1">{{ time1 }}</p>
      </div>
      <!-- 告警声音 -->
      <audio
        ref="audio"
        controls="controls"
        style="display: none"
      >
        <source src="@/assets/audio/alarmLong.mp3">
      </audio>
      <div class="top-bar__right__item">
        <el-tooltip
          v-if="showFullScren"
          effect="dark"
          :content="isFullScren?$t('navbar.screenfullF'):$t('navbar.screenfull')"
          placement="bottom"
        >
          <div class="top-bar__item">
            <i
              :class="isFullScren?'icon-tuichuquanping':'icon-quanping'"
              @click="handleScreen"
            />
          </div>
        </el-tooltip>
      </div>
      <div class="top-bar__right__user">
        <el-dropdown style="height: 100%;">
          <span
            class="el-dropdown-link"
            style="height: 50%;position: relative;top: -1px;"
          >
            <img
              class="top-bar__img"
              :src="userInfo.avatar"
            >
            <span class="top-bar__user_name">{{ userInfo.user_name }}</span>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <router-link to="/wel/index">{{ $t('navbar.dashboard') }}</router-link>
            </el-dropdown-item>
            <el-dropdown-item>
              <router-link to="/info/index">{{ $t('navbar.userinfo') }}</router-link>
            </el-dropdown-item>
            <el-dropdown-item
              v-if="this.website.switchMode"
              @click.native="switchDept"
            >{{ $t('navbar.switchDept') }}
            </el-dropdown-item>
            <el-dropdown-item
              divided
              @click.native="logout"
            >{{ $t('navbar.logOut') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-dialog
        title="用户信息选择"
        append-to-body
        :visible.sync="userBox"
        width="350px"
      >
        <avue-form
          ref="form"
          v-model="userForm"
          :option="userOption"
          @submit="submitSwitch"
        />
      </el-dialog>
    </div>
    <AlarmDialog
      v-show="dialogVisible"
      :alarm-list="alarmList"
      :dict="dict"
      @closeDialog="dialogVisible = false"
    />
  </div>
</template>
<script>
import {resetRouter} from '@/router/router';
import {mapGetters, mapState} from "vuex";
import {fullscreenToggel, listenfullscreen} from "@/util/util";
import configProjectVersion from '@/config/configProjectVersion';
import dayjs from 'dayjs';
import website from "@/config/website";
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';
import { getAuthCode } from '@/api/monitoring/bicycleMap';
import { details } from '@/api/top';
import AlarmDialog from './alarmDialog.vue';
import { getVersion } from "@/api/user";
import { getWebsocketParam } from '../../../api/user';

export default {
  name: "Top",
  components: {
    AlarmDialog
  },
  // 数据字典
  dicts: [
    'alarmType',
    'alarmLevel',
    'licenceColor'
  ],
  filters: {},
  data() {
    return {
      userBox: false,
      userForm: {
        deptId: '',
        roleId: ''
      },
      userOption: {
        labelWidth: 70,
        submitBtn: true,
        emptyBtn: false,
        submitText: '切换',
        column: [
          {
            label: '部门',
            prop: 'deptId',
            type: 'select',
            props: {
              label: 'deptName',
              value: 'id'
            },
            dicUrl:  '/blade-system/dept/select',
            span: 24,
            display: false,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "blur"
            }],
          },
          {
            label: '角色',
            prop: 'roleId',
            type: 'select',
            props: {
              label: 'roleName',
              value: 'id'
            },
            dicUrl: website + '/blade-system/role/select',
            span: 24,
            display: false,
            rules: [{
              required: true,
              message: "请选择角色",
              trigger: "blur"
            }],
          },
        ]
      },
      projectName: configProjectVersion.nameWithVersion,
      projectNameEn: configProjectVersion.nameWithVersionEn,
      version: configProjectVersion.version,
      time1: '',
      time2: '',
      timer: null,
      alarmData: {},
      alarmList: [],
      dialogVisible: false,
      isPlayAudio: true, // 是否播放告警声音
      isPlayDialog: true, // 是否显示告警弹窗
      isPlayContent: true // 是否播放告警内容
    };
  },
  created() {
    this.getSystemVersion();
  },
  mounted() {
    listenfullscreen(this.setScreen);
    this.initTime();
    console.log('-> this.userInfo', this.userInfo);
    this.initWebSocket();
    // this.$nextTick(()=>{
    //   this.getDetails();
    //   let alarmTimer = setInterval(()=>{
    //     this.getDetails();
    //   }, 60 * 1000);
    //   this.$once('hook:destroyed', () => {
    //     clearInterval(alarmTimer);
    //     alarmTimer = null;
    //   });
    // })
  },
  beforeDestroy(){
    this.clear();
  },
  computed: {
    ...mapState({
      showDebug: state => state.common.showDebug,
      showTheme: state => state.common.showTheme,
      showLock: state => state.common.showLock,
      showFullScren: state => state.common.showFullScren,
      showCollapse: state => state.common.showCollapse,
      showSearch: state => state.common.showSearch,
      showMenu: state => state.common.showMenu,
      showColor: state => state.common.showColor
    }),
    ...mapGetters([
      "userInfo",
      "isFullScren",
      "tagWel",
      "tagList",
      "isCollapse",
      "tag",
      "logsLen",
      "logsFlag"
    ])
  },
  methods: {
    getSystemVersion() {
      getVersion().then(res => {
        const { data } = res.data;
        this.version = data;
      });
    },
    // 首页告警消息数量
    async getDetails() {
      const query = {
        startTime: this.$moment().startOf('day').unix(),
        endTime: this.$moment().endOf('day').unix(),
        serviceRole: '1' // 随便传一个服务角色(不然接口会报错为空)
      };
      const { data, code } = await details(query);
      if (code === 200 && data) {
        this.alarmData = data;
      }
    },
    // 初始化WebSocket
    async initWebSocket () {
      if (typeof WebSocket === 'undefined') {
        console.log('您的浏览器不支持WebSocket');
        return;
      }
      const { data: socketCode } = await getAuthCode();
      // const wsLocation = process.env.NODE_ENV === 'development' ? '59.41.7.83:20845' : '59.41.7.83:20845';
      getWebsocketParam().then(res => {
        const wsLocation = res.data.data;
        const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
        const wsUrl = `${protocol}${wsLocation}/ws/alarmRemind/push/${socketCode}`;
        let webSocket = new ReconnectingWebSocket(wsUrl);
        let list = [];
        webSocket.onopen = () => {
          console.log('首页告警通知webSocket连接成功');
        };
        webSocket.onmessage = (e) => {
          const data = JSON.parse(e.data);
          const locationData = this.$utils.wgs84togcj02(data.startLon, data.startLat);
          data.startLon = locationData[0];
          data.startLat = locationData[1];
          this.$EventBus.$emit('realTimeMap', data);
          switch (data.reminderWay) {
          // 仅强制弹窗
          case 1:
            if (Number(data.alarmLevel) > 1) {
              list.push(data);
            }
            break;
            // 仅声音
          case 2:
            if (this.$refs['audio'] && this.isPlayAudio) {
              this.$refs['audio'].play();
              // 存在"告警播放内容"时播放内容, 15秒内只播放一次告警内容
              if (this.isPlayContent && data.alarmContent) {
                this.isPlayContent = false;
                window.speechSynthesis.speak(new SpeechSynthesisUtterance(data.alarmContent));
                setTimeout(() => {
                  this.isPlayContent = true;
                }, 15 * 1000);
              }
            }
            break;
            // 强制弹窗和声音
          case 3:
            if (Number(data.alarmLevel) > 1) {
              list.push(data);
            }
            break;
          }
        };
        let timer = setInterval(() => {
          this.alarmList = JSON.parse(JSON.stringify(list));
          if (this.alarmList && this.alarmList.length) {
            if (this.isPlayDialog) {
              this.dialogVisible = true;
              setTimeout(()=>{
                this.dialogVisible = false;
              }, 8 * 1000);
            }
            // 数组中含有reminderWay = 3(强制弹窗和声音)的, 播放声音
            let result = this.alarmList.find(item => item.reminderWay === 3);
            if (result && this.$refs['audio'] && this.isPlayAudio) {
              this.$refs['audio'].play();
              // 存在"告警播放内容"时播放内容, 15秒内只播放一次告警内容
              if (this.isPlayContent && result.alarmContent) {
                this.isPlayContent = false;
                window.speechSynthesis.speak(new SpeechSynthesisUtterance(result.alarmContent));
                setTimeout(() => {
                  this.isPlayContent = true;
                }, 15 * 1000);
              }
            }
          }
          list = [];
        }, 15 * 1000);
        webSocket.onerror = () => {
          console.log('数据传输已断开, 正在尝试重新连接');
          // this.$message.error('数据传输已断开, 正在尝试重新连接');
        };
        this.$once('hook:beforeDestroy', () => {
          webSocket.close();
          webSocket = null;
          clearInterval(timer);
          timer = null;
        });
      });
    },
    handleScreen() {
      fullscreenToggel();
    },
    setCollapse() {
      this.$store.commit("SET_COLLAPSE");
    },
    setScreen() {
      this.$store.commit("SET_FULLSCREN");
    },
    switchDept() {
      const userId = this.userInfo.user_id;
      const deptColumn = this.findObject(this.userOption.column, "deptId");
      deptColumn.dicUrl = website.apiPrefix + `/blade-system/dept/select?userId=${userId}`;
      deptColumn.display = true;
      const roleColumn = this.findObject(this.userOption.column, "roleId");
      roleColumn.dicUrl = website.apiPrefix + `/blade-system/role/select?userId=${userId}`;
      roleColumn.display = true;
      this.userBox = true;
    },
    submitSwitch (form, done) {
      this.$store.dispatch("refreshToken", form).then(() => {
        this.userBox = false;
        this.$router.push({path: "/", query: { isRouter: this.$route.fullPath }});
      });
      done();
    },
    logout() {
      this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
        confirmButtonText: this.$t("submitText"),
        cancelButtonText: this.$t("cancelText"),
        type: "warning"
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          resetRouter();
          this.$router.push({path: "/login"});
        });
      });
    },
    initTime(){
      this.timer = setTimeout(()=> {
        this.clear();
        const nowTime = dayjs().format('MM-DD HH:mm:ss').split(' ');
        this.time1 = nowTime[0];
        this.time2 = nowTime[1];
        this.initTime();
      }, 1000);
    },
    clear(){
      clearTimeout(this.timer);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.inform-icon{
  height: 22px;
  width: 22px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100%;
}
.dialog-icon {
  width: 28px;
  height: 28px;
}
//.inform-icon-close{
//  background-image: url("/bdsplatform/static/images/icons/audioCloseWhite.svg");
//}
//.inform-icon-open{
//  background-image: url("/bdsplatform/static/images/icons/audioOpenWhite.svg");
//}
//.dialog-icon-close{
//  background-image: url("/bdsplatform/static/images/icons/dialogClose1.svg");
//}
//.dialog-icon-open{
//  background-image: url("/bdsplatform/static/images/icons/dialogOpen1.svg");
//}
//.top-bar__right__item_icon{
//  width: 45px !important;
//}
//.information-icon {
//  background-image: url("/bdsplatform/static/images/icons/icon-information.svg");
//}
</style>
