<template>
  <div class="avue-logo">
    <transition name="fade">
      <div class="avue-left-bar">
        <div class="avue-breadcrumb"
            :class="[{ 'avue-breadcrumb--active': isCollapse }]"
            v-if="showCollapse">
          <i class="icon-navicon"
            @click="setCollapse"></i>
        </div>
      </div>
      <!-- <span v-if="keyCollapse"
            class="avue-logo_subtitle"
            key="0">
        {{website.logo}}
      </span> -->
    </transition>
    <transition-group name="fade">
      <template v-if="!keyCollapse">
        <span class="top-bar__item" key="1" v-if="showSearch" :style="toggleFocusStyle">
          <top-search @toggleFocus="toggleFocus"></top-search>
        </span>
      </template>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import topSearch from "./top/top-search.vue";
export default {
  name: "logo",
  components:{
    topSearch
  },
  data() {
    return {
      isSearchFocus: false
    };
  },
  created() {},
  computed: {
    ...mapState({
        showCollapse: state => state.common.showCollapse,
        showSearch: state => state.common.showSearch
      }),
    ...mapGetters(["website", "keyCollapse", "isCollapse"]),
    toggleFocusStyle(){
      if(!this.isSearchFocus) {
        return {}
      } else {
        return {
          backgroundColor: '',
          color: ''
        }
      }
    }
  },
  methods: {
    setCollapse() {
        this.$store.commit("SET_COLLAPSE");
    },
    toggleFocus(isFocus) {
      this.isSearchFocus = isFocus
    }
  }
};
</script>

<style lang="scss">
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-active {
  transition: opacity 2.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.avue-logo {
  // position: fixed;
  // top: 0;
  // left: 0;
  width: 200px;
  height: 44px;
  line-height: 44px;
  //background-color: #409EFE;
  font-size: 20px;
  overflow: hidden;
  box-sizing: border-box;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  color: #fff;
  z-index: 1024;
  &_title {
    display: block;
    text-align: center;
    font-weight: 300;
    font-size: 20px;
  }
  &_subtitle {
    display: block;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
  }
  position: relative;
}
</style>
<style lang="less" scoped>
.avue-logo{
  .avue-left-bar{
    height: 42px;
    position: absolute;
    top: 0;
    right: 12px;
    z-index: 1000;
    i{
        color: #000000;
        line-height: 42px;
    }
  }
  .top-search{
    width: 200px !important;
    left: 0px;
  }
  .top-bar__item {
    height: 100%;
    background-color: #EBF1F6;
  }
}
</style>
