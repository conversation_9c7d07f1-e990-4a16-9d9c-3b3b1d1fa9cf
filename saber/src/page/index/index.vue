<template>
  <div class="avue-contail" :class="{'avue--collapse':isCollapse}">
    <div class="avue-header" :class="{'avue-left-visible': isVisibleMenu}">
      <!-- 顶部导航栏 -->
      <top ref="top"/>
    </div>
    <div class="avue-layout">
      <div class="avue-left" :class="{'avue-left-visible': isVisibleMenu}">
        <!-- 左侧导航栏 -->
        <sidebar/>
      </div>
      <div class="avue-main" :class="{'avue-main-container': isVisibleMenu}">
        <!-- 顶部标签卡 -->
        <tags :class="{'avue-left-visible': isVisibleMenu}" />
        <transition name="fade-scale">
          <search class="avue-view" v-show="isSearch"></search>
        </transition>
        <!-- 主体视图层 -->
        <div
          v-show="!isSearch"
          id="avue-view"
          style="height:calc(100% - 36px);overflow-y:auto;overflow-x:hidden;"
          :class="{'avue-content': isVisibleMenu}"
        >
          <keep-alive>
            <router-view class="avue-view" v-if="$route.meta.keepAlive"/>
          </keep-alive>
          <router-view class="avue-view" v-if="!$route.meta.keepAlive"/>
        </div>
      </div>
    </div>
    <div class="avue-shade" @click="showCollapse"></div>
    <div class="back-container">
      <el-button
        v-show="showBackBtn && isVisibleMenu"
        size="normal"
        type="primary"
        class="back-btn"
        @click="toPage"
      >
        返回
      </el-button>
    </div>
  </div>
</template>

<script>
  import {mapGetters} from "vuex";
  import tags from "./tags";
  import search from "./search";
  import top from "./top/";
  import sidebar from "./sidebar/";
  import admin from "@/util/admin";
  import {validatenull} from "@/util/validate";
  import {calcDate} from "@/util/date.js";
  import {getStore, setStore} from "@/util/store.js";
  import { refreshQSToken } from '@/api/video/gbVideo.js';
  import { refreshToken as refresh } from '@/api/user';

  export default {
    components: {
      top,
      tags,
      search,
      sidebar
    },
    name: "index",
    provide() {
      return {
        index: this
      };
    },
    data() {
      return {
        //搜索控制
        isSearch: false,
        //刷新token锁
        refreshLock: false,
        //刷新token的时间
        refreshTime: "",
        isVisibleMenu: true,
        showBackBtn: false,
        refreshQSTime: null, // 青柿Token刷新时间
        treePageList: ['realTimeMap', 'trackInfo', 'instruction', 'terminalConfiguration']
      };
    },
    watch: {
      $route: {
        handler(newVal) {
          if (Object.keys(newVal.params).length !== 0 || Object.keys(newVal.query).length !== 0) {
            this.showBackBtn = true;
            console.log('我是从this.$router.push跳转过来的', newVal);
          } else {
            this.showBackBtn = false;
            console.log('我是正常跳转', newVal);
          }
        },
        deep: true,
        immediate: true
      }
    },
    created() {
      //实时检测刷新token
      if (window.sessionStorage.getItem('saber-integration')) {
        this.refreshIntegrationToken();
      } else if (!localStorage.getItem('cedi__Access-Token')) {
        this.refreshToken();
      }
    },
    mounted() {
      this.init();
      // 国能
      this.gnInit();
      if (!window.sessionStorage.getItem('saber-integration')) {
        this.getQSToken();
        this.refreshQSTime = setInterval(() => {
            this.getQSToken();
        }, 120 * 60 * 1000);
      }
      if (!window.localStorage.hasOwnProperty("cedi__Access-Token") && !window.sessionStorage.getItem('saber-integration')) {
        this.getVehicleTree();
      }
    },
    beforeDestroy() {
      clearInterval(this.refreshTime);
      this.refreshTime = '';
      clearInterval(this.refreshQSTime);
      this.refreshQSTime = null;
    },
    computed: mapGetters(["isMenu", "isLock", "isCollapse", "website", "menu", "userInfo"]),
    props: [],
    methods: {
      // 获取终端树
      getVehicleTree() {
        const regex = new RegExp(this.treePageList.join('|'), 'i');
        if (!regex.test(this.$route.path)) {
         this.$store.dispatch('GetVehicleTree');
        }
      },
      // 获取青柿平台token
      getQSToken() {
        refreshQSToken().then(res => {
          const { code, data } = res.data;
          if (code === 200 && data) {
            localStorage.setItem('QS_TOKEN', data.urltoken);
          }
        });
      },
      toPage() {
        this.$router.push(this.$route.query.isRouter);
      },
      gnInit() {
        // 国能系统进入平台需要隐藏菜单栏
        if (localStorage.getItem('cedi__Access-Token') || sessionStorage.getItem('saber-integration')) {
          let that = this;
          // this.isVisibleMenu = true;
          if (localStorage.getItem('cedi__DEFAULT_COLOR')) {
            const { value } = JSON.parse(localStorage.getItem('cedi__DEFAULT_COLOR'));
            document.documentElement.style.setProperty('--gn-color', value);
            document.documentElement.style.setProperty('--gn-color-rgb', that.hexToRgb(value));
          }
          // 监听localStorage变化
          window.addEventListener("storage", function(event) {
            if (event.key === 'cedi__DEFAULT_COLOR') {
              const { value } = JSON.parse(event.newValue);
               document.documentElement.style.setProperty('--gn-color', value);
               document.documentElement.style.setProperty('--gn-color-rgb', that.hexToRgb(value));
            }
          });
        } else {
          this.isVisibleMenu = false;
        }
      },
      showCollapse() {
        this.$store.commit("SET_COLLAPSE");
      },
      // 初始化
      init() {
        this.$store.commit("SET_SCREEN", admin.getScreen());
        window.onresize = () => {
          setTimeout(() => {
            this.$store.commit("SET_SCREEN", admin.getScreen());
          }, 0);
        };
        this.$store.dispatch("FlowRoutes").then(() => {
        });
      },
      //打开菜单
      openMenu(item = {}) {
        this.$store.dispatch("GetMenu", item.id).then(data => {
          if (data.length !== 0) {
            this.$router.$avueRouter.formatRoutes(data, true);
          }
          //当点击顶部菜单后默认打开第一个菜单
          /*if (!this.validatenull(item)) {
            let itemActive = {},
              childItemActive = 0;
            if (item.path) {
              itemActive = item;
            } else {
              if (this.menu[childItemActive].length === 0) {
                itemActive = this.menu[childItemActive];
              } else {
                itemActive = this.menu[childItemActive].children[childItemActive];
              }
            }
            this.$store.commit('SET_MENU_ID', item);
            this.$router.push({
              path: this.$router.$avueRouter.getPath({
                name: (itemActive.label || itemActive.name),
                src: itemActive.path
              }, itemActive.meta)
            });
          }*/

        });
      },
      // 定时检测token
      refreshToken() {
        this.refreshTime = setInterval(() => {
          const token = getStore({
            name: "token",
            debug: true
          }) || {};
          const date = calcDate(token.datetime, new Date().getTime());
          if (validatenull(date)) return;
          if (date.seconds >= this.website.tokenTime && !this.refreshLock) {
            this.refreshLock = true;
            this.$store
              .dispatch("refreshToken")
              .then(() => {
                this.refreshLock = false;
              })
              .catch(() => {
                this.refreshLock = false;
              });
          }
        }, 10000);
      },
      // 刷新中间页token
      refreshIntegrationToken () {
        this.refreshTime = setInterval(() => {
          const token = JSON.parse(sessionStorage.getItem('saber-token')) || {};
          const date = calcDate(token.datetime, new Date().getTime());
          if (validatenull(date)) return;
          if (date.seconds >= this.website.tokenTime && !this.refreshLock) {
            this.refreshLock = true;
            const { content: refreshToken } = JSON.parse(sessionStorage.getItem('saber-refreshToken'));
            const { content: tenantId } = JSON.parse(sessionStorage.getItem('saber-tenantId'));
            refresh(refreshToken, tenantId, this.userInfo.dept_id, this.userInfo.role_id).then(res => {
              this.refreshLock = false;
              const data = res.data;
              setStore({ name: 'token', content: data.access_token, type: 1 });
              setStore({ name: 'refreshToken', content: data.refresh_token, type: 1 });
              setStore({ name: 'userInfo', content: data, type: 1 });
              this.$store.commit('SET_USER_INFO_INTEGRATION', data);
            }).catch(() => {
              this.refreshLock = false;
            });
          }
        }, 10000);
      },
      hexToRgb(hex) {
        // 去除可能的 '#' 符号并确保长度为6
        hex = hex.replace('#', '').toLowerCase();
        if (hex.length === 3) {
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
        }

        // 将每两位16进制数转换为十进制
        const bigint = parseInt(hex, 16);
        const r = (bigint >> 16) & 255;
        const g = (bigint >> 8) & 255;
        const b = bigint & 255;

        // 返回RGB格式的字符串
        return `${r},${g},${b}`;
      }
    }
  };
</script>
<style lang="less" scoped>
.avue-left-visible {
  display: none;
}
.avue-main-container {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
}
.avue-content {
  height: 100% !important;
}
.back-container {
  position: fixed;
  top: 50%;
  right: -60px;
  z-index: 9999;
  transition: right 0.3s ease; /* 定义right属性变化的动画，持续时间为0.3秒，使用ease缓动函数 */
}
.back-container:hover {
  right: 0px;
}
// .back-btn {
//   background-color: var(--gn-color) !important;
//   color: #FFFFFF !important;
//   border-color: var(--gn-color) !important;
// }
.avue-view {
  height: 100%;
}
</style>
<style src="../../Modules/Widgets/widgets.css"></style>
