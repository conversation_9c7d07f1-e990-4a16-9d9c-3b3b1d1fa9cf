<template>
  <el-form
    ref="loginForm"
    class="login-form"
    status-icon
    :rules="loginRules"
    :model="loginForm"
    label-width="0"
  >
    <el-form-item
      v-if="tenantMode"
      prop="tenantId"
    >
      <xh-select
        v-if="isHasOptions"
        v-model="loginForm.tenantId"
        clearable
        size="medium"
        :placeholder="$t('login.tenantId')"
      >
        <el-option
          v-for="item in tenantOptions"
          :key="item.tenantId"
          :label="item.tenantName"
          :value="item.tenantId"
        />
      </xh-select>
      <el-input
        v-else
        v-model="loginForm.tenantId"
        size="medium"
        auto-complete="off"
        :placeholder="$t('login.tenantId')"
        @keyup.enter.native="handleLogin"
      />
    </el-form-item>
    <el-form-item prop="username">
      <el-input
        v-model="loginForm.username"
        size="medium"
        auto-complete="off"
        :placeholder="$t('login.username')"
        @keyup.enter.native="handleLogin"
      />
    </el-form-item>
    <el-form-item prop="password">
      <el-input
        v-model="loginForm.password"
        size="medium"
        :type="passwordType"
        auto-complete="off"
        :placeholder="$t('login.password')"
        @keyup.enter.native="handleLogin"
      >
        <img
          slot="suffix"
          class="password-icon"
          :src="getShowPassword"
          alt=""
          @click="showPassword"
        >
      </el-input>
    </el-form-item>
    <el-form-item
      v-if="this.website.captchaMode"
      prop="code"
    >
      <el-row
        :span="24"
        :gutter="20"
      >
        <el-col :span="16">
          <el-input
            v-model="loginForm.code"
            size="medium"
            auto-complete="off"
            :placeholder="$t('login.code')"
            @keyup.enter.native="handleLogin"
          />
        </el-col>
        <el-col :span="8">
          <div class="login-code">
            <img
              :src="loginForm.image"
              class="login-code-img"
              @click="refreshCode"
            >
          </div>
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        size="medium"
        class="login-submit"
        @click.native.prevent="handleLogin"
      >{{ $t('login.submit') }}
      </el-button>
    </el-form-item>
    <el-dialog
      title="用户信息选择"
      append-to-body
      :visible.sync="userBox"
      width="350px"
    >
      <avue-form
        v-model="userForm"
        :option="userOption"
        @submit="submitLogin"
      />
    </el-dialog>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex';
import { info } from '@/api/system/tenant';
import { getCaptcha, tenantList, getVersion } from '@/api/user';
import { getTopUrl } from '@/util/util';
import website from '@/config/website';

export default {
  name: 'Userlogin',
  data() {
    return {
      tenantMode: this.website.tenantMode,
      loginForm: {
        //租户ID
        tenantId: '000000',
        //部门ID
        deptId: '',
        //角色ID
        roleId: '',
        //用户名
        username: '',
        //密码
        password: '',
        //账号类型
        type: 'account',
        //验证码的值
        code: '',
        //验证码的索引
        key: '',
        //预加载白色背景
        image: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
      },
      loginRules: {
        tenantId: [
          {
            required: false,
            message: '请输入租户ID',
            trigger: 'blur'
          }
        ],
        username: [
          {
            required: true,
            message: '请输入帐号',
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur'
          },
          {
            min: 1,
            message: '密码长度最少为6位',
            trigger: 'blur'
          }
        ]
      },
      passwordType: 'password',
      userBox: false,
      userForm: {
        deptId: '',
        roleId: ''
      },
      userOption: {
        labelWidth: 70,
        submitBtn: true,
        emptyBtn: false,
        submitText: '登录',
        column: [
          {
            label: '部门',
            prop: 'deptId',
            type: 'select',
            props: {
              label: 'deptName',
              value: 'id'
            },
            dicUrl: '/blade-system/dept/select',
            span: 24,
            display: false,
            rules: [
              {
                required: true,
                message: '请选择部门',
                trigger: 'blur'
              }
            ]
          },
          {
            label: '角色',
            prop: 'roleId',
            type: 'select',
            props: {
              label: 'roleName',
              value: 'id'
            },
            dicUrl: '/blade-system/role/select',
            span: 24,
            display: false,
            rules: [
              {
                required: true,
                message: '请选择角色',
                trigger: 'blur'
              }
            ]
          }
        ]
      },
      isShowPasswordIcon: require('@/assets/images/login/show-password.png'),
      notShowPasswordIcon: require('@/assets/images/login/not-show-password.png'),
      tenantOptions: [],
      isHasOptions: false
    };
  },
  watch: {
    'loginForm.deptId'() {
      const column = this.findObject(this.userOption.column, 'deptId');
      if (this.loginForm.deptId.includes(',')) {
        column.dicUrl = website.apiPrefix + `/blade-system/dept/select?deptId=${this.loginForm.deptId}`;
        column.display = true;
      }
      else {
        column.dicUrl = '';
      }
    },
    'loginForm.roleId'() {
      const column = this.findObject(this.userOption.column, 'roleId');
      if (this.loginForm.roleId.includes(',')) {
        column.dicUrl = website.apiPrefix + `/blade-system/role/select?roleId=${this.loginForm.roleId}`;
        column.display = true;
      }
      else {
        column.dicUrl = '';
      }
    }
  },
  created() {
    this.getSystemVersion();
    this.getTenant();
    this.refreshCode();
    this.getTenantList();
  },
  mounted() {
  },
  computed: {
    ...mapGetters([
      'tagWel',
      'userInfo'
    ]),
    getShowPassword() {
      return this.passwordType === 'password' ? this.notShowPasswordIcon : this.isShowPasswordIcon;
    }
  },
  props: [],
  methods: {
    getTenantList() {
      tenantList().then(res => {
        if (res.data.code === 200) {
          this.tenantOptions = res.data.data;
          if (this.tenantOptions && this.tenantOptions.length) {
            this.isHasOptions = true;
          }
        }
      });
    },
    refreshCode() {
      if (this.website.captchaMode) {
        getCaptcha().then(res => {
          const data = res.data;
          this.loginForm.key = data.key;
          this.loginForm.image = data.image;
        });
      }
    },
    getSystemVersion() {
      getVersion().then(res => {
        const { data } = res.data;
        this.$emit('editSystemVersion', data);
      });
    },
    showPassword() {
      this.passwordType === ''
        ? (this.passwordType = 'password')
        : (this.passwordType = '');
    },
    submitLogin(form, done) {
      if (form.deptId !== '') {
        this.loginForm.deptId = form.deptId;
      }
      if (form.roleId !== '') {
        this.loginForm.roleId = form.roleId;
      }
      this.handleLogin();
      done();
    },
    handleLogin() {
      console.log('-> this.loginForm', this.loginForm);
      if (!this.loginForm.username) {
        this.$message.error('请输入用户名');
        return false;
      }
      if (!this.loginForm.password) {
        this.$message.error('请输入密码');
        return false;
      }
      if (!this.loginForm.code) {
        this.$message.error('请输入验证码');
        return false;
      }
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '登录中,请稍后。。。',
            spinner: 'el-icon-loading'
          });
          this.$store.dispatch('LoginByUsername', this.loginForm).then(() => {
            if (this.website.switchMode) {
              const deptId = this.userInfo.dept_id;
              const roleId = this.userInfo.role_id;
              if (deptId.includes(',') || roleId.includes(',')) {
                this.loginForm.deptId = deptId;
                this.loginForm.roleId = roleId;
                this.userBox = true;
                this.$store.dispatch('LogOut').then(() => {
                  loading.close();
                });
                return false;
              }
            }
            this.$router.push({ path: this.tagWel.value });
            loading.close();
          }).catch(() => {
            loading.close();
            this.refreshCode();
          });
        }
      });
    },
    getTenant() {
      let domain = getTopUrl();
      // 临时指定域名，方便测试
      //domain = "https://bladex.vip";
      info(domain).then(res => {
        const data = res.data;
        if (data.success && data.data.tenantId) {
          this.tenantMode = false;
          this.loginForm.tenantId = data.data.tenantId;
          this.$parent.$refs.login.style.backgroundImage = `url(${data.data.backgroundUrl})`;
        }
      });
    }
  }
};
</script>

<style>
</style>
