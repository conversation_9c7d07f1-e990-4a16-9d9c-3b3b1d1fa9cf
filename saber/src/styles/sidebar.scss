
.el-menu--popup {
  .el-menu-item {
    height: 44px;
    line-height: 44px;
    background-color: #20222a;
    i, span {
      color: rgba(255, 255, 255, 0.9);
    }
    &:hover {
      i, span {
        color: #000000;
      }
    }
    &.is-active {
      background-color: rgba(1, 38, 119, 0.8);
      &:before {
        content: '';
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: transparent;
        position: absolute;
      }
      i, span {
        color: #fff;
      }
    }
  }
  // 有子级菜单的二级菜单样式
  .menu-wrapper>.el-submenu>.el-submenu__title {
    height: 44px;
    line-height: 44px;
    background-color: #20222a;
    i, span {
      color: rgba(255, 255, 255, 0.9);
    }
    &:hover {
      background-color: #ecf5ff;
      i, span {
        color: #000000;
      }
    }
  }
}
.avue-sidebar {
  user-select: none;
  position: relative;
  // padding-top: 60px;
  height: 100%;
  background-image: linear-gradient(to bottom, #ECF2F6, #ECF2F6);
  transition: width .2s;
  box-sizing: border-box;
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
  .el-submenu {
    .el-menu-item {
      background-color: #EBF1F6;
    }
  }
  .el-scrollbar__wrap {
    overflow-x: hidden;
    // FIXME 设置为0, 防止菜单栏出现闪烁问题(不确定问题是否出在这里), 将滚动条设置为3px, 视觉效果跟0px一致, 设置为0px、1px、2px依旧会闪烁
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    &::-webkit-scrollbar{
      width: 3px !important;
    }
  }
  &--tip {
    width: 90%;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    position: absolute;
    top: 5px;
    left: 5%;
    color: #ccc;
    z-index: 2;
    text-align: center;
    font-size: 14px;
    background-color: rgba(0, 0, 0, .4);
  }
  .el-menu-item, .el-submenu__title {
    border-bottom: 1px solid #EBF1F6;
    height: 44px !important;
    line-height: 44px !important;
    i:nth-child(1) {
      margin-right: 10px;
    }
    i, span {
      color: #606266;
    }
    &:hover {
      background: #F5F8FB;
      i, span {
        color: #409EFF;
      }
    }
    &.is-active {
      &:before {
        content: '';
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: #409EFF;
        position: absolute;
      }
      background-color: #F5F8FB;
      i, span {
        color: #409EFF;
      }
    }
  }
}
