$height: 50px;
.avue-top {
    padding: 0 20px 0 0;
    position: relative;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    color: #000000;
    font-size: 28px;
    height: $height;
    line-height: $height;
    box-sizing: border-box;
    white-space: nowrap;
    background: linear-gradient(to right, #EBF1F6, #EBF1F6);
    // background: url('../assets/images/top-bg.png') center no-repeat;
    // background-size: 100% 100%;
    // color: #fff;
    .el-menu-item {
        i, span {
            font-size: 13px;
        }
    }
    .el-menu--horizontal > .el-menu-item {        height: $height;
        line-height: $height;
    }
}
.avue-breadcrumb {
    height: 100%;
    i {
        font-size: 30px !important;
    }
    &--active {
        transform: rotate(90deg);
    }
}
.top-menu {
    box-sizing: border-box;
    .el-menu-item {
        padding: 0 10px;
        border: none !important;
    }
}
.top-search {
    line-height: 50px;
    position: absolute !important;
    left: 20px;
    top: 0;
    width: 400px !important;
    .el-input__inner {
        font-size: 13px;
        border: none;
        background-color: transparent;
        &::placeholder {
            color: #fff;
        }
    }
}
.top-bar__right__user {
    display: flex;
    align-items: center;
    padding: 0 12px;
    height: 100%;
    .top-bar__img {
        margin: -2px 8px 0 5px;
        //padding: 2px;
        width: 24px;
        height: 24px;
        border-radius: 100%;
        box-sizing: border-box;
        border: 2px solid #000000;
        vertical-align: middle;
    }
    .el-dropdown-link {
        cursor: pointer;
    }
    .top-bar__user_name {
        font-size: 16px;
        color: #000000;
        position: relative;
        top: 1px;
    }
}
.top-bar__left,
.top-bar__right {
    height: 100%;
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    i {
        line-height: 28px;
        font-size: 22px !important;
    }
}
.top-bar__left {
    left: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 12px 0;
}
.top-bar__right {
    right: 20px;
    display: flex;
    align-items: center;
    .top-bar__right__item {
        width: 56px;
        height: 28px;
        border-right: 1px solid #000000;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
.top-bar__item {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 28px;
    margin: 0;
    font-size: 16px;
    &--show {
        display: inline-block !important;
    }
    .el-badge__content.is-fixed {
        top: 4px;
        right: 8px;
        background-color: #FF3434;
    }
    & div {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
.top-bar__title {
    height: 100%;
    padding: 0 40px;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: inherit;
    font-weight: 400;
}
.project-name,
.project-name-en {
    font-size: 22px;
    display: flex;
    align-items: center;
    height: 20px;
    margin-bottom: 2px;
    letter-spacing: 3px;
    font-family: "Microsoft YaHei", Arial, sans-serif;
    font-weight: bold;
}
.project-name-en {
    font-size: 12px;
    height: 14px;
    margin-bottom: 0;
    letter-spacing: 0;
}
.version {
    letter-spacing: 0;
}
.top-bar__logo {
    position: absolute;
    left: 0;
    width: 100px;
    height: 100%;
    display: flex;
    align-items: center;
    .logo-img {
        height: 28px;
        margin-left: 12px;
    }
}
.top-bar__right__item.time-item {
    width: 90px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    $itemHeight: 16px;
    .top-bar__time1,
    .top-bar__time2 {
        font-size: 14px;
        height: $itemHeight;
        line-height: $itemHeight;
    }
}
