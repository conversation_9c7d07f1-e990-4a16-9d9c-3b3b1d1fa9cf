:root {
    // --gn-color: #409EFF;
    --gn-color: #52C41A;
    // --gn-color的rgb值
    --gn-color-rgb: 82,196,26
}
.el-button {
    border-radius: 6px !important;
}
// 按钮样式
.gn-button-group, .avue-crud {
  .el-button+.el-button{
    margin-left: 8px !important;
  }
  // crud右侧按钮(与上方el-button保持一致)
  .el-tooltip+.el-tooltip{
    margin-left: 8px !important;
  }
}
// 主按钮样式
.el-button--primary, .common-search-btn {
    background-color: var(--gn-color) !important;
    color: #FFFFFF !important;
    border-color: var(--gn-color) !important;
}
// 次按钮样式
.el-button--default, .common-default-btn {
    background: #FFFFFF !important;
    color: var(--gn-color) !important;
    border-color: var(--gn-color) !important;
}
// 表格操作列文字样式
.avue-view .el-button--text {
    color: var(--gn-color);
}
// 表格操作栏按钮样式
.table-button-edit, .table-button-edit+span {
    color: #1991FF !important;
}
.table-button-del, .table-button-del+span {
    color: #E32D2D !important;
}
.table-button-view, .table-button-view+span {
    color: #FB7B2B !important;
}
.table-button-add, .table-button-add+span {
    color: #29C32A !important;
}
.is-disabled.table-button-edit,
.is-disabled.table-button-del,
.is-disabled.table-button-view,
.is-disabled.table-button-add,
{
  span {
    color: #c0c4cc !important;
  }
}
// 表格特殊文字样式
.active-label {
    color: var(--gn-color) !important;
    cursor: pointer;
    text-decoration: underline;
}

// 分页按钮样式
.el-pagination {
    padding: 5px !important;
    &.is-background {
        .el-pager li, .btn-next, .btn-prev {
            background-color: #FFFFFF !important;
            border: 1px solid #D9D9D9;
        }
    }
}
.el-pagination.is-background {
    .btn-prev, .btn-next, .el-pager li {
        border-radius: 6px !important;
    }
}
.el-pagination.is-background .el-pager li:not(.disabled).active, .el-pagination.is-background .el-pager li:not(.disabled).active:hover {
    background-color: var(--gn-color) !important;
    color: #FFFFFF !important;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: var(--gn-color) !important;
}
// radio按钮组
.el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background-color: var(--gn-color) !important;
    color: #FFFFFF !important;
    border-color: var(--gn-color) !important;
    box-shadow: -1px 0 0 0 var(--gn-color) !important;
}
.el-radio-button__inner:hover {
    color: var(--gn-color) !important;
}
.el-radio.is-bordered.is-checked {
    border-color: var(--gn-color) !important;
}
// input输入框
.el-input__inner {
    color: #646464 !important;
    border: 1px solid #E3E3E3 !important;
    // padding-left: 10px !important;
    &:focus {
        border-color: #8C8C8C !important;
        // box-shadow: 0 3px 10px #D7D7D7;
        box-shadow: 0 0 0 2px #D7D7D7;
    }
    &::placeholder {
        color: #BCBCBC !important;
    }
    &:focus::placeholder {
        color: #999999 !important;
    }
}
.el-form-item.is-error .el-input__inner {
    border-color: #EE3E3E !important;
}

// select下拉框
.el-select-dropdown {
    margin-top: 4px !important;
    .popper__arrow {
        display: none !important;
    }
    .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
        background-color: #EFEFEF !important;
    }
    .el-select-dropdown__item.selected {
        color: var(--gn-color) !important;
    }
}

// autocomplete下拉框
.el-autocomplete-suggestion {
    margin-top: 5px !important;
    .popper__arrow {
        display: none !important;
    }
    .el-autocomplete-suggestion__list li:hover {
        background-color: #EFEFEF !important;
    }
}

// picker时间选择器
.el-picker-panel {
    .el-date-table td span, .el-month-table td .cell, .el-date-table td.start-date div, .el-date-table td.end-date div {
        border-radius: 0%;
    }
    .el-date-table td.current:not(.disabled) span, .el-month-table td.current:not(.disabled) .cell, .el-date-table td.start-date span, .el-date-table td.end-date span {
        color: #FFFFFF;
        background-color: var(--gn-color);
    }
    .el-date-table td.today span, .el-month-table td.today .cell {
        color: #606266;
        border: 1px solid var(--gn-color);
    }
    .el-date-table td.available:hover, .el-month-table td .cell:hover, .el-picker-panel__footer .el-button--text {
        color: var(--gn-color);
    }
    .el-date-table td.in-range div {
        background-color: #ECECEC;
    }
}

// radio单选按钮
.el-radio {
    .el-radio__input.is-checked .el-radio__inner {
        border-color: var(--gn-color);
        background: none;
    }
    .el-radio__inner {
        width: 18px;
        height: 18px;
        &::after {
          width: 6px;
          height: 6px;
          background-color: var(--gn-color);
        }
        &:hover {
            border-color: var(--gn-color);
        }
    }
    .el-radio__label {
        color: #989898 !important;
        padding-left: 8px;
    }
}

// checkbox多选框
.el-checkbox {
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: var(--gn-color) !important;
        border-color: var(--gn-color) !important;
    }
    .el-checkbox__input.is-focus .el-checkbox__inner {
        border-color: var(--gn-color);
    }
    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        &::after {
            left: 5px;
            height: 8px;
        }
        &:hover {
            border-color: var(--gn-color);
        }
    }
    .el-checkbox__label {
        color: #989898 !important;
        padding-left: 8px;
    }
}

// tabs标签页
.el-tabs {
    .el-tabs__item:hover, .el-tabs__item.is-active {
        color: var(--gn-color);
    }
    .el-tabs__active-bar {
        background-color: var(--gn-color);
    }
}

// slider滑块
.el-slider__bar {
    background-color: var(--gn-color) !important;
}
.el-slider__button {
    border-color: var(--gn-color) !important;    
}

// crud表格查询条件样式
.head-container {
    padding: 0 0 10px;
    .xh-header {
        border: 1px solid #DEDEDE;
        // box-shadow: 2px 2px 3px #E6E6E6;
        border-radius: 5px;
        padding-top: 19px;
        .xh-header-content-item {
            .el-form-item {
                margin-bottom: 19px !important;
                .el-form-item__label {
                    padding-right: 10px !important;
                }
            }
        }
    }
}

// avue表格样式
.avue-view {
    .avue-crud {
        height: 100%;
        display: flex;
        flex-direction: column;
        .avue-crud__search {
            padding: 10px 20px;
            .el-card__body {
                border: 1px solid #DEDEDE;
                box-shadow: 2px 2px 3px #E6E6E6;
                border-radius: 5px;
                padding: 19px 0 !important;
            }
            .el-collapse-item__wrap, .el-collapse-item__content, .el-collapse-item__conten, .avue-form__row {
                padding: 0 !important;
            }
            .el-collapse, .el-collapse-item__wrap {
                border: none;
            }
            .avue-form__row, .avue-form__menu, .el-form-item {
                margin-bottom: 0 !important;
            }
            .el-form-item__label {
                padding-right: 10px !important;
            }
            .avue-form__menu {
                display: flex;
                justify-content: end;
                padding: 0 32px 0 0 !important;
            }
        }
        .avue-crud__search+.el-card {
            flex: 1;
            .el-card__body {
                padding: 15px 20px 10px !important;
                .avue-crud__menu {
                    min-height: 36px;
                    padding: 0 0 15px;
                    margin-bottom: 0;
                    .avue-crud__right {
                        margin-left: 8px;
                    }
                }
                .el-form {
                    height: calc(100% - 47px);
                    .el-table {
                        font-size: 14px;
                        max-height: none !important;
                        height: 100% !important;
                        .el-table__body-wrapper { // TODO avue表格底部多出滑动条, 暂时没想到更好的办法
                            //overflow: hidden !important;
                            overflow-y: auto !important;
                        }
                        .el-table__fixed-body-wrapper {
                            height: calc(100% - 54px); // 减去表格表头高度
                        }
                    }
                }
            }
        }
        .avue-crud__pagination {
            padding: 0 20px;
        }
    }
}

// avue分页
.avue-crud__pagination {
    text-align: left;
}

// avue tree样式
.avue-tree-container {
    .basic-container__card {
        background-color: #F0F2F5;
        border: none !important;
    }
    .el-card__body {
        padding: 10px !important;
        .avue-tree {
            height: 100%;
            display: flex;
            flex-direction: column;
            .avue-tree__filter {
                margin-bottom: 20px;
                .el-input__inner {
                    height: 50px;
                    line-height: 50px;
                    border-color: rgba(var(--gn-color-rgb), 0.5) !important;
                    box-shadow: 0 0 3px 2px rgba(var(--gn-color-rgb), 0.3);
                    font-size: 14px;
                    &::placeholder {
                        font-size: 14px;
                        color: rgba(var(--gn-color-rgb), 0.5) !important;
                    }
                }
            }
            .avue-tree__content {
                background-color: #FFFFFF;
                border-radius: 5px;
                border: 2px solid rgba(var(--gn-color-rgb), 0.5);
                box-shadow: 0 0 3px 2px rgba(var(--gn-color-rgb), 0.3);
                padding: 32px 24px;
                .el-scrollbar__bar {
                    display: none;
                }
                .el-tree {
                    width: max-content;
                    min-width: 100%;
                }
                .el-tree>.el-tree-node>.el-tree-node__content {
                    margin-bottom: 10px;
                    padding: 10px !important;
                    background-color: rgba(var(--gn-color-rgb), 0.3);
                }
                .el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node {
                    .el-tree-node__content {
                        padding-top: 10px;
                        padding-bottom: 10px;
                        margin-bottom: 10px;
                    }
                }
                .el-tree-node:focus > .el-tree-node__content {
                    background: rgba(var(--gn-color-rgb), 0.7);
                }
                .el-tree-node__content {
                    height: auto;
                    border-radius: 5px;
                    &:hover {
                        background: rgba(var(--gn-color-rgb), 0.1);
                    }
                    .el-tree-node__expand-icon {
                        padding: 1px;
                        border: 1px solid rgba(var(--gn-color-rgb), 0.9);
                        margin-right: 6px;
                        background-color: 1px solid rgba(var(--gn-color-rgb), 0.7);
                        &.expanded {
                            // transform: none;
                            transform: rotate(0deg);
                        }
                    }
                    .el-icon-caret-right {
                        &::before {
                          content: '\e6d9';
                          color: rgba(var(--gn-color-rgb), 0.9);
                          font-weight: bold;
                          font-size: 12px;
                          transform:scale(.9);
                          display: inline-block;
                        }
                        &.expanded::before {
                          content: '\e6d8';
                        }
                      }
                    // 去掉展开收起图标
                    .is-leaf.el-tree-node__expand-icon {
                        display: none;
                    }
                }
            }
        }
    }
}

// avue按钮间距
.avue-group--header .avue-form__menu--center, .avue-crud__menu .avue-crud__left, .avue-crud__menu .avue-crud__right {
    letter-spacing: -3px;
    .el-button {
        margin: 0;
    }
}


// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
    // 左侧终端树
    .media-terminal-tree-container {
        width: 230px !important;
    }
}
