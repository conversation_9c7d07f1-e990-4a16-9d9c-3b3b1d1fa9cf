import request from '@/router/axios';

export const getUsualList = (current, size, params) => {
  console.log('-> params', params);
  if (params?.timerange?.length > 0) {
    params.startTime = params.timerange[0] / 1000;
    params.endTime = params.timerange[1] / 1000;
  };
  delete params.timerange;
  return request({
    url: '/blade-log/usual/log/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getApiList = (current, size, params) => {
  return request({
    url: '/blade-log/api/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getErrorList = (current, size, params) => {
  return request({
    url: '/blade-log/error/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};


export const getUsualLogs = (id) => {
  return request({
    url: '/blade-log/usual/detail',
    method: 'get',
    params: {
      id
    }
  });
};
export const getApiLogs = (id) => {
  return request({
    url: '/blade-log/api/detail',
    method: 'get',
    params: {
      id
    }
  });
};
export const getErrorLogs = (id) => {
  return request({
    url: '/blade-log/error/detail',
    method: 'get',
    params: {
      id
    }
  });
};

