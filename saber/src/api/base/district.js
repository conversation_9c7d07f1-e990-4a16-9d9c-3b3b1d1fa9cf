import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

/**
 * @typedef District 行政区划实体
 *
 * @property {Int} id 行政区划ID
 *
 * @property {Int} name 告警类型
 * @property {Int} districtAlias 提醒方式 前端自定义
 * @property {Int} districtType 行政区划类型 Enumerate_DistrictType
 * @property {String} attribute 行政区划属性
 * @property {String} remark 备注
 * @property {Array.<Point>} polygon_points 行政区划点
 */

/**
 * @typedef Point
 * @property {Number} longitude 经度（°）
 * @property {Number} latitude 纬度（°）
 */

/**
 * 行政区划树
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Array.<District>}} Result
 * @returns {Promise<Result>}
 */
export function tree (queryParams) {
  queryParams = formatPaginationParam(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/region/citys', {
      params: params
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 行政区划详情
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: District}} Result
 * @returns {Promise<Result>}
 */
export function detail (id) {
  let params = jsonToUnderline({
    cityId: id
  });
  return new Promise((resolve, reject) => {
    request.get('/baseinfo/region/districtbounds', {
      params: params
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { tree, detail };
