import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';
import store from '@/store/index';
import Store from '@/store/index';

/**
 * @typedef Dept 车组
 * @property {String} name 车组名称
 * @property {Int} type 车组类型 1企业,2其他
 * @property {String} region 所属地区
 * @property {Int} pid 上级车组ID
 * @property {String} pname 上级车组ID
 * @property {String} adminName 车组管理员名称
 * @property {String} adminPhone 车组管理员电话
 * @property {Int} enabled 状态 1使用,0不使用
 * @property {Int} label 标签
 * @property {String} updateTime 更新时间
 */

/**
 * 获取车组数据
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Dept>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function getDepts(queryParams) {
  queryParams = formatPaginationParam(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('systemsetting/dept', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车组分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Dept>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams = formatPaginationParam(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('systemsetting/dept', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车组新增
 * @param {Dept} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('systemsetting/dept', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车组删除
 * @param {Array.<Int>} ids ID数组
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del(ids) {
  return new Promise((resolve, reject) => {
    request({
      url: 'systemsetting/dept',
      method: 'delete',
      data: ids
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车组编辑
 * @param {Job} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('systemsetting/dept', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取正在使用的车组
 * @typedef {{code: Number, msg: String, data: String}} Result
 * @returns {Promise<Result>}
 */
export function getEnabledDepts() {
  return new Promise((resolve, reject) => {
    if (!Store.state.dept.dept) {
      request.get('/blade-system/dept/treeWith').then(res => {
        // let resHump = jsonToHump(res);
        // Store.commit('SET_DEPT', resHump.data);
        resolve(res);
      }).catch((error) => {
        reject(error);
      });
    } else {
      const data = Store.state.dept.dept;
      resolve(data);
    }
    // request.get('/blade-system/dept/treeWith').then(res => {
    //   let resHump = jsonToHump(res);
    //   resolve(resHump);
    // }).catch((error) => {
    //   reject(error);
    // });
  });
}

/**
 * 车组导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/systemsetting/dept/export', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车组及车辆列表
 */
export function getDeptVehicle() {
  return new Promise((resolve, reject) => {
    request.get('baseinfo/vehicle/deptlist').then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

function updateDeptPer(arr, list = [], key) {
  return arr.map((obj) => {
    const item = { ...obj };
    // item[key] = !list.includes(item.id);
    if (item.children?.length) {
      item.children = updateDeptPer(item.children, list, key);
    }
    return item;
  });
}

function getNewDepts(tenantId, isRefresh) {
  const obj = {
    url: `/blade-system/dept/treeWith`,
    method: 'get'
  };
  if (tenantId) {
    obj.params = { tenantId };
  }
  return new Promise((resolve, reject) => {
    if (!Store.state.dept.dept || isRefresh) {
      request(obj).then(res => {
        // let resHump = jsonToHump(res);
        // Store.commit('SET_DEPT', resHump.data);
        resolve(res);
      }).catch((error) => {
        reject(error);
      });
    } else {
      const data = Store.state.dept.dept;
      resolve(data);
    }
  });
}

export function getDeptPerInit(obj = {}) {
  const {
    tenantId,
    key = 'disabled',
    isRefresh = true
  } = obj;
  const promise = [getNewDepts(tenantId, isRefresh)];
  const isAdmin = store.getters?.userInfo?.role_name?.includes('administrator');
  if (!isAdmin) {
    promise.push(request({
      url: '/vdm-base-info/device/user/dept/regulates',
      method: 'get'
    }));
  }
  return new Promise((resolve, reject) => {
    if (!Store.state.dept.deptPer || isRefresh) {
      console.log('获取监管机构');
      Promise.all(promise).then(([res1, res2]) => {
        let data = res1.data || [];
        if (!isAdmin && data.length && res2) {
          data = updateDeptPer(data, res2.data, key);
        }
        // Store.commit('SET_DEPT_PER', {
        //   data,
        //   init: res1.data
        // });
        resolve({
          data,
          init: res1.data
        });
      });
    }
    else {
      console.log('获取监管机构-缓存');
      const {
        data,
        init
      } = JSON.parse(JSON.stringify(Store.state.dept.deptPer));
      resolve({
        data,
        init
      });
    }
  });
}

export function getDeptListByName(deptName) {
  return new Promise((resolve, reject) => {
    request.get('/blade-system/dept/getDeptList', { params: { deptName } }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  add,
  edit,
  del,
  getDepts,
  exportAll,
  getEnabledDepts,
  pagination,
  getDeptVehicle
};
