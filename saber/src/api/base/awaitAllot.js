import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
const JSONbig = require('json-bigint');

/**
 * 待分配终端管理分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  const current = queryParams.page + 1;
  const size = queryParams.size;
  queryParams = formatPaginationParamNew(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/virtual/target/list?current=${current}&size=${size}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 变更
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  const data = {
    deviceId: BigInt(queryParams.deviceId),
    targetModel: Number(queryParams.targetModel),
    deviceType: Number(queryParams.deviceType),
    targetFlag: queryParams.targetFlag
  };
  let params = JSONbig.stringify(jsonToUnderline(data));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/settargetmodel', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, edit };
