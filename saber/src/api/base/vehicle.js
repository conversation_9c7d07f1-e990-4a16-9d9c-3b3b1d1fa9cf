import request from '../utils/request'; // FIXME 真实联调的时候请更换到request，假数据可用requestMock
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import defaultValue from '@/utils/core/defaultValue';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';
const JSONbig = require('json-bigint');
/**
 * @typedef Vehicle 车辆
 * @property {String} licencePlate 车牌号码
 * @property {Int} vehicleModel 车辆类型
 * @property {Int} vehicleColor 车辆颜色
 * @property {Int} licenceColor 车牌颜色
 * @property {Int} vehicleState 车辆状态
 * @property {Int} vehicleUseType 行业类型
 * @property {Int} vehicleDept 车组
 * @property {Int} terminalId 序列号
 * @property {String} vendorId 厂商编号
 * @property {Int} deviceType 设备型号
 * @property {String} vehicleOwner 隶属用户
 * @property {Int} yearCheckTime 年检时间
 * @property {Int} netSignTime 入网时间
 * @property {String} updateTime 更新时间
 * @property {String} createTime 创建时间
 */

/**
 * 车辆分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @typedef {{total: Number, content: Array.<Vehicle>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/baseinfo/vehicle/list`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取全部车辆
 * @param {Object} [queryParams]
 * @typedef {{total: Number, content: Array.<Vehicle>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function all (queryParams) {
  queryParams = defaultValue(queryParams, {});
  queryParams.start = 0;
  queryParams.count = 9999;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/get', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/baseinfo/vehicle/save', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (id) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/baseinfo/vehicle/delete?ids=${id}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/baseinfo/vehicle/update', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆批量绑定
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function batchBinding (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/baseinfo/vehicle/batchBinding', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });

}



/**
 * 车辆详情
 * @param {Int} id
 * @typedef {{code: Number, msg: String, data: Vehicle}} Result
 * @returns {Promise<Result>}
 */
export function detail (id) {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/detail', {
      params: {
        id: id
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询多个车辆的详情
 * @param {Array.<Int>|String} multiIds
 * @typedef {{code: Number, msg: String, data: Array.<Vehicle>}} Result
 * @returns {Promise<Result>}
 */
export function multiDetail (multiIds) {
  if (typeof multiIds === 'object') {
    multiIds = multiIds.join(',');
  }
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/moredetail', {
      params: {
        ids: multiIds
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      console.log(9999, resHump);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });

  // return new Promise((resolve, reject) => {
  //   let multiDetails = [];
  //   for (let i = 0; i < multiIds.length; i++) {
  //     multiDetails.push(new Promise((resolve, reject) => {
  //       request.get('/baseinfo/vehicle/detail', {
  //         params: {
  //           id: multiIds[i]
  //         }
  //       }).then(res => {
  //         let resHump = jsonToHump(res);
  //         resolve(resHump.data);
  //       }).catch((error) => {
  //         reject(error);
  //       });
  //     }));
  //   }
  //   Promise.all(multiDetails).then(dataArray=>{
  //     let result = {
  //       code: 0,
  //       msg: '',
  //       data: dataArray
  //     };
  //     resolve(result);
  //   }).catch(error => {
  //     reject(error);
  //   })
  // });
}

/**
 * 查询某车辆里程统计
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function searchCarMileage (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/mileage/querylatelymileagebyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询某车辆告警统计
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function searchCarAlarm (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/historicalalarm/querylatelyalarmbyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询车辆归属
 * @param {Int}
 * @typedef {{code: Number, msg: String, data: Vehicle}} Result
 * @returns {Promise<Result>}
 */
export function searchVehicleOwnerId () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/thirdplatform/select', {
      params: {
        is_top: 1
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆信息导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  // queryParams = formatPaginationParams(queryParams);
  queryParams.current = queryParams.page+1;
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/baseinfo/vehicle/export', queryParams).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆不在线导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function offlineExport (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/offline/export', {params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构，没有通道号
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function tree (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/tree', {params: params}).then(res => {
      // console.log('/baseinfo/vehicle/tree', res);
      // let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(res.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构，没有通道号(懒加载)
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function treeNew (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/vehicle/newtree', params).then(res => {
      resolve(res.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构，没有通道号(新)
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function deptTagTree () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/target/depttagtree').then(res => {
      resolve(res?.data || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 动态树结构接口
 * @param {Object} queryParams
 * @param {Array} [queryParams.expandedNodeIds] 展开的节点ID列表
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data为树结构数组
 * @returns {Promise<r>}
 */
export function dynamicTree (queryParams = {}) {
  const expandedNodeIds = queryParams.expandedNodeIds || [];
  if (!expandedNodeIds.includes(0)) {
    expandedNodeIds.unshift(0);
  }

  const params = JSONbig.stringify({
    expandedNodeIds
  });
  return new Promise((resolve, reject) => {
    request.post('/vdm-websocket/tree/dynamicTree', params).then(res => {
      resolve(res?.data || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构，带有通道号
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function treeWithChannel (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/rnsstree/channel', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构，带有通道号(新)
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function deptTagTreeChannel () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/target/depttagtree/channel').then(res => {
      resolve(res?.data || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构(公务车)
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function officialTree (queryParams) {
  queryParams.unitCode = queryParams.unitCode ? queryParams.unitCode : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/target/depttagtreegwc', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump?.data || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车牌号模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function searchVehicle (queryParams, id) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/match', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(id === 'id' ? resHump.data.list : resHump.data.resData);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 批量导入车辆
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function addbatch (queryParams, importDeptId) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/baseinfo/vehicle/importExcel?deptId='+importDeptId, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端手机号/唯一性编码 模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function queryPhone (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/device/queryterminalphoneblur', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 外设序列号模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function queryDeviceSerial (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/device/querydeviceserialblur', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取上级平台
 * @param {Object} queryParams
 * @param {Array} [queryParams.id]
 * @returns {Promise<Result>}
 */
export function getPlatformList () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/thirdplatform/select', {
      params: {
        is_top: 1
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      let list = [];
      Object.keys(resHump.data).forEach((item)=>{
        let form = {
          label: resHump.data[item],
          value: item
        };
        list.push(form);
      });
      resolve(list);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function getRuleBindRealtime() {
  return new Promise((resolve, reject) => {
    request.get('/security-wrapper/securitymanagement/rulebindrealtime').then(res => {
      resolve(res.data);
    }).catch((error) => {
      reject(error);
    });
  });
}
export function pushRule(data){
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/pushrule', data).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
// export function getPlatformList (queryParams) {
//   // let params = jsonToUnderline(queryParams);
//   return new Promise((resolve, reject) => {
//     request.get('/regulatorycenter/platform', {params: queryParams}).then(res => {
//       let result = res.data.res_data.map(item => {
//         return {
//           value: item.id,
//           label: item.name
//         };
//       });
//       resolve(result);
//     }).catch((error) => {
//       reject(error);
//     });
//   });
// }

/**
 * 获取单位树结构
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function deptTree () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/dept/getdepttree').then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 名称模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function searchName (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/matchdevice', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump.data);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function rnssTree (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/tree/rnss', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function rnssChannelTree (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/vehicle/tree/channelrnss', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}


export function unbind({id, targetType, deptId}) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/baseinfo/vehicle/unbind?id=${id}&targetType=${targetType}&deptId=${deptId}`).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 根据组织机构查询子级终端信息
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function devicelocstates (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/vehicle/devicelocstates', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function connectuniqueid(queryParams) {
  let params = JSONbig.stringify(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/target/connectuniqueid`, params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 批量修改组织
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function updateDeptBatch(queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/baseinfo/vehicle/batchUpdate', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  add,
  edit,
  del,
  pagination,
  detail,
  multiDetail,
  searchCarMileage,
  searchCarAlarm,
  all,
  exportAll,
  tree,
  treeNew,
  deptTagTree,
  dynamicTree,
  deptTagTreeChannel,
  treeWithChannel,
  officialTree,
  updateDeptBatch,
  searchVehicleOwnerId,
  searchVehicle,
  searchName,
  addbatch,
  getPlatformList,
  queryPhone,
  queryDeviceSerial,
  getRuleBindRealtime,
  pushRule,
  deptTree,
  devicelocstates,
  connectuniqueid
};
