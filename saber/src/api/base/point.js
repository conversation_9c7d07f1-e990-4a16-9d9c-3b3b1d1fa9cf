import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

/**
 * @typedef Point 预设点实体
 *
 * @property {Int} id 预设点ID
 *
 * @property {String} pointName 告警类型
 * @property {Int} pointType 预设点类型 Enumerate_PointType
 * @property {Int} mapLevel 预设点属性
 * @property {String} remark 备注
 * @property {Number} longitude 经度（°）
 * @property {Number} latitude 纬度（°）
 */

/**
 * 预设点分页
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.pointName] 预设点名称
 * @typedef {{total: Number, content: Array.<Point>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParam(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo/point/list', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 预设点新增
 * @param {Point} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/point/add', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 预设点删除
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function del (id) {
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/point/delete', {
      id: id
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 预设点修改
 * @param {Point} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/point/edit', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 预设点详情
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: Point}} Result
 * @returns {Promise<Result>}
 */
export function detail (id) {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo/point/detail', {
      params: {
        id: id
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 预设点导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/point/export', {params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { add, edit, del, pagination, detail, exportAll };
