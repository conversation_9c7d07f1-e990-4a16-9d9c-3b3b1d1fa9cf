import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
const JSONbig = require('json-bigint');
import moment from 'moment';
import axios from 'axios';

/**
 * 区域新增
 * @param {Region} queryParams
 * @returns {Promise<Result>}
 */
export function regionAdd (queryParams) {
  queryParams.deptId = BigInt(queryParams.deptId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/region/add', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域删除
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function regionDel (id) {
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/region/delete', {
      id: id
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域修改
 * @param {Region} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function regionEdit (queryParams) {
  queryParams.deptId = BigInt(queryParams.deptId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/region/edit', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域详情
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: Region}} Result
 * @returns {Promise<Result>}
 */
export function regionDetail (id) {
  return new Promise((resolve, reject) => {
    request.get(`/baseinfo-wrapper/baseinfo/region/detail?id=${id}`).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/region/export', {params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域分类新增
 * @param {Region} queryParams
 * @returns {Promise<Result>}
 */
export function regionlassifyAdd (queryParams) {
  queryParams.deptId = BigInt(queryParams.deptId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/region/group/add', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域分类修改
 * @param {Region} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function regionlassifyEdit (queryParams) {
  queryParams.deptId = BigInt(queryParams.deptId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/region/group/update', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域分类删除
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function regionlassifyDel (id) {
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/region/group/delete', {
      id: id
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}


/**
 * 获取区域树结构
 * @param
 * @typedef {{code: Number, msg: String, data: Region}} Result
 * @returns {Promise<Result>}
 */
export function regionGroups () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/region/groups').then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域下发查询
 * @param {Region} queryParams
 * @returns {Promise<Result>}
 */
export function querypolybyvehicle (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/querypolybyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


/**
 * 区域下发新增
 * @param {Region} queryParams
 * @returns {Promise<Result>}
 */
export function setpolybyvehicle (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/setpolybyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域下发新增(批量)
 * @param {Region} queryParams
 * @returns {Promise<Result>}
 */
export function polybyVehicleAll (queryParams) {
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : undefined;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/setpolybyvehicles', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域下发编辑
 * @param {Region} queryParams
 * @returns {Promise<Result>}
 */
export function editpolybyvehicle (queryParams) {
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : undefined;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : undefined;
  queryParams.fence.deptId = queryParams.fence?.deptId ? BigInt(queryParams.fence.deptId) : undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/editpolybyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域下发删除
 * @param {Region} queryParams
 * @returns {Promise<Result>}
 */
export function deletepolybyvehicle (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/deletepolybyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取所有电子围栏区域
 * @param
 * @typedef {{code: Number, msg: String, data: Region}} Result
 * @returns {Promise<Result>}
 */
export function regionAll () {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/region/fence/list').then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 国能地图POI
 * @param {Object} data
 * @returns {Object}
 */
export function gnPlaceSearch (data) {
  return new Promise((resolve, reject) => {
    axios.post('https://maplbs.ceic.com/weatherUtils/feature/placeSearch', data, {
      authorization: false,
      meta: {
        isToken: false
      },
      withCredentials: false
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
export default { regionAdd, regionEdit, regionDel, regionDetail, exportAll, regionGroups, regionlassifyAdd, regionlassifyEdit, regionlassifyDel, querypolybyvehicle, setpolybyvehicle, polybyVehicleAll, editpolybyvehicle, deletepolybyvehicle, regionAll };
