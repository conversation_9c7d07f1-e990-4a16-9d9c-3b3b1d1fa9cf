import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';

/**
 * 系统字典分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/dict/parent-list`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 系统字典新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/blade-system/dict/submit', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 系统字典删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (id) {
  let ids = id.join(',');
  return new Promise((resolve, reject) => {
    request.post(`/blade-system/dict/remove?ids=${ids}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 系统字典修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/blade-system/dict/submit', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 系统字典子级分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function getChildList (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/dict/child-list`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 系统字典详情
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getDictionary (queryParams) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/dict/dictionary`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 系统字典树
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getDictTree () {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/dict/tree?code=DICT`).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, edit, del, getChildList, getDictionary, getDictTree };
