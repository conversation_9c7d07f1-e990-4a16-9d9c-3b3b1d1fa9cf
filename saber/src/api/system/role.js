import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';

/**
 * 角色管理分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/role/list`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 角色新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/blade-system/role/submit', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 角色删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (id) {
  let ids = id.join(',');
  return new Promise((resolve, reject) => {
    request.post(`/blade-system/role/remove?ids=${ids}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 角色修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/blade-system/role/submit', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 权限树
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function grantTree () {
  return new Promise((resolve, reject) => {
    request.get('/blade-system/menu/grant-tree').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 权限树修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function grant (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/blade-system/role/grant', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 该用户角色权限
 * @param {String} roleIds
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function getRole (roleIds) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/role-tree-keys?roleIds=${roleIds}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 角色列表
 * @param {String} tenantId
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function getRoleTree (tenantId) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/role/tree`, {params: {tenantId}}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 上级角色权限树
 * @param {String} roleId
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function getRoleTreeById (roleId) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/role/tree-by-id`, {params: {roleId}}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, edit, del, grantTree, grant, getRole, getRoleTreeById };
