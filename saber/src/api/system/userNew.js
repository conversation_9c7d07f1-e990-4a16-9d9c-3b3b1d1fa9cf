import request from "@/router/axios";
import btRequest from "../utils/request";
import jsonToUnderline from "@/utils/helper/jsonToUnderline";
import jsonToHump from "@/utils/helper/jsonToHump";

export function pagination(queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.size = queryParams.size;
  const { current, size, ...params } = queryParams;
  return new Promise((resolve, reject) => {
    request({
      url: "/blade-user/page",
      method: "get",
      params: {
        ...params,
        current,
        size,
      },
    })
      .then((res) => {
        let resHump = jsonToHump(res.data);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: {
            content: resHump.data.records,
            total: resHump.data.total,
          },
        };
        console.log(result, res);
        resolve(result);
      })
      .catch((error) => {
        reject(error);
      });
  });
}
export const del = (ids) => {
  return new Promise((resolve, reject) => {
    request({
      url: "/blade-user/remove",
      method: "post",
      params: {
        ids: ids.join(',')
      },
    })
      .then((res) => {
        let resHump = jsonToHump(res);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: resHump.data.resData,
        };
        resolve(result);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export const add = (row) => {
  return new Promise((resolve, reject) => {
  request({
    url: "/blade-user/submit",
    method: "post",
    data: row,
    params: {
      monitDeptId: row.monitDeptId,
    },
  }).then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const edit = (row) => {
  return new Promise((resolve, reject) => {
    request({
      url: "/blade-user/update",
      method: "post",
      data: row,
      params: {
        monitDeptId: row.monitDeptId,
      },
    }).then((res) => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData,
      };
      resolve(result);
    })
    .catch((error) => {
      reject(error);
    });
});
}

export const updatePlatform = (userId, userType, userExt) => {
  return new Promise((resolve, reject) => {
  request({
    url: "/blade-user/update-platform",
    method: "post",
    params: {
      userId,
      userType,
      userExt,
    },
  }).then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getUser = (id) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/blade-user/detail',
      method: 'get',
      params: {
        id,
      }
    }).then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getUserPlatform = (id) => {
  return new Promise((resolve, reject) => {
    request({
    url: "/blade-user/platform-detail",
    method: "get",
    params: {
      id,
    },
  }).then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getUserInfo = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/blade-user/info',
      method: 'get',
    }).then((res) => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData,
      };
      resolve(result);
    })
    .catch((error) => {
      reject(error);
    })
});
}
export const resetPassword = (userIds) => {
  return new Promise((resolve, reject) => {
    request({
    url: "/blade-user/reset-password",
    method: "post",
    params: {
      userIds,
    },
  }).then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const updatePassword = (oldPassword, newPassword, newPassword1) => {
  return new Promise((resolve, reject) => {
    request({
    url: "/blade-user/update-password",
    method: "post",
    params: {
      oldPassword,
      newPassword,
      newPassword1,
    },
  }).then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const updateInfo = (row) => {
  return new Promise((resolve, reject) => {
    request({
    url: "/blade-user/update-info",
    method: "post",
    data: row,
  }).then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const grant = (userIds, roleIds) => {
  return request({
    url: "/blade-user/grant",
    method: "post",
    params: {
      userIds,
      roleIds,
    },
  });
};

export const lock = (userIds) => {
  return request({
    url: "blade-user/disableUser",
    method: "get",
    params: {
      userIds,
    },
  });
};

export const unlock = (userIds) => {
  return request({
    url: "/blade-user/unlock",
    method: "post",
    params: {
      userIds,
    },
  });
};

/**
 * 获取用户绑定的车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.userId 用户ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function userBindVehicleDetail(queryParams) {
  queryParams.size = undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    btRequest
      .get(`/baseinfo-wrapper/baseinfo/user/bindvehiclelist`, {
        params: params,
      })
      .then((res) => {
        let resHump = jsonToHump(res);
        resolve(resHump);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

/**
 * 获取用户未绑定的车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.userId 用户ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function userUnbindVehicleDetail(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    btRequest
      .get(`/baseinfo-wrapper/baseinfo/user/unbindvehiclelist`, {
        params: params,
      })
      .then((res) => {
        let resHump = jsonToHump(res);
        resolve(resHump);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

/**
 * 用户绑定车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.userId 用户ID
 * @param {Array.<Int>} queryParams.vehicleIds 车辆ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function userBindVehicleEdit(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    btRequest
      .post("/baseinfo-wrapper/baseinfo/user/bindvehicle", params)
      .then((res) => {
        let resHump = jsonToHump(res);
        resolve(resHump);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function addbatch(queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/blade-user/importExcel', queryParams).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
export function exportAll(queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.size = queryParams.size;
  const { current, size, page, ...params } = queryParams;
  return new Promise((resolve, reject) => {
    request.post(`/blade-user/export?current=${current}&size=${size}`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  add,
  addbatch,
  exportAll,
  edit,
  del,
  pagination,
};