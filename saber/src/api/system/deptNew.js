import request from "@/router/axios";
import btRequest from "../utils/request";
import jsonToUnderline from "@/utils/helper/jsonToUnderline";
import jsonToHump from "@/utils/helper/jsonToHump";

export function pagination() {
  return new Promise((resolve, reject) => {
    request({
        url: '/blade-system/dept/lazy-list',
        method: 'get',
        params: {
          parentId: 0
        }
  }).then((res) => {
    let resHump = jsonToHump(res.data);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: {
        content: resHump.data,
        total: resHump.data,
      },
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getLazyList = (parentId, params) => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/lazy-list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  })
  .then((res) => {
    let resHump = jsonToHump(res);
    resolve(resHump.data.data);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const del = (ids) => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/remove',
    method: 'post',
    params: {
      ids: ids.join(','),
    }
  })
  .then((res) => {
    resolve(res);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const add = ({id, ...row}) => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/submit',
    method: 'post',
    data: {id, ...row}
  })
  .then((res) => {
    resolve(res.data);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const edit = (row) => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/submit',
    method: 'post',
    data: row
  })
  .then((res) => {
    resolve(res.data);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getDept = (id) => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/detail',
    method: 'get',
    params: {
      id,
    }
  })
  .then((res) => {
    let resHump = jsonToHump(res);
    resolve(resHump.data.data);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getDeptTree = () => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/treeAll',
    method: 'get'
  })
  .then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getUserDeptTree = (tenantId) => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/treeWith',
    method: 'get',
    params: {
      tenantId,
    }
  })
  .then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export const getDeptLazyTree = (parentId) => {
  return new Promise((resolve, reject) => {
    request({
    url: '/blade-system/dept/lazy-tree',
    method: 'get',
    params: {
      parentId
    }
  })
  .then((res) => {
    let resHump = jsonToHump(res);
    let result = {
      code: resHump.code,
      msg: resHump.msg,
      data: resHump.data.resData,
    };
    resolve(result);
  })
  .catch((error) => {
    reject(error);
  });
});
}

export default {
  add,
  edit,
  del,
  pagination,
};
