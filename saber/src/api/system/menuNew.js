import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';

/**
 * 菜单管理分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/lazy-list`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 菜单新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/blade-system/menu/submit', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 菜单删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (id) {
  let ids = id.join(',');
  return new Promise((resolve, reject) => {
    request.post(`/blade-system/menu/remove?ids=${ids}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 菜单修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/blade-system/menu/submit', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 菜单列表(包含按钮权限)
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getLazyList (queryParams) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/lazy-list`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 菜单页面(不包含按钮权限)
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getLazyMenuList (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/lazy-menu-list`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 *
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getMenuList (queryParams) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/menu-list`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 菜单列表(不含按钮权限)
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getMenuTree (tenantId) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/tree`, {params: {tenantId}}).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 菜单详情
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getMenu (id) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/detail`, {params: {id}}).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 顶部菜单
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getTopMenu () {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/top-menu`).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 系统菜单
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function getRoutes (topMenuId) {
  return new Promise((resolve, reject) => {
    request.get(`/blade-system/menu/routes`, {params: {topMenuId}}).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, edit, del, getLazyList, getLazyMenuList, getMenuList, getMenuTree, getMenu, getTopMenu, getRoutes };
