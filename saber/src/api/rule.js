import request from './utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';
const JSONbig = require('json-bigint');

/**
 * 告警规则树
 * @param {Object} queryParams
 * @param {Number} [queryParams.parent_id=0] 父级id
 * @returns {Promise<PaginationResult>}
 */
export function get(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/security-wrapper/securitymanagement/ruletree`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 告警规则分配树
 * @param {Object} queryParams
 * @param {Number} [queryParams.parent_id=0] 父级id
 * @returns {Promise<PaginationResult>}
 */
export function getRuleAllotTree(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/security-wrapper/securitymanagement/rulebindtree`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 告警规则分配树
 * @param {Object} queryParams
 * @param {Number} [queryParams.parent_id=0] 父级id
 * @returns {Promise<PaginationResult>}
 */
export function allRuleSbysystemDept(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/security-wrapper/securitymanagement/allrulesbysystemdept`, {params: params}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 根据组织机构id查询电子围栏
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function regionFencebyDept(queryParams) {
  queryParams.deptId = queryParams.deptId ? BigInt(queryParams.deptId) : undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post(`/security-wrapper/securitymanagement/fenceby`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data || []
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则管理分页
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {Int} [queryParams.alarmType] 告警类型
 * @param {Int} [queryParams.autoDeal] 自动成就
 * @param {Int} [queryParams.isOpen] 是否开启
 * @typedef {{total: Number, content: Array.<AlarmSetting>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.size = queryParams.size;
  queryParams.page = undefined;
  queryParams.sort = undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/rulelist', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则新增
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  const deptIdList = typeof queryParams.deptIds === 'string' ? [queryParams.deptIds] : queryParams.deptIds;
  queryParams.deptIds = deptIdList.map(item => BigInt(item));
  queryParams.creator = undefined;
  queryParams.deptId = undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/ruleaddoredit', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则删除
 * @param {Array.<String>} ids 主键
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function del (ids) {
  let params = jsonToUnderline(ids);
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/ruledel', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则修改
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  queryParams.deptIds = undefined;
  queryParams.deptId = queryParams.deptId ? BigInt(queryParams.deptId) : undefined;
  queryParams.creator = queryParams.creator ? BigInt(queryParams.creator) : undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/ruleaddoredit', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则分配
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */
export function ruleBindVehicle(queryParams) {
  queryParams.addTags = queryParams.addTags.map(item => BigInt(item));
  queryParams.delTags = queryParams.delTags.map(item => BigInt(item));
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/rulebind', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则日志列表
 * @param {Object} queryParams
 * @param {Number} [queryParams.startTime] 开始时间
 * @param {Number} [queryParams.endTime] 结束时间
 * @param {String} [queryParams.ruleName] 规则名称
 * @param {Number} [queryParams.export] 导出（0：不导出 1：导出）
 * @param {Number} [queryParams.rule_type_id] 规则类型id（查询单条规则的操作日志）
 * @param {Number} [queryParams.ruleId] 规则id(查询单条规则的操作日志)
 * @returns {Promise<PaginationResult>}
 */
export function getLogs(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/security-wrapper/securitymanagement/rulelog`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 绑定车辆详情列表
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号
 * @param {Number} [queryParams.rule_type_id] 规则类型id（查询单条规则的操作日志）
 * @param {Number} [queryParams.ruleId] 规则id(查询单条规则的操作日志)
 * @returns {Promise<PaginationResult>}
 */
export function getRuleVehicle(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/security-wrapper/securitymanagement/rulebinds`, {params: params}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function ruledoc(ruleType) {
  return new Promise((resolve, reject) => {
    request.get(`/security-wrapper/securitymanagement/ruledoc?rule_type=${ruleType}`, ).then(res => {
      let resHump = jsonToHump(res);
      const result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      }
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  })
}

export function deviceTree() {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-alarm/config/rules/deptDevice`, ).then(res => {
      let resHump = jsonToHump(res);
      const result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则分配告警规则列表查询
 * @param {Object} queryParams
 * @param {Number} [queryParams.parent_id=0] 父级id
 * @returns {Promise<PaginationResult>}
 */
export function ruleListWith(queryParams) {
  queryParams.deptId = queryParams.deptId ? BigInt(queryParams.deptId) : undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post(`/security-wrapper/securitymanagement/rulelistwith`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则分配监管对象列表查询
 * @param {Object} queryParams
 * @param {Number} [queryParams.parent_id=0] 父级id
 * @returns {Promise<PaginationResult>}
 */
export function ruleBindTags(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/security-wrapper/securitymanagement/rulebindtags`, {params: params}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 规则分配监管对象列表查询
 * @param {Object} queryParams
 * @param {Number} [queryParams.parent_id=0] 父级id
 * @returns {Promise<PaginationResult>}
 */
export function ruledetail(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/security-wrapper/securitymanagement/ruledetail`, {params: params}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { get, getRuleAllotTree, pagination, add, del, edit, ruleBindVehicle, getLogs, getRuleVehicle, ruledoc, deviceTree, ruleListWith, ruleBindTags, ruledetail, allRuleSbysystemDept, regionFencebyDept };
