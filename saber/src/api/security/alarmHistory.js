import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';
import moment from 'moment';
// import autoInsertParamDeptId from '@/utils/helper/autoInsertParamDeptId';

/**
 *  历史告警条件检索分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {Int} [queryParams.alarmType] 告警类型
 * @param {Int} [queryParams.startTime] 开始时间时间戳
 * @param {Int} [queryParams.endTime] 开始时间时间戳
 * @param {Int} [queryParams.deptId] 车组id
 * @param {Int} [queryParams.dealState] 处理状态
 * @param {String} [queryParams.licencePlate] 车牌号
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  queryParams = formatPaginationParam(queryParams);
  // queryParams.dealStatus = queryParams.dealStatus !== '' ? queryParams.dealStatus : undefined;
  // queryParams.alarmType = queryParams.alarmType !== '' ? queryParams.alarmType : undefined;
  queryParams.startTime = queryParams.startTime ? moment(queryParams.startTime).unix() : undefined;
  queryParams.endTime = queryParams.endTime ? moment(queryParams.endTime).unix() : undefined;
  queryParams.sort = undefined;
  let params = jsonToUnderline(queryParams);
  params['deal_status'] === null && delete params['deal_status'];
  params['alarm_type'] === null && delete params['alarm_type'];
  return new Promise((resolve, reject) => {
    request.post('/vdm-statistic/stat/alarm/fatigue-drive/page', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: [],
          total: 0
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 历史告警统计分析
 * @param {Object} queryParams
 * @param {Int} [queryParams.deptId] 车组id
 * @typedef {{code: Number, msg: String, data: Object}}
 * @returns {Promise<Result>}
 */
export function statistic (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-statistic/stat/alarm/fatigue-drive/page', params).then(res => {
      let resHump = jsonToHump(res);
      resolve({
        data: {
          resData: []
        }
      });
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 历史告警导出
 * @param {Object} queryParams
 * @param {Int} [queryParams.alarmType] 告警类型
 * @param {Int} [queryParams.startTime] 开始时间时间戳
 * @param {Int} [queryParams.endTime] 开始时间时间戳
 * @param {Int} [queryParams.deptId] 车组id
 * @param {Int} [queryParams.dealState] 处理状态
 * @param {String} [queryParams.licencePlate] 车牌号
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  queryParams = formatPaginationParam(queryParams);
  // queryParams.dealStatus = queryParams.dealStatus !== '' ? queryParams.dealStatus : undefined;
  // queryParams.alarmType = queryParams.alarmType !== '' ? queryParams.alarmType : undefined;
  queryParams.startTime = queryParams.startTime ? moment(queryParams.startTime).unix() : undefined;
  queryParams.endTime = queryParams.endTime ? moment(queryParams.endTime).unix() : undefined;
  queryParams.sort = undefined;
  let params = jsonToUnderline(queryParams);
  params['deal_status'] === null && delete params['deal_status'];
  params['alarm_type'] === null && delete params['alarm_type'];
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/historicalalarm/export', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 历史告警规则详情
 * @typedef {{code: Number, msg: String, data: Object}}
 * @returns {Promise<Result>}
 */
export function ruleDetail (param) {
  let params = jsonToUnderline(param);
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/historicalalarm/ruledetail', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查字典
 * @typedef {{code: Number, msg: String, data: Object}}
 * @returns {Promise<Result>}
 */
export function getDictDetail (param) {
  return new Promise((resolve, reject) => {
    request.get('/blade-system/dict-biz/getDictTreeByCodeCache?code=' + param).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, exportAll, getDictDetail };
