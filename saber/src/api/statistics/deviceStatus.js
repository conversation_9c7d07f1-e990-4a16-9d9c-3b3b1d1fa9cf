import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import qs from 'qs';

/**
 *   车辆日里程统计
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  // queryParams = formatPaginationParam(queryParams);
  if (queryParams.startTime) {
    queryParams.startTime = Math.floor(queryParams.startTime / 1000);
  }
  if (queryParams.endTime) {
    queryParams.endTime = Math.floor(queryParams.endTime / 1000);
  }
  const { sort, page, ...others } = queryParams;
  const url = qs.stringify(others)
  return new Promise((resolve, reject) => {
    request.get(`/vdm-statistic/statistic/deviceStatus/device/status?current=${page + 1}&${url}`).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data ? resHump.data.records : [],
          total: resHump.data ? resHump.data.total : 0
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}


export const getList = (params) => {
  return request({
    url: `/vdm-statistic/statistic/deviceStatus/alarm/page`,
    method: 'get',
    params
  });
};


export default { pagination };
