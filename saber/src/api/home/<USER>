import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
// import autoInsertParamDeptId from '@/utils/helper/autoInsertParamDeptId';

/**
 * 未处理实时告警数
 */
export function alarmNotDealNumber (params) {
  // queryParams = autoInsertParamDeptId(queryParams);
  // let params = jsonToUnderline(queryParams);
  // return new Promise((resolve, reject) => {
  //   request.post('securitymanagement/historicalalarm/queryrealtimealarmnumbydeptid', params).then(res => {
  //     let resHump = jsonToHump(res);
  //     resolve(resHump);
  //   }).catch((error) => {
  //     reject(error);
  //   });
  // });
}
/**
 * 企业总数
 */
export function deptNum () {
  return new Promise((resolve, reject) => {
    request.get('reportform/deptnum').then(res => {
      console.log(res);
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
/**
 * 车辆总数
 */
export function vehicleNum () {
  return new Promise((resolve, reject) => {
    request.get('reportform/vehiclenum').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
// /**
//  * 根据车组id获取所有告警数目和未处理的告警数目
//  * @param {Number} dept_id 车组id
//  * @returns {Promise<PaginationResult>}
//  */
// export function historicalAlarm (queryParams) {
//   // queryParams = autoInsertParamDeptId(queryParams);
//   let params = jsonToUnderline(queryParams);
//   return new Promise((resolve, reject) => {
//     request.post('securitymanagement/historicalalarm/queryalarmnumbydeptid', params).then(res => {
//       let resHump = jsonToHump(res);
//       resolve(resHump);
//     }).catch((error) => {
//       reject(error);
//     });
//   });
// }
/**
 * 车辆出省数量
 */
export function outProvince (params) {
  return new Promise((resolve, reject) => {
    request.get('reportform/outprovince', {params: params}).then(res => {
      console.log(res);
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
/**
 * 车辆状态统计
 */
export function vehicleStatistic () {
  return new Promise((resolve, reject) => {
    request.get('reportform/vehiclestatistic').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
/**
 * 根据车组id获取近7天或者半年里程
 * @param {Number} dept_id 车组id
 * @returns {Promise<PaginationResult>}
 */
export function mileageAlarm (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('securitymanagement/mileage/querylatelymileagebydept', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}
/**
 * 根据车组id获取近7天告警数目
 * @param {Number} dept_id 车组id
 * @returns {Promise<PaginationResult>}
 */
export function sevenAlarm (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('securitymanagement/realtimealarm/querylatelysevendaysalarm', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 根据车组id获取近半年告警数目
 * @param {Number} dept_id 车组id
 * @returns {Promise<PaginationResult>}
 */
export function halfYearAlarm (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('securitymanagement/realtimealarm/querylatelyhalfyearalarm', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}
/**
 * 车辆类型统计
 */
export function vehiceTypeStatistic () {
  return new Promise((resolve, reject) => {
    request.get('reportform/vehicetypestatistic').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
/**
 * 根据IP获取定位位置
 */
export function mapIp (params) {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/map/ip', {params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      // reject(error);
      resolve(error); // 无论是否成功，都应该继续处理逻辑
    });
  });
}
