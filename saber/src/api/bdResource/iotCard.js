import request from '../utils/request';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';
/**
 * 获取未绑定物联网卡
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function getUnbindIotCard (num="") {
 
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/iotCard?number=${num}`).then(res => {
      let result = jsonToHump(res);
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function getDeviceNumList (str, status=0) {
 
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/device/terminal/deviceNum?deviceNum=${str}&status=${status}`).then(res => {
      let result = jsonToHump(res);
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function getDeviceNumDetail (id) {
 
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/device/terminal/info?id=${id}`).then(res => {
      let result = jsonToHump(res);
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default { getUnbindIotCard, getDeviceNumList, getDeviceNumDetail};
