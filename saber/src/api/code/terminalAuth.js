import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

/**
 *   终端鉴权分页接口
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams.sort = undefined;
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-statistic/statistics/vehicleRunning/companyAllInfo?size=${queryParams.size}&current=${queryParams.current}`, params).then(res => {
      let resHump = jsonToHump(res);
      const mockList = [
        {
          deptName: '1',
          children: [
            {
              deptName: '2'
            },
            {
              deptName: '3'
            },
            {
              deptName: '4'
            }
          ]
        },
        {
          deptName: '5',
          children: [
            {
              deptName: '6'
            },
            {
              deptName: '7'
            }
          ]
        }
      ];
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          // content: resHump.data.records,
          content: mockList,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default { pagination };
