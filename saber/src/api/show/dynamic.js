import request from "@/router/axios";
export const onLineTrend = () => {
  return request({
    url: "/vdm-statistic/screen/dynamic/onLine/trend",
    method: "get",
  });
};

export const alarmStatics = () => {
  return request({
    url: "/vdm-statistic/screen/dynamic/alarm/statics",
    method: "get",
  });
};
export const onlineTerminal = () => {
  return request({
    url: "/vdm-statistic/screen/dynamic/online/terminal",
    method: "get",
  });
};


export const locationTrend = () => {
  return request({
    url: "/vdm-statistic/screen/dynamic/location/trend",
    method: "get",
  });
};

export const targetOdometer = () => {
  return request({
    url: "/vdm-statistic/screen/dynamic/target/odometer",
    method: "get",
  });
};

export default {
  onLineTrend,
  alarmStatics,
  onlineTerminal,
  locationTrend,
  targetOdometer
};
