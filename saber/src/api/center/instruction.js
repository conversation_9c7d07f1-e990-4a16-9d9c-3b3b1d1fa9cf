import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import defaultValue from '@/utils/core/defaultValue';
const JSONbig = require('json-bigint');

/**
 * 文字指令下发
 * @param {Object} queryParams
 * @param {Array} [queryParams.licencePlates] 车牌号码
 * @param {Number} [queryParams.infoFlag] 文本标志
 * @param {Number} [queryParams.infoType] 文本类型
 * @param {Number} [queryParams.showOn] 终端显示器
 * @param {Number} [queryParams.broadcastOn] 终端TTS播读
 * @param {String} [queryParams.text] 指令内容
 * @param {Number} [queryParams.alarmId] 告警id
 * @returns {Promise<PaginationResult>}
 */
export function sendTextMsg (queryParams) {
  queryParams.infoFlag = defaultValue(queryParams.infoFlag, 1);
  queryParams.infoType = defaultValue(queryParams.infoType, 1);
  queryParams.showOn = defaultValue(queryParams.showOn, 4);
  queryParams.broadcastOn = defaultValue(queryParams.broadcastOn, 8);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/sendtextmsg', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 拍照指令下发
 * @param {Object} queryParams
 * @param {String} queryParams.licencePlate 车牌号码
 * @param {Number} queryParams.channel 通道
 * @param {Number} [queryParams.picNum] 图片数量
 * @param {Number} [queryParams.interval] 拍照间隔
 * @param {Number} [queryParams.saveFlag] 保存标志(1:上传,0：保存)
 * @param {Number} [queryParams.resolution] 分辨率
 * @param {Number} [queryParams.ratio] 图像质量
 * @param {Number} [queryParams.light] 亮度
 * @param {Number} [queryParams.contrast] 对比度
 * @param {Number} [queryParams.saturation] 饱和度
 * @param {Number} [queryParams.color] 色度
 * @param {Number} [queryParams.alarmId] 告警id
 * @returns {Promise<PaginationResult>}
 */
export function sendPhotoMsg (queryParams) {
  queryParams.picNum = defaultValue(queryParams.picNum, 1);
  queryParams.interval = defaultValue(queryParams.interval, 0);
  queryParams.saveFlag = defaultValue(queryParams.saveFlag, 0);
  queryParams.resolution = defaultValue(queryParams.resolution, 0x00);
  queryParams.ratio = defaultValue(queryParams.ratio, 5);
  queryParams.light = defaultValue(queryParams.light, 127);
  queryParams.contrast = defaultValue(queryParams.contrast, 64);
  queryParams.saturation = defaultValue(queryParams.saturation, 64);
  queryParams.color = defaultValue(queryParams.color, 127);

  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/sendphotomsg', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 监听指令下发
 * @param {Object} queryParams
 * @param {Array} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.phone] 电话号码
 * @param {Number} [queryParams.flag] 监听类型
 * @param {Number} [queryParams.alarmId] 告警id
 * @returns {Promise<PaginationResult>}
 */
export function sendMonitorMsg (queryParams) {
  queryParams.flag = defaultValue(queryParams.flag, 1);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/querycallback', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 点名
 * @param {Object} queryParams
 * @param {Array} [queryParams.licencePlate] 车牌号码
 * @returns {Promise<PaginationResult>}
 */
export function sendLocationMsg (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/sendlocationmsg', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆停车时长
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {String} [queryParams.licenceColor] 车牌颜色
 * @returns {Promise<PaginationResult>}
 */
export function parkingstate (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/parkingstate', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询驾驶员信息和车辆状态
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {String} [queryParams.licenceColor] 车牌颜色
 * @returns {Promise<PaginationResult>}
 */
export function driverInfo (queryParams) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-statistic/bt/statistics/driver/driverAndVehicleState', {params: queryParams}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

