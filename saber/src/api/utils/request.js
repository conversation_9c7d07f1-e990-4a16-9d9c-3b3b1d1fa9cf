import axios from 'axios';
import router, {resetRouter} from '@/router/router';
import { Notification, MessageBox } from 'element-ui';
import {getToken} from '@/util/auth';
import store from '../../store';
import Config from '@/config/settings';
import apiErrorCodeEnum from '@/enumerate/apiErrorCode';
import website from "@/config/website";
import { downLoadFile } from '@/utils';
let messageDelayFlag = false
// 创建axios实例
console.log("website.apiPrefix is "+ website.apiPrefix);
const service = axios.create({
  // baseURL: process.env.VUE_APP_API, // api 的 base_url
  baseURL: website.apiPrefix, // FIXME 使用代理
  timeout: Config.timeout // 请求超时时间
});

// request拦截器
service.interceptors.request.use(
  config => {

    /**
     * 逻辑修改：适配 国能平台
     * 国能项目中，需要将位置平台集成到国能平台中。在国能平台中，会通过iframe的形式将位置平台的页面嵌入进去，解决跨域取token的问题。
     * 为了方便使用，位置平台也要保留自己的登录验证方式，所以这里需要做兼容处理。
     * 如果localStorage中包含国能平台的token，则表示当前是国能平台在访问页面，则添加国能平台的token配置；否则就是自己的位置平台，就添加blade的token设置
     */
    //是否是国能平台
    let isCE = window.localStorage.hasOwnProperty("cedi__Access-Token");
    // console.log("isCE request is "+isCE);
    config.headers['Content-Type'] = 'application/json';
    // 如果是集成页面
    if (sessionStorage.getItem('saber-integration')) {
      const { content } = JSON.parse(sessionStorage.getItem('saber-token'));
      config.headers[website.tokenHeader] = 'bearer ' + content;
    } else if (getToken() && !isCE) {
      //如果是位置平台
      config.headers[website.tokenHeader] = 'bearer ' + getToken()
    } else if(isCE){
      //如果是国能平台
      let currentToken =window.localStorage.getItem("cedi__Access-Token")
      let currentTokenValue= JSON.parse(currentToken).value;
      config.headers[website.ceTokenHeader] = '' + currentTokenValue;
    }

    return config;
  },
  error => {
    console.log("here is error");
    // Do something with request error
    console.log(error); // for debug
    Promise.reject(error);
  }
);

// response 拦截器
service.interceptors.response.use(
  response => {
    if (response.data.code !== apiErrorCodeEnum.SUCCESS && response.data.code !== 0) {
      let message = response.data.msg
      if(response.data.code === 207) {
        setTimeout(() => {
          downLoadFile(`${response.data.msg}`, '错误原因', 'xml')
        }, 1500)
        message = '导入失败，具体请查看下载文件'
      }
      Notification.error({
        customClass: 'error-notification',
        iconClass: 'el-icon-warning-outline',
        message
      });
      // return Promise.reject(new Error(response.data.msg));// 'error'->response.data.msg
      return Promise.reject(response.data);// 'error'->response.data Error对象实际上并没有对业务起到帮助，这里统一改为返回值
    } else {
      return response.data;
    }
  },
  error => {
    let code = 0;
    try {
      code = error.response.data.code;
    } catch (e) {
      if (error.toString().indexOf('Error: timeout') !== -1) {
        Notification.error({
          customClass: 'error-notification',
          iconClass: 'el-icon-warning-outline',
          message: '网络请求超时'
        });
        return Promise.reject(error);
      }
    }
    if (code === 403) {
      router.push({ path: '/401' });
    } else if (code === 401) {
      console.log("here is logcheck 2");
      if (window.localStorage.hasOwnProperty("cedi__Access-Token") || window.localStorage.hasOwnProperty("cedi__Login_Userinfo")) {
        console.log("here is logcheck 3 ");
        //如果是底座系统,token失效后，刷新页面即可
        parent.location.reload();
      }else{
        console.log("here is logcheck 4");
        //如果是位置平台，则跳转到自己的登录页面
        store.dispatch('FedLogOut').then(() => router.push({path: '/login'}));
      }
    } else {
      if (!messageDelayFlag) {
        const errorMsg = error.response.data.msg;
        messageDelayFlag = true;
        Notification.error({
          customClass: 'error-notification',
          iconClass: 'el-icon-warning-outline',
          message: errorMsg || '接口请求失败'
        });
        setTimeout(()=> {
          messageDelayFlag = false;
        }, 3000);
      }
    }
    return Promise.reject(error);
  }
);
export default service;
