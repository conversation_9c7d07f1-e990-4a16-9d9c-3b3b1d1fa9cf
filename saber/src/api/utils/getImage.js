import {getToken} from '@/util/auth';
function getImage (imageUrl) {
  return new Promise((resolve, reject) => {
    let request = new XMLHttpRequest();
    request.responseType = 'blob';
    request.open('get', imageUrl, true);
    request.timeout = 5000;
    if (getToken()) {
      request.setRequestHeader('Authorization', 'Bearer ' + getToken());
    }
    request.onreadystatechange = e => {
      if (request.readyState === XMLHttpRequest.DONE && request.status === 200) {
        resolve(request);
      }
    };
    request.send(null);
  });
}

export default getImage;
