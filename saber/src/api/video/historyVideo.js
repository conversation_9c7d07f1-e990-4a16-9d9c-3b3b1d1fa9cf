import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';

/**
 * 资源目录检索
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channelId=1] 视频通道
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @param {Number} [queryParams.alarm=0] 报警标志
 * @param {Number} [queryParams.mediaType=0] 音视频类型
 * @param {Number} [queryParams.streamType=0] 码流类型
 * @param {Number} [queryParams.storageType=0] 存储器类型
 * @returns {Promise<PaginationResult>}
 */
export function queryVideoList (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryvideorecordlist', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          msgNumber: resHump.data.msgNumber,
          listNum: resHump.data.listNum,
          recordList: resHump.data.recordList
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 历史视频开始播放
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {String} [queryParams.ip] ip地址
 * @param {String} [queryParams.tcpPort] tcp实时传输音视频端口号
 * @param {String} [queryParams.udpPort] udp实时传输音视频端口号
 * @param {Number} [queryParams.channelId=1] 视频通道
 * @param {Number} [queryParams.mediaType] 音视频类型，0：音视频，1：音频，2：视频，3：视频或音频
 * @param {Number} [queryParams.streamType] 码流类型，0：所有码流，1：主码流，2：子码流
 * @param {Number} [queryParams.storageType] 存储器类型，0：所有存储器，1：主存储器，2：灾备存储器
 * @param {Number} [queryParams.playType] 回放方式0：正常回放，1：快进回放，2：关键帧快退回放，3：关键帧播放，4：单帧上传
 * @param {Number} [queryParams.forwardTimes] 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @returns {Promise<PaginationResult>}
 */
export function playHistoryVideo (queryParams) {
  let params = jsonToUnderline(queryParams);
  console.log(params);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryvideorecordplay', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        rtmpUrl: resHump.rtmpUrl,
        flvUrl: resHump.flvUrl,
        wsUrl: resHump.wsUrl
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 历史视频停止播放
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channelId=1] 视频通道
 * @param {Number} [queryParams.controlCmd=2] 回放控制 0：开始回放，1：暂停回放，2：结束回放，3：快进回放 4：关键帧快退回放，5：拖动回放，6：关键帧播放
 * @param {Number} [queryParams.forwardTimes=0] 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍
 * @param {Number} [queryParams.playTime] 拖动回放位置（时间戳：秒）
 * @returns {Promise<PaginationResult>}
 */
export function stopHistoryVideo (queryParams) {
  queryParams.controlCmd = 2;
  queryParams.forwardTimes = 0;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryvideorecordcontrol', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        result: resHump.result
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 历史视频回放控制
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channelId=1] 视频通道
 * @param {Number} [queryParams.controlCmd=0] 回放控制 0：开始回放，1：暂停回放，2：结束回放，3：快进回放 4：关键帧快退回放，5：拖动回放，6：关键帧播放
 * @param {Number} [queryParams.forwardTimes=0] 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍
 * @param {Number} [queryParams.playTime] 拖动回放位置（时间戳：秒）
 * @returns {Promise<PaginationResult>}
 */
export function controlHistoryVideo (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryvideorecordcontrol', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        result: resHump.result
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 视频上传
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channelId=1] 视频通道
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @param {Number} [queryParams.alarm=0] 报警标志
 * @param {Number} [queryParams.mediaType=0] 音视频类型
 * @param {Number} [queryParams.streamType=0] 码流类型
 * @param {Number} [queryParams.storageType=0] 存储器类型
 * @returns {Promise<PaginationResult>}
 */
export function uploadVideo (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/upload', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        result: resHump.result
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 视频下载
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channelId=1] 视频通道
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @returns {Promise<PaginationResult>}
 */
export function downloadVideo (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/download', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        uploadFlag: resHump.uploadFlag,
        filePath: resHump.filePath
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 停止视频上传
 * @param {Object} queryParams
 * @param {String} queryParams.licencePlate 车牌号码
 * @param {Number} [queryParams.control=2] 上传控制0：暂停，1：继续，2：取消
 * @returns {Promise<*>}
 */
export function stopUploadVideo (queryParams) {
  queryParams.control = 2;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/controlupload', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 继续视频上传
 * @description 其实没什么用，因为不知道终端最后传递的是哪个视频
 * @deprecated
 * @param {Object} queryParams
 * @param {String} queryParams.licencePlate 车牌号码
 * @param {Number} [queryParams.control=1] 上传控制0：暂停，1：继续，2：取消
 * @returns {Promise<*>}
 */
export function continueUploadVideo (queryParams) {
  queryParams.control = 1;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/controlupload', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查看终端当前录像状态
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @returns {Promise<PaginationResult>}
 */
export function getVideoState (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/historystate', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      console.log('getVideoState#result', result);
      if (resHump.data.code === 0) {
        resolve(result);
      } else {
        reject(result);
      }
    }).catch((error) => {
      console.log('getVideoState#error', error);
      let errorHump = jsonToHump(error);
      let errorResult = {
        code: errorHump.code,
        msg: errorHump.msg,
        data: errorHump.data
      };
      reject(errorResult);
    });
  });
}

/**
 * 获取上传状态
 * @param queryParams
 * @return {Promise<any>}
 */
export function getUploadState (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('monitorcars/vehicle/recordstate', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      if (resHump.data.state === 1) {
        resolve(result);
      } else {
        reject(result);
      }
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  playHistoryVideo,
  stopHistoryVideo,
  controlHistoryVideo
};
