import request from "@/router/axios";
import md5 from "js-md5";
import jsonToUnderline from "@/utils/helper/jsonToUnderline";
import jsonToHump from "@/utils/helper/jsonToHump";
import formatPaginationParamNew from "@/utils/helper/formatPaginationParamNew";
const JSONbig = require('json-bigint');

export function pagination(queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.size = queryParams.size;
  const { current, size, systemName } = queryParams;
  return new Promise((resolve, reject) => {
    request({
      url: '/vdm-inter-manager/interManager/sminterfacesystem/list',
      method: 'get',
      params: {
        systemName,
        current,
        size,
      }
    }).then((res) => {
        let resHump = jsonToHump(res.data);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: {
            content: resHump.data.records,
            total: resHump.data.total,
          },
        };
        console.log(result, res);
        resolve(result);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function detail(id) {
  return new Promise((resolve, reject) => {
    request
      .get(`/vdm-inter-manager/interManager/sminterfacesystem/detail?id=${id}`)
      .then((res) => {
        let resHump = jsonToHump(res);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: resHump.data.resData,
        };
        resolve(result);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function del(ids) {
  const formatIds = ids.join(",");
  return new Promise((resolve, reject) => {
    request({
      url: '/vdm-inter-manager/interManager/sminterfacesystem/remove',
      method: 'post',
      params: {
        ids: formatIds,
      }
    })
      .then((res) => {
        resolve(res);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function add(row) {
  return new Promise((resolve, reject) => {
    request({
      url: '/vdm-inter-manager/interManager/sminterfacesystem/submit',
      method: 'post',
      data: row
    })
      .then((res) => {
        let resHump = jsonToHump(res);
        resolve(resHump);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function edit(row) {
  return new Promise((resolve, reject) => {
    request({
      url: '/vdm-inter-manager/interManager/sminterfacesystem/update',
      method: 'post',
      data: row
    }).then((res) => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    })
    .catch((error) => {
      reject(error);
    });
  });
}

export const getBindList = (params,data) => {
  return request({
    url: '/vdm-inter-manager/interManager/sminterfacesystem/obj/page',
    method: 'post',
    data,
    params
  })
}

export const handleBind = (data) => {
  return request({
    url: `/vdm-inter-manager/interManager/sminterfacesystem/obj/bind`,
    method: 'post',
    data: JSONbig.stringify(data),
    headers: {
      'Content-Type': 'application/json'
    }
  })
}


export const binded = (data) => {
  return request({
    url: `/vdm-inter-manager/interManager/sminterfacesystem/obj/map`,
    method: 'post',
    data: JSONbig.stringify(data),
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

export const bindAlarmRule = (queryParams) => {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request({
      url: "/security-wrapper/securitymanagement/rulebindsystem",
      method: "post",
      data: params
    }).then((res) => {
      let resHump = jsonToHump(res.data);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
};

export const ruleSbysystem = (queryParams) => {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request({
      url: "/security-wrapper/securitymanagement/rulesbysystem",
      method: "get",
      params: params,
    }).then((res) => {
      let resHump = jsonToHump(res.data);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
};

export default {
  add,
  edit,
  del,
  pagination,
  detail,
};