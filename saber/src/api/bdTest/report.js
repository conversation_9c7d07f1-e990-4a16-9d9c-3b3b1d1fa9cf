import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';

/**
 *   北斗识别检测统计报告
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams.sort = undefined;
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  return new Promise((resolve, reject) => {
    request.post(`/vdm-bd-check/check/checkReport?size=${queryParams.size}&current=${queryParams.current}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump?.data?.records || [],
          total: resHump?.data?.total || 0
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 *   北斗识别检测统计报告
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function detailPagination(queryParams) {
  return new Promise((resolve, reject) => {
    queryParams.sort = undefined;
    queryParams.current = queryParams.page + 1;
    queryParams.page = undefined;
    request.post(`/vdm-bd-check/check/checkDetail?size=${queryParams.size}&current=${queryParams.current}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump?.data?.records || [],
          total: resHump.data?.total || 0
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 开始检测 808协议
 * @returns {Promise<unknown>}
 * @param terminalNo
 * @param ids
 */
export function startCheck(terminalNo, ids) {
  return new Promise((resolve, reject) => {
    request.post('/terminalcheck-wrapper/terminalcheck/startrotocolcheck', {
      device_no: terminalNo,
      ids
    }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 开始检测 mqtt协议
 * @param terminalNo
 * @param ids
 * @returns {Promise<unknown>}
 */
export function startCheckMQTT(terminalNo, ids) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-mqtt-check/mqtt/send/startcheck', {
      device_no: terminalNo,
      ids
    }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 结束检测
 * @param id
 * @returns {Promise<unknown>}
 */
export function finishedCheck(id) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-bd-check/check/finishCheck?ids=${id}`).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 报告下载
 * @param id
 * @returns {Promise<unknown>}
 */
export function getReport(id) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-bd-check/check/createCheckReport`, {
      params: {
        ids: id
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取委托企业列表
 * @returns {Promise<unknown>}
 */
export function allCompanyName() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-bd-check/company/allCompanyName').then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 测试用重置检测接口
 * @returns {Promise<unknown>}
 */
export function refreshCheck(id) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-bd-check/check/resetCheckStatus', {
      params: {
        reportId: id
      }
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default {
  pagination,
  detailPagination
};
