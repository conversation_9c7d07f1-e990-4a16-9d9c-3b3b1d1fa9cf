import request from './utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';

/**
 * 首页消息通知
 * @param {Object} queryParams
 * @param {Number} queryParams.user 用户
 * @returns {Promise<PaginationResult>}
 */
export function details(queryParams) {
  queryParams = formatPaginationParamNew(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-alarm/alarm/realtime/alarm/homepagemsg`, params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { details };
