import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';
import moment from 'moment';

/**
 * 实时监控分页
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {Int} [queryParams.alarmType] 告警类型
 * @param {Int} [queryParams.autoDeal] 自动成就
 * @param {Int} [queryParams.isOpen] 是否开启
 * @typedef {{total: Number, content: Array.<AlarmSetting>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  const current = queryParams.page + 1;
  const size = queryParams.size;
  queryParams = formatPaginationParamNew(queryParams);
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  queryParams.alarmType = typeof queryParams.alarmType === 'string' ? [queryParams.alarmType] : queryParams.alarmType;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-alarm/alarm/realtime/alarm/page?current=${current}&size=${size}`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 持续中的告警分页
 * @param {Object} queryParams
 * @param {Int} [queryParams.alarmType] 告警类型
 * @param {Int} [queryParams.autoDeal] 自动成就
 * @param {Int} [queryParams.isOpen] 是否开启
 * @typedef {{total: Number, content: Array.<AlarmSetting>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function sustainPagination(queryParams) {
  queryParams = formatPaginationParamNew(queryParams);
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  queryParams.alarmType = typeof queryParams.alarmType === 'string' ? [queryParams.alarmType] : queryParams.alarmType;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-alarm/alarm/realtime/continue`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时监控 按告警类型分组的告警数量
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function alarmCount(queryParams) {
  queryParams = formatPaginationParamNew(queryParams);
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  queryParams.alarmType = typeof queryParams.alarmType === 'string' ? [queryParams.alarmType] : queryParams.alarmType;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/num-type-alarm', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 按告警类型分组的未处理告警数量
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function pendingCount(queryParams) {
  queryParams = formatPaginationParamNew(queryParams);
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  queryParams.alarmType = typeof queryParams.alarmType === 'string' ? [queryParams.alarmType] : queryParams.alarmType;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/num-type-pending', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时告警数
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function realtimeCount(queryParams) {
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/num-alarm', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 告警车辆数
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function realtimeCarCount(queryParams) {
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/num-car', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时监控导出
 * @param {Object} queryParams
 * @param {Int} [queryParams.alarmType] 告警类型
 * @param {Int} [queryParams.startTime] 开始时间时间戳
 * @param {Int} [queryParams.endTime] 开始时间时间戳
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll(queryParams) {
  queryParams = formatPaginationParamNew(queryParams);
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  queryParams.alarmType = typeof queryParams.alarmType === 'string' ? [queryParams.alarmType] : queryParams.alarmType;
  queryParams.operateType = 'company_export'; // 运输企业用户告警处理 导出
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/export', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 申诉告警
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function appealAlarm(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('/vdm-alarm/alarm/realtime/manual/company-appeal-alarm', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 审核告警
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function auditAlarm(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('/vdm-alarm/alarm/realtime/manual/audit-appeal', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}


/**
 * 告警处理
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function companyDealAlarms(queryParams) {
  // queryParams.redeal = 0; // 是否再次处理（0：否，1：是）
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('/vdm-alarm/alarm/realtime/manual/company-deal-alarm', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时监控详情
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function details(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/info', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 告警人员信息
 * @param {AlarmSetting} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function alarmUser(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/driver', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 人工干预处理(告警事件跟踪)
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function eventTrace(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/trace', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 告警自动处理开关状态
 * @returns {Promise<Result>}
 */
export function manpowerType() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-alarm/alarm/realtime/manual-mode').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 设置告警自动处理开关
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function setManpower(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('/vdm-alarm/alarm/realtime/manual-mode', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时告警操作上锁
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function lock(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('/vdm-alarm/alarm/realtime/manual/lock', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时告警操作解锁
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function unlock(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('/vdm-alarm/alarm/realtime/manual/unlock', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 告警详情轨迹
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function detailList(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-alarm/alarm/realtime/alarmdetaillist', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, exportAll, alarmCount, pendingCount, realtimeCount, realtimeCarCount, appealAlarm, companyDealAlarms, details, sustainPagination, eventTrace, auditAlarm, manpowerType, setManpower, lock, unlock, alarmUser, detailList };
