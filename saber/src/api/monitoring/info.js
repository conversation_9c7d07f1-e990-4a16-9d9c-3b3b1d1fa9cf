import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';
// import autoInsertParamDeptId from '@/utils/helper/autoInsertParamDeptId';
import axios from 'axios';
const JSONbig = require('json-bigint');

/**
 * 监控信息
 * @param {Object} queryParams
 * @param {Number} [queryParams.start=0] 页码
 * @param {Number} [queryParams.count=10] 每页多少项
 * @param {String} [queryParams.department] 车组名称
 * @param {String} [queryParams.vehicle_model] 车辆类型
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  queryParams = formatPaginationParam(queryParams);
  queryParams.sort = undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/vehiclestates', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 监控信息导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  queryParams = formatPaginationParam(queryParams);
  queryParams.sort = undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/exportvehiclestates', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
  * 四维逆地理编码
  * @param {Object} params
  * @returns {Object}
 */
export function fourDimensionalAddress (queryParams) {
  return new Promise((resolve, reject) => {
    axios.get('http://c.qqearth.com:81/SE_RGC2', {
      params: queryParams,
      authorization: false,
      meta: {
        isToken: false
      },
      withCredentials: false
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 高德逆地理编码
 * @param {Object} params
 * @param {String} [key] 高德Web服务API类型Key
 * @param {String} [location] 坐标点
 * @returns {Object}
 */

export function AutoNaviAddress (queryParams) {
  return new Promise((resolve, reject) => {
    axios.get('https://restapi.amap.com/v3/geocode/regeo', {
      params: queryParams,
      authorization: false,
      meta: {
        isToken: false
      },
      withCredentials: false
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
  * 国能逆地理编码
  * @param {Object} params
  * @returns {Object}
 */
export function gnAddress (queryParams) {
  return new Promise((resolve, reject) => {
    axios.get('http://*************/api/geocoder', {
      params: queryParams,
      authorization: false,
      meta: {
        isToken: false
      },
      withCredentials: false
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 四维地图逆地理编码(后台接口)
 * @param {Object} queryParams
 * @returns {Promise<Result>}
 */
export function vehicleAddress (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/siwei', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取终端实时信息
 * @param {Object} queryParams
 * @param {Array} [queryParams.deviceIds] 终端id列表
 * @returns {Promise<PaginationResult>}
 */
export function terminalStates (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/realloc', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data?.resData || []
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default { pagination, exportAll, fourDimensionalAddress, AutoNaviAddress, gnAddress, vehicleAddress, terminalStates };
