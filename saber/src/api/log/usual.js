import request from '@/router/axios'; 
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

/**
 *   终端日志分页接口
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams.current = queryParams.page + 1;
  const { sort, page, time, menuOpt=[], ...params } = queryParams
  const menu = menuOpt[menuOpt.length-2]?.split('-')[1]
  const operation = menuOpt[menuOpt.length-1]?.split('-')[0]
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/blade-log/usual/log/list`, { params: { menu, operation, ...params } }).then(({data: resHump}) => {
      // let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}


export const getMenuList = (current, size, params) => {
  return request({
    url: '/blade-log/usual/log/menu',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export default { pagination, getMenuList };