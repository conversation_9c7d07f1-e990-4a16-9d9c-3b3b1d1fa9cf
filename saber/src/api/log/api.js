import request from '@/router/axios'; 
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

/**
 *   终端日志分页接口
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination({interCode, ...queryParams}) {
  queryParams.sort = undefined;
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  queryParams.time = undefined;
  const params = queryParams
  if(interCode) {
    params.interCode = interCode
  }
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/vdm-inter-manager/ininterlog/list`, { params }).then(({data: resHump}) => {
      // let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function listSystem(queryParams) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-inter-manager/interManager/sminterfacesystem/list-system`, { msg:queryParams }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


export function serviceListAll (queryParams) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-inter-manager/service/listAll`, { msg:queryParams }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination };