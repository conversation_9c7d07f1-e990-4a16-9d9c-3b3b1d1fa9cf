import defined from '../Core/defined'
import DeveloperError from '../Core/DeveloperError'

'use strict';

/**
 * ReconnectingWebSocket
 * @alias ReconnectingWebSocket（自动重连WebSocket）
 *
 * @description 可以自动重连的Websocket，在原生WebSocket上再封了一层，避免Websocket构造失败造成不可预料的结果
 * @param {String} url websocket的url
 * @param [protocols]
 * @param [options]
 *
 * @constructor
 *
 * @exception {DeveloperError} url is required.
 */
function ReconnectingWebSocket(url, protocols, options) {
  //>>includeStart('debug', pragmas.debug);
  if (!defined(url)) {
    throw new DeveloperError('url is required.');
  }
  //>>includeEnd('debug');

  this.url = url;

  // Default settings
  var settings = {

    /** Whether this instance should log debug messages. */
    debug: false,

    /** Whether or not the websocket should attempt to connect immediately upon instantiation. */
    automaticOpen: true,

    /** The number of milliseconds to delay before attempting to reconnect. */
    reconnectInterval: 1000,

    /** The maximum number of milliseconds to delay a reconnection attempt. */
    maxReconnectInterval: 30000,

    /** The rate of increase of the reconnect delay. Allows reconnect attempts to back off when problems persist. */
    reconnectDecay: 1.5,

    /** The maximum time in milliseconds to wait for a connection to succeed before closing and retrying. */
    timeoutInterval: 2000,

    /** The maximum number of reconnection attempts to make. Unlimited if null. */
    maxReconnectAttempts: null,

    /** The binary type, possible values 'blob' or 'arraybuffer', default 'blob'. */
    binaryType: 'blob'
  };
  if (!options) { options = {}; }

  // Overwrite and define settings with options if they exist.
  for (var key in settings) {
    if (typeof options[key] !== 'undefined') {
      console.log(options[key])
      this[key] = options[key];
    } else {
      this[key] = settings[key];
    }
  }

  /** The number of attempted reconnects since starting, or the last successful connection. Read only. */
  this.reconnectAttempts = 0;

  /**
   * The current state of the connection.
   * Can be one of: WebSocket.CONNECTING, WebSocket.OPEN, WebSocket.CLOSING, WebSocket.CLOSED
   * Read only.
   */
  this.readyState = WebSocket.CONNECTING;

  /**
   * A string indicating the name of the sub-protocol the server selected; this will be one of
   * the strings specified in the protocols parameter when creating the WebSocket object.
   * Read only.
   */
  this.protocol = null;

  // Private state variables
  var that = this;
  var ws;
  var forcedClose = false;
  var timedOut = false;
  var eventTarget = document.createElement('div');

  // Wire up "on*" properties as event handlers
  eventTarget.addEventListener('open',       function(event) { that.onopen(event); });
  eventTarget.addEventListener('close',      function(event) { that.onclose(event); });
  eventTarget.addEventListener('connecting', function(event) { that.onconnecting(event); });
  eventTarget.addEventListener('message',    function(event) { that.onmessage(event); });
  eventTarget.addEventListener('error',      function(event) { that.onerror(event); });

  // Expose the API required by EventTarget
  this.addEventListener = eventTarget.addEventListener.bind(eventTarget);
  this.removeEventListener = eventTarget.removeEventListener.bind(eventTarget);
  this.dispatchEvent = eventTarget.dispatchEvent.bind(eventTarget);

  /**
   * This function generates an event that is compatible with standard
   * compliant browsers and IE9 - IE11
   *
   * This will prevent the error:
   * Object doesn't support this action
   *
   * http://stackoverflow.com/questions/19345392/why-arent-my-parameters-getting-passed-through-to-a-dispatched-event/19345563#19345563
   * @param s String The name that the event should use
   * @param args Object an optional object that the event will use
   */
  function generateEvent(s, args) {
    var evt = document.createEvent('CustomEvent');
    evt.initCustomEvent(s, false, false, args);
    return evt;
  }

  /**
   * Transmits data to the server over the WebSocket connection.
   *
   * @param data a text string, ArrayBuffer or Blob to send to the server.
   */
  this.send = function(data) {
    if (ws) {
      if (that.debug || ReconnectingWebSocket.debugAll) {
        console.debug('ReconnectingWebSocket', 'send', that.url, data);
      }
      return ws.send(data);
    } else {
      throw 'INVALID_STATE_ERR : Pausing to reconnect websocket';
    }
  };

  /**
   * Closes the WebSocket connection or connection attempt, if any.
   * If the connection is already CLOSED, this method does nothing.
   */
  this.close = function(code, reason) {
    // Default CLOSE_NORMAL code
    if (typeof code === 'undefined') {
      code = 1000;
    }
    forcedClose = true;
    if (ws) {
      console.log('关闭ws')
      ws.close(code, reason);
    }
  };

  /**
   * Additional public API method to refresh the connection if still open (close, re-open).
   * For example, if the app suspects bad data / missed heart beats, it can try to refresh.
   */
  this.refresh = function() {
    if (ws) {
      ws.close();
    }
  };

  /**
   * 新建立webSocket连接
   * @param {Boolean} reconnectAttempt 是否进行重连尝试
   */
  this.open = function (reconnectAttempt) {
    try{
      ws = new WebSocket(that.url, protocols || []);
      if(defined(ws)){
        ws.binaryType = this.binaryType;

        if (reconnectAttempt) {
          if (this.maxReconnectAttempts && this.reconnectAttempts > this.maxReconnectAttempts) {
            return;
          }
        } else {
          eventTarget.dispatchEvent(generateEvent('connecting'));
          this.reconnectAttempts = 0;
        }

        if (that.debug || ReconnectingWebSocket.debugAll) {
          console.debug('ReconnectingWebSocket', 'attempt-connect', that.url);
        }

        var localWs = ws;
        var timeout = setTimeout(function() {
          if (that.debug || ReconnectingWebSocket.debugAll) {
            console.debug('ReconnectingWebSocket', 'connection-timeout', that.url);
          }
          timedOut = true;
          localWs.close();
          timedOut = false;
        }, that.timeoutInterval);

        ws.onopen = function(event) {
          clearTimeout(timeout);
          if (that.debug || ReconnectingWebSocket.debugAll) {
            console.debug('ReconnectingWebSocket', 'onopen', that.url);
          }
          that.protocol = ws.protocol;
          that.readyState = WebSocket.OPEN;
          that.reconnectAttempts = 0;
          var e = generateEvent('open');
          e.isReconnect = reconnectAttempt;
          reconnectAttempt = false;
          eventTarget.dispatchEvent(e);
        };

        ws.onclose = function(event) {
          clearTimeout(timeout);
          ws = null;
          if (forcedClose) {
            that.readyState = WebSocket.CLOSED;
            eventTarget.dispatchEvent(generateEvent('close'));
          } else {
            that.readyState = WebSocket.CONNECTING;
            var e = generateEvent('connecting');
            e.code = event.code;
            e.reason = event.reason;
            e.wasClean = event.wasClean;
            eventTarget.dispatchEvent(e);
            if (!reconnectAttempt && !timedOut) {
              if (that.debug || ReconnectingWebSocket.debugAll) {
                console.debug('ReconnectingWebSocket', 'onclose', that.url);
              }
              eventTarget.dispatchEvent(generateEvent('close'));
            }

            var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
            setTimeout(function() {
              that.reconnectAttempts++;
              that.open(true);
            }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
          }
        };
        ws.onmessage = function(event) {
          if (that.debug || ReconnectingWebSocket.debugAll) {
            console.debug('ReconnectingWebSocket', 'onmessage', that.url, event.data);
          }
          var e = generateEvent('message');
          e.data = event.data;
          eventTarget.dispatchEvent(e);
        };
        ws.onerror = function(event) {
          if (that.debug || ReconnectingWebSocket.debugAll) {
            console.debug('ReconnectingWebSocket', 'onerror', that.url, event);
          }
          eventTarget.dispatchEvent(generateEvent('error'));
        };
      }
    } catch(e){
      console.log(e);
    }

  };

  /**
   * 刷新页面
   */
  this.updateUrl = function () {
    this.close();
    if (this.automaticOpen === true) {
      this.open(false);
    }
  };

  // Whether or not to create a websocket upon instantiation
  if (this.automaticOpen === true) {
    this.open(false);
  }

}

/**
 * An event listener to be called when the WebSocket connection's readyState changes to OPEN;
 * this indicates that the connection is ready to send and receive data.
 * @param event
 */
ReconnectingWebSocket.prototype.onopen = function(event) {};

/**
 * An event listener to be called when the WebSocket connection's readyState changes to CLOSED.
 * @param event
 */
ReconnectingWebSocket.prototype.onclose = function(event) {};

/**
 * An event listener to be called when a connection begins being attempted.
 * @param event
 */
ReconnectingWebSocket.prototype.onconnecting = function(event) {};

/**
 * An event listener to be called when a message is received from the server.
 * @param event
 */
ReconnectingWebSocket.prototype.onmessage = function(event) {};

/**
 * An event listener to be called when an error occurs.
 * @param event
 */
ReconnectingWebSocket.prototype.onerror = function(event) {};

/**
 * @returns {Boolean} true if the object has been destroyed, false otherwise.
 */
ReconnectingWebSocket.prototype.isDestroyed = function() {
  return this._isDestroyed === true;
};

/**
 * 销毁
 */
ReconnectingWebSocket.prototype.destroy = function() {
  this.close()
  this._isDestroyed = true
};

/**
 * Whether all instances of ReconnectingWebSocket should log debug messages.
 * Setting this to true is the equivalent of setting all instances of ReconnectingWebSocket.debug to true.
 */
ReconnectingWebSocket.debugAll = false;

ReconnectingWebSocket.CONNECTING = WebSocket.CONNECTING;
ReconnectingWebSocket.OPEN = WebSocket.OPEN;
ReconnectingWebSocket.CLOSING = WebSocket.CLOSING;
ReconnectingWebSocket.CLOSED = WebSocket.CLOSED;

export default ReconnectingWebSocket;
