// 图资引入
import ICON_CAR_ONLINE from '@/assets/images/car/car-online.png';
import ICON_CAR_OFFLINE from '@/assets/images/car/car-offline.png';
import ICON_CAR_WARN from '@/assets/images/car/car-warn.png';
import ICON_CAR_ERROR from '@/assets/images/car/car-error.png';
import ICON_CAR_UNUSED from '@/assets/images/car/car-unused.png';
import ICON_CAR_TRACK from '@/assets/images/car/car-track.png';
import ICON_CAR_TRACK_STOP from '@/assets/images/car/car-track-stop.png';

/**
 * fixme
 * 车辆状态
 * OFFLINE,ONLINE,ERROR,UNUSED,WARN用于车辆状态标识
 * TRACKSTOP,TRACK用于车辆轨迹查询标识
 */
const CAR_STATUS = {
  ONLINE: {
    icon: ICON_CAR_ONLINE,
    color: '#4bdaff',
    text: '正常'
  },
  OFFLINE: {
    icon: ICON_CAR_OFFLINE,
    color: '#7c7c7c',
    text: '维修'
  },
  ERROR: {
    icon: ICON_CAR_ERROR,
    color: '#fc0e22',
    text: '终端异常'
  },
  UNUSED: {
    icon: ICON_CAR_UNUSED,
    color: '#fda245',
    text: '超过15天未使用'
  },
  WARN: {
    icon: ICON_CAR_WARN,
    color: '#d7a8ff',
    text: '维修'
  },
  TRACKSTOP: {
    icon: ICON_CAR_TRACK_STOP,
    color: '#7c7c7c',
    text: '停驶'
  },
  TRACK: {
    icon: ICON_CAR_TRACK,
    color: '#4bdaff',
    text: '行驶'
  }
};
const CAR_STATUS_ARRAY = [
  CAR_STATUS.OFFLINE,
  CAR_STATUS.ONLINE,
  CAR_STATUS.ERROR,
  CAR_STATUS.UNUSED,
  CAR_STATUS.WARN,
  CAR_STATUS.TRACKSTOP,
  CAR_STATUS.TRACK
];

// 车辆选择框列表颜色
const CAR_LIST_STATUS = [
  'icon_car_stop',
  'icon_car_move',
  'icon_car_error',
  'icon_car_unused',
  'icon_car_warning'
];
export function getCarStatus () {
  return CAR_STATUS_ARRAY;
}
export function getCarListStatus () {
  return CAR_LIST_STATUS;
}
