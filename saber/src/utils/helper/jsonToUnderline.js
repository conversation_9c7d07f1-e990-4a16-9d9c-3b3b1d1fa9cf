import jsonToHump from '@/utils/helper/jsonToHump';

/**
 * 字符串的驼峰格式转下划线格式，eg：helloWorld => hello_world
 * @param {String} s
 * @return {String}
 */
function hump2Underline (s) {
  return s.replace(/([A-Z])/g, '_$1').toLowerCase();
}

/**
 * JSON对象的key值转换为下划线格式
 * @description 前端的接口参数需要发送到后台数据前都要使用这个函数，将JavaScript语言的驼峰命名的变量转为下划线分隔的内容
 * @param {Object} obj 对象，即json.parse的那个值
 * @return {Object}
 */
function jsonToUnderline (obj) {
  if (obj instanceof Array) {
    obj.forEach(function (v, i) {
      jsonToUnderline(v);
    });
  } else if (obj instanceof Object) {
    Object.keys(obj).forEach(function (key) {
      let newKey = hump2Underline(key);
      if (newKey !== key) {
        obj[newKey] = obj[key];
        delete obj[key];
      }
      // FIXME 打补丁 如果前端传值空字符串，改为传null，利用go特性自动转为默认值，为了解决下拉选择传参""后，后台go语言无法处理的问题。本项修改针对/baseInfo/dict?dictCode=
      // if (obj[newKey] === '') {
      // obj[newKey] = null;
      // }

      jsonToUnderline(obj[newKey]);
    });
  }
  return obj;
}

export default jsonToUnderline;
