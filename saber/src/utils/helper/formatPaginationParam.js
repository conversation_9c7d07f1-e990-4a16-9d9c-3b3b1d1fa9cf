import defined from '@/utils/core/defined';
/**
 * 格式化请求后台的数据
 * @param {Object} param
 * @param {Number} [param.page] 页码
 * @param {Number} [param.size] 每页的数量
 * @param {Number} [param.start] 开始的游标
 * @param {Number} [param.count] 游标往后查询多少项
 * @return {Object}
 */
function formatPaginationParam (param) {
  if (defined(param)) {
    if (defined(param.page) && defined(param.size)) {
      param.start = param.page * param.size;
      param.count = param.size;
      // param.page = undefined;
      // param.size = undefined;
    }
  }
  return param;
}

export default formatPaginationParam;
