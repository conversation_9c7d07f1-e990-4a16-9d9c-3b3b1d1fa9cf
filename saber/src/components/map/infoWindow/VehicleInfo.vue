<template>
  <div class="vehicle-container">
    <div class="container-header">
      <p class="container-header-title">{{ vehicleData.targetName }}</p>
      <div v-if="operationButton && vehicleData.treeId.includes('rnss')">
        <el-checkbox
          v-model="followOpen"
          class="follow-check"
          @change="followChange"
        >
          跟踪终端
        </el-checkbox>
        <!-- <span
          class="more-button"
          @click="moreDetail"
        >
          更多
        </span> -->
      </div>
      <div
        class="container-header-btn"
        @click="closeHandle"
      >
        <i class="el-icon-circle-close"/>
      </div>
    </div>
    <div class="vehicle-state">
      <div class="state-container">
        <!-- 设备详情 -->
        <div
          class="state-content-top"
        >
          <div v-if="temperatureList[0]">
            <span class="state-content-label">一路温度：</span>
            <span class="state-content-value">{{ temperatureList[0] }}</span>
          </div>
          <div v-if="temperatureList[1]">
            <span class="state-content-label">二路温度：</span>
            <span class="state-content-value">{{ temperatureList[1] }}</span>
          </div>
          <div v-if="temperatureList[2]">
            <span class="state-content-label">三路温度：</span>
            <span class="state-content-value">{{ temperatureList[1] }}</span>
          </div>
          <div v-if="temperatureList[3]">
            <span class="state-content-label">四路温度：</span>
            <span class="state-content-value">{{ temperatureList[1] }}</span>
          </div>
        </div>
        <div class="state-content-bottom">
          <div class="state-content-bottom-sign">
            <span class="state-content-label">序列号：</span>
            <span>{{ vehicleData.deviceUniqueId }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">终端类别：</span>
            <span>{{ getEnumDictLabel('bdmDeviceType', vehicleData.deviceType) }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">终端类型：</span>
            <span>{{ getEnumDictLabel('bdmDeviceType', vehicleData.deviceCategory) }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">赋码编号：</span>
            <span>{{ vehicleData.deviceNum }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">使用单位：</span>
            <span>{{ vehicleData.deptName }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">定位时间：</span>
            <span>{{ vehicleData.locTime ? parseTime(vehicleData.locTime) : '-' }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">当前位置：</span>
            <span>{{ position }}</span>
          </div>
        </div>
      </div>
      <div
        v-if="false"
        class="basics"
      >
        <div class="basics-info">
          <div class="basics-info-left">
            <div>
              <span class="state-content-label">司机姓名：</span>
              <span>{{ driverData.driverName }}({{ getEnumDictLabel('sex', driverData.sex) || '未知' }})</span>
            </div>
            <div>
              <span class="state-content-label">联系电话：</span>
              <span>{{ driverData.phone }}</span>
            </div>
            <div>
              <span class="state-content-label">准驾类型：</span>
              <span>{{ getEnumDictLabel('vehicleType', driverData.vehicleType) }}</span>
            </div>
            <div>
              <span class="state-content-label">从业类别：</span>
              <span>{{ getEnumDictLabel('certificateType', driverData.certificateType) }}</span>
            </div>
          </div>
        </div>
        <div class="basics-image">
          <el-image
            v-if="driverData.photoUrl"
            :src="driverData.photoUrl"
            :fit="fit"
          />
          <el-image
            v-else
            :src="require('@/assets/images/monitor/default.jpeg')"
            :fit="fit"
          />
        </div>
      </div>
      <div
        v-if="false"
        class="basics"
      >
        <div class="basics-info">
          <p class="basics-info-title">
            车辆基本资料
          </p>
          <div class="basics-info-left">
            <div>
              <span>车辆型号：</span>
              <span>xxx</span>
            </div>
            <div>
              <span>车架号：</span>
              <span>17658245147</span>
            </div>
            <div>
              <span>车身颜色：</span>
              <span>黄色</span>
            </div>
            <div>
              <span>运营年限：</span>
              <span>xxxx</span>
            </div>
            <div>
              <span>入户时间：</span>
              <span>xxxxx</span>
            </div>
            <div>
              <span>运输介质：</span>
              <span>xxxxx</span>
            </div>
            <div>
              <span>额定荷载：</span>
              <span>xxxxx</span>
            </div>
            <div>
              <span>所属机构：</span>
              <span>xxxxx</span>
            </div>
            <div style="width: 100%">
              <span>所属机构：</span>
              <span>石河子市新长运旅客运输有限公司</span>
            </div>
          </div>
        </div>
        <div class="basics-image">
          <el-image
            v-if="vehicleData.url"
            :src="vehicleData.url"
            :fit="fit"
          />
          <p>暂无图片</p>
        </div>
      </div>
    </div>
    <div
      v-if="operationButton"
      class="vehicle-btn"
    >
      <!-- <el-button
        size="small"
        @click="onReport"
      >点名</el-button>
      <el-button
        v-if="vehicleData.isVideoPlay"
        size="small"
        @click="gotoPhoto"
      >拍照</el-button>
      <el-button
        size="small"
        @click="gotoInstruction"
      >指令</el-button> -->
      <el-button
        size="small"
        @click="gotoTrack"
      >轨迹回放</el-button>
      <el-button
        v-if="vehicleData.isVideoPlay && !isOfficial"
        size="small"
        @click="gotoVideo"
      >实时视频</el-button>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/api/utils/share';
import getLabel from '@/utils/getLabel';
import { sendLocationMsg, parkingstate, driverInfo } from '@/api/center/instruction';
import { fourDimensionalAddress, gnAddress, vehicleAddress } from '@/api/monitoring/info.js';

export default {
  components: {

  },
  props: {
    vehicleData: {
      type: Object,
      default: ()=>{return {};}
    },
    followCar: { // 跟踪车辆集合
      type: Array,
      default: () => {
        return [];
      }
    },
    operationButton: {
      type: Boolean,
      default: true
    },
    isOfficial: {
      type: Boolean,
      default: false
    }
  },
  dicts: [
    'certificateType', 'vehicleType', 'sex', 'bdmDeviceType'
  ],
  data(){
    return{
      followOpen: false, // 开启跟踪
      driverData: {},
      temperatureList: [], // 温度列表
      position: '' // 当前位置
    };
  },
  watch: {
    vehicleData: {
      handler (newVal) {
        this.position = newVal.position;
        this.setFollowState();
        // this.getDriverInfo();
        if (!this.position) {
          this.getAddress(newVal);
        }
        // 车辆类型为冷链车时
        if (newVal.vehicleModel === '73' || newVal.vehicleModel === '33') {
          this.setTemperature();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods:{
    // 处理温度
    setTemperature() {
      this.temperatureList = this.vehicleData.temperature ? this.vehicleData.temperature.split(',') : [];
    },
    // // 四维逆地理编码
    // getAddress(data) {
    //   const query = {
    //     point: `${data.longitude}, ${data.latitude}`,
    //     st: 'Rgc2',
    //     uid: 'gzhgxh',
    //     cd: 'gcj02',
    //     output: 'json'
    //   };
    //   fourDimensionalAddress(query).then(res => {
    //     if (res.status === 200) {
    //       let { district_text, address } = res.data.result;
    //       district_text = district_text.replace(/>/g, '');
    //       this.vehicleData.position = district_text + address;
    //     }
    //   });
    // },
    // 四维逆地理编码(后台接口)
    async getAddress(data) {
      const query = {
        lon: Number(data.longitude),
        lat: Number(data.latitude)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          this.position = res.data;
          this.vehicleData.position = res.data;
          // this.$set(this.vehicleData, 'position', res.data);
        }
      });
    },
    // 国能逆地理编码
    getGNAddress(data) {
      const query = {
        postStr: {
          lon: data.longitude,
          lat: data.latitude,
          ver: 1
        },
        type: 'geocode'
      };
      gnAddress(query).then(res => {
        if (res.status === 200) {
          let { status, formatted_address } = res.data.result;
          if(status === 0) {
            this.position = formatted_address;
            this.vehicleData.position = formatted_address;
          }
        }
      });
    },
    // 获取驾驶员信息
    getDriverInfo() {
      const query = {
        licencePlate: this.vehicleData.licencePlate,
        licenceColor: this.vehicleData.licenceColor
      };
      driverInfo(query).then((res) => {
        this.driverData = res.data;
      }).catch((err) => {
        console.log('err', err);
      });
    },
    // 点击跟踪车辆按钮
    followChange (v) {
      this.$emit('followHandle', {
        open: v,
        ...JSON.parse(JSON.stringify(this.vehicleData))
      });
    },
    /**
     * 更多详情
     */
    moreDetail () {
      this.$router.push({
        path: '/base/vehicleDataBoard/' + this.vehicleData.vehicleId,
        query: {
          licencePlate: this.vehicleData.licencePlate,
          vehicleId: this.vehicleData.vehicleId,
          isRouter: this.$route.fullPath
        }
      });
    },
    // 设置跟踪车辆按钮
    setFollowState () {
      this.followOpen = false;
      this.followCar.forEach(item => {
        if (this.vehicleData.treeId === item.treeId) {
          this.followOpen = item.open;
        }
      });
    },
    /**
     * 视频
     */
    gotoVideo () {
      // this.$router.push({path: '/monitor/vehicle/video/live', query: {licencePlate: this.vehicleData.licencePlate}});
      this.$router.push({
        path: '/monitoring/videoLiveRTVS/index',
        query: {
          deviceId: this.vehicleData.deviceId,
          deviceType: this.vehicleData.deviceType,
          targetName: this.vehicleData.targetLabel,
          isRouter: this.$route.fullPath
        }
      });
    },
    /**
     * 轨迹
     */
    gotoTrack () {
      const trackInfo = {
        deviceId: this.vehicleData.deviceId,
        deviceType: this.vehicleData.deviceType,
        targetType: this.vehicleData.targetType,
        targetId: this.vehicleData.targetId,
        targetName: this.vehicleData.targetLabel,
        id: this.vehicleData.treeId
      }
      if (this.isOfficial) {
        this.$emit('handleTrack', trackInfo);
      } else {
        localStorage.setItem('TRACK_INFO', JSON.stringify(trackInfo));
        this.$router.push({
          path: '/monitoring/trackInfo/index',
          query: {
            isRouter: this.$route.fullPath
          }
        });
      }
    },
    /**
     * 点名
     */
    onReport () {
      let params = {
        licencePlate: this.vehicleData.licencePlate,
        vehicleId: this.vehicleData.vehicleId
      };
      sendLocationMsg(params).then(res => {
        console.log(res);
        this.$emit('redrawVehicle', res.data);
        this.$message({
          type: 'success',
          message: '点名成功'
        });
      }).catch(() => {});
    },
    /**
     * 指令下发
     */
    gotoInstruction () {
      this.$router.push({path: '/center/instruction/index', query: {id: this.vehicleData.treeId, targetName: this.vehicleData.targetName, deviceType: this.vehicleData.deviceType, deviceId: this.vehicleData.deviceId, isRouter: this.$route.fullPath}});
    },
    /**
     * 拍照
     */
    gotoPhoto () {
      this.$router.push({path: '/center/instruction/index', query: {id: this.vehicleData.treeId, targetName: this.vehicleData.targetName, from: 'photoInstruction', isRouter: this.$route.fullPath}});
    },
    closeHandle () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.container-header{
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgb(216, 236, 254);
    padding: 0 10px;
    &-title{
        color: rgb(51, 51, 51);
        font-size: 20px;
        font-weight: 600;
    }
    &-btn{
        font-size: 24px;
        cursor: pointer;
    }
}
.state-container{
    width: 100%;
    background: rgb(243, 249, 255);
    display: flex;
    flex-direction: column;
    padding: 0 12px 8px;
}
.vehicle-state{
    // padding: 10px 10px;
    // width: 100%;
    margin: 10px 10px;
    max-height: 220px;
    overflow: auto;
}
.state-content-top{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    &>div{
        padding-top: 6px;
        width: 50%;
    }
}
.state-content-label{
  display: inline-block;
  width: 70px;
  font-weight: 600;
  white-space: nowrap;
}
.state-content-bottom{
    &-sign{
        padding: 6px 0 0 0;
    }
}
.basics{
        width: 100%;
        display: flex;
        background: linear-gradient(270deg,#2d46c7,#fff) 0 0 no-repeat,linear-gradient(270deg,#fff,#2d46c7) 100% 0 no-repeat,linear-gradient(270deg,#2d46c7,#fff) 0 100% no-repeat,linear-gradient(270deg,#fff,#2d46c7) 100% 100% no-repeat;
        background-size: 51% 3px,51% 3px;
        padding: 10px;
    &-info{
        flex: 2;
        height: 100%;
        &-left>div{
            padding-top: 6px;
        }
    }
    &-image{
      flex: 1;
        ::v-deep .el-image{
          height: 110px;
        }
    }
}
.vehicle-btn{
  display: flex;
  justify-content: center;
  padding-bottom: 10px;
}
.more-button{
  color: #2398ff;
  padding: @xhSpacingBase @xhSpacingBase;
  margin-right: @xhSpacingBase;
  border-radius: @xhBorderRadiusBase;
  cursor: pointer;
  // margin-left: 285px;
}
.follow-check{
  // margin-left: 50px;
}
.state-content-value {
  white-space: nowrap;
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
    // 底部表格
    .vehicle-state {
      max-height: 150px !important;
    }
}
</style>
