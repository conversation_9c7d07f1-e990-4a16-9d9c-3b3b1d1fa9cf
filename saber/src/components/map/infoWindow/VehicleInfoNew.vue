<template>
  <div class="vehicle-container">
    <div class="container-header">
      <p class="container-header-title">{{ vehicleData.targetName }}</p>
      <div v-if="operationButton && vehicleData.trackable">
        <el-checkbox
          v-model="followOpen"
          class="follow-check"
          @change="followChange"
        >
          跟踪终端
        </el-checkbox>
      </div>
      <div
        class="container-header-btn"
        @click="closeHandle"
      >
        <i class="el-icon-circle-close"/>
      </div>
    </div>
    <div class="vehicle-state">
      <div class="state-container">
        <!-- 设备详情 -->
        <div class="state-content-bottom">
          <div class="state-content-bottom-sign">
            <span class="state-content-label">序列号：</span>
            <span>{{ vehicleData.uniqueId }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">终端类别：</span>
            <span>{{ getEnumDictLabel('bdmDeviceType', vehicleData.deviceType) }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">终端类型：</span>
            <span>{{ getEnumDictLabel('bdmDeviceType', vehicleData.deviceCategory) }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">赋码编号：</span>
            <span>{{ vehicleData.deviceNum }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">使用单位：</span>
            <span>{{ vehicleData.deptName }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">定位时间：</span>
            <span>{{ vehicleData.time ? parseTime(vehicleData.time) : '-' }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">当前位置：</span>
            <span>{{ position }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="operationButton"
      class="vehicle-btn"
    >
      <el-button
        size="small"
        @click="gotoTrack"
      >轨迹回放</el-button>
      <el-button
        v-if="vehicleData.videoable && !isOfficial"
        size="small"
        @click="gotoVideo"
      >实时视频</el-button>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/api/utils/share';
import getLabel from '@/utils/getLabel';
import { fourDimensionalAddress, gnAddress, vehicleAddress } from '@/api/monitoring/info.js';

export default {
  components: {

  },
  props: {
    vehicleData: {
      type: Object,
      default: ()=>{return {};}
    },
    followCar: { // 跟踪车辆集合
      type: Array,
      default: () => {
        return [];
      }
    },
    operationButton: {
      type: Boolean,
      default: true
    },
    isOfficial: {
      type: Boolean,
      default: false
    }
  },
  dicts: [
    'bdmDeviceType'
  ],
  data(){
    return{
      followOpen: false, // 开启跟踪
      position: '' // 当前位置
    };
  },
  watch: {
    vehicleData: {
      handler (newVal) {
        this.position = newVal.position;
        this.setFollowState();
        if (!this.position) {
          this.getAddress(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods:{
    // 四维逆地理编码(后台接口)
    async getAddress(data) {
      const query = {
        lon: Number(data.longitude),
        lat: Number(data.latitude)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          this.position = res.data;
          this.vehicleData.position = res.data;
        }
      });
    },
    // 国能逆地理编码
    getGNAddress(data) {
      const query = {
        postStr: {
          lon: data.longitude,
          lat: data.latitude,
          ver: 1
        },
        type: 'geocode'
      };
      gnAddress(query).then(res => {
        if (res.status === 200) {
          let { status, formatted_address } = res.data.result;
          if(status === 0) {
            this.position = formatted_address;
            this.vehicleData.position = formatted_address;
          }
        }
      });
    },
    // 点击跟踪车辆按钮
    followChange (v) {
      this.$emit('followHandle', {
        open: v,
        ...JSON.parse(JSON.stringify(this.vehicleData)),
        deviceId: this.vehicleData.deviceIdStr,
        position: [this.vehicleData.longitude, this.vehicleData.latitude]
      });
    },
    // 设置跟踪车辆按钮
    setFollowState () {
      this.followOpen = false;
      this.followCar.forEach(item => {
        if (this.vehicleData.deviceIdStr === item.deviceIdStr) {
          this.followOpen = item.open;
        }
      });
    },
    /**
     * 视频
     */
    gotoVideo () {
      this.$router.push({
        path: '/monitoring/videoLiveRTVS/index',
        query: {
          deviceId: this.vehicleData.deviceIdStr,
          deviceType: this.vehicleData.deviceType,
          targetName: this.vehicleData.targetName,
          isRouter: this.$route.fullPath
        }
      });
    },
    /**
     * 轨迹
     */
    gotoTrack () {
      const trackInfo = {
        deviceId: this.vehicleData.deviceIdStr,
        deviceType: this.vehicleData.deviceType,
        targetType: this.vehicleData.targetType,
        targetId: this.vehicleData.targetId,
        targetName: this.vehicleData.targetName,
        id: this.vehicleData.deviceIdStr
      }
      if (this.isOfficial) {
        this.$emit('handleTrack', trackInfo);
      } else {
        localStorage.setItem('TRACK_INFO', JSON.stringify(trackInfo));
        this.$router.push({
          path: '/monitoring/trackInfo/index',
          query: {
            isRouter: this.$route.fullPath
          }
        });
      }
    },
    closeHandle () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.container-header{
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgb(216, 236, 254);
    padding: 0 10px;
    &-title{
        color: rgb(51, 51, 51);
        font-size: 20px;
        font-weight: 600;
    }
    &-btn{
        font-size: 24px;
        cursor: pointer;
    }
}
.state-container{
    width: 100%;
    background: rgb(243, 249, 255);
    display: flex;
    flex-direction: column;
    padding: 0 12px 8px;
}
.vehicle-state{
    // padding: 10px 10px;
    // width: 100%;
    margin: 6px 6px;
    max-height: 260px;
    overflow: auto;
}
.state-content-label{
  display: inline-block;
  width: 70px;
  font-weight: 600;
  white-space: nowrap;
}
.state-content-bottom{
    &-sign{
        padding: 6px 0 0 0;
    }
}
.basics{
        width: 100%;
        display: flex;
        background: linear-gradient(270deg,#2d46c7,#fff) 0 0 no-repeat,linear-gradient(270deg,#fff,#2d46c7) 100% 0 no-repeat,linear-gradient(270deg,#2d46c7,#fff) 0 100% no-repeat,linear-gradient(270deg,#fff,#2d46c7) 100% 100% no-repeat;
        background-size: 51% 3px,51% 3px;
        padding: 10px;
    &-info{
        flex: 2;
        height: 100%;
        &-left>div{
            padding-top: 6px;
        }
    }
    &-image{
      flex: 1;
        ::v-deep .el-image{
          height: 110px;
        }
    }
}
.vehicle-btn{
  display: flex;
  justify-content: center;
  padding-bottom: 10px;
}
.more-button{
  color: #2398ff;
  padding: @xhSpacingBase @xhSpacingBase;
  margin-right: @xhSpacingBase;
  border-radius: @xhBorderRadiusBase;
  cursor: pointer;
  // margin-left: 285px;
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
    // 底部表格
    .vehicle-state {
      max-height: 150px !important;
    }
}
</style>
