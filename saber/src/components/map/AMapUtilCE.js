'use strict';

/**
 * 高德地图工具
 * @exports AmapUtil
 * @alias AmapUtil
 */
let AmapUtil = {};

/**
 * 加载高德地图
 * @param {Function} callback 回调函数
 * @param {Object} [options] 其他的加载参数
 */
AmapUtil.loadAMap = function (callback, options) {
  const MAP_URL = 'https://maplbs.ceic.com/webapplication/CEol/js/MayMap/MayMap-1.0.1.min.js'; // 国能内网地图
  if (window.MayMap) {
    window.MayMap.setKey("b4f0d3a1-2c5e-4a8f-9b3d-5e7f3c2b1a4d");
    callback(window.MayMap);
  } else {
    let script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = MAP_URL;
    document.head.appendChild(script);
    setTimeout(() => {
      AmapUtil.loadAMap(callback, options);
    }, 500);
  }
};

export default AmapUtil;
