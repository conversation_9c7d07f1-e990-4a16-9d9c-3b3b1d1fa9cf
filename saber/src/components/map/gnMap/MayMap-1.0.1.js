import CE from './CE';
import './CE.css';
let AmapUtil = {};
AmapUtil.loadAMap = function (callback) {
  /**
  * 仿高德api封装openlayers
  * @param attribute {Object} 所有属性
  * @param MapInit {Object} 地图方法
  * @param TileLayer {Object} 图层方法
  * @param Method {Object} 计算类方法
  * @param Measure {Object} 测距侧面方法
  * @param Track {Object} 轨迹方法
  * <AUTHOR> 2024/07/25 
  * @version v1.0.1
  */

  var currentPath = function () {
    var jsPath = document.currentScript ? document.currentScript.src : function () {
      var js = document.scripts,
        last = js.length - 1,
        src;
      for (var i = last; i > 0; i--) {
        if (js[i].readyState === 'interactive') {
          src = js[i].src;
          break;
        }
      }
      return src || js[last].src;
    }();
    return jsPath.substring(0, jsPath.lastIndexOf('/') + 1);
  }();

  function customLoadCss(url) {
    var head = document.getElementsByTagName('head')[0];
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;
    head.appendChild(link);
  }

  function customLoadJs(url) {
    var xhrObj = new XMLHttpRequest();
    xhrObj.open('GET', url, false);
    xhrObj.send(null);
    return xhrObj.responseText
  }
  
  // customLoadCss(`${currentPath}CE.css`)
  // var zdyOl = customLoadJs(`${currentPath}CE.js`)
  // eval(zdyOl)
  
  var ol = CE

  var attribute = {
    olMap: null,
    eventName: {},
    key: "",
    mapconfig: [
      {
        name: "天地图",
        maps: [
          {
            type: "矢量", urls: [
              { name: "矢量", zIndex: 0, url: "http://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=04fc4820307277ad1e12e0d389e005eb" },
              { name: "矢量注记", zIndex: 0, url: "http://t0.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=04fc4820307277ad1e12e0d389e005eb" },
            ],
          },
          {
            type: "影像",
            urls: [
              { name: "影像", zIndex: 0, url: "http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=04fc4820307277ad1e12e0d389e005eb" },
              { name: "影像注记", zIndex: 0, url: "http://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=04fc4820307277ad1e12e0d389e005eb" },
            ],
          },
        ]
      }
    ],
  };

  if (window.localStorage.hasOwnProperty("cedi__Access-Token")) {
    attribute.mapconfig = [
      {
        name: "天地图",
        maps: [
          {
            type: "矢量", urls: [
              { name: "矢量", zIndex: 0, url: "http://10.161.51.137/basemap/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" },
              { name: "矢量注记", zIndex: 0, url: "http://10.161.51.137/basemap/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" },
            ],
          },
          {
            type: "影像",
            urls: [
              { name: "影像", zIndex: 0, url: "http://10.161.51.137/basemap/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" },
              { name: "影像注记", zIndex: 0, url: "http://10.161.51.137/basemap/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" },
            ],
          },
        ]
      }
    ];
  }

  /**
   * 设置全局key值
   * @param key {String} 必填 平台申请的key值关系的权限
   * @return void
   */
  let setKey = (key) => {
    CE.CEkey = key
    attribute.key = key
  }

  /**
   * 测距侧面
   * @param layer {Object} 图层对象
   * @return void
   */
  class Measure {
    constructor(layer) {
      var measureDraw, measureValue, measureUnit, measureSketch, measureTooltipElement, measureTooltip, measureListener
      var measureSource = layer.getSource()

      // 计算面
      var formatArea = (polygon) => {
        var sourceProj = attribute.olMap.getView().getProjection();
        var area = ol.sphere.getArea(polygon, {
          projection: sourceProj
        });
        var output;
        measureValue = (Math.round(area * 100) / 100)
        measureUnit = "平方米"
        if (area > 1000000) {
          //换算成平方千米
          output = (Math.round(area / 1000000 * 100) / 100) + "平方千米";
        } else {
          output = (Math.round(area * 100) / 100) + "平方米";
        }
        return output;
      }

      // 计算线
      var formatLength = (line) => {
        var sourceProj = attribute.olMap.getView().getProjection();
        var length = ol.sphere.getLength(line, {
          projection: sourceProj
        });
        var output;
        measureValue = (Math.round(length * 100) / 100)
        measureUnit = "米"
        if (length > 1000) {
          //换算成千米
          output = (Math.round(length / 1000 * 100) / 100) + "千米";
        } else {
          output = (Math.round(length * 100) / 100) + "米";
        }
        return output;
      }

      //设置显示
      var createMeasureTooltip = () => {
        if (measureTooltipElement) {
          // $(measureTooltipElement).remove();
          measureTooltipElement.remove();
          measureTooltipElement = null;
          // me.measureTooltipElement.parentNode.removeChild(me.measureTooltipElement);
        }
        measureTooltipElement = document.createElement("div");
        measureTooltipElement.className = "tooltipbar tooltipbar-measure";
        measureTooltip = new ol.Overlay({
          element: measureTooltipElement,
          offset: [0, -15],
          positioning: "bottom-center"
        });
        attribute.olMap.addOverlay(measureTooltip);
        var element = document.querySelectorAll(".tooltipbar")
        element.forEach(e => {
          e.style.color = "red"
        })
      }

      // 事件回调
      var drawstartCallback = (evt) => {
        measureSketch = evt.feature; //绘制的要素
        var tooltipCoord = evt.coordinate; //绘制的坐标
        measureListener = measureSketch.getGeometry().on("change", function (evt1) {
          var geom = evt1.target;
          var output;
          if (geom instanceof ol.geom.Polygon) {
            //输出面积
            output = formatArea(geom);
            tooltipCoord = geom.getInteriorPoint().getCoordinates();
          } else if (geom instanceof ol.geom.LineString) {
            //输出长度
            output = formatLength(geom);
            tooltipCoord = geom.getLastCoordinate();
          }
          //将测量值设置到测量工具提示框中进行显示
          measureTooltipElement.innerHTML = output;
          //设置测量工具提示框的显示位置
          measureTooltip.setPosition(tooltipCoord);
        });
      }

      // 事件回调
      var drawendCallback = (evt, callback) => {
        measureTooltipElement.className = "tooltipbar tooltipbar-static";
        measureTooltip.setOffset([0, -7]);
        measureSketch = null;
        measureTooltipElement = null;
        createMeasureTooltip();
        ol.Observable.unByKey(measureListener);
        callback && callback()
      }

      // 添加数据
      var paintingElements = (type, cb) => {
        measureValue = 0
        if (measureDraw) {
          attribute.olMap.removeInteraction(measureDraw);
          measureDraw = null;
        }
        measureDraw = new ol.interaction.Draw({
          source: measureSource,
          type: type,
          // snapTolerance: 20,
        });
        attribute.olMap.addInteraction(measureDraw);
        createMeasureTooltip();
        //获取点位作坐标
        measureListener = undefined
        measureDraw.on('drawstart', drawstartCallback)
        measureDraw.on("drawend", (e) => drawendCallback(e, cb))
      }

      /**
      * 测距 
      * callback {Function} 回调
      * @return void
      */
      /**
      * callback 回调函数
      * @param output {Number} 测量的值
      * @param unit {String} 单位
      */
      this.RangingTool = (callback) => {
        paintingElements('LineString', () => {
          callback && callback({
            output: measureValue,
            unit: measureUnit,
          })
        })

      }

      /**
      * 侧面 
      * callback {Function} 回调
      * @return void
      */
      /**
      * callback 回调函数
      * @param output {Number} 测量的值
      * @param unit {String} 单位
      */
      this.measureArea = (callback) => {
        paintingElements('Polygon', () => {
          callback && callback({
            output: measureValue,
            unit: measureUnit,
          })
        })
      }

      /**
       * 取消编辑
       * @return void
       */
      this.cancelMeasure = () => {
        if (measureDraw) {
          attribute.olMap.removeInteraction(measureDraw);
          measureDraw = null;
        }
        if (measureTooltip) {
          attribute.olMap.removeOverlay(measureTooltip);
          measureTooltip = null;
        }
      }

      /**
       * 清除编辑和数据
       * @return void
       */
      this.clearMeasure = () => {
        measureSource.clear();
        let element = document.querySelectorAll('.tooltipbar')
        element.forEach(e => {
          e.remove()
        })
        this.cancelMeasure()
      }
    }
  }

  /**
   * 轨迹播放
   * @param params {Object} 参数集合
   * @return void
   */
  /**
   * params 参数
   * @param layer {Object} 必填 图层对象layer
   * @param data {Array<Array>} 必填 线数据
   * @param interval {Number} 两点更新的时间间隔S
   * @param multiple {Number} 播放速度
   */
  class Track {
    constructor(params) {
      var PointData = params.data.map(item => {
        return Method.TransferFiftySeven(item)
      })
      var NUSource = params.layer.getSource();
      var Pointlength = PointData.length
      var interval = params.interval ? params.interval : 1
      var multiple = params.multiple ? params.multiple : 1
      // var now = 100 * interval
      var now = 100
      var period = 1000 * interval / multiple / now
      let collect = {
        index: 0,
        reckon: 0
      }

      var watch = (obj, prop, handler) => {
        let value = obj[prop];
        Object.defineProperty(obj, prop, {
          get() {
            return value;
          },
          set(newValue) {
            if (newValue !== value) {
              handler(newValue, value);
              value = newValue;
            }
          }
        });
      }

      var time, PointFeature, LineStringFeature, crt, x, y, suspendType
      PointFeature = new ol.Feature(new ol.geom.Point(PointData[0]));
      NUSource.addFeature(PointFeature)
      LineStringFeature = new ol.Feature(new ol.geom.LineString(PointData));
      NUSource.addFeature(LineStringFeature)
      this.PointFeature = PointFeature
      this.LineStringFeature = LineStringFeature

      var updateFeature = () => {
        suspendType = false
        if (collect.index !== 0) {
          time = setInterval(() => {
            collect.reckon += period
            if (collect.reckon <= 1000 * interval / multiple) {
              crt = [crt[0] - x, crt[1] - y]
              PointFeature.setGeometry(new ol.geom.Point(crt))
            } else {
              clearInterval(time)
              time = undefined
              collect.reckon = 0
              this.play()
            }
          }, period)
        }

      }

      /**
       * 播放
       * @return void
       */
      this.play = () => {
        if (time) return
        if (suspendType) {
          updateFeature()
        } else {
          if (collect.index < Pointlength - 1) {
            collect.index++
            var startPoint = PointData[collect.index - 1]
            var endPoint = PointData[collect.index]
            x = (startPoint[0] - endPoint[0]) / now
            y = (startPoint[1] - endPoint[1]) / now
            crt = startPoint
            PointFeature.setGeometry(new ol.geom.Point(crt))
            updateFeature()
          } else {
            collect.index = 0
          }
        }
      }


      /**
       * 设置位置
       * @param index {number} 跳转的索引位置
       * @return void
       */
      this.change = (index) => {
        collect.index = index
        collect.reckon = 0
        var startPoint, endPoint
        collect.index++
        if (collect.index < Pointlength) {
          startPoint = PointData[collect.index - 1]
          endPoint = PointData[collect.index]
          x = (startPoint[0] - endPoint[0]) / now
          y = (startPoint[1] - endPoint[1]) / now
          crt = startPoint
          PointFeature.setGeometry(new ol.geom.Point(crt))
        } else {
          startPoint = PointData[collect.index - 1]
          crt = startPoint
          PointFeature.setGeometry(new ol.geom.Point(crt))
          collect.index = 0
        }
      }

      /**
       * 暂停
       * @return void
       */
      this.suspend = () => {
        suspendType = true
        clearInterval(time)
        time = undefined
      }

      /**
       * 停止
       * @return void
       */
      this.stop = () => {
        suspendType = false
        collect.index = 0
        collect.reckon = 0
        clearInterval(time)
        time = undefined
      }

      /**
       * 设置播放倍数默认1倍
       * @return void
       */
      this.setMultiple = (value) => {
        var b = collect.reckon / period
        multiple = value ? value : 1
        period = 1000 * interval / multiple / now
        this.suspend()
        setTimeout(() => {
          collect.reckon = period * b
          this.play()
        })

      }

      /**
       * 监听播放位置
       * @return void
       */
      this.moving = (cb) => {
        watch(collect, "index", (newValue, oldValue) => {
          cb && cb(oldValue)
        })
      }
    }
  }

  /**
   * 地图初始化
   * @param params {Object} 参数集合
   * @return void
   */
  /**
   * params 参数
   * @param key {String} 必填 平台申请的key值关系的权限
   * @param target {String} 必填 DOM的id
   * @param layers {Array<Object>} 图层数组
   * @param center {Array<Number>} 初始化地图定位的点
   * @param zoom {Number} 初始化地图的比例尺
   */
  class MapInit {
    constructor(params = {}) {
      if (!attribute.key) {
        console.error("请到北斗高精度一体化服务平台申请key值！")
        return
      }
      let center = params.center ? Method.TransferFiftySeven(params.center) : undefined
      // CE.CEkey = params.key
      this.olMap = new ol.Map({
        target: params.target,
        layers: params.layers || [],
        view: new ol.View({
          projection: 'EPSG:3857',
          center: center || [12956553.267236501, 4853465.584088427],
          zoom: params.zoom || 4,
          maxZoom: params.maxZoom || 18,
          minZoom: params.minZoom || 0,
        }),
        controls: ol.control.defaults.defaults({
          zoom: false,
          rotate: false,
          attribution: false
        }).extend([])
      });
      attribute.olMap = this.olMap
    }

    /**
     * 添加图层
     * @param layer {Object} 图层对象
     * @return void 
     */
    addLayer(layer) {
      this.olMap.addLayer(layer)
    }

    /**
     * 删除图层
     * @param layer {Object} 图层对象
     * @return void 
     */
    removeLayer(layer) {
      this.olMap.removeLayer(layer)
    }

    /**
     * 注销地图
     * @param layer {Object} 图层对象
     * @return void 
     */
    destroy() {
      this.olMap.dispose()
      this.olMap = null
      attribute.olMap = null
    }

    /**
    * 获取地图缩放级别
    * @return Number 比例尺 
    */
    getZoom() {
      let view = this.olMap.getView();
      var zoom = view.getZoom();
      return zoom
    }

    /**
    * 地图放大一级显示
    * @return void
    */
    zoomIn() {
      let view = this.olMap.getView();
      var zoom = view.getZoom();
      if (zoom + 1 <= 18) {
        view.setZoom(zoom + 1);
      }
    }

    /**
    * 地图缩小一级显示
    * @return void
    */
    zoomOut() {
      let view = this.olMap.getView();
      var zoom = view.getZoom();
      if (zoom - 1 >= 0) {
        view.setZoom(zoom - 1);
      }
    }

    /**
     * 地图缩放指定级别且定位到指定中心点
     * @param center {Array<Number>} 必填 坐标
     * @param zoom {Number} 必填 比例尺
     * @return void 
     */
    setZoomAndCenter(center, zoom) {
      let view = this.olMap.getView();
      let ct = Method.TransferFiftySeven(center)
      view.animate({
        center: ct,
        zoom: zoom,
        duration: 500,
      });
    }

    /**
     * 设置地图显示的中心点
     * @param center {Array<Number>} 必填 坐标
     * @return void 
     */
    setCenter(center) {
      let view = this.olMap.getView();
      let ct = Method.TransferFiftySeven(center)
      view.setCenter(ct)
    }

    /**
     * 地图平移至指定位置
     * @param center {Array<Number>} 必填 坐标
     * @return void 
     */
    panTo(center) {
      let view = this.olMap.getView();
      let ct = Method.TransferFiftySeven(center)
      view.animate({
        center: ct,
        duration: 500,
      });
    }

    /**
     * 添加控件
     * @param params {Object} 必填 参数集合
     * @return void 
     */
    /**
     * params 参数
     * @param type {String} 必填 类型
     * @param target {String} DOM的id 放入某个dom内
     * @param className {String} 添加自定义class
     * @param layers {Array<Object>} 图层数组 可用 VectorBaseMap、VectorAnnotation等生成的图层
     */
    addControl(params) {
      let options = {
        label: params.label,
        tipLabel: params.tipLabel,
        target: params.target,
        className: params.className
      }

      switch (params.type) {
        case "Zoom": //缩放
          this.olMap.addControl(new ol.control.Zoom(options))
          break;
        case "FullScreen": //全屏
          this.olMap.addControl(new ol.control.FullScreen(options))
          break;
        case "ScaleLine": //比例尺
          this.olMap.addControl(new ol.control.ScaleLine(options))
          break;
        case "ZoomSlider": //缩放滑块
          this.olMap.addControl(new ol.control.ZoomSlider(options))
          break;
        case "OverviewMap": //鹰眼
          let layers = params.layers ? params.layers : []
          if (layers.length === 0) {
            layers = [TileLayer.VectorBaseMap(), TileLayer.VectorAnnotation()]
          }
          options.layers = layers
          options.collapsed = false
          this.olMap.addControl(new ol.control.OverviewMap(options))
          break;
        case "MousePosition": //鼠标经纬度实时显示
          this.olMap.addControl(new ol.control.MousePosition(options))
          break;
        case "ZoomToExtent": //一键恢复到默认视图
          this.olMap.addControl(new ol.control.ZoomToExtent(options))
          break;
      }
    }

    /**
     * 清除所有矢量图层上的要素
     * @return void 
     */
    clearMap() {
      let layerArray = this.olMap.getLayers().getArray();
      layerArray.forEach(item => {
        let value = item.get("type")
        if (value === "vector") {
          let Source = item.getSource()
          Source.clear()
        }
      })
    }

    /**
    * 绑定事件
    * @param type {String} 必填 图层对象
    * @param callback {Object} 回调
    * @return void
    */
    /**
     * type 暂时只做了3个处理,更多事件参考ol 
     * @param click {String} 点击事件
     * @param pointermove {String} 鼠标移动
     * @param pointerdrag {String} 鼠标拖拽
     * @param movestart {String} 地图移动开始
     * @param moveend {String} 地图移动结束
     */
    /**
     * callback 事件触发的回调
     * @param event {Object} openlayers 事件对象
     * @param coordinate {Array<Number>} 鼠标位置的坐标
     * @param feature {Array<Object>} 鼠标位置的要素
     * @param data {Array<Object>} 鼠标位置的要素的自定义属性
     */
    on(type, callback) {
      this.off(type)
      let list = ["click", "pointermove", "pointerdrag"]
      attribute.eventName[type] = (event) => {
        var coordinate, feature, data
        if (list.indexOf(type) !== -1) {
          coordinate = Method.TransferTwentySix(event.coordinate);
          feature = this.olMap.forEachFeatureAtPixel(
            event.pixel,
            function (feature) {
              return feature;
            }, {
            layerFilter: function (layer) {
              return layer.get("name");
            },
          }
          );
        }
        if (feature) {
          let features = feature.get("features");
          if (features) {
            feature = features
          } else {
            feature = [feature]
          }
          data = feature.map(item => {
            return item.get("data");
          })
        } else {
          feature = []
          data = []
        }
        callback && callback({ event, coordinate, feature, data })
      }
      this.olMap.on(type, attribute.eventName[type])
    }

    /**
    * 取消绑定事件
    * @param layer {Object} 必填 图层对象
    * @return Array 最大最小坐标
    */
    off(type) {
      if (!attribute.eventName[type]) return
      this.olMap.un(type, attribute.eventName[type])
      type, attribute.eventName[type] = undefined
    }
  }

  /* 图层方法 */
  var TileLayer = {
    /**
     * 矢量底图
     * @return Object 图层对象
     */
    VectorBaseMap() {
      let urls = attribute.mapconfig[0].maps[0].urls[0]
      return new ol.layer.Tile({
        title: urls.name,
        source: new ol.source.XYZ({
          url: urls.url,
        }),
        type: "underlay" //自定义属性为了清除map数据时过滤掉底图
      })
    },

    /**
    * 矢量底图注记
    * @return Object 图层对象
    */
    VectorAnnotation() {
      let urls = attribute.mapconfig[0].maps[0].urls[1]
      return new ol.layer.Tile({
        title: urls.name,
        source: new ol.source.XYZ({
          url: urls.url,
        }),
        type: "underlay" //自定义属性为了清除map数据时过滤掉底图
      })
    },

    /**
    * 卫星底图
    * @return Object 图层对象
    */
    Satellite() {
      let urls = attribute.mapconfig[0].maps[1].urls[0]
      return new ol.layer.Tile({
        title: urls.name,
        source: new ol.source.XYZ({
          url: urls.url,
        }),
        type: "underlay" //自定义属性为了清除map数据时过滤掉底图
      })
    },

    /**
    * 卫星底图注记
    * @return Object 图层对象
    */
    SatelliteAnnotation() {
      let urls = attribute.mapconfig[0].maps[1].urls[1]
      return new ol.layer.Tile({
        title: urls.name,
        source: new ol.source.XYZ({
          url: urls.url,
        }),
        type: "underlay" //自定义属性为了清除map数据时过滤掉底图
      })
    },

    /**
    * 生成矢量图层
    * @param params {Object} 参数集合
    * @return Object 图层 layer样式参考openlayers
    */
    /**
    * params 参数
    * @param features {Array<Object>} 要素集
    * @param name {String} 图层名称
    * @param zIndex {Number} 图层层级
    */
    LabelsLayer(params = {}) {
      return new ol.layer.Vector({
        source: new ol.source.Vector({
          features: params.features || []
        }),
        name: params.name || "NUSourceLayer",
        zIndex: params.zIndex || 1,
        type: "vector"
      })
    },

    /**
    * 生成聚合图层
    * @param params {Object} 参数集合
    * @return Object 图层 layer样式参考openlayers
    */
    /**
    * params 参数
    * @param features {Array<Object>} 要素集
    * @param name {String} 图层名称
    * @param zIndex {Number} 图层层级
    * @param distance {Number} 要素之间最小距离
    */
    ClusterLayer(params = {}) {
      return new ol.layer.Vector({
        source: new ol.source.Cluster({
          distance: params.distance || 20,
          source: new ol.source.Vector({
            features: params.features || []
          })
        }),
        name: params.name || "NUSourceLayer",
        zIndex: params.zIndex || 1,
        type: "Cluster"
      })
    },

    /**
    * 加载tms
    * @param params {Object} 参数集合
    * @return Object 图层 layer样式参考openlayers
    */
    /**
    * params 参数
    * @param url {String} 必填 xyz地址
    * @param name {String} 图层名称
    * @param zIndex {Number} 图层层级
    */
    PictureLayer(params = {}) {
      return new ol.layer.Tile({
        source: new ol.source.XYZ({
          url: params.url,
        }),
        name: params.name || "NUSourceLayer",
        zIndex: params.zIndex || 1,
        type: "underlay" //自定义属性为了清除map数据时过滤掉底图
      })
    },

    /**
    * 清除图层上所有的矢量要素
    * @param layer {Object} 必填 图层对象
    * @return void
    */
    clearLayer(layer) {
      let Source = layer.getSource()
      Source.clear()
    },

    /**
    * 设置图层显示范围
    * @param layer {Object} 必填 图层对象
    * @param list {Array} 必填 左下右上
    * @return void
    */
    setExtent(layer, list) {
      let zx = Method.TransferFiftySeven([list[0], list[1]])
      let ys = Method.TransferFiftySeven([list[2], list[3]])
      layer.setExtent([...zx, ...ys])
    },

    /**
     * 图层显示隐藏
     * @param layer {Object} 必填 图层对象
     * @param type {Boolean} 必填 是否显示隐藏
     * @return void 
     */
    setVisible(layer, type) {
      layer.setVisible(type)
    },

    /**
    * feature 要素添加
    * @param params {Object} 参数集合
    * @return Object 要素 feature样式设置参考openlayers 
    */
    /**
    * params 参数
    * @param layer {Object} 必填 图层对象
    * @param type {String} 必填 数据类型 Point 点、LineString 线、Polygon 面、Rectangle矩形、Circle圆
    * @param data {Object} 自定义属性
    * @param radius {Number} 添加圆的时候必填（半径）
    */
    add(params = {}) {
      let essential
      switch (params.type) {
        case "Point":
          essential = new ol.geom.Point(params.coordinate)
          essential.transform("EPSG:4326", "EPSG:3857")
          break;
        case "LineString":
          essential = new ol.geom.LineString(params.coordinate)
          essential.transform("EPSG:4326", "EPSG:3857")
          break;
        case "Polygon":
          essential = new ol.geom.Polygon(params.coordinate)
          essential.transform("EPSG:4326", "EPSG:3857")
          break;
        case "Rectangle":
          essential = new ol.geom.Polygon(params.coordinate)
          essential.transform("EPSG:4326", "EPSG:3857")
          break;
        case "Circle":
          let coordinate = Method.TransferFiftySeven(params.coordinate)
          essential = new ol.geom.Circle(coordinate, params.radius / ol.proj.getPointResolution('EPSG:3857', 1, coordinate, 'm'))
          break;
      }
      if (!essential) {
        console.error("请输入类型")
      }
      let Source
      let type = params.layer.get("type")
      switch (type) {
        case "Cluster":
          Source = params.layer.getSource().getSource();
          break;

        default:
          Source = params.layer.getSource();
          break;
      }

      var feature = new ol.Feature(essential);
      feature.set("data", params.data || {})
      Source.addFeature(feature);
      return feature
    },

    /**
    * feature 要素添加
    * @param geojson {Object} 必填 GeoJSON 数据
    * @return Object 要素feature样式设置参考openlayers 
    */
    addGeoJSON(layer, geojson) {
      let feature = new ol.format.GeoJSON().readFeature(geojson, {
        dataProjection: "EPSG:4326",
        featureProjection: "EPSG:3857",
      });
      let Source = layer.getSource();
      Source.addFeature(feature)
      return feature
    },

    /**
    * feature 要素添加
    * @param geojsons {Object} 必填 GeoJSON 数据
    * @return Array<Object> 要素feature样式设置参考openlayers 
    */
    addGeoJSONs(layer, geojsons) {
      let features = new ol.format.GeoJSON().readFeatures(geojsons, {
        dataProjection: "EPSG:4326",
        featureProjection: "EPSG:3857",
      });
      let Source = layer.getSource();
      Source.addFeatures(features)
      return features
    },

    /**
    * feature 要素删除
    * @param layer {Object} 必填 图层对象
    * @param feature {Object} 必填 要素对象
    * @return void
    */
    remove(layer, feature) {
      let Source = layer.getSource();
      Source.removeFeature(feature);
    },

    /**
    * 获取地图视图范围
    * @param layer {Object} 必填 图层对象
    * @return Array 最大最小坐标 坐下右上的坐标
    */
    getExtent(layer) {
      let Source = layer.getSource();
      var extent = Source.getExtent()
      let front = Method.TransferTwentySix([extent[0], extent[1]])
      let after = Method.TransferTwentySix([extent[2], extent[3]])
      return [...front, ...after]
    },

    /**
     * 根据地图覆盖物自动缩放至合适视野级别
     * @param layer {Object} 必填 图层对象
     * @return void 
     */
    setFitView(layer) {
      let Source = layer.getSource();
      let extent = Source.getExtent()
      if (!attribute.olMap) {
        console.error("请初始化地图！")
        return
      }
      let view = attribute.olMap.getView();
      view.fit(extent, {
        duration: 500,
      });
    },

    /**
     * 信息窗体
     * @param params {Object} 必填 图层对象
     * @return Object marker对象
     */
    /**
    * params 参数
    * @param center {Array<Number>} 必填 坐标点位
    * @param html {HTMLElement} 内容
    * @param offset {Array<Number>} 偏移位置
    */
    InfoWindow(params) {
      var center = Method.TransferFiftySeven(params.center)
      var bubbleElement = document.querySelector(".bubble_tool_tip")
      bubbleElement = document.createElement("div");
      bubbleElement.className = `bubble_tool_tip`;
      bubbleElement.style.position = "relative"
      bubbleElement.style.background = "rgb(255, 255, 255, 1)"
      bubbleElement.style.fontSize = "12px"
      var str = `
        <div class='bubble_tip_body'>
          ${params.html || "暂无内容"}
        </div>
        <div class='bubble_tip_bottom' style="position: relative;width: 20px;margin: 0 auto;">
          <span style=" position: absolute;width: 0;height: 0;border-top: 12px solid rgb(255, 255, 255, 1); border-right: 10px solid transparent;border-left: 10px solid transparent;border-bottom: 10px solid transparent;"></span>
        </div>
      `
      bubbleElement.innerHTML = str;

      var marker = new ol.Overlay({
        element: bubbleElement,
        position: center,
        offset: params.offset || [0, -11]
      });

      attribute.olMap.addOverlay(marker);

      // 解决positioning不生效
      marker.setPositioning("bottom-center")
      return marker
    },

    /**
     * 关闭信息窗体
     * @param marker {Object} marker对象
     * @return void
     */
    closeWindow(marker) {
      attribute.olMap.removeOverlay(marker);
    },

    /**
     * 关闭所有信息窗体
     * @return void
     */
    closeAllWindow() {
      attribute.olMap.getOverlays().clear();
    },

    /**
     * 绘制点、折线、多边形、矩形、圆
     * @param param {Object} 必填 图层对象
     * @return void
     */
    /**
     * param 对象
     * @param layer {Object} 必填 图层对象
     * @param type {String} 必填 绘制要素类型 Point点、LineString线、Polygon面、Rectangle矩形、Circle圆
     * @param state {Boolean} 是否点击完销毁
     * @param callback {Function} 点击事件回调
     */
    /**
     * callback 回调函数
     * @param event {Object} 点击事件对象
     * @param coordinate {Array} 点击的坐标
     * @param radius {Number} 半径（米）
     * @param feature {Object} feature要素
     */
    MouseTool(param) {
      var source = param.layer.getSource()
      var geometryFunction, type
      switch (param.type) {
        case "Rectangle":
          type = "LineString"
          geometryFunction = function (coordinates, geometry) {
            if (!geometry) {
              geometry = new ol.geom.Polygon([]);
            }
            var start = coordinates[0];
            var end = coordinates[1];
            geometry.setCoordinates([
              [start, [start[0], end[1]], end, [end[0], start[1]], start]
            ]);
            return geometry;
          };
          break;

        default:
          type = param.type
          break;
      }
      var draw = new ol.interaction.Draw({
        source: source,
        type: type,
        geometryFunction: geometryFunction,
        maxPoints: geometryFunction ? 2 : undefined
      });
      let drawCallback = (event) => {
        var radius, wkt
        if (type === "Circle") {
          var circle = event.feature.getGeometry();
          var centerLogLat = circle.getCenter()
          var geom2 = new ol.geom.Point(centerLogLat)
          wkt = new ol.format.WKT().writeGeometry(geom2)
          let sketchCoords_1 = Method.TransferTwentySix(event.target.sketchCoords_[0]);
          let sketchCoords_2 = Method.TransferTwentySix(event.target.sketchCoords_[1]);
          radius = ol.sphere.getDistance(sketchCoords_1, sketchCoords_2);
        } else {
          wkt = new ol.format.WKT().writeFeature(event.feature);
        }
        var Geome, coordinate
        var Geome = new ol.format.WKT().readGeometry(wkt, {
          dataProjection: "EPSG:3857",
          featureProjection: "EPSG:4326",
        });
        var coordinate = Geome.getCoordinates()
        param.callback && param.callback({ event, coordinate, radius, feature: event.feature })
        setTimeout(() => {
          if (param.state) {
            source.removeFeature(event.feature)
          }
          attribute.olMap.removeInteraction(draw);
          draw.un("drawend", drawCallback)
        })
      }
      // 回调
      draw.on("drawend", drawCallback)
      attribute.olMap.addInteraction(draw);
    },

    /**
     * 编辑点、折线、多边形、圆（矩形未实现）
     * @param param {Object} 必填 图层对象
     * @return void
     */
    /**
     * param 对象
     * @param layer {Object} 必填 图层对象
     * @param feature {Object} 要素对象如果传值则会默认选中
     * @param callback {Function} 编辑完成后的回调
     */
    /**
     * callback 回调函数
     * @param event {Object} 事件对象
     * @param coordinate {Array} 点击的坐标
     * @param radius {Number} 半径（米）
     * @param feature {Object} 要素对象
     * @param data {Object} 要素上的数据
     */
    MouseToolEditor(param) {
      let selectInteraction, modifyInteraction, editparam, coordinate, type
      if (selectInteraction) {
        attribute.olMap.removeInteraction(selectInteraction);
        selectInteraction = null;
      }
      selectInteraction = new ol.interaction.Select({
        wrapX: false,
        layers: [param.layer] //过滤条件  编辑图层1
      });

      var rmovecallback = (event) => {
        coordinate = event.coordinate
      }

      if (param.feature) {
        selectInteraction.getFeatures().push(param.feature);
        type = param.feature.getGeometry().getType()
        if (type === "Circle") {
          attribute.olMap.on('pointermove', rmovecallback)
        }
      }
      selectInteraction.on("select", function (e) {
        var features = e.selected;
        if (features.length > 0) {
          var feature = features[0];
          type = feature.getGeometry().getType()
          if (type === "Circle") {
            attribute.olMap.on('pointermove', rmovecallback)
          }
        } else {
          attribute.olMap.un('pointermove', rmovecallback)
          param.callback && param.callback(editparam)
          setTimeout(() => {
            if (selectInteraction) {
              attribute.olMap.removeInteraction(selectInteraction);
              selectInteraction = null;
            }
            if (modifyInteraction) {
              attribute.olMap.removeInteraction(modifyInteraction);
              modifyInteraction = null;
            }
          }, 100)
          type = undefined
          editparam = null
        }
      });

      //创建一个交互修改对象
      modifyInteraction = new ol.interaction.Modify({
        features: selectInteraction.getFeatures(),
        // snapTolerance:20
      });
      modifyInteraction.on("modifyend", function (e) {
        var features = e.features.array_;
        var feature = features[0];
        var wkt, radius

        switch (type) {
          case "Circle":
            var circle = feature.getGeometry();
            var centerLogLat = circle.getCenter()
            var geom2 = new ol.geom.Point(centerLogLat)
            wkt = new ol.format.WKT().writeGeometry(geom2)
            let sketchCoords_1 = Method.TransferTwentySix(centerLogLat);
            let sketchCoords_2 = Method.TransferTwentySix(coordinate);
            radius = ol.sphere.getDistance(sketchCoords_1, sketchCoords_2);
            break;

          default:
            wkt = new ol.format.WKT().writeFeature(feature);
            break;
        }
        var Geome = new ol.format.WKT().readGeometry(wkt, {
          dataProjection: "EPSG:3857",
          featureProjection: "EPSG:4326",
        });
        var dinates = Geome.getCoordinates()
        let data = feature.values_.data;
        editparam = {
          coordinate: dinates,
          radius: radius,
          data: data,
          feature: feature,
          event: e
        }
      });

      attribute.olMap.addInteraction(selectInteraction);
      attribute.olMap.addInteraction(modifyInteraction);
    },
  }

  // 函数集合
  var Method = {
    /**
     * 4326转3857
     * @param list {Array<Number>} 必填 坐标数组
     * @return Array 转换完的数组
     */
    TransferFiftySeven(list) {
      return ol.proj.transform(list, "EPSG:4326", "EPSG:3857");
    },

    /**
     * 3857转4326
     * @param list {Array<Number>} 必填 坐标数组
     * @return Array 转换完的数组
     */
    TransferTwentySix(list) {
      return ol.proj.transform(list, "EPSG:3857", "EPSG:4326");
    },

    /**
    * 计算长度、面积
    * @param params {Object} 必填 集合
    * @return Object output计算的值 unit单位
    */
    /**
     * params 
     * @param type {String} 必填 类型LineString线、Polygon面
     * @param coordinate {Array} 必填 坐标集合线二维面三维数组
     */
    GeometryUtil(params) {
      var output, sphere, geom, unit
      switch (params.type) {
        case "LineString":
          geom = new ol.geom.LineString(params.coordinate).transform("EPSG:4326", "EPSG:3857")
          sphere = ol.sphere.getLength(geom, {
            projection: "EPSG:3857"
          });
          output = (Math.round(sphere * 100) / 100)
          unit = "米"
          break;
        case "Polygon":
          geom = new ol.geom.Polygon(params.coordinate).transform("EPSG:4326", "EPSG:3857")
          sphere = ol.sphere.getArea(geom, {
            projection: "EPSG:3857"
          });
          output = (Math.round(sphere * 100) / 100)
          unit = "平方米"
          break;
      }
      return { output, unit }
    },
  }



  window.MayMap = {
    setKey,
    MapInit,
    TileLayer,
    Method,
    Measure,
    Track,
    ol
  };
  callback(window.MayMap);
}

export default AmapUtil;