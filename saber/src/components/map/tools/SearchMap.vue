<template>
  <!-- <a class="planel_button" @click="clickSearchBox">搜索</a> -->
  <div class="searchResultsBox">
    <el-input
      id="keyword"
      v-model="keyword"
      placeholder="在地图上搜索"
      size="mini"
      style="width: 250px"
      clearable
      @blur="blur"
    />
    <slot />
    <!-- <el-button slot="append" icon="el-icon-search" size="mini" class="spanWidth"></el-button> -->
  </div>
</template>
<script>
export default {
  name: 'SearchMap',
  props: {
  },
  data () {
    return {
      map: null,
      AMap: null,
      loaded: false,
      keyword: '',
      mapPlaceSearch: null
    };
  },
  watch: {
    keyword: function (val) {
      if (val === '' || val === undefined) {
        this.mapPlaceSearch?.clear();
      }
    }
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} _mapObj 地图组件
     * @param {Object} _mapObj.AMap 高德地图
     * @param {Object} _mapObj.map 地图实例
     * @param {Object} _mapObj.mousetool 地图工具
     * @param {Boolean} _mapObj.loaded 是否加载完毕
     */
    init (_mapObj) {
      this.map = _mapObj.map;
      this.AMap = _mapObj.AMap;
      this.loaded = _mapObj.loaded;
      this.searchLocation();
    },
    /**
     * 地图检索
     */
    searchLocation () {
      // 输入提示
      var autoOptions = {
        input: 'keyword'
      };
      var auto = new this.AMap.AutoComplete(autoOptions);
      // 构造地点查询类
      this.mapPlaceSearch = new this.AMap.PlaceSearch({
        map: this.map
      });
      auto.on('select', e => {
        this.mapPlaceSearch.setCity(e.poi.adcode);
        this.mapPlaceSearch.search(e.poi.name);
      });
    },
    /**
     * 清空搜索结果
     */
    clearSearchResult () {
      this.keyword = '';
      this.mapPlaceSearch?.clear();
    },
    /**
     * 失焦事件
     **/
    blur (e) {
      this.$emit('blur', e);
    }
  }
};
</script>
<style lang="less">
.spanWidth{
  margin-left: 5px;
}
.planelSpan{
  margin-right: 10px;
}
.searchResultsBox{
  display: flex;
  flex-direction: row;
  // position: absolute;
  // right: 40px;
  // top: 0px;
  align-items: center;
  z-index: 1000;
  background: #fff;
  border-radius: 3px;
  padding: 4px;
  box-shadow: 0px 0px 5px #888888;
}
</style>

<style lang="less" scoped>
.searchResultsBox {
  /deep/ .el-input__inner {
    border-right: none !important;
  }
}
</style>