<template>
  <!-- <a class="planel_button" @click="clickSearchBox">搜索</a> -->
  <div class="searchResultsBox">
    <el-input
      id="keyword"
      v-model="keyword"
      placeholder="在地图上搜索"
      size="mini"
      style="width: 250px"
      clearable
      @blur="blur"
      @focus="isShowResultList = true"
    />
    <slot/>
    <div class="result-list">
      <div
        v-for="(item, index) in resultList"
        v-show="isShowResultList"
        :key="index"
        class="result-item"
        @click="selectItem(item)"
      >
        {{ item.name }}
      </div>
      <div
        v-if="isShowNoResult"
        class="no-result"
      >暂无数据</div>
    </div>
    <!-- <el-button slot="append" icon="el-icon-search" size="mini" class="spanWidth"></el-button> -->
  </div>
</template>
<script>
import { debounce } from 'lodash';
import { gnPlaceSearch } from '@/api/base/regionManage';

export default {
  name: 'SearchMapCE',
  props: {},
  data() {
    return {
      map: null,
      AMap: null,
      loaded: false,
      keyword: '',
      resultList: [],
      isShowNoResult: false,
      isShowResultList: false
    };
  },
  watch: {
    keyword(val) {
      if (val === '' || val === undefined) {
        this.isShowNoResult = false;
        this.resultList = [];
      }
      else if (val.length >= 2){
        this.searchResult(val);
      }
    }
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} _mapObj 地图组件
     * @param {Object} _mapObj.AMap 高德地图
     * @param {Object} _mapObj.map 地图实例
     * @param {Object} _mapObj.mousetool 地图工具
     * @param {Boolean} _mapObj.loaded 是否加载完毕
     */
    init(_mapObj) {
      this.map = _mapObj.map;
      this.AMap = _mapObj.AMap;
      this.loaded = _mapObj.loaded;
    },
    /**
     * 清空搜索结果
     */
    clearSearchResult() {
      this.keyword = '';
    },
    /**
     * 失焦事件
     **/
    blur(e) {
      this.$emit('blur', e);
    },
    searchResult: debounce(function (val) {
      gnPlaceSearch({
        key: 'b4f0d3a1-2c5e-4a8f-9b3d-5e7f3c2b1a4d',
        keyWord: val
      }).then((res) => {
        this.resultList = res.data.data || [];
        if (this.resultList.length === 0) {
          this.isShowNoResult = true;
        }
        this.isShowResultList = true;
      });
    }, 500),
    selectItem(item) {
      const locations = item.lonlat.split(',').map(item => Number(item));
      this.map.setZoomAndCenter(locations, 16);
      this.isShowResultList = false;
    }
  }
};
</script>
<style lang="less">
.spanWidth {
  margin-left: 5px;
}

.planelSpan {
  margin-right: 10px;
}

.searchResultsBox {
  display: flex;
  flex-direction: row;
  // position: absolute;
  // right: 40px;
  // top: 0px;
  align-items: center;
  z-index: 1000;
  background: none;
  border-radius: 3px;
  padding: 4px;
  box-shadow: 0px 0px 5px #888888;
}
</style>

<style lang="less" scoped>
.searchResultsBox {
  position: relative;

  ::v-deep .el-input__inner {
    border: none !important;
  }
}

.result-list {
  position: absolute;
  top: 46px;
  width: 294px;
  max-height: 340px;
  overflow-y: auto;
}

.result-item {
  padding: 0 8px;
  height: 34px;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #333333;
  cursor: pointer;
  background-color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    background-color: #ececec;
  }
}
.no-result {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #333333;
  background-color: #fff;
}
</style>
