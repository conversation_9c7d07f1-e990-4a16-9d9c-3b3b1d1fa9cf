<template>
  <div>
    <a class="planel_button planelSpan" @click="showSelect">卫星</a>
    <!-- 设置地图中心 -->
    <!-- <div class="layerSelect" v-show="showLayerSelect">
      <el-radio-group v-model="layers">
        <el-radio :label="1" @click.native.prevent="selectRoadNet(1)">路网</el-radio>
      </el-radio-group>
    </div> -->
  </div>
</template>
<script>
export default {
  name: 'LayerSelect',
  props: {},
  data() {
    return {
      map: null,
      AMap: null,
      loaded: false,
      vectorBaseMap: null,
      vectorLayer: null,
      satelliteLayer: null,
      satelliteAnnotationLayer: null,
      showLayerSelect: false,
      layers: 1
    };
  },
  methods: {
    init(_mapObj) {
      this.map = _mapObj.map;
      this.AMap = _mapObj.AMap;
      this.loaded = _mapObj.loaded;
      this.vectorBaseMap = _mapObj.vectorBaseMap;
      this.vectorLayer = _mapObj.vectorLayer;
      this.satelliteLayer = _mapObj.satelliteLayer;
      this.satelliteAnnotationLayer = _mapObj.satelliteAnnotationLayer;
      this.addCELayer = _mapObj.addCELayer;
    },
    showSelect() {
      this.showLayerSelect = !this.showLayerSelect;
      if (this.showLayerSelect) {
        this.showSatelliteLayer();
      }
      else {
        this.hideSatelliteLayer();
      }
      this.$emit('changeLayerSelect', this.showLayerSelect);
    },
    /**
     * 显示卫星图图层
     */
    showSatelliteLayer() {
      if (!this.loaded) {
        setTimeout(() => {
          this.showSatelliteLayer();
        }, 200);
      }
      else {
        if (this.satelliteLayer) {
          this.map.removeLayer(this.vectorBaseMap);
          this.map.removeLayer(this.vectorLayer);
          this.map.addLayer(this.satelliteLayer);
          this.map.addLayer(this.satelliteAnnotationLayer);
          this.addCELayer();
        }
      }
    },
    /**
     * 隐藏卫星图图层
     */
    hideSatelliteLayer() {
      if (!this.loaded) {
        setTimeout(() => {
          this.hideSatelliteLayer();
        }, 200);
      }
      else {
        if (this.vectorLayer) {
          this.map.removeLayer(this.satelliteLayer);
          this.map.removeLayer(this.satelliteAnnotationLayer);
          this.map.addLayer(this.vectorBaseMap);
          this.map.addLayer(this.vectorLayer);
          this.addCELayer();
        }
      }
    },
    /**
     * 路网图层交互逻辑还不完善
     * 如果有需求后期待完善
     */
    selectRoadNet(e) {
      e === this.layers ? this.layers = '' : this.layers = e;
      // this.$emit('selectRoadLayer', this.layers)
      if (showLayer === 1) {
        this.hideRoadNet();
      }
      else {
        this.showRoadNet();
      }
    },
    /**
     * 显示路网
     */
    showRoadNet() {
      if (!this.loaded) {
        setTimeout(() => {
          this.showRoadNet();
        }, 200);
      }
      else {
        // if(this.roadNet){
        //   this.roadNet.show();
        // }else{
        //   this.roadNet = new this.AMap.TileLayer.RoadNet();
        //   this.roadNet.setMap(this.map);
        // }
      }
    },
    /**
     * 隐藏路网
     */
    hideRoadNet() {
      if (!this.loaded) {
        setTimeout(() => {
          this.hideRoadNet();
        }, 200);
      }
      else {
        // if(this.roadNet){
        //   this.roadNet.hide();
        // }
      }
    }
  }
};
</script>
<style lang="less" scoped>
.planelSpan {
  margin-right: 10px;
}
</style>
