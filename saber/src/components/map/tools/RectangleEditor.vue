<template>
  <div>
    <el-popover placement="bottom" trigger="click" @show="showRectangle" @hide="hideRectangle">
      <el-button @click="startEditRectangle" title="鼠标点击绘制矩形，双击结束" size="small">开始绘制</el-button>
      <el-button @click="clearRectangle" size="small">清除</el-button>
      <el-button @click="editRectangle" size="small">编辑</el-button>
      <el-button @click="endEditRectangle" size="small">结束编辑</el-button>
      <a class="planel_button planelSpan" slot="reference" ref="toggleButton">矩形</a>
    </el-popover>
  </div>
</template>

<script>
import defaultValue from '@/utils/core/defaultValue';
export default {
  name: 'RectangleEditor',
  components: {
  },
  data() {
    return {
      /** 地图对象 */
      map: null,
      /** 高德地图 : 方便调用 API */
      AMap: null,
      /** 高德地图鼠标工具 */
      mousetool: null,
      loaded: false,
      toolParam: {
        isEditingRectangle: true
      },
      drawRectangle: null
    };
  },
  props: {
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} mapWidget 地图组件
     * @param {Object} mapWidget.AMap 高德地图
     * @param {Object} mapWidget.map 地图实例
     * @param {Object} mapWidget.mousetool 地图工具
     * @param {Boolean} mapWidget.loaded 是否加载完毕
     */
    init(mapWidget){
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.mousetool = this._mapWidget.mousetool;
      this.loaded = this._mapWidget.loaded;
    },
    /**
     * 开始绘制矩形
     */
    startEditRectangle(){
      if (!this.loaded) {
        setTimeout(()=>{
          this.startEditRectangle();
        }, 200)
      } else {
        this.toolParam.isEditingRectangle = true;
        this.clearRectangle();
        // 用鼠标工具画矩形
        this.mousetool.rectangle();
        //添加事件
        this.mousetool.on('draw' ,(e)=>{
          this.drawRectangle = e.obj;
          this.mousetool.close();
          console.log('坐标点路径为', e.obj.getPath());//获取路径范围
        });
      }
    },
    /**
     * 清除矩形
     */
    clearRectangle(){
      if(this.rectangleEditor){
        this.rectangleEditor.close();
        this.rectangleEditor = undefined;
      }
      if(this.drawRectangle){
        this.map.remove(this.drawRectangle);
        this.drawRectangle = undefined;
      }
    },
    /**
     * 编辑矩形
     */
    editRectangle(){
      if(!this.rectangleEditor){
        console.log(this.drawRectangle)
        // 实例化矩形编辑器，传入地图实例和要进行编辑的矩形实例
        this.rectangleEditor = new this.AMap.RectangleEditor(this.map, this.drawRectangle);
        // 开启编辑模式
        this.rectangleEditor.open();
      }
      this.toolParam.isEditingRectangle = true;
    },
    /**
     * 结束编辑矩形
     */
    endEditRectangle(){
      if(this.rectangleEditor){
        this.rectangleEditor.close();
        this.rectangleEditor = undefined;
      }
    },
    /**
     * 获取矩形的所有点的数据
     * @return {String}
     */
    getPolygonPath(){
      if(this.drawRectangle){
        return this.drawRectangle.getPath();
      }else{
        return '';
      }
    },
    /**
     * 获取边界点
     * @return {Array.<{longitude, latitude: Number}>}
     */
    getRectangleBounds(){
      if(this.drawRectangle){
        let bounds = this.drawRectangle.getBounds();
        return [
          {
            longitude: bounds.southWest.lng,
            latitude: bounds.southWest.lat
          },
          {
            longitude: bounds.northEast.lng,
            latitude: bounds.northEast.lat
          }
        ];
      }else{
        return '';
      }
    },
    /**
     * 初始化编辑的矩形
     * @param {Array} param.lngLatArray
     * @param {Number} param.lngLatArray[].longitude 经度
     * @param {Number} param.lngLatArray[].altitude 纬度
     * @param {String} [param.fillColor='#00b0ff'] 填充颜色
     * @param {String} [param.strokeColor='#80d8ff'] 线条颜色
     * @param {String} [param.borderWeight=1] 线条宽度
     */
    setEditRectangle(param){
      let lngLatArray = param.lngLatArray;
      if(!this.loaded){
        setTimeout(()=>{
          this.setEditRectangle(param);
        }, 200)
      }else{
        this.clearRectangle();
        this.drawRectangle = new this.AMap.Rectangle({
          bounds: new this.AMap.Bounds(
            new this.AMap.LngLat(lngLatArray[0].longitude, lngLatArray[0].latitude),// southWest
            new this.AMap.LngLat(lngLatArray[1].longitude, lngLatArray[1].latitude) // northEast
          ),
          fillColor: defaultValue(param.fillColor, '#00b0ff'),
          strokeColor: defaultValue(param.strokeColor, '#80d8ff'),
          borderWeight: defaultValue(param.borderWeight, 2),
        });
        this.map.add(this.drawRectangle);
        this.editRectangle();
      }
    },
    /**
     * 显示矩形
     */
    showRectangle(){
      this.$emit('changeRectangle', true)
    },
    /**
     * 隐藏矩形
     */
    hideRectangle(){
      this.$emit('changeRectangle', false)
    },
    /**
     * 开启el-popover的编辑弹窗
     */
    toggleShow(){
      this.$refs.toggleButton.click();
    }
  }
}
</script>

<style scoped>
  .planelSpan{
    margin-right: 10px;
  }
</style>
