<template>
  <div>
    <!--手动打开关闭-->
    <!-- <el-popover placement="bottom" trigger="manual" v-model="showPolylineEditor">
      <el-button @click="startEditPolyline" title="鼠标点击绘制折线，双击结束" size="small">开始绘制</el-button>
      <el-button @click="clearPolyline" size="small">清除</el-button>
      <el-button @click="editPolyline" size="small">编辑</el-button>
      <el-button @click="endEditPolyline" size="small">结束编辑</el-button>
      <a class="planel_button planelSpan" slot="reference" @click="openEditPolyline">直线</a>
    </el-popover> -->
    <!--点击打开关闭-->
    <el-popover
      placement="bottom"
      trigger="click"
      @show="showPolyline"
      @hide="hidePolyline"
    >
      <el-button
        title="鼠标点击绘制折线，双击结束"
        size="small"
        @click="startEditPolyline"
      >
        开始绘制
      </el-button>
      <el-button
        size="small"
        @click="clearPolyline"
      >
        清除
      </el-button>
      <el-button
        size="small"
        @click="editPolyline"
      >
        编辑
      </el-button>
      <el-button
        size="small"
        @click="endEditPolyline"
      >
        结束编辑
      </el-button>
      <a
        slot="reference"
        ref="toggleButton"
        class="planel_button planelSpan"
      >直线</a>
    </el-popover>
  </div>
</template>

<script>
import defaultValue from '@/utils/core/defaultValue';
/**
 * 折线编辑组件
 */
export default {
  name: 'PolylineEditor',
  components: {
  },
  props: {
  },
  data () {
    return {
      /** 地图对象 */
      map: null,
      /** 高德地图 : 方便调用 API */
      AMap: null,
      /** 高德地图鼠标工具 */
      mousetool: null,
      loaded: false,
      toolParam: {
        isEditingPolyline: true
      },
      showPolylineEditor: false
    };
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} mapWidget 地图组件
     * @param {Object} mapWidget.AMap 高德地图
     * @param {Object} mapWidget.map 地图实例
     * @param {Object} mapWidget.mousetool 地图工具
     * @param {Boolean} mapWidget.loaded 是否加载完毕
     */
    init (mapWidget) {
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.mousetool = this._mapWidget.mousetool;
      this.loaded = this._mapWidget.loaded;
    },
    /**
     * 设置折线图层是否可见
     */
    setPolylinesVisible (visible) {
      if (this.polylineOverlayGroup) {
        if (visible) {
          this.polylineOverlayGroup.show();
        } else {
          this.polylineOverlayGroup.hide();
        }
      }
    },
    /**
     * 删除所有折线
     */
    clearPolylines () {
      if (this.polylineOverlayGroup) {
        this.polylineOverlayGroup.clearOverlays();
        this.map.remove(this.polylineOverlayGroup);
        this.polylineOverlayGroup = undefined;
      }
    },

    /**
     * 开始绘制折线
     */
    startEditPolyline () {
      if (!this.loaded) {
        setTimeout(() => {
          this.startEditPolyline();
        }, 200);
      } else {
        this.toolParam.isEditingPolyline = true;
        this.clearPolyline();
        // 用鼠标工具画折线
        this.mousetool.polyline();
        // 添加事件
        this.mousetool.on('draw', (e) => {
          this.drawPolyline = e.obj;
          this.mousetool.close();
          console.log('坐标点路径为', e.obj.getPath());// 获取路径范围
        });
      }
    },
    /**
     * 清除折线
     */
    clearPolyline () {
      if (this.polylineEditor) {
        this.polylineEditor.close();
        this.polylineEditor = undefined;
      }
      if (this.drawPolyline) {
        this.map.remove(this.drawPolyline);
        this.drawPolyline = undefined;
      }
    },
    /**
     * 编辑折线
     */
    editPolyline () {
      if (!this.polylineEditor) {
        // 实例化折线编辑器，传入地图实例和要进行编辑的折线实例
        this.polylineEditor = new this.AMap.PolylineEditor(this.map, this.drawPolyline);
        // 开启编辑模式
        this.polylineEditor.open();
      }
      this.toolParam.isEditingPolyline = true;
    },
    /**
     * 结束编辑折线
     */
    endEditPolyline () {
      if (this.polylineEditor) {
        this.polylineEditor.close();
        this.polylineEditor = undefined;
      }
    },
    /**
     * 获取折线的数据
     * @return {Array.<{longitude, latitude: Number}>}
     */
    getPolylinePath () {
      if (this.drawPolyline) {
        let paths = this.drawPolyline.getPath();
        let points = [];
        for (let i = 0; i < paths.length; i++) {
          points.push({
            longitude: paths[i].lng,
            latitude: paths[i].lat
          });
        }
        return points;
      } else {
        return '';
      }
    },
    /**
     * 打开折线绘制面板
     */
    openEditPolyline () {
      this.showPolylineEditor = !this.showPolylineEditor;
      this.clearPolyline();
    },
    /**
     * 初始化编辑的折线
     * @param {Array} param.lngLatArray
     * @param {Number} param.lngLatArray[].longitude 经度
     * @param {Number} param.lngLatArray[].altitude 纬度
     * @param {String} [param.fillColor='#00b0ff'] 填充颜色
     * @param {String} [param.strokeColor='#80d8ff'] 线条颜色
     * @param {String} [param.borderWeight=1] 线条宽度
     */
    setEditPolyline (param) {
      if (!param || !param.lngLatArray || param.lngLatArray.length === 0) {
        this.clearPolyline();
        return;
      }
      let lngLatArray = param.lngLatArray;
      if (!this.loaded) {
        setTimeout(() => {
          this.setEditPolyline(param);
        }, 200);
      } else {
        this.clearPolyline();
        let path = [];
        for (let j = 0; j < lngLatArray.length; j++) {
          path.push(new this.AMap.LngLat(lngLatArray[j].longitude, lngLatArray[j].latitude));
        }
        this.drawPolyline = new this.AMap.Polyline({
          path: path,
          fillColor: defaultValue(param.fillColor, '#2398ff'),
          strokeColor: defaultValue(param.strokeColor, '#006bb1'),
          borderWeight: defaultValue(param.borderWeight, 2)
        });
        this.map.add(this.drawPolyline);
        this.editPolyline();
      }
    },
    /**
     * 显示折线
     */
    showPolyline () {
      this.$emit('changePolyline', true);
    },
    /**
     * 隐藏折线
     */
    hidePolyline () {
      this.$emit('changePolyline', false);
    },
    /**
     * 开启el-popover的编辑弹窗
     */
    toggleShow () {
      this.$refs.toggleButton.click();
    }
  }
};
</script>

<style scoped>
  .planelSpan{
    margin-right: 10px;
  }
</style>
