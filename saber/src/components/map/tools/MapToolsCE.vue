<template>
  <div>
    <el-popover
      placement="bottom"
      trigger="click"
      @show="showMapTools"
      @hide="hideMapTools"
    >
      <el-button
        size="small"
        @click="onRuleClick"
      >测距</el-button>
      <el-button
        size="small"
        @click="onMeasureAreaClick"
      >测面</el-button>
      <el-button
        size="small"
        @click="onCloseClick"
      >清除</el-button>
      <a
        slot="reference"
        class="planel_button planelSpan"
      >工具</a>
    </el-popover>
  </div>
</template>

<script>
/**
 * 折线编辑组件
 */
export default {
  name: 'MapTools',
  components: {},
  data() {
    return {
      /** 地图对象 */
      map: null,
      /** 国能地图 : 方便调用 API */
      AMap: null,
      /** 国能地图测距测面工具 */
      Measure: null,
      loaded: false
    };
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} mapWidget 地图组件
     * @param {Object} mapWidget.AMap 高德地图
     * @param {Object} mapWidget.map 地图实例
     * @param {Object} mapWidget.Measure 地图工具
     * @param {Boolean} mapWidget.loaded 是否加载完毕
     */
    init(mapWidget) {
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.Measure = this._mapWidget.Measure;
      this.loaded = this._mapWidget.loaded;
    },
    /**
     * 测距
     */
    onRuleClick() {
      this.Measure.RangingTool(e => {
        console.log(e);
      });
    },
    /**
     * 测面
     */
    onMeasureAreaClick() {
      this.Measure.measureArea(e => {
        console.log(e);
      });
    },
    /**
     * 清除
     */
    onCloseClick() {
      this.Measure.clearMeasure();
    },
    onCancelClick(){
      this.Measure.cancelMeasure();
    },
    showMapTools() {
      this.$emit('changeMapTools', true);
    },
    hideMapTools() {
      this.$emit('changeMapTools', false);
    }
  }
};
</script>

<style scoped>
.planelSpan {
  margin-right: 10px;
}
</style>
