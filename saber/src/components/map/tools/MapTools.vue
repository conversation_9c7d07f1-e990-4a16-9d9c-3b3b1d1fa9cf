<template>
  <div>
    <el-popover placement="bottom" trigger="click" @show="showMapTools" @hide="hideMapTools">
      <el-button @click="onRuleClick" size="small">测距</el-button>
      <el-button @click="onMeasureAreaClick" size="small">测面</el-button>
      <el-button @click="onCloseClick" size="small">清除</el-button>
      <a class="planel_button planelSpan" slot="reference">工具</a>
    </el-popover>
  </div>
</template>

<script>
/**
 * 折线编辑组件
 */
export default {
  name: 'MapTools',
  components: {
  },
  data() {
    return {
      /** 地图对象 */
      map: null,
      /** 高德地图 : 方便调用 API */
      AMap: null,
      /** 高德地图鼠标工具 */
      mousetool: null,
      loaded: false
    };
  },
  props: {
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} mapWidget 地图组件
     * @param {Object} mapWidget.AMap 高德地图
     * @param {Object} mapWidget.map 地图实例
     * @param {Object} mapWidget.mousetool 地图工具
     * @param {Boolean} mapWidget.loaded 是否加载完毕
     */
    init(mapWidget){
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.mousetool = this._mapWidget.mousetool;
      this.loaded = this._mapWidget.loaded;
    },
    /**
     * 测距
     */
    onRuleClick() {
      this.mousetool.rule({});
    },
    /**
     * 测面
     */
    onMeasureAreaClick() {
      this.mousetool.measureArea({});
    },
    /**
     * 清除
     */
    onCloseClick() {
      this.mousetool.close(true)
    },
    showMapTools(){
      this.$emit('changeMapTools', true)
    },
    hideMapTools(){
      this.$emit('changeMapTools', false)
    },
  }
}
</script>

<style scoped>
  .planelSpan{
    margin-right: 10px;
  }
</style>
