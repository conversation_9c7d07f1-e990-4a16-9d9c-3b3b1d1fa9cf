<template>
  <div>
    <el-popover
      placement="bottom"
      trigger="click"
      @show="showPolygon"
      @hide="hidePolygon"
    >
      <el-button
        title="鼠标点击绘制多边形，双击结束"
        size="small"
        @click="startEditPolygon"
      >
        开始绘制
      </el-button>
      <el-button
        size="small"
        @click="clearPolygon"
      >
        清除
      </el-button>
      <el-button
        size="small"
        @click="editPolygon"
      >
        编辑
      </el-button>
      <el-button
        size="small"
        @click="endEditPolygon"
      >
        结束编辑
      </el-button>
      <a
        slot="reference"
        ref="toggleButton"
        class="planel_button planelSpan"
      >多边形</a>
    </el-popover>
  </div>
</template>

<script>
/**
 * 多边形编辑组件
 */
import defaultValue from '@/utils/core/defaultValue';
export default {
  name: 'PolygonEditor',
  components: {
  },
  props: {
  },
  data () {
    return {
      /** 地图对象 */
      map: null,
      /** 高德地图 : 方便调用 API */
      AMap: null,
      /** 高德地图鼠标工具 */
      mousetool: null,
      loaded: false,
      toolParam: {
        isEditingPolygon: true
      },
      showPolygonEditor: false
    };
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} mapWidget 地图组件
     * @param {Object} mapWidget.AMap 高德地图
     * @param {Object} mapWidget.map 地图实例
     * @param {Object} mapWidget.mousetool 地图工具
     * @param {Boolean} mapWidget.loaded 是否加载完毕
     */
    init (mapWidget) {
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.mousetool = this._mapWidget.mousetool;
      this.loaded = this._mapWidget.loaded;
    },
    /**
     * 绘制多边形
     * @param {Array} polygonParamArray
     * @param {Array} polygonParamArray[].lngLatArray 经纬度数组
     * @param {Number} polygonParamArray[].lngLatArray[].longitude 经度
     * @param {Number} polygonParamArray[].lngLatArray[].altitude 纬度
     * @param {Number} [polygonParamArray[].fillColor=#ffffff] 多边形填充颜色
     * @param {Number} [polygonParamArray[].strokeColor=red] 线条颜色
     * @param {Number} [polygonParamArray[].borderWeight=1] 线条宽度，默认为 1
     * @param {Element} [polygonParamArray[].detailDomElement]
     * @param {Boolean} [initVisible=true] 初始化时是否可见
     */
    drawPolygons (polygonParamArray, initVisible) {
      if (!this.loaded) {
        setTimeout(() => {
          this.drawPolygons(polygonParamArray, initVisible);
        }, 200);
      } else {
        let polygonCollection = [];
        for (let i = 0; i < polygonParamArray.length; i++) {
          let polygonParam = polygonParamArray[i];
          let lngLatArray = polygonParam.lngLatArray;
          let path = [];
          // eslint-disable-next-line space-before-blocks
          for (let j = 0; j < lngLatArray.length; j++){
            path.push(new this.AMap.LngLat(lngLatArray[j].longitude, lngLatArray[j].latitude));
          }
          let polygon = new this.AMap.Polygon({
            path: path,
            fillColor: defaultValue(polygonParam.fillColor, '#ffffff'), // 多边形填充颜色
            fillOpacity: 0.4, // 多边形填充透明度
            borderWeight: defaultValue(polygonParam.borderWeight, 1), // 线条宽度，默认为 1
            strokeColor: defaultValue(polygonParam.strokeColor, 'red') // 线条颜色
          });
          polygonCollection.push(polygon);

          // 单击事件
          let clickHandle = polygon.on('click', (e) => {
            // console.log(JSON.parse(JSON.stringify(e.target.getExtData())));
            // let extData = e.target.getExtData();
            console.log('click-->', e);

            let closeButton = document.createElement('div');
            closeButton.innerHTML = `
                  <div style="position: absolute; right: 10px; top: 16px; z-index: 100; cursor: pointer;
                      border: solid 2px #ffbe00; border-radius: 50%; color: #ffbe00;
                      padding: 2px; width: 21px; height: 21px; text-align: center;">
                      ×
                  </div>`;
            polygonParam.detailDomElement.appendChild(closeButton);
            closeButton.addEventListener('click', () => {
              this.closeInfoWindow();
            });

            // 得到的数据
            this._infoWindow = new this.AMap.InfoWindow({
              isCustom: true, // 使用自定义窗体
              content: polygonParam.detailDomElement,
              // offset: new this.AMap.Pixel(0, - iconHeight - triangleHeight - padding * 2),
              closeWhenClickMap: true
            });
            let longitude = e.lnglat.lng;
            let latitude = e.lnglat.lat;
            this._infoWindow.open(this.map, [longitude, latitude]);
          });
        }

        let polygonOverlayGroup = new this.AMap.OverlayGroup(polygonCollection);
        this.map.add(polygonOverlayGroup);
        this.polygonOverlayGroup = polygonOverlayGroup;

        initVisible = defaultValue(initVisible, true);
        this.setPolygonsVisible(initVisible);
      }
    },
    /**
     * 设置多边形图层是否可见
     */
    setPolygonsVisible (visible) {
      if (this.polygonOverlayGroup) {
        if (visible) {
          this.polygonOverlayGroup.show();
        } else {
          this.polygonOverlayGroup.hide();
        }
      }
    },
    /**
     * 删除所有多边形
     */
    clearPolygons () {
      if (this.polygonOverlayGroup) {
        this.polygonOverlayGroup.clearOverlays();
        this.map.remove(this.polygonOverlayGroup);
        this.polygonOverlayGroup = undefined;
      }
    },

    /**
     * 开始绘制多边形
     */
    startEditPolygon () {
      if (!this.loaded) {
        setTimeout(() => {
          this.startEditPolygon();
        }, 200);
      } else {
        this.toolParam.isEditingPolygon = true;
        this.clearPolygon();
        // 用鼠标工具画多边形
        this.mousetool.polygon();
        // 添加事件
        this.mousetool.on('draw', (e) => {
          this.drawPolygon = e.obj;
          this.mousetool.close();
          console.log('坐标点路径为', e.obj);// 获取路径范围
        });
      }
    },
    /**
     * 清除多边形
     */
    clearPolygon () {
      if (this.polygonEditor) {
        this.polygonEditor.close();
        this.polygonEditor = undefined;
      }
      if (this.drawPolygon) {
        this.map.remove(this.drawPolygon);
        this.drawPolygon = undefined;
      }
    },
    /**
     * 编辑多边形
     */
    editPolygon () {
      if (!this.polygonEditor) {
        // 实例化多边形编辑器，传入地图实例和要进行编辑的多边形实例
        this.polygonEditor = new this.AMap.PolygonEditor(this.map, this.drawPolygon);
        // 开启编辑模式
        this.polygonEditor.open();
      }
      this.toolParam.isEditingPolygon = true;
    },
    /**
     * 结束编辑多边形
     */
    endEditPolygon () {
      if (this.polygonEditor) {
        this.polygonEditor.close();
        this.polygonEditor = undefined;
      }
    },
    /**
     * 初始化编辑的多边形
     * @param {Array} param.lngLatArray
     * @param {Number} param.lngLatArray[].longitude 经度
     * @param {Number} param.lngLatArray[].altitude 纬度
     * @param {String} [param.fillColor='#00b0ff'] 填充颜色
     * @param {String} [param.strokeColor='#80d8ff'] 线条颜色
     * @param {String} [param.borderWeight=1] 线条宽度
     */
    setEditPolygon (param) {
      let lngLatArray = param.lngLatArray;
      if (!this.loaded) {
        setTimeout(() => {
          this.setEditPolygon(param);
        }, 200);
      } else {
        this.clearPolygon();
        let path = [];
        for (let j = 0; j < lngLatArray.length; j++) {
          path.push(new this.AMap.LngLat(lngLatArray[j].longitude, lngLatArray[j].latitude));
        }
        this.drawPolygon = new this.AMap.Polygon({
          path: path,
          fillColor: defaultValue(param.fillColor, '#00b0ff'),
          strokeColor: defaultValue(param.strokeColor, '#80d8ff'),
          borderWeight: defaultValue(param.borderWeight, 2)
        });
        this.map.add(this.drawPolygon);
        this.editPolygon();
      }
    },
    /**
     * 获取多边形的数据
     * @return {String}
     */
    getPolygonPath () {
      if (this.drawPolygon) {
        return this.drawPolygon.getPath();
      } else {
        return '';
      }
    },
    /**
     * 打开多边形绘制面板
     */
    openEditPolygon () {
      this.showPolygonEditor = !this.showPolygonEditor;
      this.clearPolygon();
    },
    /**
     * 显示多边形
     */
    showPolygon () {
      this.$emit('changePolygon', true);
    },
    /**
     * 隐藏多边形
     */
    hidePolygon () {
      this.$emit('changePolygon', false);
    },
    /**
     * 开启el-popover的编辑弹窗
     */
    toggleShow () {
      this.$refs.toggleButton.click();
    }
  }
};
</script>

<style scoped>
  .planelSpan{
    margin-right: 10px;
  }
</style>
