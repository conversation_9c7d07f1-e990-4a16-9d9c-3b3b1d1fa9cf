<template>
  <input 
    ref="playerRange"  
    class="player_range" 
    type="range" 
    v-model="HistoryRange" 
    @mousedown="onStartMoving"
    @click="handleClick">
</template>

<script>

export default {
  name: 'Range',
  props: {
    value: {
      type: Number,
      default: 0.3
    }
  },
  data() {
    return {
      HistoryRange : 3
    }
  },
  watch:{
    value (_val) {
      this.HistoryRange = _val * 100;
    }
  },
  mounted() {
    this.setRange(this.value)
  },
  methods: {
    getRange(){
      return this.HistoryRange * 0.01;
    },
    setRange(_val){
      this.HistoryRange = _val * 100;
    },
    onRangeMove(type){
      let rangeValue = this.HistoryRange * 0.01;
      // console.info('--> HistoryRange',rangeValue);
      this.$emit('update:value', rangeValue, type);
    },
    handleClick() {
      this.$emit('closeAnimation');
      this.onRangeMove();
    },
    // 鼠标按下进度条
    onStartMoving(){
      this.$emit('closeAnimation');
      let document = this.$refs.playerRange;
      // 注册 mousemove 事件
      document.addEventListener("mousemove", this.onMoving);
      // 注册 mouseup 事件
      document.addEventListener("mouseup", this.onEndMoving);
    },
    // 鼠标拖动进度条
    onMoving(){
      this.onRangeMove('range');
    },
    // 鼠标放开进度条
    onEndMoving(){
      this.onRangeMove();
      let document = this.$refs.playerRange;
      //  注销 mousemove 事件
      document.removeEventListener("mousemove", this.onMoving);
      //  注销 mouseup 事件
      document.removeEventListener("mouseup", this.onEndMoving);
    },
  }
}
</script>

<style scoped>

/** range */
.player_range{
  -webkit-appearance: none;
  border-radius:2px;
  width:100%;
  height:4px;
  box-shadow:inset #529bec 0 0 5px;
  outline : none;
  transition:.1s;
  outline : none;  
}
.player_range::-webkit-slider-thumb{
  -webkit-appearance: none;
  border: 3px solid #fff;
  width:14px;
  height:14px;
  background:#529bec;
  border-radius:50%;
  transition:.1s;
}

.player_range::-webkit-slider-thumb:hover,
.player_range::-webkit-slider-thumb:active{
  width:16px;
  height:16px;
}
/** -----range------- */
</style>
