<template>
  <div
    v-if="trackTimeDialogVisible"
    class="track-container"
  >
    <div class="head-title">
      <div>轨迹分段列表</div>
      <div
        class="close-btn"
        @click="closeDialog"
      >
        <i class="el-icon-error"/>
      </div>
    </div>
    <div class="track-list">
      <div
        v-for="(item, index) in trackTimeList"
        :key="index"
        class="track-item"
      >
        <el-button
          plain
          @click="onDeviceTrackSearch(item)"
        >
          {{ parseTime(item.startTm) }} ~ {{ parseTime(item.endTm) }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { trackingByParagraph } from '@/api/monitoring/track.js';
import { parseTime } from '@/api/utils/share';
export default {
  name: 'CarTrackTimeDialog',
  props: {

  },
  data() {
    return{
      query: {},
      trackTimeList: [],
      trackTimeDialogVisible: false
    };
  },
  methods: {
    // 查询轨迹分段的列表
    queryTrackingByParagraph (data) {
      this.query = data;
      trackingByParagraph({
        deviceId: data.deviceId,
        startTime: data.start,
        endTime: data.end,
        byInterval: true
      }).then(res => {
        if (res.code === 200) {
          this.trackTimeList = res.data || [];
          if (this.trackTimeList?.length) {
            this.trackTimeDialogVisible = true;
          } else {
            this.$message.warning('查询轨迹分段为空, 请确认该时间范围内是否存在轨迹!');
          }
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 查询某一段时间的轨迹
    onDeviceTrackSearch (data) {
      const params = {
        ...this.query,
        start: data.startTm,
        end: data.endTm
      };
      this.$emit('onDeviceTrackSearch', params);
    },
    followCloseAll() {
      this.$emit('followCloseAll');
    },
    closeDialog() {
      this.trackTimeDialogVisible = false;
      this.trackTimeList = [];
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.track-container{
  padding: 5px 15px 8px;
  background-color: #E1E5EE;
  border: 1px solid #bfbfbf;
  position: relative;
}
.head-title{
  font-size: 16px;
  color: var(--gn-color);
  height: 36px;
  line-height: 36px;
  display: flex;
  justify-content: space-between;
}
.track-list {
  max-height: 240px;
  overflow: auto;
}
.track-item {
  margin-bottom: 6px;
}
.close-btn{
  font-size: 22px;
  cursor: pointer;
}
</style>