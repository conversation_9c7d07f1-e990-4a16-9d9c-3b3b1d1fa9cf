<template>
  <div class="info_window">
    <div class="info_title">
      <div class="info_title_name">
        <div class="icon_logo" />
        <span class="blank" />
        <span>车辆列表</span>
      </div>
      <div>
        <a
          class="info_title_close"
          @click="onCloseInfoWindow"
        >
          X
        </a>
      </div>
    </div>
    <span
      v-for="item in info"
      :key="item.id"
    >
      <span
        class="link"
        @click="openCarList(item)"
      >{{ item.licencePlate }}
      </span>
    </span>
  </div>
</template>

<script>
import {queryVehicleState} from '@/api/monitoring/track.js';
export default {
  name: 'CarDetailWindow',
  components: {

  },
  props: {
    // 车辆信息
    info: {
      type: Array,
      default: () => {
        return [];
      }
    },
    map: {
      type: Object,
      default: null
    },
    amap: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      carInfo: [],
      detailWindow: null
    };
  },
  watch: {
    info: function (val) {
    }
  },
  methods: {
    // 打开车辆列表
    openCarList (val) {
      // 点聚合的情况下点击聚合图标显示具体车辆, 点击车辆在调用接口展示VehicleInfoWindow车辆详情
      // 系统中用了VehicleInfo.vue作为车辆详情组件, 不再使用VehicleInfoWindow
      // 系统中暂时没有使用该部分逻辑, 因此直接去除这部分代码, 后面需要再加上时可直接使用VehicleInfo组件
    },
    onCloseInfoWindow () {
      this.$emit('onCloseInfoWindow');
    }
  }
};
</script>

<style lang="less" scoped>
.info_window{
  padding:10px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.info_list{
  display: flex;
  flex-direction: row;
  color: #303133;
  font-size: 2px;
  padding: 10px 10px 0 10px;
  text-align: left;
  min-width: 250px;
  border-collapse: collapse;
}
.info_title{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.info_title_name{
  display: flex;
  align-items: center;
}
.icon_logo{
  width: 4px;
  height: 20px;
  background: #118de3;
}
.info_title_close{
  color: rgba(169, 169, 169, 0.4);
  font-weight: bolder;
  cursor: pointer;
}
.info_title_close:hover{
  color: #118de3;
}
.blank{
  margin: 2px;
}
.info_pl{
  margin-left: 5px;
  margin-top: 5px;
}
.link {
  color: #00f;
  cursor: pointer;
  margin-right: 10px;
}
</style>
