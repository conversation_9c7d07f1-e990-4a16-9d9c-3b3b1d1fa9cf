<template>
  <div v-show="isShowMultipFilesDownload">
    <div
      :key="id"
      class="multiple_files_download"
    >
      <span>附件：</span>
      <!-- <a :href="item" :download="item" target="_blank">{{item}}</a> -->
      <div>
        <span class="file_name">{{ handleDownloadFileName(filesStr) }}</span>
        <!--<a id="downloadFile" @click="downloadFile(filesStr)" class="download_btn">下载</a>-->
        <span
          class="download_btn"
          @click="downloadFile(filesStr)"
        >下载</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MultipleFilesDownload',
  components: {
  },
  props: {
    filesStr: ''
  },
  data () {
    return {

    };
  },
  computed: {
    isShowMultipFilesDownload () {
      return this.filesStr != '';
    }
  },
  mounted () {
  },
  methods: {
    handleDownloadFileName (url) {
      if (url) {
        var fileNameStartIndex = url.lastIndexOf('/') + 1;
        return url.substring(fileNameStartIndex);
      }
    },
    downloadFile (url) {
      // FIXME 临时下载处理  ， 需要真正的下载功能  by sh.shen
      // window.open(url)//FIXME yln
      this.$download(this.$serverURL + url, 'path');

      // var requestData = {
      //   file_url: url
      // }
      // this.$http.post('/officalcars/systemsetting/downloadbbs',requestData).then(res => {
      //   console.log(res)
      //   window.open(this.$serverURL + '/' + res.data)
      //   this.$message({
      //     message: '下载成功',
      //     type: 'success'
      //   });
      // }).catch(err => {
      //   console.log(err)
      //   this.$message({
      //     message: '下载失败',
      //     type: 'error'
      //   });
      // })
    }
  }
};
</script>

<style lang="less" scoped>
  .multiple_files_download {
    text-align: left;
    display: flex;
    margin: 5px 0;
    >div {
      display: flex;
      flex: 1;
      justify-content: space-between;
      .file_name {
        color: blue;
        text-decoration: underline;
      }
    }
  }
  .download_btn {
    background-color: #409eff;
    color: #fff;
    padding: 0px 40px;
    border-radius: 5px;
    cursor: pointer;
  }
  .download_btn:hover {
    opacity: 0.8;
  }
</style>
