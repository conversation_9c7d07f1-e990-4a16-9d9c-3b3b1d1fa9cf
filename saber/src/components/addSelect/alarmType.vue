<template>
  <!-- abandon -->
  <div class="layout">
    <el-cascader
      ref="cascaderRef"
      v-model="selectedType"
      :props="config"
      :options="deptOptions"
      :show-all-levels="false"
      size="small"
      clearable
      filterable
      placeholder="告警类型"
      collapse-tags
      @change="change"
    />
  </div>
</template>

<script>
import {getDictDetail} from '@/api/security/alarmHistory';
export default {
  name: 'DeptSingleSelect',
  props: {
    value: {
      type: [String, Array, Object],
      default: null
    }
  },
  data () {
    return {
      loading: false,
      selectedType: [],
      deptOptions: [],
      config: {
        value: 'dictCode',
        label: 'dictName',
        checkStrictly: false,
        multiple: true
      }
    };
  },
  watch: {
    selectedType (array) {
      if (!array) {
        return;
      }
      let arr;
      if (array.length > 0) {
        arr = array.map(item => {
          if (item && item.length > 1) {
            return item[item.length - 1];
          } else {
            return item[0];
          }
        });
      } else if (array.length === 0) {
        arr = null;
      }
      this.$emit('input', arr);
    },
    value (v) {
      if (!v) {
        this.selectedType = null;
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.getDict();
    });
  },
  methods: {
    getDict () {
      getDictDetail(16).then(res => {
        this.deptOptions = this.ruleData(res.data);
      });
    },
    ruleData (data) {
      data.forEach(item => {
        if (!item.children || item.children.length === 0) {
          delete item.children;
        } else {
          item.children = this.ruleData(item.children);
        }
      });

      return data;
    },
    getArr (data) {
      let arr = [];
      data.forEach(item => {
        let obj = {
          value: item.dictCode,
          label: item.dictName
        };
        if (item.children && item.children.length > 0) {
          item.children = this.getArr(item.children, item.children);
          obj.children = item.children;
        }
        arr.push(obj);
      });
      return arr;
    },
    /**
     * 监听选中节点
     */
    change (value) {
      let nodeInfo = this.$refs.cascaderRef.getCheckedNodes();
      this.$emit('change', nodeInfo);
    }
  }
};
</script>
<style lang="less" scoped>
.el-cascader-panel{
  height: 420px;
}
.layout ::v-deep.el-cascader{
  width: 180px;
}
</style>
