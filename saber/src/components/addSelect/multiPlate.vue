<template>
  <div class="layout">
    <div @click="inputFocus">
      <el-input
        ref="inputPost"
        v-model="inputPost"
        clearable
        size="small"
        placeholder="请输入车牌号码"
        class="input_zone"
        @input="inputChange"
        @clear="inputClear"
      />
    </div>

    <div
      v-show="treeShow"
      class="hide_input"
      @click="treeHandle"
    >
      <div class="tree_pick">
        <div
          v-for="(item,index) in checkList"
          :key="index"
          class="pick_item"
        >
          {{ item }}
          <i
            class="el-icon-error pick_del"
            @click="pickDel(item)"
          />
        </div>
      </div>
      <div class="tree">
        <el-checkbox-group v-model="checkList">
          <div
            v-for="(item,index) in treeData"
            :key="index"
            class="checkItem"
          >
            <!-- {{ item.label }} -->
            <el-checkbox
              :label=" item.label"
              @change="checkboxChange "
            />
          </div>
          <!-- <el-checkbox label="复选框 A" />
          <el-checkbox label="复选框 B" />
          <el-checkbox label="复选框 C" />
          <el-checkbox
            label="禁用"
            disabled
          />
          <el-checkbox
            label="选中且禁用"
            disabled
          /> -->
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script>
import {searchVehicle} from '@/api/base/vehicle';
export default {
  props: {
    value: {
      type: [String, Array, Object],
      default: null
    }
  },
  data () {
    return {
      inputPost: '',
      config: {
        selNum: 10,
        delay: 500
      },
      treeData: [],
      // treeData: [{
      //   label: '粤A05371',
      //   value: 90131
      // }, {
      //   label: '粤A05372',
      //   value: 90132
      // }, {
      //   label: '粤A05373',
      //   value: 90133
      // }, {
      //   label: '粤A05374',
      //   value: 90134
      // }, {
      //   label: '粤A05375',
      //   value: 90135
      // }],
      addFlag: false,
      treeShow: false,
      checkList: [],
      checkSave: [],
      inputSave: '',
      clearFlag: false
      // checkList: ['选中且禁用', '复选框 A', '粤A05374', '粤A05375', '1374', '1355', '133113', '13fsdf', '137adadq']
    };
  },
  watch: {
    treeData: {
      handler (newVal) {
        this.getChecked(newVal);
      },
      deep: true,
      immediate: true
    },
    inputPost: {
      handler (newVal) {
        // let
        console.log('vvv===========', newVal, this.checkList);
        // let arr = [];
        if (this.treeShow) {
          let arr = JSON.parse(JSON.stringify(this.checkList));

          if (this.strJudge(newVal)) {
            arr.unshift(newVal);
          }

          console.log('inputPost', arr, this.treeShow);

          this.$emit('input', arr);
        }

        // this.$emit('input', newVal);
      },
      deep: true,
      immediate: true
    },
    value (v) {
      if (v === null) {
        this.checkSave = [];
        this.checkList = [];
        this.inputSave = '';
        this.inputPost = '';
      }
    },
    treeShow: {
      handler (newVal) {
        // console.log('998');
      },
      deep: true,
      immediate: true
    }
  },
  mounted () {
    // 改变组件v-model值后无法同时改变组件内部input值, 因此使用EventBus监听
    this.$EventBus.$on('licencePlate', (list) => {
      this.checkSave = list;
      this.checkList = list;
      this.inputPost = list[list.length - 1];
    });
  },
  beforeDestroy () {
    this.$EventBus.$off('licencePlate');
  },
  methods: {
    inputClear () {
      this.clearFlag = true;
      this.checkSave = [];
      this.checkList = [];
      this.inputSave = '';
      this.$emit('input', []);
    },
    emitHandle () {
      let arr = JSON.parse(JSON.stringify(this.checkList));
      if (this.inputPost && !this.addFlag) {
        arr.unshift(this.inputPost);
      }
      console.log('emitHandle', arr);

      this.$emit('input', arr);
    },
    getChecked (data) {
      let arr = data.map(item => {
        return false;
      });
      this.checkedArr = arr;
      // console.log('this.checkedArrthis.checkedArr', this.checkedArr);
    },
    inputFocus (e) {
      // console.log('iii');
      e.stopPropagation();
      // 避免清空时候触发
      if (this.clearFlag) {
        this.clearFlag = false;
        return;
      }

      if (!this.treeShow) {
        this.treeShow = true;
        document.addEventListener('click', this.treeClose);
        this.$nextTick(() => {
          // if (this.addFlag) {
          //   this.inputPost = '';
          // } else {
          //   this.inputPost = this.inputPost.split(',')[0];
          // }

          this.inputPost = this.inputSave;
          this.addFlag = false;

          this.checkList = JSON.parse(JSON.stringify(this.checkSave));

          this.getSearchVehicle();
        });
      }
    },
    treeClose () {
      if (this.treeShow) {
        this.treeShow = false;

        // console.log('999');

        // 如果触发选择,则输入为空,否则保留
        this.inputSave = this.addFlag ? '' : this.inputPost;
        // 加不加输入值
        let str = (this.addFlag || !this.strJudge(this.inputPost)) ? '' : this.inputPost + ',';
        // console.log('ssstttrr', str, this.checkList);
        // 加选择值
        this.checkList.forEach(item => {
          str += item + ',';
        });
        console.log('treeClose', str, this.checkList);
        if (str[str.length - 1] === ',') {
          str = str.slice(0, str.length - 1);
        }
        // 保存选择内容
        this.checkSave = JSON.parse(JSON.stringify(this.checkList));
        this.checkList = [];
        this.inputPost = str;
      }
      console.log('treeClose2', this.inputPost);
      document.removeEventListener('click', this.treeClose);
    },
    inputChange (value) {
      setTimeout(() => {
        if (this.treeShow && value === this.inputPost) {
          this.getSearchVehicle(this.inputWrite);
        }
      }, this.config.delay);
    },
    getSearchVehicle () {
      searchVehicle({
        licence_plate: this.inputPost
      }, 'id').then((req) => {
        let opts = [];
        for (let i = 0; i < req.length; i++) {
          const element = req[i];
          opts.push({
            value: element.id,
            label: element.licencePlate
          });
        }
        this.treeData = opts;
        // console.log('ttteee', opts);
      });
    },
    pickDel (value) {
      let k;
      for (let i = 0; i < this.checkList.length; i++) {
        if (this.checkList[i] === value) {
          k = i;
        }
      }
      if (k || k === 0) {
        this.checkList.splice(k, 1);
      }
      this.emitHandle();
    },
    treeHandle (e) {
      e.stopPropagation();
    },
    checkboxChange () {
      // console.log('check!');

      this.addFlag = true;
      this.emitHandle();
    },
    strJudge (value) { // str是否存在
      let str = value.replace(/\s*/g, '');
      return str !== '';
    }
  }
};
</script>

<style scoped>
.layout{
  position: relative;
}
.hide_input{
  position: absolute;
  top: 32px;
  left: 0;
  width: 222px;
  height: 400px;
  background-color: #fff;
  z-index: 9999;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 8px 0px;
  padding: 8px;
  overflow-y: scroll;
}
.tree_pick{
  display: flex;
  flex-wrap: wrap;
}
.tree_pick{
  min-height: 41px;
  padding-bottom: 8px;
}
.pick_item{
  padding:1px 2px;
  border: 1px solid #439efb;
  border-radius: 3px;
  cursor: default;
  margin-right: 5px;
  margin-bottom: 5px;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;
}
.pick_del{
  color: #909399;
  padding: 5px;
  cursor: pointer;
}

.layout ::v-deep .el-input__inner{
  width: 180px;
}
.checkItem{
  padding: 3px;
}
</style>
