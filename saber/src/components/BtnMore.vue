<template>
  <div class="btnMore">
    <a
      v-show="!isDown"
      class="planel_button hvr-icon-hang common-search-btn"
      @click="handleClick"
    >
      <!-- 展示所有 -->
      <!-- <i class="icon_downup el-icon-caret-bottom"></i> -->
      <i class="animation_down el-icon-caret-bottom" />
    </a>
    <a
      v-show="isDown"
      class="planel_button common-search-btn"
      @click="handleClick"
    >
      <!-- 自动隐藏 -->
      <i class="animation_up el-icon-caret-top" />
    </a>
  </div>
</template>

<script>
export default {
  name: 'BtnMore',
  components: {
  },
  props: {
    isDown: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // MoreSearch:false
    };
  },
  mounted () {
  },
  methods: {
    handleClick (evt) {
      this.$emit('click', evt);
    }
  }
};
</script>

<style lang="less" scoped>
@import "../assets/global.less";
.btnMore{
  display:flex;
}
.icon_downup{
  animation: rotate 0.5s ease-in-out infinite alternate both;
}
@keyframes rotate{
  from{
    transform: rotate(180deg);
  }
  to{
    transform: rotate(0deg);
  }
}

.animation_down{
  animation: down 0.5s ease-in-out infinite alternate both;
}
@keyframes down{
  from{
    transform: translateY(-20%);
  }
  to{
    transform: translateY(20%);
  }
}
.animation_up{
  animation: up 0.5s ease-in-out infinite alternate both;
}
@keyframes up{
  from{
    transform: translateY(-20%);
  }
  to{
    transform: translateY(20%);
  }
}

</style>
