<template>
  <el-dialog
    title="审核"
    :visible.sync="dialogShow"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="560px"
    @close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      :model="form"
      :rules="rules"
      size="small"
      label-width="100px"
    >
      <!-- 审核批量数据 -->
      <div>
        <h4>审核事件选择</h4>
        <div class="event-list">
          <div
            v-for="(item,index) in someData"
            :key="index"
          >
            <span class="list-style">车牌号码</span><span>{{ item.licencePlate }} </span>
            <span class="list-style">告警类型</span><span>{{ EnumerationTypeHandling('alarmType',item.alarmType) }}</span>
          </div>
        </div>
      </div>
      <div>
        <el-form-item
          prop="appealState"
          label="审核状态"
        >
          <el-radio-group
            v-model="form.appealState"
          >
            <el-radio :label="2">
              通过
            </el-radio>
            <el-radio :label="3">
              驳回
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div v-if="form.appealState === 3">
        <el-form-item
          prop="auditContent"
          label="审核理由"
        >
          <el-input
            v-model="form.auditContent"
            type="textarea"
            placeholder="请填写审核理由"
            maxlength="140"
            :autosize="{ minRows: 3, maxRows: 6}"
            show-word-limit
          />
        </el-form-item>
      </div>
      <div class="btn">
        <el-button
          type="info"
          @click="$emit('update:dialogVisible', false);"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="onAlarmDeal()"
        >
          审核
        </el-button>
      </div>
    </el-form>
  </el-dialog>
</template>
<script>
import { auditAlarm, unlock } from '@/api/monitoring/realTimeMonitoring';
export default {
  name: 'IndexSomeAudit',
  props: {
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    isAlarmDetails: {
      type: Boolean,
      default: false
    },
    auditSomeData: {
      type: Array,
      default: null
    }
  },
  data () {
    return {
      dialogShow: false,
      someData: [], // 批量数据
      form: {
        auditContent: '',
        appealState: 2
      },
      rules: {
        auditContent: [{ required: true, message: '请填写审核理由', trigger: 'blur' }]
      }
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  watch: {
    dialogVisible: {
      handler (newVal) {
        this.dialogShow = newVal;
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate();
        }
      },
      immediate: true
    },
    auditSomeData: {
      handler (newVal) {
        this.someData = newVal;
      },
      immediate: true
    }
  },
  methods: {
    // 关闭弹窗事件
    closeDialog () {
      this.form = {
        auditContent: '',
        appealState: 2
      };
      // if (this.isAlarmDetails) {
      //   let obj = {
      //     serviceRole: this.someData[0].serviceRole,
      //     id: this.someData[0].id,
      //     operation: this.someData[0].operation
      //   };
      //   unlock(obj).then(res=>{});
      // }
      this.$emit('update:dialogVisible', false);
    },
    /**
     *审核(批量审核)
     *@param {[]String} id 告警id
     *@param {String} auditContent 审核理由
     */
    onAlarmDeal () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let appealList = [];
          this.someData.forEach((item) => {
            appealList.push(item.id);
          });
          const query = {
            idList: appealList,
            auditContent: this.form.auditContent,
            appealState: this.form.appealState,
            serviceRole: 2,
            operateType: 'third_audit' // 第三方告警处理 审核
          };
          auditAlarm(query).then((res) => {
            this.$message.success(res.msg);
            this.$emit('refresh');
            this.$emit('update:dialogVisible', false);
          });
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
@import "../../assets/less/variables.less";
::v-deep.el-form{
    text-align: start;
    .el-form-item{
        margin-bottom: 20px;
    }
}
::v-deep.el-dialog__header {
  background: @xhUIColorOther;
}
::v-deep.el-form-item {
    width: 500px;
}
::v-deep.el-dialog__title{
    color: @xhBackgroundColor2;
}
::v-deep.el-dialog__headerbtn .el-dialog__close{
  color: #fff;
}
.btn{
    text-align: center;
}
.event-list{
  width: 100%;
  height: 140px;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #ccc;
  overflow-y: auto;
  div{
    margin: 0 0 5px 0;
  }
}
.list-style{
  color:#909399;
  margin: 10px;
}
::v-deep.el-radio{
  margin-right: 20px;
}
::v-deep.el-input__inner{
  height: 40px;
}
</style>
