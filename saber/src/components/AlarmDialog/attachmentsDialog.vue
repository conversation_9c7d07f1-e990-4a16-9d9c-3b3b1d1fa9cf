<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    @close="closeDialog"
  >
    <template slot="title">
      <div class="header-title">
        <span>{{ attachmentsData.targetName }}</span>
        <span>{{ attachmentsData.alarmType }}</span>
        <span>{{ attachmentsData.startTime }}</span>
      </div>
    </template>
    <div
      v-if="alamrData.length"
      class="attachments-container"
    >
      <div class="active-container">
        <img
          v-if="!videoVisible"
          :src="imgSrc"
        >
        <video
          v-if="videoVisible"
          autoplay
          controls
          class="active-video"
        >
          <source
            :src="videoSrc"
            type="video/mp4"
          >
        </video>
      </div>
      <div class="img-container">
        <div
          v-for="(item, index) in accessory.imgList"
          :key="index"
          class="img-item"
          @click="handleImgClick(item)"
        >
          <img
            :src="item"
            alt=""
          >
        </div>
        <div class="img-item">
          <img
            :src="require('@/assets/images/alarm-video.png')"
            alt=""
            @click="handleVideoClick"
          >
        </div>
      </div>
    </div>
    <div
      v-else
      class="attachments-container-text"
    >
      未获取到任何多媒体信息
    </div>
    <div class="attachments-btn">
      <el-button
        v-for="(item, index) in alamrData"
        :key="index"
        type="info"
        @click="handleAlarmPoint(index)"
      >
        附件{{ index + 1 }}
      </el-button>
    </div>
    <template slot="footer">
      <div class="attachments-footer">
        <div>
          <el-button
            type="primary"
            icon="el-icon-download"
            @click="handleDownload"
          >当前下载</el-button>
          <el-button
            type="primary"
            icon="el-icon-download"
            @click="handleDownloadAll"
          >全部下载</el-button>
        </div>
        <div v-if="false">
          <el-checkbox
            v-model="timeChecked"
            class="time-btn"
            @change="handleTimeChange"
          >显示时间</el-checkbox>
          <el-button
            type="primary"
          >补传附件</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { downLoadFile } from '@/utils';
import FileSave from 'file-saver'
import Jszip from 'jszip'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    alarmPointList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    attachmentsData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      timeChecked: false,
      imgSrc: '',
      videoSrc: '',
      videoVisible: false,
      accessory: { // 当前展示的告警附件
        imgList: [],
        alarmVideo: ''
      },
      activeIndex: 0,
      alamrData: [] // 告警附件列表
    };
  },
  watch: {
    alarmPointList: {
      handler(newVal) {
        if (newVal.length) {
          this.setAlarmData(newVal);
        }
      },
      deep: true
    }
  },
  methods: {
    // 下载全部附件
    handleDownloadAll() {
      if (!this.alamrData.length) {
        this.$message.warning("当前暂无附件可下载");
        return;
      }
      let zip = new Jszip();
      let num = 0;
      let len = 0
      const title = "全部附件";
      const downLoad = () => {
        if (num === len) {
          // this.$message.error('请输入赋码编号');
          zip.generateAsync({ type: "blob" }).then((content) => {
            FileSave.saveAs(content, title);
          });
        }
      }
      const cbFun = (ele) => {
        this.getImage(ele).then((res) => {
          const url = ele.split('?');
          const fileName = url[0].split('/');
          zip.file(fileName[fileName.length - 1], res.response, { binary: true });
          num = num + 1;
          downLoad()
        }).catch((err) => {
          num = num + 1;
          downLoad()
        })
      };
      for (let index = 0; index < this.alamrData.length; index++) {
        const list = Object.values(this.alamrData[index]) || []
        len += list.length
        if(list.length) {
          list.forEach( item => cbFun(item))
        }
      }
    },
    // 下载当前附件
    handleDownload() {
      if (!this.alamrData.length) {
        this.$message.warning('当前暂无附件可下载');
        return;
      }
      let path = this.videoVisible ? this.videoSrc : this.imgSrc;
      downLoadFile(path);
    },
    getImage (imageUrl) {
      return new Promise((resolve, reject) => {
        let request = new XMLHttpRequest();
        request.responseType = 'blob';
        request.open('get', imageUrl, true);
        request.timeout = 5000;
        request.onreadystatechange = e => {
          if (request.readyState === XMLHttpRequest.DONE && request.status === 200) {
            resolve(request);
          }
        };
        request.send(null);
      });
    },
    // 处理附件数据
    setAlarmData(data) {
      this.alamrData = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.appendixId && (element.img1 || element.img2 || element.img3 || element.mp4)) {
          this.alamrData.push({
            img1: element.img1,
            img2: element.img2,
            img3: element.img3,
            mp4: element.mp4
          });
        }
      }
      if (this.alamrData.length) {
        this.handleAlarmPoint(0);
      }
    },
    // 附件按钮点击时
    handleAlarmPoint(index) {
      this.accessory.alarmVideo = this.alamrData[index].mp4;
      let list = [];
      for (let i = 1; i < 4; i++) {
        if (this.alamrData[index]['img' + i]) {
          list.push(this.alamrData[index]['img' + i]);
        }
      }
      this.accessory.imgList = list;
      this.imgSrc = list[0];
    },
    // 点击视频弹窗时
    handleVideoClick() {
      this.videoVisible = true;
      this.videoSrc = this.accessory.alarmVideo;
    },
    // 点击图片弹窗时
    handleImgClick(value) {
      this.videoVisible = false;
      this.imgSrc = value;
    },
    handleTimeChange() {

    },
    closeDialog() {
      this.videoVisible = false;
      this.accessory = {
        imgList: [],
        alarmVideo: ''
      };
      this.timeChecked = false;
      this.imgSrc = '';
      this.videoSrc = '';
      this.activeIndex = 0;
      this.$emit('update:dialogVisible', false);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-dialog__body {
    padding: 7px;
}
.attachments-container {
    display: flex;
}
.attachments-container-text {
    font-size: 20px;
    height: 700px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.attachments-btn {
  margin-top: 10px;
}
.active-container {
    height: 68vh;
    flex: 1;
    img {
        width: 100%;
        height: 100%;
    }
    .active-video {
        width: 100%;
        height: 100%;
        object-fit: fill;
    }
}
.img-container {
    width: 200px;
    margin-left: 10px;
    .img-item {
      border: 1px solid rgb(174, 186, 197);
      img {
        width: 100%;
      }
    }
}
.attachments-footer {
    display: flex;
    justify-content: space-between;
    .time-btn {
        margin-right: 10px;
    }
}
.header-title {
  span {
    padding-right: 20px;
  }
}
</style>
