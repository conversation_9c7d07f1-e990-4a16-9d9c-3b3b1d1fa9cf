<template>
  <el-dialog
    title="申诉"
    :visible.sync="dialogShow"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="560px"
    @close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      :model="form"
      :rules="rules"
      size="small"
      label-width="100px"
    >
      <!-- 申诉批量数据 -->
      <div>
        <h4>申诉事件选择</h4>
        <div class="event-list">
          <div
            v-for="(item,index) in someData"
            :key="index"
          >
            <span class="list-style">车牌号码</span><span>{{ item.licencePlate }} </span>
            <span class="list-style">告警类型</span><span>{{ EnumerationTypeHandling('alarmType',item.alarmType) }}</span>
          </div>
        </div>
      </div>
      <div>
        <el-form-item
          prop="appealReason"
          label="申诉理由"
        >
          <el-input
            v-model="form.appealReason"
            type="textarea"
            placeholder="请填写申诉理由"
            maxlength="140"
            :autosize="{ minRows: 3, maxRows: 6}"
            show-word-limit
          />
        </el-form-item>
      </div>
      <div>
        <el-form-item
          label="申诉附件"
          prop="appealAttach"
        >
          <el-upload
            ref="upload"
            class="upload-demo"
            action="/fs/group1/upload"
            :on-remove="handleRemove"
            :headers="uploadHeaders"
            :on-success="handleSuccess"
            :on-exceed="handleLimit"
            :limit="1"
          >
            <!-- <span v-if="form.appealAttach" /> -->
            <el-button
              slot="trigger"
              size="small"
              type="primary"
            >
              选取文件
            </el-button>
          </el-upload>
        </el-form-item>
      </div>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="$emit('update:dialogVisible', false)"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="onAlarmDeal()"
      >
        申诉
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { appealAlarm, unlock } from '@/api/monitoring/realTimeMonitoring';
import { getToken } from '@/util/auth';
export default {
  name: 'AlarmDialog',
  props: {
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    isAlarmDetails: {
      type: Boolean,
      default: false
    },
    appealSomeData: {
      type: Array,
      default: null
    }
  },
  data () {
    return {
      dialogShow: false,
      someData: [], // 批量数据
      form: { // 批量处理表单
        appealReason: '',
        appealAttach: '',
      },
      uploadHeaders: {'Authorization': 'Bearer ' + getToken()}, // upload token
      rules: {
        appealReason: [{ required: true, message: '请填写申诉理由', trigger: 'blur' }]
      }
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  watch: {
    dialogVisible: {
      handler (newVal) {
        this.dialogShow = newVal;
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate();
          this.$refs.upload.clearFiles();
        }
      },
      immediate: true
    },
    appealSomeData: {
      handler (newVal) {
        this.someData = newVal;
      },
      immediate: true
    }
  },
  methods: {
    handleLimit() {
      this.$message.warning('只能上传1个文件')
    },
    // 关闭弹窗事件
    closeDialog () {
      this.form = {
        appealReason: '',
        appealAttach: ''
      };
      // if (this.isAlarmDetails) {
      //   let obj = {
      //     serviceRole: this.someData[0].serviceRole,
      //     id: this.someData[0].id,
      //     operation: this.someData[0].operation
      //   };
      //   unlock(obj).then(res=>{});
      // }
      this.$emit('update:dialogVisible', false);
    },
    // 图片上传成功回调
    handleSuccess(file){
      this.form.appealAttach = file.data;
    },
    handleRemove (file, fileList) {
      this.form.appealAttach = '';
    },
    /**
     *申诉(批量申诉)
     *@param {[]String} id 告警id
     *@param {String} appealReason 申诉理由
     */
    onAlarmDeal () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let appealList = [];
          this.someData.forEach((item) => {
            appealList.push(item.id);
          });
          const query = {
            idList: appealList,
            appealReason: this.form.appealReason,
            appealAttach: this.form.appealAttach,
            operateType: 'company_appeal' // 运输企业用户告警处理 申诉
          };
          appealAlarm(query).then((res) => {
            this.$message.success(res.msg);
            this.$emit('refresh');
            this.$emit('update:dialogVisible', false);
          });
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
@import "../../assets/less/variables.less";
::v-deep.el-form{
    text-align: start;
    .el-form-item{
        margin-bottom: 20px;
    }
}
::v-deep.el-dialog__header {
  background: @xhUIColorOther;
}
::v-deep.el-form-item {
    width: 500px;
}
::v-deep.el-dialog__title{
    color: @xhBackgroundColor2;
}
::v-deep.el-dialog__headerbtn .el-dialog__close{
  color: #fff;
}
.event-list{
  width: 100%;
  height: 140px;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #ccc;
  overflow-y: auto;
  div{
    margin: 0 0 5px 0;
  }
}
.list-style{
  color:#909399;
  margin: 10px;
}
::v-deep.el-radio{
  margin-right: 20px;
}
::v-deep.el-input__inner{
  height: 40px;
}
</style>
