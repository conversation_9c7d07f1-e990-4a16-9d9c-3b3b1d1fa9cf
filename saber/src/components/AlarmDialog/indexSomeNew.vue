<template>
  <div
    v-if="dialogVisible"
    class="dialog"
  >
    <el-dialog
      title="告警处理"
      :visible.sync="dialogShow"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="800px"
      @close="closeDialog"
    >
      <el-form
        ref="ruleForm"
        :model="form"
        :rules="rules"
        size="small"
        label-width="auto"
      >
        <!-- 告警批量数据 -->
        <div class="event-container">
          <div class="event-list">
            <div>告警事件选择</div>
            <div class="event-flex-box">
              <div
                v-for="(item,index) in someData"
                :key="index"
                class="event-item"
              >
                <span
                  class="close"
                  @click="removeEventItem(item)"
                >
                  <i class="el-icon-close"/>
                </span>
                <div>
                  <span class="list-style">监控对象：</span><span>{{ item.targetName }} </span>
                </div>
                <div>
                  <span class="list-style">告警类型：</span><span>{{ item.alarmType }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <el-row
          :span="24"
          class="form-container"
        >
          <el-col :span="7">
            <el-form-item
              label="是否紧急"
              prop="urgent"
              label-width="80px"
            >
              <el-radio-group
                v-model="form.urgent"
              >
                <el-radio :label="3">
                  是
                </el-radio>
                <el-radio :label="0">
                  否
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              prop="ttsAudio"
              label="是否TTS播报"
              label-width="100px"
            >
              <el-radio-group
                v-model="form.ttsAudio"
              >
                <el-radio :label="1">
                  是
                </el-radio>
                <el-radio :label="0">
                  否
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label="是否终端屏幕展示"
              label-width="140px"
            >
              <el-radio-group
                v-model="form.terminalScreen"
              >
                <el-radio :label="2">
                  是
                </el-radio>
                <el-radio :label="0">
                  否
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="处理内容"
              prop="content"
            >
              <el-input
                v-model="form.content"
                type="textarea"
                maxlength="140"
                rows="6"
                :placeholder="'请输入处理内容'"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="closeDialog"
        >
          取消
        </el-button>
        <el-button
          :loading="loading"
          type="primary"
          size="small"
          @click="onAlarmDeal"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { dealAlarms } from '@/api/monitoring/realTimeMonitoring';

export default {
  name: 'AlarmDialogNew',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    alarmSomeData: {
      type: Array,
      default: null
    },
    isAlarmDetails: {
      type: Boolean,
      default: false
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    serviceRole: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      dialogShow: false,
      someData: [], // 批量数据
      form: { // 批量处理表单
        ttsAudio: 0,
        terminalScreen: 0,
        content: '',
        urgent: 0
      },
      rules: {
        // content: [
        //   {
        //     required: true,
        //     message: '请填写处理内容',
        //     trigger: 'blur'
        //   }
        // ]
      },
      loading: false
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling() {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  watch: {
    dialogVisible: {
      handler(newVal) {
        this.dialogShow = newVal;
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate();
        }
      },
      immediate: true
    },
    alarmSomeData: {
      handler(newVal) {
        this.someData = newVal;
      },
      immediate: true
    }
  },
  methods: {
    // 关闭弹窗事件
    closeDialog() {
      this.form = {
        ttsAudio: 0,
        terminalScreen: 0,
        content: '',
        urgent: 0
      };
      this.$emit('closeDialog', false);
    },
    /**
     *实时告警单个处理(批量处理)
     *@param {[]String} id 告警id id使用数据的key值--string代替 required
     *@param {String} ttsAudio 是否tts播报 required
     *@param {Int} terminalScreen 是否终端屏幕展示 required
     *@param {String} urgent 是否紧急 required
     *@param {String} content 处理内容 required
     */
    onAlarmDeal() {
      if (this.someData.length === 0) {
        this.$message.error('请至少选择一条告警事件');
        return false;
      }
      // this.$refs.ruleForm.validate((valid) => {
      // if (valid) {
      this.loading = true;
      const alarmList = this.someData.map((item) => {
        return item.id;
      });
      let handleMeasuresList = [
        this.form.urgent ? this.form.urgent : undefined,
        this.form.ttsAudio ? this.form.ttsAudio : undefined,
        this.form.terminalScreen ? this.form.terminalScreen : undefined,
      ].filter(Boolean);
      let params = {
        ids: alarmList,
        handleMeasuresList: handleMeasuresList,
        handleContent: this.form.content,
        handleState: 1 // 1 确认告警
      };
      dealAlarms(params).then((res) => {
        this.$message.success(res.msg);
        this.$emit('refresh');
        // 刷新页面右上角告警数目
        // this.$EventBus.$emit('alarmNumber', true);
        this.closeDialog();
      }).finally(()=>{
        this.loading = false;
      });
      // }
      // });
    },
    removeEventItem(removeItem) {
      this.someData = this.someData.filter(item => item.id !== removeItem.id);
      this.$emit('selectionChange', this.someData);
    }
  }
};
</script>
<style scoped lang="less">
.event-container {
  height: 200px;
  padding: 20px 30px;
  color: #3c3c3c;
}

.event-list {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  border: 1px solid #e1e5e8;
  padding: 10px 20px;

  .event-flex-box {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8px;
  }

  .event-item {
    border: 1px solid #bfbfbf;
    padding: 18px 12px;
    border-radius: 4px;
    margin: 0 24px 20px 0;
    position: relative;

    div + div {
      margin-top: 8px;
    }

    .close {
      position: absolute;
      right: 0;
      top: 0;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;

      i {
        font-size: 14px;
      }
    }
  }
}

::v-deep .el-dialog__body {
  padding: 0;
}

.form-container {
  border-top: 1px solid #e1e5e8;
  padding: 20px 30px 0;
}
</style>
