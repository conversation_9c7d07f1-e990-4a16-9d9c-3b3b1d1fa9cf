<template>
  <div
    v-if="dialogVisible"
    class="dialog"
  >
    <el-dialog
      :title="alarmTitle(alarmData.alarmName)"
      :visible.sync="dialogShow"
      :close-on-click-modal="false"
      width="540px"
      :modal-append-to-body="false"
      @close="closeDialog"
    >
      <el-form
        ref="ruleForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item
          prop="dealDescribe"
          label="处理描述"
        >
          <el-input
            v-model="form.dealDescribe"
            :disabled="alarmData.dealState !== 0"
            style="width: 220px"
            placeholder="请输入处理描述"
          />
        </el-form-item>
        <el-form-item
          prop="textTip"
          label="文字提醒"
        >
          <el-input
            v-model="form.textTip"
            class="position-right"
            :disabled="alarmData.dealState !== 0"
            placeholder="请填写下发文字"
          />
          <el-button
            v-show="alarmData.dealState === 0"
            type="primary"
            @click="onSendText()"
          >
            下发文字
          </el-button>
        </el-form-item>
        <el-form-item
          label="监听"
        >
          <el-input
            v-model="form.phone"
            clearable
            class="position-right"
            :disabled="alarmData.dealState !== 0"
            placeholder="请输入电话号码"
          />
          <el-button
            v-show="alarmData.dealState === 0"
            type="primary"
            @click="onChannel()"
          >
            开始监听
          </el-button>
        </el-form-item>
        <el-form-item
          label="拍照"
        >
          <xh-select
            v-model="form.photoChannel"
            clearable
            placeholder="请选择拍照通道号"
            :disabled="alarmData.dealState !== 0"
            class="position-right"
          >
            <el-option
              v-for="item in photoOptions"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            />
          </xh-select>
          <el-button
            v-show="alarmData.dealState === 0"
            type="primary"
            @click="onPhoto()"
          >
            立即拍照
          </el-button>
        </el-form-item>
        <el-form-item label="解除告警">
          <xh-select
            v-model="form.removeAlarm"
            clearable
            size="small"
            class="position-right"
            :disabled="alarmData.dealState !== 0"
            placeholder="请选择解除告警类型"
          >
            <el-option
              v-for="item in dict.dict.removeAlarm"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </xh-select>
        </el-form-item>
        <el-form-item
          label="处理人"
          prop="dealMan"
        >
          <el-input
            v-model="form.dealMan"
            :disabled="alarmData.dealState !== 0"
            style="width: 220px"
            placeholder="请填写处理人"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :disabled="alarmData.dealState !== 0"
            maxlength="140"
            :autosize="{ minRows: 2, maxRows: 6 }"
            show-word-limit
            placeholder="请输入备注内容"
            style="width: 220px"
          />
        </el-form-item>
        <div
          v-if="alarmData.dealState === 0"
          class="btn"
        >
          <el-button
            type="info"
            @click="closeDialog()"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            @click="dealAlarm()"
          >
            确认处理
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { dealoneAlarm } from '@/api/security/alarmLive';
import { sendPhotoMsg, sendMonitorMsg, sendTextMsg } from '@/api/center/instruction.js';
export default {
  name: 'AlarmDialog',
  dicts: ['removeAlarm'],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    alarmData: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      dialogShow: false,
      form: { // 批量处理表单
        dealDescribe: '',
        textTip: '',
        remark: '',
        removeAlarm: null,
        dealMan: null,
        flag: 0,
        phone: '',
        photoChannel: '',
        textTipSwith: false,
        flagSwith: false,
        photoChannelSwith: false
      },
      photoOptions: [
        {
          value: 1,
          label: '通道1'
        }, {
          value: 2,
          label: '通道2'
        }, {
          value: 3,
          label: '通道3'
        }, {
          value: 4,
          label: '通道4'
        }
      ],
      rules: {
        dealMan: [{ required: true, message: '请填写处理人', trigger: 'blur' }],
        dealDescribe: [{ required: true, message: '请填写处理描述', trigger: 'blur' }],
        textTip: [{ required: true, message: '请填写文字提醒', trigger: 'blur' }]
      }
    };
  },
  computed: {
    // title处理
    alarmTitle () {
      return (val) => {
        return '告警类型:' + val;
      };
    }
  },
  watch: {
    dialogVisible: {
      handler (newVal) {
        this.dialogShow = newVal;
        if (this.alarmData.removeAlarm > 1) {
          this.form.removeAlarm = this.alarmData.removeAlarm;
        }
        this.form.dealMan = this.alarmData.dealMan;
        this.form.dealDescribe = this.alarmData.dealDescribe;
        this.form.remark = this.alarmData.remark;
        this.form.textTip = this.alarmData.remindText;
        this.alarmData.monitorChannel && (this.form.phone = this.alarmData.monitorChannel);
        this.alarmData.phoneChannel && (this.form.photo = Number(this.alarmData.phoneChannel));
      },
      immediate: true
    }
  },
  methods: {
    // 关闭弹窗事件
    closeDialog () {
      this.$emit('closeDialog', false);
    },
    /** 文字指令下发 ==> 调用指令下发的文字指令下发接口
     *@param {String} alarmId 告警id no
     *@param {String} licencePlate 车牌号 required
     *@param {String} text 通道 0-普通通话，1-监听 required
     */
    onSendText () {
      let param = {
        alarmId: this.alarmData.key,
        text: this.form.textTip,
        licencePlates: [this.alarmData.licencePlate],
        vehicleId: this.alarmData.licencePlateId
      };
      if (!this.form.textTip) {
        this.$message({
          type: 'error',
          message: '下发文字内容不能为空'
        });
        return;
      }
      sendTextMsg(param).then(res => {
        this.$message({
          type: 'success',
          message: res.msg
        });
        this.textTipSwith = true;
      });
    },
    /** 监听指令下发 ==> 调用指令下发的监听指令下发接口
     *@param {String} alarmId 告警id no
     *@param {String} licencePlate 车牌号 required
     *@param {Int} flag 通道 0-普通通话，1-监听 required
     *@param {String} phone 电话号码 required
     */
    onChannel () {
      let param = {
        licencePlate: this.alarmData.licencePlate,
        flag: this.form.flag,
        phone: this.form.phone,
        alarmId: this.alarmData.key,
        vehicleId: this.alarmData.licencePlateId
      };
      if (!this.form.phone) {
        this.$message({
          type: 'warning',
          message: '电话号码不能为空'
        });
        return;
      }
      sendMonitorMsg(param).then(res => {
        this.$message({
          type: 'success',
          message: res.msg
        });
        this.flagSwith = true;
      });
    },
    /** 拍照指令下发 ==> 调用指令下发的拍照指令下发接口
     *@param {String} alarmId 告警id no
     *@param {String} licencePlate 车牌号 required
     *@param {String} channel 通道 required
     */
    onPhoto () {
      let param = {
        alarmId: this.alarmData.key,
        licencePlate: this.alarmData.licencePlate,
        channel: this.form.photoChannel,
        vehicleId: this.alarmData.licencePlateId
      };
      if (!this.form.photoChannel) {
        this.$message({
          type: 'warning',
          message: '拍照监听通道不能为空'
        });
        return;
      }
      sendPhotoMsg(param).then(res => {
        this.$message({
          type: 'success',
          message: res.msg
        });
        this.photoChannelSwith = true;
      });
    },
    /**
     *实时告警单个处理
     *@param {String} id 告警id id使用数据的key值--string代替 required
     *@param {String} dealDescribe 处理描述 no
     *@param {Int} removeAlarm 解除告警 no
     *@param {String} dealMan 处理人 required
     *@param {String} remark 备注 no
     */
    dealAlarm () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let param = {
            id: this.alarmData.key + '',
            dealMan: this.form.dealMan,
            remark: this.form.remark,
            dealDescribe: this.form.dealDescribe
          };
          this.form.removeAlarm && (param.removeAlarm = this.form.removeAlarm);
          this.textTipSwith && (param.remindText = this.form.textTip);
          this.flagSwith && (param.monitorChannel = this.form.phone);
          this.photoChannelSwith && (param.phoneChannel = String(this.form.photoChannel));
          dealoneAlarm(param).then((res) => {
            if (res.code === 0) {
              this.$message.success('告警处理成功');
              if (!(this.$route.path === '/security/alarm/alarmDetails')) {
                this.$parent.crud.toQuery();
              }
              // 刷新页面右上角告警数目
              this.$EventBus.$emit('alarmNumber', true);
              this.closeDialog();
              this.$emit('getAlarmDetail');
            }
          });
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
@import "../../assets/less/variables.less";
::v-deep.el-form {
  text-align: start;
  .el-form-item {
    margin-bottom: 20px;
  }
}
::v-deep.el-dialog__header {
  background: @xhUIColorOther;
}
::v-deep.el-form-item {
  width: 500px;
}
::v-deep.el-dialog__title {
  color: @xhBackgroundColor2;
}
::v-deep.el-dialog__headerbtn .el-dialog__close {
  color: #fff;
}
.btn {
  text-align: center;
}
.btn-disabled {
  cursor: not-allowed;
}
::v-deep.el-radio {
  margin-right: 20px;
}
::v-deep.el-input__inner {
  height: 40px;
}
.position-right {
  width: 220px;
  margin-right: 15px;
}
</style>
