<template>
  <el-dialog
    v-drag
    :visible.sync="isShowTable"
    append-to-body
    title="业务对象列表"
    width="90%"
    @closed="handleCancel"
  >
    <el-tabs
      v-model="active"
      v-loading="dialogLoading"
      tab-position="left"
      @tab-click="tabChange"
    >
      <el-tab-pane
        v-for="item in tabList"
        :key="`${item.value}-${item.catogory}`"
        :label="item.label"
        :name="`${item.value}-${item.catogory}`"
      >
        <headList
          :ref="`head${item.value}-${item.catogory}`"
          :options="headsOption[item.type]"
          :dict="dict"
          :active="active"
          :isShowTable="isShowTable"
          v-bind="$attrs"
          :deptList="activeDeptMap[`${item.value}-${item.catogory}`]"
          @getData="getPage"
        />
        <tableTab
          :ref="`table${item.value}-${item.catogory}`"
          :heads="tableHeads[item.type]"
          :totalPage="totalPages[`${item.value}-${item.catogory}`]"
          :isShowTable="isShowTable"
          :tableData="tableData[`${item.value}-${item.catogory}`]"
          @getData="getList"
          @rowClick="rowClick"
        />
      </el-tab-pane>
    </el-tabs>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="handleCancel"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        保存
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import tableTab from './module/tableTmp.vue';
import headList from './module/headList.vue';
import { getDictDetail } from "@/api/security/alarmHistory";
import { deviceType } from '@/api/base/driver.js';
import { getDeptPerInit } from '@/api/base/dept';
import { pagination as vehiclePagination } from '@/api/base/vehicle';
import { pagination as driverPagination } from '@/api/base/driver';
import { pagination as facilityPagination } from '@/api/base/facility';
import { pagination as carriageManagementPagination } from '@/api/base/carriageManagement';
import { pagination as precisionEquipmentPagination } from '@/api/base/precisionEquipment';
import { pagination as shipManagementPagination } from '@/api/base/shipManagement';
import { pagination as expatriatePagination } from '@/api/base/expatriate';
import { pagination as vehicleTruckPagination } from '@/api/base/vehicleTruck';
import { pagination as containerManagePagination } from "@/api/base/containerManage";
import { pagination as visitorPagination } from '@/api/base/visitor';
export default {
  components: {
    tableTab,
    headList
  },
  props: {
    isShowTable: {
      type: Boolean,
      default: false
    },
    targetModel: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data () {
    const tableHeads = {
      vehicle: [
        { columnName: 'number', columnTitle: '车辆编号' },
        { columnName: 'categoryName', columnTitle: '车辆类型' },
        { columnName: 'deptName', columnTitle: '所属机构' },
      ],
      vehicleTruck: [
        { columnName: 'number', columnTitle: '车辆编号' },
        { columnName: 'categoryName', columnTitle: '车辆类型' },
        { columnName: 'deptName', columnTitle: '所属机构' },
      ],
      use: [
        { columnName: 'name', columnTitle: '人员姓名' },
        { columnName: 'wkno', columnTitle: '工号' },
        { columnName: 'industryName', columnTitle: '从业类型' },
        { columnName: 'postName', columnTitle: '岗位类型' },
        { columnName: 'deptName', columnTitle: '所属机构' }
      ],
      base: [
        { columnName: 'code', columnTitle: '编号' },
        { columnName: 'categoryName', columnTitle: '设施类型' },
        { columnName: 'name', columnTitle: '设施名称' },
        { columnName: 'deptName', columnTitle: '所属机构' }
      ],
      carriage: [
        { columnName: 'number', columnTitle: '车厢编号' },
        { columnName: 'deptName', columnTitle: '所属机构' }
      ],
      precision: [
        { columnName: 'number', columnTitle: '装备编号' },
        { columnName: 'deptName', columnTitle: '所属机构' }
      ],
      ship: [
        { columnName: 'number', columnTitle: '货船编号' },
        { columnName: 'deptName', columnTitle: '所属机构' }
      ],
      container: [
        { columnName: 'number', columnTitle: '集装箱编号' },
        { columnName: 'deptName', columnTitle: '所属机构' }
      ],
      visitor: [
        { columnName: 'name', columnTitle: '人员姓名' },
        { columnName: 'idNumber', columnTitle: '身份证号' },
        { columnName: 'industryName', columnTitle: '从业类型' },
        { columnName: 'postName', columnTitle: '岗位类型' },
        { columnName: 'deptName', columnTitle: '所属机构' }
      ]
    };
    const tabList = [
      { label: '车辆', value: 1, catogory: 'target', type: 'vehicle', api: vehiclePagination, targetFlag: 'number', targetModel: '其他' },
      { label: '人员', value: 2, catogory: 'target',type: 'use', api: driverPagination, targetFlag: 'wkno', targetModel: '人员定位' },
      { label: '基础设施', value: 3, catogory: 'target',type: 'base', api: facilityPagination, targetFlag: 'code', targetModel: '其他' },
      { label: '铁路货车车厢', value: 8, catogory: 'target',type: 'carriage', api: carriageManagementPagination, targetFlag: 'number', targetModel: '铁路货车' },
      { label: '精密装备', value: 9, catogory: 'target',type: 'precision', api: precisionEquipmentPagination, targetFlag: 'number', targetModel: '其他' },
      { label: '货船', value: 7, catogory: 'target',type: 'ship', api: shipManagementPagination, targetFlag: 'number', targetModel: '其他' },
      { label: '外派', value: 5, catogory: 'target',type: 'use', api: expatriatePagination, targetFlag: 'wkno', targetModel: '外派' },
      { label: '矿用卡车', value: 10, catogory: 'target',type: 'vehicleTruck', api: vehicleTruckPagination, targetFlag: 'number', targetModel: '其他' },
      { label: '集装箱', value: 11, catogory: 'target',type: 'container', api: containerManagePagination, targetFlag: 'number', targetModel: '集装箱' },
      { label: '访客', value: 12, catogory: 'target',type: 'visitor', api: visitorPagination, targetFlag: 'idNumber', targetModel: '访客' },
    ];
    this.initKey = tabList.reduce((obj, item) => {
      obj[`${item.value}-${item.catogory}`] = [];
      return obj;
    }, {});
    const headsOption = {
      vehicle: [
        {
          label: '车辆编号',
          value: 'number',
          type: 'input',
        },
        {
          label: '车辆类型',
          value: 'category',
          type: 'vehicleType'
        },
        // {
        //   label: '所属机构',
        //   value: 'deptId',
        //   type: 'dept'
        // }
      ],
      vehicleTruck: [
        {
          label: '车辆编号',
          value: 'number',
          type: 'input',
        },
        {
          label: '车辆类型',
          value: 'category',
          type: 'vehicleTruckType'
        },
        // {
        //   label: '所属机构',
        //   value: 'deptId',
        //   type: 'dept'
        // }
      ],
      use: [
        {
          label: '人员姓名',
          value: 'name',
          type: 'input',
        },
        {
          label: '工号',
          value: 'wkno',
          type: 'input'
        },
        {
          label: '从业类型',
          value: 'industry',
          type: 'bdmWorkerPost'
        },
        // {
        //   label: '所属机构',
        //   value: 'deptId',
        //   type: 'dept'
        // }
      ],
      base: [
        {
          label: '设施类型',
          value: 'category',
          type: 'facilityType'
        },
        {
          label: '设施名称',
          value: 'name',
          type: 'input'
        },
        // {
        //   label: '所属机构',
        //   value: 'deptId',
        //   type: 'dept'
        // }
      ],
      carriage: [
        {
          label: '车厢编号',
          value: 'number',
          type: 'input'
        },
        // {
        //   label: '所属机构',
        //   value: 'deptId',
        //   type: 'dept'
        // }
      ],
      precision: [
        {
          label: '装备编号',
          value: 'number',
          type: 'input'
        },
        // {
        //   label: '所属机构',
        //   value: 'deptId',
        //   type: 'dept'
        // }
      ],
      ship: [
        {
          label: '货船编号',
          value: 'number',
          type: 'input'
        },
        // {
        //   label: '所属机构',
        //   value: 'deptId',
        //   type: 'dept'
        // }
      ],
      container: [
        {
          label: '集装箱编号',
          value: 'number',
          type: 'input'
        }
      ],
      visitor: [
        {
          label: '人员姓名',
          value: 'name',
          type: 'input',
        },
        {
          label: '身份证号',
          value: 'idNumber',
          type: 'input'
        },
        {
          label: '从业类型',
          value: 'industry',
          type: 'bdmWorkerPost'
        },
      ]
    };
    return {
      tableHeads,
      tabList,
      active: Object.keys(this.initKey)[0],
      tableData: JSON.parse(JSON.stringify(this.initKey)),
      activeDeptMap: JSON.parse(JSON.stringify(this.initKey)),
      headsOption,
      dict: {
        deviceType: {},
        vehicleType: [],
        vehicleTruckType: [],
        bdmWorkerPost: []
      },
      totalPages: {},
      deptOptions: [],
      changeTableRow: null,
      dialogLoading: false
    };
  },
  watch: {
    isShowTable (val) {
      if (val) {
        this.getList();
        this.$nextTick(() => {
          if(!this.activeDeptMap[this.active].length) {
            this.activeDeptMap[this.active] = this.deptOptions;
          }
        });
      } else {
        this.tableData = JSON.parse(JSON.stringify(this.initKey));
        this.active = Object.keys(this.initKey)[0];
      }
    }
  },
  // 复制平台管理页面的代码, 修改了部分逻辑
  created() {
    this.getDictData();
    getDeptPerInit().then(res => {
      this.deptOptions = res.data;
    });
  },
  methods: {
    // 点击表格行
    rowClick (row) {
      this.changeTableRow = JSON.parse(JSON.stringify(row));
    },
    getDictData() {
      deviceType().then(res => {
        if (res?.data?.[0]?.children) {
          const obj = {};
          this.dict.bdmDeviceType = res.data[0].children;
          res.data[0].children.forEach(item => {
            obj[item.value + '-device'] = item.children;
          });
          this.dict.deviceType = obj;
        }
      });
      getDictDetail("bdm_car_category").then((res) => {
        const list = res.data || [];
        this.dict.vehicleType = list;
      });
      getDictDetail("bdm_truck_category").then((res) => {
        const list = res.data || [];
        this.dict.vehicleTruckType = list;
      });
      getDictDetail("facility_type").then((res) => {
        const list = res.data || [];
        this.dict.facilityType = list;
      });
      getDictDetail("bdm_worker_post").then((res) => {
        const list = res.data || [];
        this.dict.bdmWorkerPost = list;
      });
    },
    handleCancel () {
      this.$emit('update:isShowTable', false);
    },
    handleSubmit () {
      if (!this.changeTableRow) {
        this.$message.warning('请选择数据');
        return;
      }
      const result = this.tabList.find(item => item.value + '-' +item.catogory === this.active);
      let targetFlag = this.changeTableRow[result.targetFlag];
      let targetModelLabel = result.targetModel;
      // 选择车辆时判断该车辆是否有车牌颜色
      if (result.type === 'vehicle' && (this.changeTableRow.category === '1' || this.changeTableRow.category === '2')) {
        const list = this.changeTableRow[result.targetFlag].split('-');
        targetFlag = list[0];
        targetModelLabel = list[1];
      } else if (result.type === 'vehicle') {
        targetModelLabel = '其他';
      }
      const targetModelObj = this.targetModel.find(item => item.label === targetModelLabel);
      const targetModel = targetModelObj?.value || '15'; // 没找到匹配项时默认"其他"选项
      this.$emit('rowClick', {
        targetFlag: targetFlag,
        targetModel: targetModel
      });
      this.$nextTick(() => {
        this.$emit('update:isShowTable', false);
        this.changeTableRow = null;
      });
    },
    tabChange () {
      if (!this.tableData[this.active].length) {
        this.getList();
      }
      if(!this.activeDeptMap[this.active].length) {
        this.activeDeptMap[this.active] = this.deptOptions;
      }
    },
    getPage () {
      const name = `table${this.active}`;
      const dom = this.$refs[name]?.[0];
      if(dom) {
        dom.pageChange(1);
      } else {
        this.getList();
      }
    },
    getList () {
      const name = `table${this.active}`;
      const txt = `head${this.active}`;
      const moreData = this.$refs[txt]?.[0].query || {};
      const params = this.$refs[name]?.[0].query || { size: 10, current: 1 };
      const result = this.tabList.find(item => item.value + '-' +item.catogory === this.active);
      this.dialogLoading = true;
      result.api({
        ...params,
        ...moreData,
        page: params.current - 1 // 适配分页列表参数不一致的情况
      }).then(res => {
        if (res.code === 200) {
          this.$set(this.tableData, this.active, res.data?.content || []);
          this.$set(this.totalPages, this.active, res.data?.total || 0);
        }
      }).finally(() => {
        this.dialogLoading = false;
      });
    }
  }

};
</script>
<style lang="less" scoped>
.confirm-table {
  width: 100%;
  .list-item {
    width: 100%;
    display: flex;
    &.head {
      .left,
      .right {
        flex: 1;
        text-align: center;
        background-color: #409eff;
        color: #fff;
        padding: 5px 10px;
      }
    }
    .left,
    .right {
      flex: 1;
      text-align: center;
      padding: 5px 10px;
    }
  }
}
</style>
