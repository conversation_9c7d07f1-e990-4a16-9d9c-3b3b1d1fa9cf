<template>
  <div class="layout">
    <div
      class="input-decorate el-input__inner"
      @click="treeOpen"
    >
      <div class="input-decorate-content">
        {{ valueDisplay }}
        <span
          v-show="!valueDisplay"
          class="input-decorate-hold el-input__clear"
        >请选择终端</span>
      </div>
      <i
        v-show="valueDisplay"
        class="el-icon-circle-close input-close-icon"
        @click="iconClear"
      />
    </div>
    <transition name="el-zoom-in-top">
      <div
        v-show="treeShow"
        class="layout-content"
        @click="(e)=>{e.stopPropagation();}"
      >
        <el-input
          v-show="!loading"
          ref="filterText"
          v-model="filterText"
          class="input-search"
          placeholder="搜索终端"
          size="small"
          clearable
        />
        <vue-easy-tree
          ref="tree"
          v-loading="loading"
          class="layout-tree"
          :data="deptOptions"
          node-key="id"
          height="500px"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        >
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node"
          >
            <el-radio
              v-if="data.type !== 'dept' && data.type !== 'dept_son'"
              v-model="currentValue"
              :label="data.id"
              @input="handleCheckChange(data)"
            >
              {{ node.label }}
            </el-radio>
            <span v-else>
              {{ node.label }}
            </span>
          </span>
        </vue-easy-tree>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    isFilter: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => ({
        deviceId: '',
        deviceType: ''
      })
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      deptOptions: [],
      valueDisplay: null,
      treeShow: false,
      filterText: '',
      timer: null, // 定时器
      currentValue: null,
      loading: false,
      deviceTypeEnum: {
        '1': 'rnss',
        '2': 'wear',
        '3': 'rdss',
        '4': 'monit',
        '5': 'pnt',
      }
    };
  },
  watch: {
    filterText: {
      handler(newVal) {
        this.debounce(newVal, 300);
      }
    },
    value: {
      handler(val) {
        this.currentValue = this.deviceTypeEnum[val.deviceType] + '-' + val.deviceId;
        if (this.deptOptions.length > 0) {
          this.setCurrentValue();
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getDept();
  },
  methods: {
    handleNodeClick(node, treeNode) {
      // 取消选中
      this.$refs.tree.setCurrentKey(null);
      // console.log(data);
    },
    debounce(newVal, delay) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.$refs.tree.filter(newVal);
      }, delay);
    },
    // 获取单位树
    getDept() {
      this.loading = true;
      this.$store.dispatch('GetVehicleTree').then(res => {
        if (this.isFilter) {
          let treeData = [];
          for (let child of res) {
            let filteredChild = this.filterTree(child);
            if (filteredChild) {
              treeData.push(filteredChild);
            }
          }
          this.deptOptions = treeData;
        } else {
          this.deptOptions = res;
        }
        if (this.currentValue) {
          this.setCurrentValue();
        }
        this.loading = false;
      }).catch(() => {
        this.deptOptions = [];
        this.loading = false;
      });
    },
    setCurrentValue(){
      this.$nextTick(() => {
        const node = this.$refs.tree.getNode(this.currentValue);
        if (node) {
          this.valueDisplay = node.data.label;
        }
      });
    },
    filterTree (tree) {
      // 基本情况：如果当前节点没有 children 或者是叶子节点
      if (!tree.children || tree.children.length === 0) {
        return tree.id.includes('rnss') ? tree : null;
      }
      // 遍历所有子节点
      let filteredChildren = [];
      for (let child of tree.children) {
        let filteredChild = this.filterTree(child);
        if (filteredChild) {
          filteredChildren.push(filteredChild);
        }
      }
      // 如果当前节点的子节点列表为空，检查当前节点是否满足条件
      if (filteredChildren.length === 0 && !tree.id.includes('rnss')) {
        return null;
      }
      // 构建新的节点，保留满足条件的子节点
      return {
        ...tree,
        children: filteredChildren
      };
    },
    // 清空按钮
    iconClear(e) {
      if (e) {
        e.stopPropagation();
      }
      this.valueDisplay = null;
      this.$emit('input', null);
      this.currentValue = null;
      this.filterText = '';
    },
    // 选择后触发
    handleCheckChange(data) {
      this.valueDisplay = data.label;
      let params = {
        deviceId: data.originData.deviceId,
        deviceType: data.originData.deviceType
      };
      this.$emit('input', params);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 关闭监听
    treeClose() {
      if (this.treeShow) this.treeShow = false;
      document.removeEventListener('click', this.treeClose);
    },
    // 显示单位树
    treeOpen(e) {
      e.stopPropagation();
      this.treeShow = !this.treeShow;
      if (this.treeShow) {
        document.addEventListener('click', this.treeClose);
      }
      this.$nextTick(() => {
        this.$refs.filterText.focus();
      });
    },
    // 清空
    clearAll() {
      this.valueDisplay = null;
      this.$emit('input', null);
      this.currentValue = null;
      this.filterText = '';
    },
  }
};
</script>

<style lang="less" scoped>
.layout {
  position: relative;
}

.input-decorate {
  //width: 156px;
  height: 32px;
  line-height: 32px;
  cursor: pointer;
  position: relative;
  border: 1px solid #bfbfbf;
}

.input-decorate-content {
  //width: 110px;
  width: 100%;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-right: 5px;
}

.input-decorate-hold {
  font-size: 13px;
  color: #c0c4cc;
}

.input-close-icon {
  position: absolute;
  right: 10px;
  top: 0;
  line-height: 32px;
  color: #c0c4cc;
}

.layout-content {
  position: absolute;
  top: 32px;
  left: 0;
  z-index: 2001;
  box-shadow: 0 0 8px 0 rgb(0 0 0 / 20%);
  // width: 400px;
  background-color: white;
}

.input-search {
  width: 100%;
}

.input-search ::v-deep .el-input__inner {
  border-radius: 0;
}

.layout-tree {
  min-width: 400px;
  height: 500px;
  // overflow-y: scroll;
}

</style>
