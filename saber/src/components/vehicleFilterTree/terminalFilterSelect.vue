<template>
  <div class="layout">
    <el-autocomplete
      v-model="filterText"
      :fetch-suggestions="terminalSearchHandle"
      :highlight-first-item="true"
      :trigger-on-focus="false"
      size="small"
      placeholder="请输入监控对象"
      clearable
      @select="terminalSelectHandle"
      @clear="terminalClearHandle"
    />
  </div>
</template>

<script>
import { targetFilter } from '@/api/base/terminalManage';
export default {
  props: {
    value: {
      type: Object,
      default: () => ({
        deviceId: '',
        deviceType: ''
      })
    },
    isTrack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterText: '',
      timer: null, // 定时器
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val?.targetName) {
          this.filterText = `${val.targetName} ( ${val.deptName} )`;
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    terminalSearchHandle(_val, _callback) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.getTerminalOptions(_val).then(res=>{
          _callback(res);
        });
      }, 500);
    },
    async getTerminalOptions (_val) {
      let optionsReturn = [];
      const query = {
        content: _val,
        trackable: this.isTrack ? 1 : undefined
      };
      const { code, data } = await targetFilter(query);
      if (code === 200 && data.resData) {
        for (let index = 0; index < data.resData.length && index <= 20; index++) {
          const element = data.resData[index];
          optionsReturn.push({
            value: `${element.targetName} ( ${element.deptName} )`,
            targetName: element.targetName,
            deviceId: element.deviceId,
            deviceType: element.deviceType,
            deptName: element.deptName
          });
        }
      }
      return optionsReturn;
    },
    // 清空按钮
    terminalClearHandle(e) {
      if (e) {
        e.stopPropagation();
      }
      this.$emit('input', null);
      this.filterText = '';
    },
    // 选择后触发
    terminalSelectHandle(data) {
      let params = {
        deviceId: data.deviceId,
        deviceType: data.deviceType
      };
      this.$emit('input', params);
    },
    // 清空
    clearAll() {
      this.$emit('input', null);
      this.filterText = '';
    },
  }
};
</script>

<style lang="less" scoped>

</style>
