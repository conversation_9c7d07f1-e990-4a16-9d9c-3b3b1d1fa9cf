<template>
    <el-dialog
      :visible.sync="msgVisible"
      :modal="false"
      title="错误提示"
      width="40%"
      :append-to-body="true"
      @close="handleClose"
      class="msg-dialog"
    >
      <u-table
        :data="msgData"
        use-virtual
        :row-height="54"
        :header-cell-style="{'text-align':'center'}"
        :cell-style="{'text-align':'center'}"
        style="width: 100%"
        height="325px"
      >
        <u-table-column
          prop="sort"
          label="序号"
          width="120"
        />
        <u-table-column
          prop="details"
          label="错误详情(列名称)"
        >
          
        </u-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </u-table>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="msgVisible = false"
        >关闭</el-button>
      </span>
    </el-dialog>
  </template>
  
  <script>
  export default {
    props: {
      msgData: {
        type: Array,
        required: true
      },
      isParent: {
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        msgVisible: false
      };
    },
    methods: {
      handleClose() {
        if(!this.isParent) {
          this.$EventBus.$emit('clearFileErr')
        } else {
          this.$emit('clearFileErr')
        }
      }
    }
  };
  </script>
  
  <style scoped>
  .tag{
    cursor: pointer;
  }
  .msg-dialog /deep/.el-dialog__body{
    padding: 5px;
    max-height: 335px;
    overflow: overlay;
  }
  </style>
  