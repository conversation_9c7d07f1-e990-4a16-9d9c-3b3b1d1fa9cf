<template>
  <div class="footer">
    <div class="tab">
      <div
        class="tab-item"
        :class="{'active-item': activeItem}"
        @click="activeItem = !activeItem"
      >
        <el-badge
          :value="null"
          :max="99"
          class="item"
        >
          <i class="el-icon-odometer"/>
          <div>运行分析</div>
        </el-badge>
      </div>
      <div
        class="tab-item"
        :class="{'active-item': !activeItem}"
        @click="activeItem = !activeItem"
      >
        <el-badge
          :value="null"
          :max="99"
          class="item"
        >
          <i class="el-icon-map-location"/>
          <div>点位回放</div>
        </el-badge>
      </div>
    </div>
    <div class="tab-container">
      <div
        v-show="activeItem"
        class="table"
      >
        <div
          ref="chart"
          class="echarts-box"
        />
      </div>
      <div
        v-show="!activeItem"
        class="table"
      >
        <u-table
          ref="uTable"
          use-virtual
          highlight-current-row
          inverse-current-row
          :data="data"
          :beautify-table="true"
          :header-cell-style="headerStyle"
          :border="false"
          row-height="54"
          :height="230"
          style="width: 100%;"
          @row-click="rowClick"
        >
          <u-table-column
            prop="customizeId"
            label="编号"
            width="75"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            label="终端类别"
            prop="deviceType"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ getEnumDictLabel("bdmDeviceType", scope.row.deviceType) }}
            </template>
          </u-table-column>
          <u-table-column
            label="终端类型"
            prop="deviceCategory"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ getEnumDictLabel("bdmDeviceType", scope.row.deviceCategory) }}
            </template>
          </u-table-column>
          <u-table-column
            label="序列号"
            prop="deviceUniqueId"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          />
          <u-table-column
            label="目标类型"
            prop="targetType"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ getEnumDictLabel("targetType", scope.row.targetType) | nullValueStr }}
            </template>
          </u-table-column>
          <u-table-column
            label="监控对象"
            prop="targetName"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
                {{ scope.row.targetName || $utils.emptymap.targetName }}
            </template>
          </u-table-column>
          <u-table-column
            label="定位时间"
            prop="time"
            show-overflow-tooltip
            width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ parseTime(scope.row.time) }}
              </span>
            </template>
          </u-table-column>
          <u-table-column
            label="速度(km/h)"
            prop="speed"
            show-overflow-tooltip
            min-width="100"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </u-table>
      </div>
    </div>
    <div class="progress-bar">
      <div class="progress-btn">
        <el-button
          type="primary"
          icon="el-icon-d-arrow-left"
          circle
          title="减慢速度"
          @click="slowAnimation"
        />
      </div>
      <div class="progress-btn">
        <el-button
          v-if="isStart"
          type="primary"
          icon="el-icon-video-play"
          circle
          @click="startAnimation"
        />
        <el-button
          v-else
          type="primary"
          icon="el-icon-video-pause"
          circle
          @click="onPlayerPause"
        />
      </div>
      <div class="progress-btn">
        <el-button
          type="primary"
          icon="el-icon-d-arrow-right"
          circle
          title="加快速度"
          @click="fastAnimation"
        />
      </div>
      <div class="progress-btn">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          circle
          @click="stopAnimation"
        />
      </div>
      <div class="slider-item">
        <el-slider
          v-model="percentageVal"
          :max="maxPercentage"
          :min="0"
          @input="handlePercentageInput"
          @mousedown.native="handleMouseDown"
          @change="handlePercentageChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/api/utils/share';
export default {
  props: {
    pointList: {
      type: Array,
      default: ()=>{return[];}
    },
    dict: {
      type: Object,
      default: ()=>{return{};}
    },
    percentage: {
      type: Number,
      default: 0
    },
    isStart: {
      type: Boolean,
      default: true
    },
    playSpeed: {
      type: Number,
      default: 1300
    },
    timerSpeed: {
      type: Number,
      default: 0
    }
  },
  data(){
    return{
      activeItem: true,
      chartElement: null,
      chartData: [],
      isOpen: true,
      data: [],
      headerStyle: {
        'background-color': '#87CEFA',
        'font-size': '14px'
      },
      xData: [],
      maxPercentage: 100,
      percentageVal: 0,
      isRoll: false
    };
  },
  watch:{
    // $attrs: {
    //   handler(newVal){
    //     if (!newVal['active-index'] && this.isOpen) {
    //       this.$nextTick(()=>{
    //         this.init();
    //         this.isOpen = false;
    //       });
    //     }
    //   }
    // },
    pointList: {
      handler (newVal) {
        this.data = newVal;
        this.xData = newVal.map((item)=>{
          return this.parseTime(item.time);
        });
        this.chartData = newVal.map((item)=>{
          return item.speed;
        });
        this.init();
        this.maxPercentage = this.pointList.length - 1;
      },
      deep: true
    },
    percentage: {
      handler(newVal) {
        this.isRoll = false;
        this.percentageVal = newVal;
      }
    }
  },
  methods:{
    // 减慢播放速度
    slowAnimation() {
      if (this.playSpeed === 2500) {
        return;
      }
      this.$emit('update:playSpeed', this.playSpeed + 400);
      // this.$emit('update:timerSpeed', this.timerSpeed + 150);
      if (!this.isStart) {
        this.onPlayerPause();
        this.$nextTick(() => {
          this.$emit('startAnimation');
        });
      }
    },
    // 加快播放速度
    fastAnimation() {
      // 最高速度限制
      if (this.playSpeed === 100) {
        return;
      }
      this.$emit('update:playSpeed', this.playSpeed - 400);
      // this.$emit('update:timerSpeed', this.timerSpeed - 150);
      if (!this.isStart) {
        this.onPlayerPause();
        this.$nextTick(() => {
          this.$emit('startAnimation');
        });
      }
    },
    // 拖动进度条触发
    handleMouseDown() {
      this.isRoll = true;
      console.log("鼠标点击滑块事件");
    },
    // 滑动时
    handlePercentageInput(val) {
      if (this.isRoll) {
        this.$emit('editPosition', val);
        this.$emit('carMoving', val);
      }
    },
    // 滑动完成后
    handlePercentageChange(val) {
      this.$emit('update:percentage', val);
      this.$emit('trackRangeMove');
      if (!this.isStart) {
        this.onPlayerPause();
        this.$nextTick(() => {
          this.$emit('startAnimation');
        });
      }
    },
    // 开始动画
    startAnimation () {
      this.$emit('startAnimation');
    },
    // 暂停动画
    onPlayerPause() {
      this.$emit('onPlayerPause');
    },
    // 终止动画
    stopAnimation () {
      this.$emit('stopAnimation');
    },
    // 行点击时
    rowClick (row) {
      this.$emit('goToPoint', row);
    },
    // 初始化echarts
    init(){
      this.chartElement = this.$echarts.init(this.$refs.chart);
      this.chartElement.setOption({
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.xData,
          axisLabel: {
            formatter: function(value) {
              const list = value.split(' '); // 日期根据空格切割
              let str = list[0] + '\n' + list[1];
              return str;
            }
          }
        },
        grid: {
          x: 40,
          y: 20,
          x2: 35,
          y2: 30,
          borderWidth: 1,
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            type: 'line',
            areaStyle: {},
            smooth: true,
            name: '速度(km/h)',
            data: this.chartData
          }
        ]
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.footer{
  width: calc(100% - 310px);
  height: 250px;
  display: flex;
  padding: 10px;
  background: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  box-shadow: 0 0 5px #ccc;
  .tab{
    width: 50px;
    background: #f5f5f5;
    height: 100%;
    align-items: center;
    .tab-item{
      color: #777676;
      height: 50%;
      text-align: center;
      font-size: 14px;
      padding: 0 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      border-radius: 5px;
      vertical-align: middle;
      position: relative;
      cursor: pointer;
      i{
        font-size: 20px;
      }
    }
    .active-item{
      background: #3885f2;
      color: #fff;
    }
  }
  .tab-container{
    width: calc(100% - 50px);
    padding: 0 10px;
    .table{
      height: 100%;
      ::v-deep .el-table .el-table__cell{
        padding: 5px 0;
      }
    }
  }
}
.echarts-box{
    width: 100% !important;
    height: 100% !important;
}
::v-deep.current-row{
    color: green;
  }
.progress-bar{
  width: 100%;
  position: absolute;
  top: -50px;
  left: 0;
  background: rgba(0,0,0,.1);
  border-radius: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px
}
.slider-item{
  margin: 0 10px;
  width: calc(100% - 80px);
}
.progress-btn{
  padding: 5px;
}
</style>
