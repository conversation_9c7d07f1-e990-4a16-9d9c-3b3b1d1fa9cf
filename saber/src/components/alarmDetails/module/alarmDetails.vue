<template>
  <div class="details">
    <div class="left-map">
      <MapWidget
        ref="MapWidget"
        :class="{'map-info': alarmInfo.limitSpeed}"
        :tool-config="toolConfig"
        @mapLoaded="mapLoaded"
      />
    </div>
    <!-- <div
      v-if="chechoutAlarmType(alarmInfo.limitSpeed)"
      class="speed-limit"
    >
      <div class="speed-number">
        <span style="font-size: 40px">{{ alarmInfo.limitSpeed }}</span>
      </div>
      <div class="speed-title">限速行驶</div>
    </div> -->
    <!-- 右侧告警信息 -->
    <AlarmInfo
      ref="alarmInfo"
      :alarm-info="Object.keys(alarmInfo) ? alarmInfo : alarmData"
      :dict="dict"
      v-bind="$attrs"
      v-on="$listeners"
    />
    <!-- 底部表格 -->
    <AlarmTable
      :alarmTable="alarmTable"
      :dict="dict"
    />
  </div>
</template>

<script>
import { details, alarmAttach, detailList } from '@/api/monitoring/realTimeMonitoring';
import { vehicleAddress } from '@/api/monitoring/info.js';
import MapWidget from '../../map/MapWidgetAMap.vue';
import AlarmInfo from './alarmInfo.vue';
import AlarmTable from './alarmTable.vue';
import { parseTime } from '@/api/utils/share';
export default {
  components:{
    MapWidget, AlarmInfo, AlarmTable
  },
  props:{
    alarmData: {
      type: Object,
      default: ()=>{return {};}
    },
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
  },
  data(){
    return{
      mapEl: null,
      map: null,
      AMap: null,
      readyState: 0,
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false, // 工具栏
        showMapLabel: false // 地图标注
      }, // 控制工具按钮
      infoWindowContent: null,
      infoWindow: null,
      dataDetail: {
        longitude: 104.0789581,
        latitude: 36.9358958,
        targetName: '新F57322',
        maxSpeed: '70km/h',
        speedLimit: '70km/h',
        startAlarmTime: '2023-06-01 11:15:40',
        startAlarmAddress: '甘肃省白银市景泰县中泉镇白水村民委员会西926米217省道北5米'
      },
      trackPointGroups: null,
      alarmPointGroups: null,
      trackPointGroupsSub: null,
      polyline: null,
      // 单个点文本层
      signalGroups: null,
      alarmInfo: {},
      alarmPointList: [], // 告警点列表
      alarmTable: [],
      // 时间速度层
      timeSpeedGroups: null,
    };
  },
  watch: {
    readyState (v) {
      if (v === 2) {
        this.afterMap();
        this.setTrackPoint(this.alarmPointList);
        this.setTimeSpeed(this.alarmPointList);
      }
    },
    alarmData:{
      handler(){
        this.getAlarmDetails('start');
      },
      deep: true,
      immediate: true
    }
  },
  methods:{
    /**
     * 时间速度加载
     */
    setTimeSpeed (currentData) {
      if (!currentData.length) {
        return;
      }
      this.timeSpeedGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      for (let i = 0; i < currentData.length; i++) {
        if (this.isAlarmPoint(currentData[i])) {
          let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
          let timeSpeedText = new this.AMap.LabelMarker({
            position: position,
            text: {
              content: parseTime(currentData[i].time) + ', ' + this.alarmInfo.alarmType, // 直接展示告警详情的告警类型
              direction: 'right',
              offset: [10, -18],
              style: {
                'fontSize': 12,
                'backgroundColor': 'rgba(255, 255, 255, 0.1)',
                'fillColor': '#f41e1e',
                'strokeColor': 'white',
                'strokeWidth': 4
              }
            },
            rank: 2
          });
          this.timeSpeedGroups.add(timeSpeedText);
        }
      }
      this.map.add(this.timeSpeedGroups);
    },
    // 获取告警详情
    async getAlarmDetails(type){
      const form = {
        ids: [this.alarmData.id]
      };
      await details(form).then(res=>{
        if (res.data) {
          this.alarmInfo = res.data;
        }
        if (this.alarmInfo?.handleTime) {
          this.alarmTable = [{
            dealMeasures: this.alarmInfo?.handleMeasures,
            dealContent: this.alarmInfo?.handleContent,
            createTime: this.alarmInfo?.handleTime,
            createUserName: this.alarmInfo?.handler
          }];
        } else {
          this.alarmTable = [];
        }
      });
      if (type === 'start') {
        const params = {
          deviceId: this.alarmData.deviceId,
          deviceType: this.alarmData.deviceType
        };
        this.getTrackData(params);
      }
    },
    // 获取轨迹数据
    async getTrackData(form){
      // 请求告警轨迹点
      const query = {
        ids: [this.alarmData.id]
      };
      let result = { };
      try {
        // 车辆轨迹点
        result = await detailList(query);
      } catch (error) {
        console.log('轨迹接口请求失败');
      }
      // 防止出现接口请求缓慢的情况, 例如先打开粤A12345的告警详情界面, 在轨迹没请求完成之前退出页面再次进入粤A14725的页面会导致页面会绘制两个车辆的轨迹
      if (this.alarmData.deviceId !== form.deviceId || this.alarmData.deviceType !== form.deviceType) {
        return;
      }
      if (result.code === 200 && result.data?.length) {
        for (let index = 0; index < result.data.length; index++) {
          const element = result.data[index];
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        this.alarmPointList = this.$utils.wgs84togcj02Batch(result.data);
      } else {
        this.alarmPointList = [];
        this.$message.warning('查询告警点位为空, 请联系管理员查看处理');
      }
      if (this.polyline) {
        this.clearMap(); // 以防万一, 再次执行清除轨迹操作(特殊情况下会造成多段轨迹同时存在地图的问题, 例如进入详情页面在轨迹接口未加载完成的时候退出页面再次进入)
        this.$nextTick(()=>{
          this.afterMap();
          this.setTrackPoint(this.alarmPointList);
          this.setTimeSpeed(this.alarmPointList);
        });
      }else{
        this.readyState++;
      }
      // 默认展示告警附件(clearMap会清除附件, 因此清除完后再进行赋值)
      if (this.alarmPointList.length) {
        for (let index = 0; index < this.alarmPointList.length; index++) {
          const element = this.alarmPointList[index];
          if (element.appendixId && (element.img1 || element.img2 || element.img3 || element.mp4)) {
            this.$refs.alarmInfo.accessoryHandle(element);
            break;
          }
        }
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    // 获取处理措施
    getMeasuresLabel(dictName, val){
      const valList = val.split(',');
      let labelList = valList.map(element => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][element]) {
          return this.dict.dict[dictName][element].label;
        }
      });
      let str = labelList.toString();
      return str;
    },
    clearMap(){
      // this.$refs.MapWidget.clearAll(); // TODO 不生效
      if (this.polyline) {
        this.map.remove(this.polyline);
        this.trackPointGroups && this.map.remove(this.trackPointGroups);
        // this.map.remove(this.alarmPointGroups);
        this.trackPointGroupsSub && this.map.remove(this.trackPointGroupsSub);
        this.timeSpeedGroups && this.map.remove(this.timeSpeedGroups);
        this.clearSignalPointText();
        this.$refs.alarmInfo.clearAccessory();
      }
    },
    // 获取告警附件
    async getAlarmAttach (currentData) {
      if (currentData.appendixId) {
        this.$refs.alarmInfo.accessoryHandle(currentData);
      } else {
        this.$refs.alarmInfo.accessoryHandle({});
      }
      // if (currentData.appendixId) {
      //   const query = {
      //     appendixId: currentData.appendixId
      //   };
      //   const { code, data } = await alarmAttach(query);
      //   if (code === 200 && data) {
      //     this.$refs.alarmInfo.accessoryHandle(data);
      //   } else {
      //     this.$refs.alarmInfo.accessoryHandle({});
      //   }
      // } else {
      //   this.$refs.alarmInfo.accessoryHandle({});
      // }
    },
    /**
     * 定位点加载
     */
    setTrackPoint (currentData) {
      if (!currentData.length) {
        return;
      }
      this.trackPointGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      this.trackPointGroupsSub = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      [0, currentData.length - 1].forEach((i, index) => {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyleSub(index === 0 ? 's' : 'e');
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [28, 36]
          }
        });
        pointMarker.on('click', (e) => {
          this.setSinglePointText(currentData[i]);
        });
        this.trackPointGroupsSub.add(pointMarker);
        if (index === 0) {
          this.$refs.MapWidget.setFitView();
        }
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyle(currentData[i]);
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: this.isAlarmPoint(currentData[i]) ? [28, 30] : [10, 12]
          },
          rank: this.isAlarmPoint(currentData[i]) ? 3 : 1,
          zIndex: this.isAlarmPoint(currentData[i]) ? 5 : 1,
        });
        pointMarker.on('click', (e) => {
          // 处理定位
          this.setSinglePointText(currentData[i]);
          if (this.isAlarmPoint(currentData[i])) {
            this.getAlarmAttach(currentData[i]);
          }
        });
        this.trackPointGroups.add(pointMarker);
      }
      this.map.add(this.trackPointGroups);
      this.map.add(this.trackPointGroupsSub);
    },
    getIconStyle (data) {
      let icon = data.speed || this.alarmData.targetType !== '车辆' ? require('@/assets/images/car/track-run.png') : require('@/assets/images/car/track-stop.png');
      if (this.isAlarmPoint(data)) {
        icon = require('@/assets/images/car/track-alarm.png');
      }
      return icon;
    },
    isAlarmPoint (data) {
      if (data.time >= this.$moment(this.alarmData.startTime).unix() && (!this.alarmData.endTime || data.time <= this.$moment(this.alarmData.endTime).unix())) {
        return true;
      } else {
        return false;
      }
    },
    clearSignalPointText () {
      if (this.signalGroups) {
        this.signalGroups.clearOverlays();
        this.map.remove(this.signalGroups);
        this.signalGroups = null;
      }
    },
    async setSinglePointText (currentData) {
      let locAddr = '';
      const query = {
        lon: Number(currentData.longitudeTable),
        lat: Number(currentData.latitudeTable)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          locAddr = res.data;
        }
      });
      this.clearSignalPointText();
      let markerCollection = [];
      let infoWindow = null;
      let position = new this.AMap.LngLat(currentData.longitude, currentData.latitude);
      var info = [];
      info.push("<div class='input-card content-window-card' style='width: 435px;>");
      info.push("<div style='padding:7px 0px 15px 0px;><h4>" + `${this.alarmInfo.targetName}` + '</h4>');
      info.push("<p class='input-item' style='margin: 10px'>告警时间：" + `${parseTime(currentData.time)}`);
      info.push("<p class='input-item' style='margin: 10px'>速度：" + `${currentData.speed}km/h`);
      // info.push("<p class='input-item' style='margin: 10px'>经度：" + `${currentData[i].longitudeTable}`);
      // info.push("<p class='input-item' style='margin: 10px'>纬度：" + `${currentData[i].latitudeTable}`);
      info.push("<p class='input-item' style='margin: 10px'>地址：" + `${locAddr}`);
      infoWindow = new this.AMap.InfoWindow({
        position: position,
        anchor: 'bottom-center',
        content: info.join(''),
        offset: new this.AMap.Pixel(0, -30)
      });
      markerCollection.push(infoWindow);
      this.signalGroups = new this.AMap.OverlayGroup(markerCollection);
      this.map.add(this.signalGroups);
    },
    getIconStyleSub (i) {
      let icon;
      if (i === 's') {
        icon = require('@/assets/images/car/track-start.png');
      } else if (i === 'e') {
        icon = require('@/assets/images/car/track-end.png');
      }
      return icon;
    },
    afterMap () {
      let _that = this.mapEl;
      let path = _that.formatPathData(this.alarmPointList);
      this.polyline = _that.getPath(path, {});
      _that.map.add(this.polyline);
    },
    mapLoaded(v){
      this.mapEl = v;
      this.map = this.mapEl.map;
      this.AMap = this.mapEl.AMap;
      this.readyState++;
    },
    //处理后台会出现给限速值的情况
    // chechoutAlarmType(limit){
    //   let arr = [2,14,102,111,161]
    //   if(!limit || arr.includes(this.alarmInfo.alarmType)) return false
    //   return true
    // }
  }
};
</script>

<style lang="less" scoped>
.details{
    width: 100%;
    height: 100%;
}
.left-map{
    width: calc(100% - 290px);
    height: calc(100% - 190px);
}
.speed-limit{
  position: absolute;
  right: 310px;
  top: 0;
  background: #fff;
  z-index: 99;
  .speed-number{
    height: 70px;
    width: 70px;
    border-radius: 50%;
    border: 6px solid red;
    margin: 10px 10px;
    color: #000;
    font-size: 50px;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .speed-title{
    background: red;
    color: #fff;
    text-align: center;
    font-size: 15px;
    letter-spacing: 4px;
    font-weight: 600;
    padding: 2px 0;
  }
}
.map-info{
    ::v-deep .map-tool-bar{
      right: 120px;
    }
}
</style>
