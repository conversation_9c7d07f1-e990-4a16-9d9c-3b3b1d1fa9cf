<template>
  <div class="track-info">
    <div class="left-map">
      <MapWidget
        ref="MapWidgetTrack"
        class="map-info"
        :tool-config="toolConfig"
        @mapLoaded="mapLoaded"
      />
    </div>
    <CarTrackInfo
      ref="carTrackInfo"
      v-bind="$attrs"
      :alarm-data="alarmData"
      :max-search-day="maxSearchDay"
      @getTrajectoryData="getTrajectoryData"
      @pointHandle="pointHandle"
      @exportHandle="exportHandle"
    />
    <CarTrackTable
      ref="carTrackTable"
      v-bind="$attrs"
      :point-list="pointList"
      :percentage.sync="percentage"
      :is-start.sync="isStart"
      :play-speed.sync="playSpeed"
      :timer-speed.sync="timerSpeed"
      @goToPoint="goToPoint"
      @startAnimation="startAnimation"
      @stopAnimation="stopAnimation"
      @editPosition="editPosition"
      @onPlayerPause="onPlayerPause"
      @trackRangeMove="trackRangeMove"
      @carMoving="carMoving"
    />
  </div>
</template>
<script>
import MapWidget from '../../map/MapWidgetAMap.vue';
import CarTrackInfo from './carTrackInfo.vue';
import CarTrackTable from './carTrackTable.vue';
import { queryTracking, exportTracking } from '@/api/monitoring/track.js';
import { parseTime } from '@/api/utils/share';
import { vehicleAddress } from '@/api/monitoring/info.js';
import { getSystemParam } from '@/api/user';

export default {
  components:{
    MapWidget, CarTrackInfo, CarTrackTable
  },
  props: {
    alarmData: {
      type: Object,
      default: ()=>{return{};}
    },
    activeIndex: {
      type: Boolean,
      default: true
    }
  },
  data(){
    return{
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false, // 工具栏
        showMapLabel: false // 地图标注
      }, // 控制工具按钮
      isOpen: true,
      mapEl: null,
      map: null,
      AMap: null,
      readyState: 0,
      trackPointGroups: null,
      trackPointGroupsSub: null,
      polyline: null,
      // 单个点文本层
      signalGroups: null,
      pointList: [], // 轨迹点
      // 时间速度层
      timeSpeedGroups: null,
      // 车辆marker
      carMarker: null,
      lineArr: null,
      percentage: 0, // 轨迹进度条
      isStart: true, // 控制按钮
      schedule: 0, // 轨迹播放的index
      presentLocation: [], // 轨迹播放时最新的经纬度数组
      timer: null,
      playSpeed: 1300, // 车辆移动速度
      // timerSpeed: 600 // 定时器时间
      timerSpeed: 0, // 定时器时间
      labelTypeData: {},
      maxSearchDay: 3
    };
  },
  watch: {
    alarmData: {
      handler (newVal) {
        this.isOpen = true;
      },
      deep: true
    },
    activeIndex: {
      handler (newVal) {
        if (!newVal && this.isOpen) {
          this.isOpen = false;
          this.alarmData.isFilter = this.alarmData.deviceCate === 201 ? ['batch', 'invalid'] : ['invalid'];
          this.getTrajectoryData(this.alarmData);
          this.$nextTick(()=>{
            this.$refs.carTrackInfo.initValue();
            this.$refs.carTrackInfo.trackHandle();
          });
        }
      },
    },
    readyState (v) {
      if (v >= 2) {
        this.afterMap();
        // this.setTrackPoint(this.pointList);
        this.setTrackPointSub(this.pointList);
        // this.setTimeSpeed(this.pointList);
      }
    },
  },
  mounted () {
    this.getTrackMaxDay();
  },
  methods:{
    // 获取轨迹最大可查询天数
    getTrackMaxDay () {
      getSystemParam('trackSearchDay').then(res => {
        const { code, data } = res?.data;
        if (code === 200) {
          this.maxSearchDay = Number(data);
        }
      }).catch(err => {

      });
    },
    // 修改车辆位置
    editPosition(val) {
      if (this.carMarker) {
        this.carMarker.stopMove(); // 终止动画
      }
      const position = new this.AMap.LngLat(this.pointList[val].longitude, this.pointList[val].latitude);
      this.carMarker.setPosition(position);
      this.map.setCenter(position);
    },
    // 终止动画
    stopAnimation () {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.carMarker.stopMove(); // 终止动画
      this.isStart = true;
      this.percentage = 0;
      this.schedule = 0;
      this.presentLocation = [];
      this.$refs.carTrackTable.$refs.uTable.setCurrentRow(this.pointList[0]);
      // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
      // 54为单位格的高度，-3为了将滚动条调整到视图下方位置
      // 1 = this.pointList[0].customizeId 因为这里还没有给customizeId赋值, 所以直接使用1
      this.$refs.carTrackTable.$refs.uTable.pagingScrollTopLeft((1 - 3) * 54, 0);
      if (this.pointList.length) {
        const position = new this.AMap.LngLat(this.pointList[0].longitude, this.pointList[0].latitude);
        this.carMarker.setPosition(position);
        this.map.setCenter(position);
      }
    },
    // 拖拽进度条后
    trackRangeMove() {
      this.presentLocation = [];
    },
    // 暂停
    onPlayerPause() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.carMarker.stopMove(); // 终止动画
      this.isStart = true;
    },
    // 播放动画
    startAnimation () {
      this.isStart = false;
      // 高德轨迹播放
      // percentage有值时代表拖动了进度条或暂停后继续播放, 继续播放轨迹
      if ((this.percentage && this.percentage !== this.pointList.length - 1) || this.presentLocation.length) {
        let list;
        this.schedule = this.percentage;
        if (this.presentLocation.length) { // 手动点击暂停或控制速度时
          list = this.lineArr.slice(this.percentage + 1);
          list.unshift(this.presentLocation);
        } else { // 滑动进度条时
          list = this.lineArr.slice(this.percentage);
        }
        this.$nextTick(() => {
          this.carMarker.moveAlong(list, {
            duration: this.playSpeed,
            autoRotation: true
          });
        });
      } else {
        this.schedule = 0;
        this.carMarker.moveAlong(this.lineArr, {
          duration: this.playSpeed,
          autoRotation: true
        });
        this.carMoving(0);
      }
    },
    carMoving (_index) {
      if (_index && this.pointList[_index - 1].speed !== this.pointList[_index].speed) {
        const markerDom = this.carMarker.dom;
        let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
        this.labelTypeData.bgUrl = this.pointList[_index].speed ? '/bdsplatform/static/images/pic/move.png' : '/bdsplatform/static/images/pic/static.png';
        bgDom.style.backgroundImage = `url(${this.labelTypeData.bgUrl})`;
      }
      this.$refs.carTrackTable.$refs.uTable.setCurrentRow(this.pointList[_index]);
      // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
      // 54为单位格的高度，-3为了将滚动条调整到视图下方位置
      this.$refs.carTrackTable.$refs.uTable.pagingScrollTopLeft((this.pointList[_index]?.customizeId - 3) * 54, 0);
    },
    // 导出轨迹点
    exportHandle (data) {
      let qurey = {
        deviceId: BigInt(data.deviceId),
        startTime: this.$moment(data.startTime).unix(),
        endTime: this.$moment(data.endTime).unix(),
        deviceType: data.deviceType,
        targetId: BigInt(data.targetId),
        targetType: data.targetType
      };
      if (data.isFilter?.length) {
        for (let index = 0; index < data.isFilter.length; index++) {
          const element = data.isFilter[index];
          qurey[element] = 1;
        }
      }
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(255, 255, 255, 0.7)'
      });
      exportTracking(qurey).then(res => {
        loading.close();
        if (res.data) {
          this.$download(res.data, 'path');
        } else {
          this.$msgbox.alert('导出失败，请重新查询轨迹', '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          });
        }
      }).catch(res => {
        loading.close();
        console.log(res);
      });
    },
    // 显示/隐藏轨迹点
    pointHandle (val) {
      if (val) {
        this.trackPointGroups ? this.trackPointGroups.show() : this.setTrackPoint(this.pointList);
      }else{
        if (this.trackPointGroups) {
          this.trackPointGroups.hide();
        }
      }
    },
    // 获取轨迹数据
    getTrajectoryData (data) {
      this.clearMap();
      let qurey = {
        deviceId: BigInt(data.deviceId),
        deviceType: data.deviceType,
        targetId: BigInt(data.targetId),
        targetType: data.targetType,
        startTime: data.startTime ? this.$moment(data.startTime).unix() : this.$moment(data.alarmTime).unix(),
        endTime: data.endTime ? this.$moment(data.endTime).unix() : this.$moment(data.alarmEndTime).unix()
      };
      if (data.isFilter?.length) {
        for (let index = 0; index < data.isFilter.length; index++) {
          const element = data.isFilter[index];
          qurey[element] = 1;
        }
      }
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTracking(qurey).then((res)=>{
        loading.close();
        // 检查路线
        if (!res.data || !res.data.locations?.length) {
          this.$message({
            message: '无路线数据',
            type: 'error'
          });
          return;
        }
        let i = 1;
        res.data.locations.forEach(item => {
          item.customizeId = i++;
          item.targetName = res.data.targetName;
          item.deviceType = res.data.deviceType;
          item.deviceCategory = res.data.deviceCategory;
          item.deviceUniqueId = res.data.deviceUniqueId;
          item.targetType = res.data.targetType;
          item.latitudeTable = item.latitude;
          item.longitudeTable = item.longitude;
        });
        this.pointList = this.$utils.wgs84togcj02Batch(res.data.locations);
        this.readyState++;
      }).catch(err=>{
        loading.close();
      });
    },
    goToPoint (row) {
      this.map.setZoomAndCenter(18, [row.longitude, row.latitude]);
      this.setSinglePointText(row);
    },
    /**
     * 时间速度加载
     */
    setTimeSpeed (currentData) {
      this.timeSpeedGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let colorStyle = this.getColor(currentData[i]);
        let timeSpeedText = new this.AMap.LabelMarker({
          position: position,
          text: {
            content: this.parseTime(currentData[i].alarmTime) + ', ' + currentData[i].speed + 'km/h',
            direction: 'right',
            offset: [6, -18],
            style: {
              'fontSize': 12,
              'backgroundColor': 'rgba(255, 255, 255, 0.1)',
              'fillColor': colorStyle,
              'strokeColor': 'white',
              'strokeWidth': 4
            }
          }
        });
        this.timeSpeedGroups.add(timeSpeedText);
      }
      this.map.add(this.timeSpeedGroups);
    },
    /**
     * 开始结束加载
     */
    setTrackPointSub(currentData) {
      this.trackPointGroupsSub = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      [0, currentData.length - 1].forEach((i, index) => {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyleSub(index === 0 ? 's' : 'e');
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [28, 36]
          }
        });
        pointMarker.on('click', (e) => {
          this.setSinglePointText(currentData[i]);
        });
        this.trackPointGroupsSub.add(pointMarker);
        if (index === 0) {
          this.$refs.MapWidgetTrack.setFitView();
        }
      });
      this.map.add(this.trackPointGroupsSub);
    },
    /**
     * 定位点加载
     */
    setTrackPoint (currentData) {
      this.trackPointGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyle(currentData[i]);
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [18, 20]
          }
        });
        pointMarker.on('click', (e) => {
          this.$refs.carTrackTable.$refs.uTable.setCurrentRow(currentData[i]);
          // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
          // 54为单位格的高度，-3为了将滚动条调整到视图下方位置
          this.$refs.carTrackTable.$refs.uTable.pagingScrollTopLeft((currentData[i].customizeId - 3) * 54, 0);
          // 处理定位
          this.setSinglePointText(currentData[i]);
        });
        this.trackPointGroups.add(pointMarker);
      }
      this.map.add(this.trackPointGroups);
    },
    getIconStyle (data) {
      let icon = data.speed || this.alarmData.targetType !== '车辆' ? require('@/assets/images/car/track-run.png') : require('@/assets/images/car/track-stop.png');
      if (data.alarmType) {
        icon = require('@/assets/images/car/track-alarm.png');
      }
      return icon;
    },
    clearSignalPointText () {
      if (this.signalGroups) {
        this.signalGroups.clearOverlays();
        this.map.remove(this.signalGroups);
        this.signalGroups = null;
      }
    },
    // 开始结束图标
    getIconStyleSub (i) {
      let icon;
      if (i === 's') {
        icon = require('@/assets/images/car/track-start.png');
      } else if (i === 'e') {
        icon = require('@/assets/images/car/track-end.png');
      }
      return icon;
    },
    async setSinglePointText (currentData) {
      // if (this.showTimeSpeed) {
      //   this.showTimeSpeed = false;
      // }
      let locAddr = '';
      const query = {
        lon: Number(currentData.longitudeTable),
        lat: Number(currentData.latitudeTable)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          locAddr = res.data;
        }
      });
      this.clearSignalPointText();
      let markerCollection = [];
      let infoWindow = null;
      let position = new this.AMap.LngLat(currentData.longitude, currentData.latitude);
      var info = [];
      info.push("<div class='input-card content-window-card' style='width: 435px;>");
      info.push("<div style='padding:7px 0px 15px 0px;><h4>" + `${this.alarmData.targetName}` + '</h4>');
      info.push("<p class='input-item' style='margin: 10px'>定位信息：" + `${currentData.speed}` + 'km/h,' + ',' + `${this.getDirection(currentData.bearing)}` + ',' + `${this.handlePosition(currentData.longitudeTable)}` + ',' + `${this.handlePosition(currentData.latitudeTable)}`);
      info.push("<p class='input-item' style='margin: 10px'>总里程：" + `${currentData.mileage}` + 'km');
      info.push("<p class='input-item' style='margin: 10px'>定位时间：" + `${this.parseTime(currentData.time)}`);
      info.push("<p class='input-item' style='margin: 10px'>地址：" + `${locAddr}`);

      infoWindow = new this.AMap.InfoWindow({
        position: position,
        anchor: 'bottom-center',
        content: info.join('')
      });
      markerCollection.push(infoWindow);
      this.signalGroups = new this.AMap.OverlayGroup(markerCollection);
      this.map.add(this.signalGroups);
    },
    getDirection (bearing) {
      if (bearing === 0) {
        return '正北';
      }
      if (bearing) {
        switch (true) {
        case bearing === 90:
          return '正东';
        case bearing === 180:
          return '正南';
        case bearing === 270:
          return '正西';
        case bearing > 0 && bearing < 90:
          return '东北';
        case bearing > 90 && bearing < 180:
          return '东南';
        case bearing > 180 && bearing < 270:
          return '西南';
        case bearing > 270 && bearing < 360:
          return '西北';
        default:
          return '无';
        }
      }
    },
    getColor (item) {
      let color = item.running ? '#036eb8' : '#f4a11e';
      if (item.alarmType) {
        color = '#f41e1e';
      }
      return color;
    },
    clearMap(){
      if (this.polyline) {
        // this.$refs.MapWidget.clearAll(); // TODO 不生效
        this.map.remove(this.polyline);
        if (this.trackPointGroups) {
          this.trackPointGroups.clear();
          this.map.remove(this.trackPointGroups);
          this.trackPointGroups = null;
        }
        if (this.trackPointGroupsSub) {
          this.trackPointGroupsSub.clear();
          this.map.remove(this.trackPointGroupsSub);
          this.trackPointGroupsSub = null;
        }
        this.map.remove(this.carMarker);
        // this.map.remove(this.timeSpeedGroups);
        this.clearSignalPointText();
        this.stopAnimation();
        this.clearData();
      }
    },
    clearData() {
      this.pointList = []; // 清空点位
      this.playSpeed = 1300;
      // this.timerSpeed = 600;
      this.presentLocation = [];
      this.$refs?.carTrackInfo.clearData();
    },
    afterMap () {
      let _that = this.mapEl;
      let path = _that.formatPathData(this.pointList);
      this.polyline = _that.getPath(path, {});
      _that.map.add(this.polyline);
      const position = new _that.AMap.LngLat(this.pointList[0].longitude, this.pointList[0].latitude);
      this.labelTypeData = {
        iconUrl: this.judgeTerminalIcon(this.pointList[0]),
        bgUrl: '/bdsplatform/static/images/pic/static.png',
        iconWidth: 50,
        iconHeight: 50
      };
      this.carMarker = new _that.AMap.Marker({
        position: position,
        offset: new _that.AMap.Pixel(-50 / 2, -50 / 2),
        content: `<div style="position: relative;">
                      <div class="follow-marker-bg" style="position: absolute; width: 100%; height: 100%; background-image: url('${this.labelTypeData.bgUrl}'); background-size: 100%;"></div>
                      <img src="${this.labelTypeData.iconUrl}" style="display: block; width: ${this.labelTypeData.iconWidth}px; height: ${this.labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`
      });
      this.carMarker.dom.classList.add("follow-marker");
      _that.map.add(this.carMarker);
      this.carMarker.on('moving', (e)=>{
        const markerDom = e.target.dom;
        let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
        bgDom.style.transform = `rotate(${e.target['_style'].rotate}deg)`;
        this.presentLocation = [e.passedPath[e.passedPath.length - 1].lng, e.passedPath[e.passedPath.length - 1].lat];
        // 设置地图位置
        if (!this.isInBound(this.presentLocation)) {
          this.map.panTo(this.presentLocation);
        }
        if (this.percentage !== e.passedPath.length - 2 + this.schedule) {
          this.percentage = e.passedPath.length - 2 + this.schedule;
          this.carMoving(this.percentage);
        }
      });
      this.carMarker.on('movealong', ()=>{
        if (this.percentage !== this.lineArr.length - 1) {
          this.carMoving(this.lineArr.length - 1);
        }
        this.percentage = this.pointList.length - 1;
        this.isStart = true;
        this.presentLocation = [];
        this.schedule = 0;
      });
      this.lineArr = path;
    },
    /**
     * 判断是否在地图范围内
     * 在 返回 true
     */
    isInBound (_lnglat) {
      const bounds = this.map.getBounds();
      const NorthEast = bounds.getNorthEast();
      const SouthWest = bounds.getSouthWest();
      const SouthEast = [NorthEast.lng, SouthWest.lat];
      const NorthWest = [SouthWest.lng, NorthEast.lat];
      const path = [[NorthEast.lng, NorthEast.lat], SouthEast, [SouthWest.lng, SouthWest.lat], NorthWest];
      const isPointInRing = this.AMap.GeometryUtil.isPointInRing(_lnglat, path);
      return isPointInRing;
    },
    mapLoaded(v){
      this.mapEl = v;
      this.map = this.mapEl.map;
      this.AMap = this.mapEl.AMap;
      this.readyState++;
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon (val) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(String(val.deviceCategory))) {
        vehicleIcon = `/bdsplatform/static/images/pic/vehicle.png`; // 车辆
      } else if (materialsModel.includes(String(val.deviceCategory))) {
        vehicleIcon = `/bdsplatform/static/images/pic/materials.png`; // 物资
      } else if (personnelModel.includes(String(val.deviceCategory))) {
        vehicleIcon = `/bdsplatform/static/images/pic/personnel.png`; // 人员
      } else if (shortMessageModel.includes(String(val.deviceCategory))) {
        vehicleIcon = `/bdsplatform/static/images/pic/shortMessage.png`; // 短报文终端
      } else if (timeServiceModel.includes(String(val.deviceCategory))) {
        vehicleIcon = `/bdsplatform/static/images/pic/timeService.png`; // 授时终端
      } else if (monitorModel.includes(String(val.deviceCategory))) {
        vehicleIcon = `/bdsplatform/static/images/pic/monitor.png`; // 监测终端
      } else if (String(val.deviceCategory) === '0') {
        vehicleIcon = `/bdsplatform/static/images/pic/other.png`; // 其他
      }
      return vehicleIcon;
    },
    parseTime
  }
};
</script>
<style lang="less" scoped>
.track-info{
    width: 100%;
    height: 100%;
}
.left-map{
    width: calc(100% - 310px);
    height: calc(100% - 260px);
}
/deep/ .amap-scalecontrol{
  display: none;
}
.follow-marker {
    transform: translate(-25px, -25px) scale(1) !important;
  }
</style>
