<template>
  <div class="details">
    <div class="left-map">
      <MapWidget
        ref="MapWidget"
        :class="{'map-info': alarmInfo.limitSpeed}"
        :tool-config="toolConfig"
        @mapLoaded="mapLoaded"
      />
    </div>
    <div
      v-if="alarmInfo.limitSpeed"
      class="speed-limit"
    >
      <div class="speed-number">
        <span style="font-size: 40px">{{ alarmInfo.limitSpeed }}</span>
      </div>
      <div class="speed-title">限速行驶</div>
    </div>
    <!-- 右侧告警信息 -->
    <AlarmInfo
      ref="alarmInfo"
      :alarm-info="alarmInfo"
      :vehicle-info="vehicleInfo"
      :user-info="userInfo"
      :dict="dict"
      v-bind="$attrs"
      v-on="$listeners"
    />
    <!-- 底部表格 -->
    <AlarmTable
      :alarmTable="alarmTable"
      :dict="dict"
    />
  </div>
</template>

<script>
import { details, alarmUser, alarmAttach, dealDetails, detailList } from '@/api/monitoring/realTimeMonitoring';
import { queryTracking } from '@/api/monitoring/track.js';
import { pagination } from '@/api/base/vehicle';
import MapWidget from '../../map/MapWidgetAMap.vue';
import AlarmInfo from './alarmInfo.vue';
import AlarmTable from './alarmTable.vue';
import { parseTime } from '@/api/utils/share';
import getImage from '@/api/utils/getImage';
export default {
  components:{
    MapWidget, AlarmInfo, AlarmTable
  },
  props:{
    alarmData: {
      type: Object,
      default: ()=>{return {};}
    },
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
  },
  data(){
    return{
      mapEl: null,
      map: null,
      AMap: null,
      readyState: 0,
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false // 工具栏
      }, // 控制工具按钮
      infoWindowContent: null,
      infoWindow: null,
      dataDetail: {
        longitude: 104.0789581,
        latitude: 36.9358958,
        licencePlate: '新F57322',
        licenceColor: '黄色',
        maxSpeed: '70km/h',
        speedLimit: '70km/h',
        startAlarmTime: '2023-06-01 11:15:40',
        startAlarmAddress: '甘肃省白银市景泰县中泉镇白水村民委员会西926米217省道北5米'
      },
      trackPointGroups: null,
      trackPointGroupsSub: null,
      polyline: null,
      // 单个点文本层
      signalGroups: null,
      alarmInfo: {},
      vehicleInfo: {},
      pointList: [], // 轨迹点列表
      alarmPointList: [], // 告警点列表
      alarmTable: [],
      userInfo: {},
      // 时间速度层
      timeSpeedGroups: null,
    };
  },
  watch: {
    readyState (v) {
      if (v === 2) {
        this.afterMap();
        this.setTrackPoint(this.pointList);
        this.setTimeSpeed(this.pointList);
      }
    },
    alarmData:{
      handler(){
        this.getAlarmDetails('start');
        this.getVehicleInfo();
        this.getUserInfo();
      },
      deep: true,
      immediate: true
    }
  },
  methods:{
    /**
     * 时间速度加载
     */
    setTimeSpeed (currentData) {
      this.timeSpeedGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: true
      });
      for (let i = 0; i < currentData.length; i++) {
        if (currentData[i].isAlarmPoint) {
          let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
          let timeSpeedText = new this.AMap.LabelMarker({
            position: position,
            text: {
              content: parseTime(currentData[i].locTime) + ', ' + currentData[i].speed + 'km/h' + ', ' + this.getEnumDictLabel('alarmType', this.alarmInfo.alarmType), // 直接展示告警详情的告警类型
              direction: 'right',
              offset: [10, -18],
              style: {
                'fontSize': 12,
                'backgroundColor': 'rgba(255, 255, 255, 0.1)',
                'fillColor': '#f41e1e',
                'strokeColor': 'white',
                'strokeWidth': 4
              }
            },
            rank: 2
          });
          this.timeSpeedGroups.add(timeSpeedText);
        }
      }
      this.map.add(this.timeSpeedGroups);
    },
    // 获取告警详情
    async getAlarmDetails(type){
      const form = {
        id: this.alarmData.id,
        serviceRole: this.alarmData.serviceRole
      };
      await details(form).then(res=>{
        this.alarmInfo = res.data;
        this.alarmInfo.serviceRole = this.alarmData.serviceRole;
        // let list = [];
        // let obj = {
        //   server: null,
        //   third: null,
        //   company: null
        // };
        // Object.keys(obj).forEach(item=>{
        //   if (this.alarmInfo[item + 'Time']) {
        //     let data = {
        //       state: this.getEnumDictLabel(item + 'State', this.alarmInfo[item + 'State']),
        //       // measures: this.getMeasuresLabel(item + 'Measures', this.alarmInfo[item + 'Measures']),
        //       measures: item === 'company' ? this.getMeasuresLabel(item + 'Measures', this.alarmInfo[item + 'Measures']) : this.alarmInfo[item + 'Measures'],
        //       time: this.alarmInfo[item + 'Time'],
        //       user: this.alarmInfo[item + 'User'],
        //       content: this.alarmInfo[item + 'Content']
        //     };
        //     list.push(data);
        //   }
        // });
        // this.alarmTable = list;
      });
      dealDetails(form).then(res=>{
        this.alarmTable = res.data;
      }).catch(err=>{
        this.alarmTable = [];
      });
      if (type === 'start') {
        const query = {
          licencePlate: this.alarmData.licencePlate,
          licenceColor: this.alarmData.licenceColor,
          isFilter: 0,
          startTime: Number(this.alarmData.startTime) ? Number(this.alarmData.startTime) - 10 * 60 : Number(this.alarmData.alarmTime) - 10 * 60, // 告警开始时间前十分钟的轨迹
          endTime: Number(this.alarmData.endTime) ? Number(this.alarmData.endTime) + 10 * 60 : Number(this.alarmData.alarmEndTime) + 10 * 60, // 告警结束时间后十分钟的轨迹
          alarm: 1 // 告警轨迹
        };
        this.getTrackData(query);
      }
    },
    // 获取轨迹数据
    async getTrackData(form){
      // 车辆轨迹点
      const { code, data } = await queryTracking(form);
      // 防止出现接口请求缓慢的情况, 例如先打开粤A12345的告警详情界面, 在轨迹没请求完成之前退出页面再次进入粤A14725的页面会导致页面会绘制两个车辆的轨迹
      if (this.alarmData.licencePlate !== data[0].licencePlate || this.alarmData.licenceColor !== data[0].licenceColor) {
        return;
      }
      if (code === 200 && data && data.length) {
        // 请求告警轨迹点
        const query = {
          serviceRole: this.alarmData.serviceRole,
          id: this.alarmData.id
        };
        await detailList(query).then(res=>{
          if (res.data && res.data.length) {
            this.alarmPointList = res.data;
          } else {
            this.alarmPointList = [];
          }
        });
        this.pointList = data;
        for (let index = 0; index < this.pointList.length; index++) {
          const element = this.pointList[index];
          const result = this.alarmPointList.find((item)=> element.alarmIds && element.alarmIds.includes(item.id));
          if (result) {
            element.isAlarmPoint = true;
            element.bdmDsmDataSh = result.bdmDsmDataSh;
          }
        }
      } else {
        let list = [];
        let startForm = {
          latitude: this.alarmInfo.startLat,
          longitude: this.alarmInfo.startLon,
          licencePlate: this.alarmInfo.licencePlate,
          licenceColor: this.alarmInfo.licenceColor,
          speed: this.alarmInfo.speed,
          address: this.alarmInfo.alarmAddress,
          locTime: this.alarmInfo.alarmTime,
          alarmType: this.alarmInfo.alarmType
        };
        let endForm = {
          latitude: this.alarmInfo.endLat,
          longitude: this.alarmInfo.endLon,
          licencePlate: this.alarmInfo.licencePlate,
          licenceColor: this.alarmInfo.licenceColor,
          speed: this.alarmInfo.speed,
          address: this.alarmInfo.alarmEndAddress,
          locTime: this.alarmInfo.alarmEndTime,
          alarmType: this.alarmInfo.alarmType
        };
        list.push(startForm);
        list.push(endForm);
        this.pointList = list;
      }
      if (this.polyline) {
        this.clearMap(); // 以防万一, 再次执行清除轨迹操作(特殊情况下会造成多段轨迹同时存在地图的问题, 例如进入详情页面在轨迹接口未加载完成的时候退出页面再次进入)
        this.$nextTick(()=>{
          this.afterMap();
          this.setTrackPoint(this.pointList);
          this.setTimeSpeed(this.pointList);
        });
      }else{
        this.readyState++;
      }
      // 默认展示第一个告警附件(clearMap会清除附件, 因此清除完后再进行赋值)
      if (this.alarmPointList[0] && this.alarmPointList[0].bdmDsmDataSh) {
        this.$refs.alarmInfo.accessoryHandle(this.alarmPointList[0].bdmDsmDataSh);
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    // 获取处理措施
    getMeasuresLabel(dictName, val){
      const valList = val.split(',');
      let labelList = valList.map(element => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][element]) {
          return this.dict.dict[dictName][element].label;
        }
      });
      let str = labelList.toString();
      return str;
    },
    // 获取用户信息
    getUserInfo(){
      let form = {
        id: this.alarmData.id,
        serviceRole: this.alarmData.serviceRole
      };
      alarmUser(form).then(res=>{
        if (res.data) {
          this.userInfo = res.data;
          res.data.photoUrl && getImage(res.data.photoUrl).then(request => {
            this.$set(this.userInfo, 'photoUrlFront', URL.createObjectURL(request.response));
          });
        } else {
          this.userInfo = {};
        }
      });
    },
    getVehicleInfo(){
      let form = {
        licencePlate: this.alarmData.licencePlate,
        page: 0,
        size: 99
      };
      pagination(form).then((res)=>{
        this.vehicleInfo = res.data.content[0];
      });
    },
    clearMap(){
      // this.$refs.MapWidget.clearAll(); // TODO 不生效
      if (this.polyline) {
        this.map.remove(this.polyline);
        this.map.remove(this.trackPointGroups);
        this.map.remove(this.trackPointGroupsSub);
        this.map.remove(this.timeSpeedGroups);
        this.clearSignalPointText();
        this.$refs.alarmInfo.clearAccessory();
      }
    },
    // 获取告警附件
    async getAlarmAttach (currentData) {
      if (currentData.isAlarmPoint && currentData.bdmDsmDataSh) {
        this.$refs.alarmInfo.accessoryHandle(currentData.bdmDsmDataSh);
      } else {
        this.$refs.alarmInfo.accessoryHandle({});
      }
      // if (currentData.appendixId) {
      //   const query = {
      //     appendixId: currentData.appendixId
      //   };
      //   const { code, data } = await alarmAttach(query);
      //   if (code === 200 && data) {
      //     this.$refs.alarmInfo.accessoryHandle(data);
      //   } else {
      //     this.$refs.alarmInfo.accessoryHandle({});
      //   }
      // } else {
      //   this.$refs.alarmInfo.accessoryHandle({});
      // }
    },
    /**
     * 定位点加载
     */
    setTrackPoint (currentData) {
      this.trackPointGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: true
      });
      this.trackPointGroupsSub = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      [0, currentData.length - 1].forEach((i, index) => {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyleSub(index === 0 ? 's' : 'e');
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [28, 36]
          }
        });
        pointMarker.on('click', (e) => {
          this.setSinglePointText([currentData[i]]);
        });
        this.trackPointGroupsSub.add(pointMarker);
        if (index === 0) {
          this.$refs.MapWidget.setFitView();
        }
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyle(currentData[i]);
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: currentData[i].isAlarmPoint ? [28, 30] : [10, 12]
          },
          rank: currentData[i].isAlarmPoint ? 3 : 1
        });
        pointMarker.on('click', (e) => {
          // 处理定位
          this.setSinglePointText([currentData[i]]);
          // this.$refs.alarmInfo.accessoryHandle(currentData[i]);
          this.getAlarmAttach(currentData[i]);
        });
        this.trackPointGroups.add(pointMarker);
      }
      this.map.add(this.trackPointGroups);
      this.map.add(this.trackPointGroupsSub);
    },
    getIconStyle (data) {
      let item = data;
      let icon = item.running ? require('@/assets/images/car/track-run.png') : require('@/assets/images/car/track-stop.png');
      if (item.isAlarmPoint) {
        icon = require('@/assets/images/car/track-alarm.png');
      }
      return icon;
    },
    clearSignalPointText () {
      if (this.signalGroups) {
        this.signalGroups.clearOverlays();
        this.map.remove(this.signalGroups);
        this.signalGroups = null;
      }
    },
    setSinglePointText (currentData) {
      this.clearSignalPointText();
      let markerCollection = [];
      let infoWindow = null;
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        var info = [];
        info.push("<div class='input-card content-window-card' style='width: 435px;>");
        info.push("<div style='padding:7px 0px 15px 0px;><h4>" + `${currentData[i].licencePlate}(${this.getEnumDictLabel('licenceColor', currentData[i].licenceColor)})` + '</h4>');
        info.push("<p class='input-item' style='margin: 10px'>告警时间：" + `${parseTime(currentData[i].locTime)}`);
        info.push("<p class='input-item' style='margin: 10px'>速度：" + `${currentData[i].speed}` + 'km/h');
        // info.push("<p class='input-item' style='margin: 10px'>经度：" + `${currentData[i].longitude}`);
        // info.push("<p class='input-item' style='margin: 10px'>纬度：" + `${currentData[i].latitude}`);
        info.push("<p class='input-item' style='margin: 10px'>地址：" + `${currentData[i].locAddr}`);
        infoWindow = new this.AMap.InfoWindow({
          position: position,
          anchor: 'bottom-center',
          content: info.join(''),
          offset: new this.AMap.Pixel(0, -30)
        });
        markerCollection.push([infoWindow]);
      }
      this.signalGroups = new this.AMap.OverlayGroup(markerCollection);
      this.map.add(this.signalGroups);
    },
    getIconStyleSub (i) {
      let icon;
      if (i === 's') {
        icon = require('@/assets/images/car/track-start.png');
      } else if (i === 'e') {
        icon = require('@/assets/images/car/track-end.png');
      }
      return icon;
    },
    afterMap () {
      // this.drawMarkerHandle();
      let _that = this.mapEl;
      let path = _that.formatPathData(this.pointList);
      // let polyline = _that.getPath(path, {strokeColor: 'red', showDir: false});
      this.polyline = _that.getPath(path, {});
      _that.map.add(this.polyline);
    },
    drawMarkerHandle () {
      let icon = require('@/assets/images/car/track-alarm.png');
      let tempMarker = new this.AMap.Marker({
        icon: icon,
        position: [this.dataDetail.longitude, this.dataDetail.latitude]
      });
      let iconStart = require('@/assets/images/car/track-start.png');
      let tempMarkerStart = new this.AMap.Marker({
        icon: iconStart,
        position: [104.0421672, 36.9580472]
      });
      let iconEnd = require('@/assets/images/car/track-end.png');
      let tempMarkerEnd = new this.AMap.Marker({
        icon: iconEnd,
        position: [104.1362659, 36.9077984]
      });
      // infowidnow 的 innerHTML
      this.infoWindowContent = [];
      this.infoWindowContent.push("<div class='input-card content-window-card' style='width: 300px;>");
      this.infoWindowContent.push("<div style='padding:7px 0px 15px 0px;><h4>" + `${this.dataDetail.licencePlate}` + '</h4>');
      this.infoWindowContent.push("<p class='input-item' style='margin: 10px'>车牌颜色：" + `${this.dataDetail.licenceColor}`);
      this.infoWindowContent.push("<p class='input-item' style='margin: 10px'>最大车速：" + `${this.dataDetail.maxSpeed}` + 'km/h,' + `限速：${this.dataDetail.speedLimit}km/h`);
      this.infoWindowContent.push("<p class='input-item' style='margin: 10px'>告警时间：" + `${this.dataDetail.startAlarmTime}`);
      this.infoWindowContent.push("<p class='input-item' style='margin: 10px'>位置：" + `${this.dataDetail.longitude}` + ',' + `${this.dataDetail.latitude}`);
      this.infoWindowContent.push("<p class='input-item' style='margin: 10px'>地址：" + `${this.dataDetail.startAlarmAddress}`);
      this.infoWindow = new this.AMap.InfoWindow({
        position: [this.dataDetail.longitude, this.dataDetail.latitude],
        offset: new this.AMap.Pixel(7, -30),
        content: this.infoWindowContent.join('')
      });
      tempMarker.on('click', () => {
        this.infoWindow.open(this.map);
      });
      this.infoWindow.open(this.map);
      this.map.add([tempMarker, tempMarkerStart, tempMarkerEnd]);
      this.$refs.MapWidget.setFitView(tempMarker);
    },
    mapLoaded(v){
      this.mapEl = v;
      this.map = this.mapEl.map;
      this.AMap = this.mapEl.AMap;
      this.readyState++;
    }
  }
};
</script>

<style lang="less" scoped>
.details{
    width: 100%;
    height: 100%;
}
.left-map{
    width: calc(100% - 290px);
    height: calc(100% - 190px);
}
.speed-limit{
  position: absolute;
  right: 310px;
  top: 0;
  background: #fff;
  z-index: 99;
  .speed-number{
    height: 70px;
    width: 70px;
    border-radius: 50%;
    border: 6px solid red;
    margin: 10px 10px;
    color: #000;
    font-size: 50px;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .speed-title{
    background: red;
    color: #fff;
    text-align: center;
    font-size: 15px;
    letter-spacing: 4px;
    font-weight: 600;
    padding: 2px 0;
  }
}
.map-info{
    ::v-deep .map-tool-bar{
      right: 120px;
    }
}
</style>
