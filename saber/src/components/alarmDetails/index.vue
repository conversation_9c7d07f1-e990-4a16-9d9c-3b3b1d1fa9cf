<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :append-to-body="true"
    @close="closeDialog"
  >
    <template slot="title">
      <div class="head">
        <div class="head-plate">{{ alarmData.targetName }} 告警详情</div>
        <div
          class="head-tab"
          :class="{'active': activeIndex}"
          @click="activeIndex = true"
        >告警处理</div>
        <div
          class="head-tab"
          :class="{'active': !activeIndex}"
          @click="activeIndex = false"
        >轨迹回放</div>
      </div>
    </template>
    <div
      v-show="activeIndex"
      class="content"
    >
      <AlarmInfoDetails
        ref="alarmInfoDetails"
        :alarmData="alarmData"
        v-bind="$attrs"
        v-on="$listeners"
      />
    </div>
    <div
      v-show="!activeIndex"
      class="content"
    >
      <CarTrackPlay
        ref="carTrackPlay"
        :active-index="activeIndex"
        :alarmData="alarmData"
        v-bind="$attrs"
      />
    </div>
  </el-dialog>
</template>
<script>
import AlarmInfoDetails from './module/alarmDetails.vue';
import CarTrackPlay from './module/carTrackPlay.vue';
export default {
  components:{
    AlarmInfoDetails, CarTrackPlay
  },
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    alarmData: {
      type: Object,
      default: ()=>{return {};}
    }
  },
  data(){
    return{
      activeIndex: true,
      activeItem: true
    };
  },
  methods:{
    closeDialog () {
      this.activeIndex = true;
      this.$refs.alarmInfoDetails.clearMap();
      this.$refs.carTrackPlay.clearMap();
      this.$emit('update:dialogVisible', false);
    },
    getAlarmDetails(){
      this.$refs.alarmInfoDetails.getAlarmDetails();
    }
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-dialog{
    width: 100%;
    height: 100%;
    margin: 0 !important;
}
::v-deep .el-dialog__header{
    border-bottom: 1px solid #ebeef5;
    background: #307dff;
    padding: 10px 20px 10px;
    .el-dialog__headerbtn {
      top: 12px;
    }
    .el-dialog__close{
      color: #fff;
    }
}
.head{
    display: flex;
}
.head-plate{
    padding-top: 3px;
    margin-right: 10px;
    border-radius: 5px;
    color: #fff;
    font-weight: 600;
    position: relative;
    font-size: 18px;
}
.head-tab{
    cursor: pointer;
    width: 105px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    font-weight: 600;
    font-size: 14px;
    color: #fff;
    position: relative;
    margin: 0 10px;
    border-radius: 5px;
    letter-spacing: 2px;
    border: 1px solid #f5f3f0;
}
.head-tab:hover{
  border: 1px solid #fac632;
}
.active{
    border: 1px solid #f5f3f0;
    background: #fac632;
    color: #000;
}
::v-deep .el-dialog__body{
    padding: 10px;
    background: #f5f5f5;
    line-height: 1.5 !important;
    max-height: 100vh;
}
.content{
    width: 100%;
    height: calc(100vh - 71px);
    display: flex;
    position: relative;
}
</style>
