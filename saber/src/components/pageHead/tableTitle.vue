<template>
  <div class="table-title">
    <div class="title">
      {{ title||'标题' }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    }
  },
  data () {
    return {
    };
  }
};
</script>

<style scoped>
.table-title{
  height: 40px;
  display: flex;
  align-items: center;
}
.title{
  display: inline-block;
  font-size: 16px;
  padding: 5px;
  margin: 0;
  background: linear-gradient(to right, #257ab5, #2360aa);
  color: #fff;
  border-radius: 3px;
  cursor: default
}
</style>
