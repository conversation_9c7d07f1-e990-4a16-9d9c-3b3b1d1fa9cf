<template>
  <el-cascader
    ref="cascaderRef"
    v-model="selectedDeptId"
    :props="config"
    :options="deptOptions"
    :show-all-levels="false"
    class="cascaderRef"
    size="small"
    clearable
    filterable
    placeholder="请选择所属机构"
    @change="change"
  />
</template>

<script>
export default {
  name: 'DeptSingleSelect',
  props: {
    value: {
      type: [
        String,
        Array,
        Object,
        Number
      ],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      selectedDeptId: [],
      deptOptions: [],
      config: {
        value: 'id',
        label: 'title',
        checkStrictly: true,
        multiple: false
      }
    };
  },
  watch: {
    selectedDeptId(array) {
      if (!array) {
        return;
      }
      this.$emit('input', array[array.length - 1]);
    },
    value(v) {
      if (!v) {
        this.selectedDeptId = null;
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getDept();
    });
  },
  methods: {
    getDept() {
      this.loading = true;
      this.$store.dispatch('GetDept').then(res => {
        this.deptOptions = this.formatData(res.data);
        this.$nextTick(() => {
          this.$emit('init', this.selectedDeptId);
        });
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /**
     * 监听选中节点
     */
    change(value) {
      let nodeInfo = this.$refs.cascaderRef.getCheckedNodes();
      this.$emit('change', nodeInfo);
    },
    formatData(data) {
      return data.map(item => {
        if (item.children.length !== 0) {
          this.formatData(item.children);
        }
        else {
          item.children = null;
        }
        return item;
      });
    }
  }
};
</script>
<style lang="less" scoped>
.el-cascader-panel {
  height: 420px;
}

.cascaderRef {
  height: 30px;
  line-height: 30px;
}

.cascaderRef ::v-deep .el-input__inner {
  //width: 180px;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
}

.cascaderRef ::v-deep .el-input--small {
  height: 32px;
  line-height: 32px;
}

.cascaderRef ::v-deep .el-input__suffix {
  height: 32px;
  line-height: 32px;
}
</style>
