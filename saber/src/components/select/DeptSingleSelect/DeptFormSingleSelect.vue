<template>
  <el-select
    ref="deptIdStrRef"
    v-model="treeSelectText"
    class="el-input-search"
    :popper-class="popperClass"
    filterable
    :disabled="disabled"
    style="width:100%"
    :filter-method="debouncedFilterTree"
    :size="size"
    :clearable="clearable"
    :placeholder="disabled ? '' : placeholder"
    @clear="clear"
  >
    <el-option
      hidden
      :value="treeValue"
      style="height:auto;"
    />
    <vue-easy-tree
      ref="tree"
      :key="treeKey"
      :data="deptOptions"
      :props="props"
      :lazy="lazy"
      :load="loadTreeNode"
      :expand-on-click-node="false"
      :default-expand-all="defaultExpand"
      node-key="id"
      height="200px"
      itemSize="30"
      @node-click="nodeClick"
    />
  </el-select>
</template>

<script>

import { getDeptPerInit } from '@/api/base/dept';
import { debounce } from "lodash";

export default {
  name: 'DeptFormSingleSelect',
  props: {
    value: {
      type: [
        String,
      ],
      required: true
    },
    placeholder: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isShow: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium'
    },
    clearable: {
      type: Boolean,
      default: false
    },
    detailName: {
      type: String,
      default: ''
    },
    deptList: {
      type: Array,
      default: null
    },
    noReq: {
      type: Boolean,
      default: false
    },
    popperClass: {
      type: String,
      default: 'dept-select-tree'
    }
  },
  data() {
    return {
      debouncedFilterTree: debounce(this.filterTree, 500),
      deptOptions: [],
      props: {
        label: 'title',
        isLeaf: 'leaf'
      },
      treeSelectText: '',
      treeValue: {
        id: '',
        label: ''
      },
      allDeptData: [],
      lazy: true,
      defaultExpand: false,
      treeKey: 0
    };
  },
  watch: {
    treeValue: {
      handler(val) {
        if (val.id) {
          this.$emit('input', val.id);
        } else {
          this.$emit('input', '');
        }
      },
      deep: true
    },
    isShow: {
      handler(val) {
        if (val) {
          this.getDept();
        } else {
          this.clear();
        }
      },
      immediate: true
    },
    deptList() {
      this.allDeptData = Object.freeze(this.deptList);
      this.deptOptions = (this.deptList || []).map(item => ({...item, children: []}));
    }
  },
  methods: {
    getDept() {
      // 非禁用状态下 获取列表 优化性能
      if(!this.disabled && !this.noReq) {
        // 如果从外部组件传递了列表数据 就不从接口获取
        getDeptPerInit().then(res => {
          this.allDeptData = Object.freeze(res.data);
          this.deptOptions = (res.data || []).map(item => ({...item, children: []}));
          this.value && this.setValue(this.value);
        });
      } else {
        this.value && this.setValue(this.value);
      }
      // 如果有部门名称 直接设置 快速回显
      if(this.detailName) {
        this.treeSelectText = this.detailName;
      }
    },
    // 有些外层组件需要请求详情接口后才给treeSelectText赋值
    setTreeValue (val) {
      this.treeSelectText = val;
    },
    filterTree(val) {
      const defaultCurrentKeys = this.$refs.tree.getCurrentKey();
      if (val) {
        const list = this.filterNestedArray(this.allDeptData, val);
        this.deptOptions = list;
        this.lazy = false;
        this.defaultExpand = true;
        this.treeKey += 1;
      } else {
        this.deptOptions = this.allDeptData.map(item => ({...item, children: []}));
        this.lazy = true;
        this.defaultExpand = false;
        this.treeKey += 1;
      }
      this.$nextTick(() => {
        if (defaultCurrentKeys) {
          this.$refs.tree.setCurrentKey(defaultCurrentKeys);
        }
      });
    },
    filterNestedArray(array, filterValue) {
      const result = [];
      result.push(...this.recursiveFilter(array, filterValue));
      return result;
    },
    recursiveFilter(items, filterValue) {
      return items.reduce((acc, item) => {
        if (item.title.includes(filterValue)) {
          acc.push({ ...item });
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.recursiveFilter(item.children, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        }
        return acc;
      }, []);
    },
    nodeClick(node) {
      const {
        id,
        title
      } = node;
      this.treeValue.id = id;
      this.treeValue.label = title;
      this.treeSelectText = title;
      this.$refs.deptIdStrRef.blur();
    },
    setValue(val) {
      this.$nextTick(() => {
        if (this.$refs.tree) {
          this.$refs.tree.setCurrentKey(val);
          const result = this.findInNestedArray(this.allDeptData, item => item.id === val);
          if (result) {
            const { id, title } = result;
            this.treeValue.id = id;
            this.treeValue.label = title;
            this.treeSelectText = title;
          }
        }
      });
    },
    // 加载子节点
    loadTreeNode (node, resolve) {
      if (node.level > 0) {
        const result = this.findInNestedArray(this.allDeptData, item => item.id === node.data.id);
        if (result) {
          const list = result.children.map(item => ({
              ...item,
              leaf: item.children?.length ? false : true,
              children: []
            }));
            resolve(list);
        } else {
          resolve([]);
        }
      } else {
        resolve(this.allDeptData.map(item => ({...item, children: []})));
      }
    },
    findInNestedArray(array, condition) {
      for (let item of array) {
        if (condition(item)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = this.findInNestedArray(item.children, condition);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    clear(){
      this.treeSelectText = '';
      this.treeValue.id = '';
      this.treeValue.label = '';
      this.$refs.tree?.setCurrentKey(null);
      this.lazy = true;
      this.defaultExpand = false;
      this.treeKey = 0;
    }
  }
};
</script>

<style lang="less" scoped>

</style>
