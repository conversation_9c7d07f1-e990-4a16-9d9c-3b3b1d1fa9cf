<template>
  <el-select
    ref="deptIdsStrRef"
    v-model="treeSelectText"
    class="el-input-search"
    filterable
    :disabled="disabled"
    style="width:100%"
    :filter-method="filterTree"
    :size="size"
    :clearable="clearable"
    :placeholder="disabled ? '' : placeholder"
    multiple
    :collapse-tags="collapseTags"
    :loading="loadingP || loading"
    @clear="clear"
    @change="handleChange"
  >
    <el-option
      hidden
      :value="treeValue"
      style="height:auto;"
    />
    <vue-easy-tree
      ref="tree"
      :data="deptOptions"
      :props="props"
      node-key="id"
      :filter-node-method="filterNode"
      height="200px"
      itemSize="30"
      show-checkbox
      :check-strictly="checkStrictly"
      @check="check"
    />
  </el-select>
</template>

<script>

import { getDeptPerInit } from '@/api/base/dept';

export default {
  name: 'DeptFormMultiSelect',
  props: {
    value: {
      type: [
        Array,
        String,
      ],
      required: true
    },
    placeholder: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isShow: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium'
    },
    clearable: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: false
    },
    deptOptionsP: {
      type: Array,
      default: () => []
    },
    loadingP: {
      type: Boolean,
      default: false
    },
    noReq: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      loading: false,
      deptOptions: [],
      props: {
        label: 'title'
      },
      treeSelectText: [],
      treeValue: {
        ids: [],
        labels: []
      }
    };
  },
  watch: {
    treeValue: {
      handler(val) {
        if (val.ids.length) {
          this.$emit('input', val.ids);
        }
        else {
          this.$emit('input', []);
        }
      },
      deep: true
    },
    isShow: {
      handler(val) {
        if (val) {
          !this.noReq && this.getDept();
        } else {
          this.clear();
        }
      },
      immediate: true
    },
    deptOptionsP(val) {
      this.deptOptions = Object.freeze(val);
      const values = Array.isArray(this.value) ? this.value?.filter( item => item) : this.value;
      values && this.setValue(values);
    }
  },
  methods: {
    handleChange(val) {
      const datas = this.$refs.tree.getCheckedNodes();
      if(datas.length !== val.length) {
        let list = [];
        if(val.length) {
          list = datas.filter( item => val.includes(item.title)).map(item => item.id);
        } else {
          list = [];
        }
        this.setValue(list);
      }
    },
    getDept() {
      this.loading = true;
      getDeptPerInit().then(res => {
        this.deptOptions = Object.freeze(res.data);
        const values = Array.isArray(this.value) ? this.value?.filter( item => item) : this.value;
        values && this.setValue(values);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    filterTree(val) {
      this.$refs.tree.filter(val);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.title.indexOf(value) !== -1;
    },
    setValue(val) {
      this.$nextTick(() => {
        if (this.$refs.tree) {
          this.clear();
          this.$refs.tree.setCheckedKeys(val);
          val.forEach(item => {
            const { data } = this.$refs.tree.getNode(item);
            const {
              id,
              title
            } = data;
            this.treeValue.ids.push(id);
            this.treeValue.labels.push(title);
            this.treeSelectText.push(title);
          });
        }
      });
    },
    clear() {
      this.treeSelectText = [];
      this.treeValue.ids = [];
      this.treeValue.labels = [];
      this.$refs.tree.setCheckedKeys([]);
    },
    check(_data, node) {
      const { checkedNodes } = node;
      this.treeSelectText = checkedNodes.map(item => item.title);
      this.treeValue.ids = checkedNodes.map(item => item.id);
      this.treeValue.labels = checkedNodes.map(item => item.title);
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";

.sidebarDeptFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarDeptFade-enter,
.sidebarDeptFade-leave-to {
  opacity: 0;
}

.sidebar-dept-container {
  position: relative;
  width: 100%;
  height: @logoBarHeight;
  line-height: @logoBarHeight;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 6px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #ffffff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
