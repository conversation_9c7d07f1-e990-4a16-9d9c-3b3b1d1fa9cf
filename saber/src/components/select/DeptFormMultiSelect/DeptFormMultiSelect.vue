<template>
  <el-select
    ref="deptIdsStrRef"
    v-model="treeSelectText"
    class="el-input-search"
    filterable
    :disabled="disabled"
    style="width:100%"
    :filter-method="debouncedFilterTree"
    :size="size"
    :clearable="clearable"
    :placeholder="disabled ? '' : placeholder"
    multiple
    :collapse-tags="collapseTags"
    :loading="loadingP || loading"
    @clear="clear"
    @change="handleChange"
  >
    <el-option
      hidden
      :value="treeValue"
      style="height:auto;"
    />
    <vue-easy-tree
      ref="tree"
      :key="treeKey"
      :data="deptOptions"
      :lazy="lazy"
      :props="defaultProps"
      :load="loadTreeNode"
      :default-expand-all="defaultExpand"
      node-key="id"
      height="200px"
      itemSize="30"
      show-checkbox
      :check-strictly="checkStrictly"
      :default-expanded-keys="defaultExpandedKeys"
      @check="check"
      @node-expand="handleNodeExpand"
      @node-collapse="handleNodeCollapse"
    />
  </el-select>
</template>

<script>

import { getDeptPerInit } from '@/api/base/dept';
import { debounce } from "lodash";

export default {
  name: 'DeptFormMultiSelect',
  props: {
    value: {
      type: [
        Array,
        String,
      ],
      required: true
    },
    placeholder: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isShow: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium'
    },
    clearable: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: false
    },
    deptOptionsP: {
      type: Array,
      default: () => []
    },
    loadingP: {
      type: Boolean,
      default: false
    },
    noReq: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      loading: false,
      debouncedFilterTree: debounce(this.filterTree, 500),
      deptOptions: [],
      defaultProps: {
        label: 'title',
        isLeaf: 'leaf'
      },
      treeSelectText: [],
      treeValue: {
        ids: [],
        labels: []
      },
      allDeptData: [],
      lazy: true,
      defaultExpand: false,
      treeKey: 0,
      filterCheckedKeys: [],
      defaultExpandedKeys: [],
      filterText: '',
      treeCheckedNodes: []
    };
  },
  watch: {
    treeValue: {
      handler(val) {
        if (val.ids.length) {
          this.$emit('input', val.ids);
        }
        else {
          this.$emit('input', []);
        }
      },
      deep: true
    },
    isShow: {
      handler(val) {
        if (val) {
          !this.noReq && this.getDept();
        } else {
          this.clear();
        }
      },
      immediate: true
    },
    deptOptionsP(val) {
      this.allDeptData = Object.freeze(val);
      this.deptOptions = (val || []).map(item => ({...item, children: []}));
      const values = Array.isArray(this.value) ? this.value?.filter( item => item) : this.value;
      values && this.setValue(values);
    }
  },
  methods: {
    handleChange(val) {
      if(this.treeCheckedNodes.length !== val.length) {
        let list = [];
        if(val.length) {
          list = this.treeCheckedNodes.filter( item => val.includes(item.title)).map(item => item.id);
        } else {
          list = [];
        }
        this.setValue(list);
      }
    },
    getDept() {
      this.loading = true;
      getDeptPerInit().then(res => {
        this.allDeptData = Object.freeze(res.data);
        this.deptOptions = (res.data || []).map(item => ({...item, children: []}));
        const values = Array.isArray(this.value) ? this.value?.filter( item => item) : this.value;
        values && this.setValue(values);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    filterTree(val) {
      if (val) {
        this.filterText = val;
        const list = this.filterNestedArray(this.allDeptData, val);
        this.deptOptions = list;
        this.lazy = false;
        this.defaultExpand = true;
        this.treeKey += 1;
      } else {
        this.filterText = '';
        this.deptOptions = this.allDeptData.map(item => ({...item, children: []}));
        this.lazy = true;
        this.defaultExpand = false;
        this.treeKey += 1;
        for (let index = 0; index < this.filterCheckedKeys.length; index++) {
          const element = this.filterCheckedKeys[index];
          const result = this.findAncestorsById(this.allDeptData, element);
          if (result) {
            this.defaultExpandedKeys = [...new Set([...this.defaultExpandedKeys, ...result])];
          }
        }
      }
      this.$nextTick(() => {
        this.filterCheckedKeys = [];
        const treeCheckedKeys = this.treeCheckedNodes.map(item => item.id);
        this.$refs.tree.setCheckedKeys(treeCheckedKeys);
      });
    },
    // 查找指定id的节点及其所有父级和祖宗级的id
    findAncestorsById(data, targetId) {
      const ancestors = [];
      return this.findRecursive(data, targetId, ancestors);
    },
    findRecursive(items, targetId, path = []) {
      for (let item of items) {
        if (item.id === targetId) {
          return [...path];
        }
        if (item.children && item.children.length > 0) {
          const result = this.findRecursive(item.children, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },
    filterNestedArray(array, filterValue) {
      const result = [];
      result.push(...this.recursiveFilter(array, filterValue));
      return result;
    },
    recursiveFilter(items, filterValue) {
      return items.reduce((acc, item) => {
        if (item.title.includes(filterValue)) {
          acc.push({ ...item });
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.recursiveFilter(item.children, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        }
        return acc;
      }, []);
    },
    setValue(val) {
      this.$nextTick(() => {
        if (this.$refs.tree) {
          this.clear();
          this.$refs.tree.setCheckedKeys(val);
          val.forEach(key => {
            const result = this.findInNestedArray(this.allDeptData, item => item.id === key);
            this.treeCheckedNodes.push(result);
            if (result) {
              const { id, title } = result;
              this.treeValue.ids.push(id);
              this.treeValue.labels.push(title);
              this.treeSelectText.push(title);
            }
          });
        }
      });
    },
    // 加载子节点
    loadTreeNode (node, resolve) {
      if (node.level > 0) {
        const result = this.findInNestedArray(this.allDeptData, item => item.id === node.data.id);
        if (result) {
          const list = result.children.map(item => ({
              ...item,
              leaf: item.children?.length ? false : true,
              children: []
            }));
            resolve(list);
        } else {
          resolve([]);
        }
      } else {
        resolve(this.allDeptData.map(item => ({...item, children: []})));
      }
    },
    findInNestedArray(array, condition) {
      for (let item of array) {
        if (condition(item)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = this.findInNestedArray(item.children, condition);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    clear() {
      this.treeSelectText = [];
      this.treeValue.ids = [];
      this.treeValue.labels = [];
      this.$refs.tree?.setCheckedKeys([]);
      this.treeCheckedNodes = [];
      this.lazy = true;
      this.defaultExpand = false;
      this.treeKey = 0;
      this.defaultExpandedKeys = [];
      this.filterText = '';
    },
    check(node, options) {
      const { checkedKeys } = options;
      const checked = checkedKeys.includes(node.id);
      if (!checked) {
        this.treeCheckedNodes = this.treeCheckedNodes.filter(item => item.id !== node.id);
      } else {
        this.treeCheckedNodes.push(node);
      }
      if (this.filterText && checked) {
        this.filterCheckedKeys.push(node.id);
      } else if (this.filterText && !checked) {
        this.filterCheckedKeys = this.filterCheckedKeys.filter(item => item !== node.id);
      }
      this.treeSelectText = this.treeCheckedNodes.map(item => item.title);
      this.treeValue.ids = this.treeCheckedNodes.map(item => item.id);
      this.treeValue.labels = this.treeCheckedNodes.map(item => item.title);
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand (data, node) {
      this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse (data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
    }
  }
};
</script>

<style lang="less" scoped>

</style>
