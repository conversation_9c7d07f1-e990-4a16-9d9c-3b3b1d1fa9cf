<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    title="绑定终端"
    append-to-body
    width="30%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="80px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <el-form-item
            label="序列号"
            prop="uniqueId"
          >
            <el-select
              v-model="form.uniqueId"
              filterable
              remote
              placeholder="请输入序列号模糊查找"
              :remote-method="uniqueIdSearchHandle"
              :loading="remoteLoading"
              @change="uniqueIdSelectHandle"
            >
              <el-option
                v-for="item in uniqueIdOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="$emit('update:dialogVisible', false)"
      >
        取消
      </el-button>
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        @click="submitHandle"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { driverUnbindVehicleDetail } from '@/api/base/driver';
import { connectuniqueid } from '@/api/base/vehicle';
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    currentData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      form: {
        uniqueId: ''
      },
      rules: {
        uniqueId: { required: true, message: '请选择序列号', trigger: 'change' }, // 序列号
      },
      timer: null, // 定时器
      uniqueIdOptions: [],
      remoteLoading: false,
      loading: false,
      uniqueIdObj: {}
    };
  },
  methods: {
    submitHandle() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const query = {
            targetId: BigInt(this.currentData.id),
            targetType: Number(this.currentData.targetType),
            targetName: this.currentData.number,
            deptId: BigInt(this.currentData.deptId),
            uniqueId: this.form.uniqueId
          };
          if (this.uniqueIdObj.specificity === 2) {
            this.loading = true;
            connectuniqueid(query).then(res => {
              this.$message.success('终端绑定成功');
              this.$emit('update:dialogVisible', false);
              this.$emit('toQuery');
            }).finally(() => {
              this.loading = false;
            });
          } else {
            this.$confirm(`${this.currentData.number} 是否绑定 ${this.form.uniqueId}终端？`, '提示', {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'warning'
            }).then(() => {
              this.loading = true;
              connectuniqueid(query).then(res => {
                this.$message.success('终端绑定成功');
                this.$emit('update:dialogVisible', false);
                this.$emit('toQuery');
              }).finally(() => {
                this.loading = false;
              });
            });
          }
        }
      });
    },
    uniqueIdSearchHandle(val) {
      if (val) {
        this.remoteLoading = true;
        if (this.timer) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.getUniqueIdOptions(val).then(res=>{
            this.uniqueIdOptions = res;
            this.remoteLoading = false;
          });
        }, 500);
      } else {
        this.uniqueIdOptions = [];
      }
    },
    async getUniqueIdOptions (val) {
      let optionsReturn = [];
      const query = {
        uniqueId: val,
        current: 1,
        size: 10
      };
      const { code, data } = await driverUnbindVehicleDetail(query);
      if (code === 200 && data.records) {
        optionsReturn = data.records.map(item => ({
          value: item.uniqueId,
          label: `${item.uniqueId} ( ${item.categoryName} )`,
          specificity: Number(item.specificity)
        }));
      }
      return optionsReturn;
    },
    // 选择后触发
    uniqueIdSelectHandle(data) {
      this.uniqueIdObj = this.uniqueIdOptions.find(item => item.value === data);
    },
    closed() {
      this.remoteLoading = false;
      this.uniqueIdOptions = [];
      this.uniqueIdObj = {};
      this.timer = null;
      this.loading = false;
      this.$refs.form?.resetFields();
      this.$emit('update:dialogVisible', false);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-autocomplete {
  width: 90%;
}
</style>
