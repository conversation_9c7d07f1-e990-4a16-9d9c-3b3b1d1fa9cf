<template>
  <el-cascader
    v-model="vehicleUseTypeValue"
    placeholder="行业类型"
    :options="options"
    :props="{
      value: 'value',
      label: 'label',
      checkStrictly: true }"
    clearable
    size="small"
    :show-all-levels="false"
  />
</template>

<script>
export default {
  model: {
    prop: 'vehicleUseType'
  },
  props: {
    options: {
      type: [Object, Array],
      required: true
    },
    vehicleUseType: {
      type: [Array, undefined]
    }
  },
  data () {
    return {
      vehicleUseTypeValue: []
    };
  },
  watch: {
    'options': {
      handler (newValue) {
        this.mapNull(newValue);
      },
      immediate: true,
      deep: true
    },
    vehicleUseTypeValue (array) {
      console.log('-> array', array)
      if (array.length > 0) {
        this.$emit('input', array[array.length - 1]);
      } else {
        this.$emit('input', null);
      }
    },
    vehicleUseType(val){
      if(!val) {
        this.vehicleUseTypeValue = val
      }
    },
  },
  methods: {
    mapNull (value) {
      value && value.forEach(item => {
        if (!item.children || item.children.length === 0) {
          item.children = null;
        } else {
          this.mapNull(item.children);
        }
      });
    }
  }
};
</script>

<style>

</style>
