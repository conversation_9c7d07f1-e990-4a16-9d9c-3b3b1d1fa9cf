<template>
  <el-cascader
    ref="cascaderRef"
    v-model="selectedDeptId"
    :options="deptOptions"
    :props="config"
    clearable
    :show-all-levels="false"
    size="small"
    class="cascaderDeptIds"
    placeholder="请选择单位"
    filterable
  />
</template>
<script>
export default {
  name: 'DeptIds',
  data () {
    return {
      deptOptions: null,
      selectedDeptId: [],
      config: {
        value: 'id',
        label: 'label',
        checkStrictly: true,
        multiple: true
      }
    };
  },
  watch: {
    selectedDeptId (array) {
      let arr = array.map(item => {
        return item[item.length - 1];
      });
      let str = arr.join(',');
      this.$emit('input', str);
    }
  },
  created () {
    this.getDept();
  },
  methods: {
    getDept () {
      this.loading = true;
      this.$store.dispatch('GetDept').then(res => {
        this.deptOptions = res.content;
        // this.$nextTick(() => {
        //   this.$emit('init', this.selectedDeptId);
        // });
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>
<style lang="less" scoped>
.cascaderDeptIds ::v-deep .el-cascader__tags {
  white-space: nowrap;
  overflow: hidden;
  flex-wrap:nowrap
}

::v-deep .el-cascader-panel{
  height: 420px;
}
</style>
