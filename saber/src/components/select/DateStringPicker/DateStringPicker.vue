<template>
  <el-date-picker
    v-model="time"
    type="date"
    :placeholder="placeholder"
    :picker-options="pickerOptions"
    :disabled="disabled"
    @change="handleChange"
  />
</template>

<script>
import moment from 'moment';
export default {
  name: 'DateStringPicker',
  props: {
    value: {
      type: null,
      default: () => {
        return null;
      }
    },
    placeholder: {
      required: true,
      type: String
    },
    timeLimit: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    let self = this;
    return {
      pickerOptions: {
        disabledDate (time) {
          return self.timeLimit ? time.getTime() > Date.now() - 8.64e6 : false;
        }
      },
      loading: false,
      time: null
    };
  },
  watch: {
    value: {
      immediate: true,
      handler: function (val) {
        if (val) {
          this.setTime(val);
        } else {
          this.time = null;
        }
      }
    }
  },
  mounted () {
  },
  methods: {
    /**
     * 设置
     * @param {String} time
     */
    setTime (time) {
      this.time = time;
    },
    /**
     * 时间变化
     * @param {Date|String} val
     */
    handleChange (val) {
      console.log('DateStringPicker#handleChange', val);
      if (val) {
        this.$emit('input', val);
      } else {
        this.$emit('input', null);
      }
    }
  }
};
</script>

<style lang="less" scoped>

</style>
