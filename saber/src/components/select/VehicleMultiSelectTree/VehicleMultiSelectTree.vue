<template>
  <div
    v-loading="loading"
    :class="{'xh-dropDown-invisible': !isDropDown}"
    class="vehicle-tree-container"
  >
    <div
      ref="selectComponentContainerDom"
      class="vehicle-tree-content"
    >
      <div
        ref="headerDom"
        class="xh-select-component-header"
      >
        <span class="xh-select-component-header-title">终端列表</span>
        <span
          v-if="pageSrc === 'rm'"
          title="介绍"
          class="xh-select-component-header-refreshBtn"
          @click="$emit('questionHandle')"
        >
          <i class="el-icon-question" />
        </span>
        <span
          v-show="nextButtonVisible"
          title="展开下一个搜索结果"
          class="xh-select-component-header-refreshBtn"
          @click="onNext"
        >
          <i class="el-icon-bottom-right" />
        </span>
        <div
          v-if="!hiddenToggleButton"
          v-show="formVideoPage && !fromSettings"
          class="xh-select-component-toggle-video-button"
          @click="changeCarList"
        />
      </div>
      <div
        ref="firstCollapseItemComponent"
        class="header"
      >
        <div class="car-filter-container">
          <i class="el-icon-search" />
          <el-input
            v-model="filterText"
            clearable
            size="small"
            placeholder="搜索终端名称/终端类型/机构"
            class="car-filter"
            @input="searchFilterText"
          />
          <i
            class="el-icon-refresh"
            @click="handleRefresh"
          />
        </div>
      </div>
      <el-collapse
        v-show="isDropDown"
        v-model="activeNames"
        class="vehicle-tree-content-collapse"
        @change="handleCollapseChange"
      >
        <el-collapse-item
          name="secondItem"
          class="xh-select-component-collapse-item"
        >
          <!--车辆标题插槽-->
          <template slot="title">
            <div class="collapse-item-title-slot">
              终端目录
            </div>
          </template>
          <!--树渲染-->
          <vue-easy-tree
            ref="tree"
            :key="treeKey"
            :data="data"
            node-key="id"
            class="easy-tree"
            draggable
            show-checkbox
            :filter-node-method="filterHandle"
            :style="tableStyle"
            :height="treeMaxHeight"
            :default-checked-keys="defaultCheckedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :indent="10"
            @check="handleCheck"
            @node-expand="handleNodeExpand"
            @node-click="handleNodeClick"
            @node-collapse="handleNodeCollapse"
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <!-- <svg-icon
                v-if="data.type === 'dept'"
                icon-class="tree"
              /> -->
              <svg-icon
                v-if="data.type === 'dept'"
                :icon-class="getTreeIconClass(data)"
              />
              <span v-if="data.type === 'dept'">
                {{ node.label }}({{ data.onlineVehicleCount }}/{{ data.vehicleCount }})
              </span>
              <span v-if="data.type === 'dept_son'">
                {{ node.label }}({{ data.onlineVehicleCount }}/{{ data.vehicleCount }})
              </span>
              <span v-if="data.type !== 'dept' && data.type !== 'dept_son'">
                <svg-icon
                  :icon-class="getStaffIconClass(data)"
                  class="svg-icon-vehicle"
                />
                <span>{{ data.originData.targetName }}</span>
                <svg-icon :icon-class="getIconByterminal(data)" />
              </span>
              <!-- <span
                v-if="data.video === 1"
                title="历史视频"
                class="tree-icon"
                @click="toHistoryVideo(data)"
              >
                <svg-icon icon-class="historyVideo" />
              </span> -->
              <span
                v-if="data.type !== 'dept' && data.type !== 'dept_son'"
                title="历史轨迹查询"
                class="tree-icon"
                @click="toHistoryTrack(data)"
              >
                <svg-icon icon-class="trajectory" />
              </span>
            </span>
          </vue-easy-tree>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import crudVehicle from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import variables from '@/assets/less/variables.js';

export default {
  name: 'VehicleMultiSelect',
  components: {},
  directives: {
    drag (el, bindings, vnode) { // 横向拖拽tree
      el.onmousedown = bindings.value ? function (e) {
        let w = el.clientWidth || 230;
        let x = e.clientX;
        document.onmousemove = function (e) {
          let k = w + e.clientX - x;
          if (k >= 230 && k <= 660) {
            el.style.width = k + 'px';
            vnode.context.$emit('setTableWidth', k);
          }
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      } : null;
    }
  },
  props: {
    formVideoPage: {
      type: Boolean,
      default: false
    },
    detailButtonVisible: {
      type: Boolean,
      default: false
    },
    fromSettings: {
      type: Boolean,
      default: false
    },
    hiddenToggleButton: {
      type: Boolean,
      default: false
    },
    widthAble: {
      type: Boolean,
      default: false
    },
    pageSrc: {
      type: String,
      default: ''
    },
    customTreeApi: {
      type: Function,
      default: null
    },
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      name: '',
      permission: {
        add: ['admin', 'vehicle:add'],
        edit: ['admin', 'vehicle:edit'],
        del: ['admin', 'vehicle:del']
      },
      activeNames: ['secondItem'],
      tableStyle: '',
      nextButtonVisible: false,
      data: [],
      bakData: [], // 记录数据
      lastCheckedChannels: [],
      lastCheckedKeys: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      variables: {...variables},
      isDropDown: true,
      loading: false,
      currentVehicleKey: [],
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      treeMaxHeight: '0px',
      filterText: '',
      treeKey: 0 // 车辆树的key(用来更新树)
    };
  },
  watch: {
    isShow:{
      handler(val) {
        if (val) {
          this.$nextTick(()=> {
            this.onRefresh();
          });
        }
      },
      immediate: true
    }
  },
  mounted () {
    if (sessionStorage.getItem('storedVehicleTreeData')) {
      this.data = JSON.parse(sessionStorage.getItem('storedVehicleTreeData'));
    }
    this.$nextTick(() => {
      this.updateStyle();
      this.$nextTick(() => {
        if (this.$route.query.id) {
          this.setSelectedVehicle(this.$route.query);
        }
      });
    });
  },
  activated () {
    this.$nextTick(() => {
      if (this.$route.query.id) {
        this.setSelectedVehicle(this.$route.query);
      }
    });
  },
  methods: {
    // 点击行
    handleNodeClick(data) {
      this.$emit('treeNodeClick', data);
    },
    flattenList(data) {
      var result = [];
      data.forEach((item)=> {
        if (item.type !== 'dept' && item.type !== 'dept_son') {
          result.push(item);
        }
        if (item.children && item.children.length > 0) {
          var childItems = this.flattenList(item.children);
          result = result.concat(childItems);
        }
      });
      return result;
    },
    toHistoryTrack (data) {
      // let route = this.$router.resolve({ path: '/monitoring/trackInfo/index', query: {deviceId: data.originData.deviceId, deviceType: data.originData.deviceType, targetName: data.label, isRouter: this.$route.fullPath} });
      // window.open(route.href, '_blank');
      const trackInfo = JSON.stringify({
        deviceId: data.originData.deviceId,
        deviceType: data.originData.deviceType,
        targetType: data.originData.targetType,
        targetId: data.originData.targetId,
        targetName: data.label,
        id: data.id
      });
      localStorage.setItem('TRACK_INFO', trackInfo);
      this.$router.push({
        path: '/monitoring/trackInfo/index',
        query: {
          isRouter: this.$route.fullPath
        }
      });
    },
    toHistoryVideo(data){
      this.$router.push({
        path: '/monitoring/historyRTVS/index',
        query: {
          licencePlate: data.originData.licencePlate,
          licenceColor: data.originData.licenceColor,
          isRouter: this.$route.fullPath
        }
      });
    },
    /**
     * 点击刷新按钮
     */
    handleRefresh() {
      // 先清除地图上的车辆图标
      this.handleCheck({}, {checkedNodes: [], checkedKeys: []});
      this.onRefresh();
    },
    /**
     * 刷新
     */
    onRefresh () {
      this.loading = true;
      this.onClear();
      if (!this.customTreeApi) {
        crudVehicle.tree().then(treeData => {
          this.data = Object.freeze(treeData);
          this.nextButtonVisible = false;
          this.loading = false;
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      } else {
        this.customTreeApi().then(treeData => {
          this.data = treeData;
          this.nextButtonVisible = false;
          this.loading = false;
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      }
    },
    /**
     * 下一个
     */
    onNext () {
      this.$refs.tree.filter(this.query, {
        ignoreLeafType: 'dept',
        expandNew: true
      });
      this.nextButtonVisible = true;
    },
    /**
     * 节点过滤(替换新的查询搜索)
     */
    searchFilterText (val) {
      this.$refs.tree.filter(val);
    },
    /**
     * 节点搜索过滤函数(替换新的查询搜索)
     */
    filterHandle (value, data, node) {
      let parentNode = node.parent; // 父级node
      let labels = [node.label]; // 当前node的名字
      let level = 1; // 层级
      while (level < node.level) {
        labels = [...labels, parentNode.label]; // 当前node名字，父级node的名字
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(d => d.indexOf(value) !== -1);
    },
    /**
     * 勾选-处理多个
     * @param node
     * @param options
     * @param options.checkedNodes 勾选的节点
     * @param options.checkedKeys 勾选节点的key
     * @param options.halfCheckedNodes
     * @param options.halfCheckedKeys
     */
    handleCheck (node, options) {
      console.time();
      let checkedNodes = options.checkedNodes;
      let checkedKeys = options.checkedKeys;
      let checkedVehicles = [];
      let checkedPhones = [];
      let currentVehicle = [];
      let emptyDevice = [];
      let currentVehicleKey = {};
      // 上次存储的车辆，这次对checked = false不处理，checked = true的置false执行取消marker操作
      const checkedKeysObj = checkedKeys.reduce((obj, cur) => {
        obj[cur] = true;
        return obj;
      }, {});
      for (const key in this.currentVehicleKey) {
        if (!checkedKeysObj[key]) {
          currentVehicle.push({
            checked: false,
            id: key
          });
        }
      }
      checkedNodes.forEach(item => {
        if (item.type !== 'dept_son' && item.type !== 'dept') {
          checkedVehicles.push({
            id: item.id,
            deviceType: item.originData.deviceType,
            deviceId: item.originData.deviceId
          });
          checkedPhones.push(item.originData);
          if (!item.originData.deviceId) {
            emptyDevice.push(item.originData.targetName);
          } else {
            currentVehicleKey[item.id] = true;
            currentVehicle.push({
              checked: true,
              id: item.id,
              deviceType: item.originData.deviceType,
              deviceId: item.originData.deviceId
            });
          }
        }
      });
      if (emptyDevice.length) {
        let str = emptyDevice.join('');
        this.$notify({
          title: `${str}没有绑定设备`,
          type: 'warning'
        });
        this.currentVehicleKey = currentVehicleKey;
        this.defaultCheckedKeys = Object.keys(currentVehicleKey);
        this.treeKey += 1;
      } else {
        this.currentVehicleKey = currentVehicleKey;
      }
      console.timeEnd();
      this.$emit('checkedVehiclesChange', {
        checkedVehicles: checkedVehicles,
        currentVehicle: currentVehicle,
        // 该参数用于marker刷新
        setFitView: true
      });
      // 终端参数设置
      if (this.fromSettings) {
        this.$emit('setSelectedCars', checkedPhones);
      }
    },
    /**
     * 设置地图中心点
     * @param {Object} data
     */
    setLocation (data) {
      console.log('serLocation-->', data);
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass (data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'vehicle'); // 车辆
      } else if (materialsModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'materials'); // 物资
      } else if (personnelModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'personnel'); // 人员
      } else if (shortMessageModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'shortMessage'); // 短报文终端
      } else if (timeServiceModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'timeService'); // 授时终端
      } else if (monitorModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'monitor'); // 监测终端
      } else if (data.originData.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(data.originData, 'other'); // 其他
      }
      return vehicleIcon;
    },
    colorStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass (data) {
      let treeIcon = '';
      if (data.onlineVehicleCount > 0) {
        treeIcon = 'treeOnline';
      } else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 获取定位终端/视频终端
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getIconByterminal (data) {
      if (data.video === 0) {
        return 'positioningTerminal';
      } else if (data.video === 1) {
        return 'videoTerminal';
      }
    },
    /**
     * 设置keys
     * @param keys
     */
    setCheckedKeys (keys) {
      this.$refs.tree.setCheckedKeys(keys);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 切换状态
     */
    changeCarList () {
      this.$emit('changeCarList');
    },
    /**
     * 收缩状态改变
     * @param val
     */
    handleCollapseChange (val) {
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 更新样式
     */
    updateStyle () {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      let firstCollapseItemDomHeight = this.$refs['firstCollapseItemComponent'].clientHeight; // 搜索条件高度
      let headerDomHeight = this.$refs['headerDom'].clientHeight; // 车辆列表标题高度/车辆目录标题高度(两个高度一致)
      // const xhSpacingBase = variables.xhSpacingBase;// @xhSpacingBase
      // const collapseItemTitleHeight = variables.tagsViewHeight; // @tagsViewHeight
      // let maxHeight = selectComponentDomHeight - firstCollapseItemDomHeight - headerDomHeight - collapseItemTitleHeight - 4 * xhSpacingBase;
      let maxHeight = selectComponentDomHeight - firstCollapseItemDomHeight - 2 * headerDomHeight - 8;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        requestAnimationFrame(this.updateStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear () {
      this.filterText = '';
      // 清除勾选和展开节点的状态
      this.defaultCheckedKeys = [];
      this.defaultExpandedKeys = [];
    },
    /**
     * 点击详情按钮
     * @param val
     */
    toDetail (val) {
      this.$emit('toDetail', val);
    },
    /**
     * 点击多选框
     * @param selection
     * @param row
     */
    clickCheckbox (selection, row) {
      this.$emit('toDetail', row);
    },
    /**
     * 清空用户选择
     * @public
     */
    clear () {
      this.$refs.tree.setCheckedKeys([]);
    },
    /**
     * 设置选中的车辆并搜索
     * @param {String} val 车牌号
     */
    setSelectedVehicle (val) {
      if (this.loading) {
        setTimeout(() => {
          this.setSelectedVehicle(val);
        }, 1000);
      } else {
        // 替换新的查询搜索
        this.filterText = val.targetName;
        this.searchFilterText(this.filterText);
        // 搜索后勾选
        this.$refs.tree.setCheckedKeys([val.id], false);
        // 勾选后下发事件
        this.$emit('checkedVehiclesChange', {
          checkedVehicles: [{
            id: val.id,
            deviceType: val.deviceType,
            deviceId: val.deviceId
          }],
          currentVehicle: [{
            checked: true,
            id: val.id,
            deviceType: val.deviceType,
            deviceId: val.deviceId
          }],
          // 该参数用于marker刷新
          setFitView: true
        });
      }
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand (data, node) {
      this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse (data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
      // 收起父节点时同时收起子节点
      if (node.data.children.length > 0) {
        let list = this.flattenKeyList(node.data.children);
        for (let i = 0; i < list.length; i++) {
          const element = list[i];
          const key = this.defaultExpandedKeys.findIndex(item => item === element);
          if (key !== -1) {
            this.defaultExpandedKeys.splice(key, 1);
          }
        }
      }
    },
    flattenKeyList(data) {
      var result = [];
      data.forEach((item)=> {
        if (item.type === 'dept') {
          result.push(item.id);
          if (item.children && item.children.length > 0) {
            var childId = this.flattenList(item.children);
            result = result.concat(childId);
          }
        }
      });
      return result;
    },
    /**
     * 更新数据，保留之前的状态
     * @see defaultCheckedKeys
     * @see defaultExpandedKeys
     */
    onUpdate () {
      this.loading = true;
      if (!this.customTreeApi) {
        crudVehicle.tree().then(treeData => {
          if (JSON.stringify(treeData) !== JSON.stringify(this.data)) {
            // console.log('crudVehicle.tree-->', treeData);
            this.defaultCheckedKeys = this.$refs.tree.getCheckedKeys(true);
            this.data = Object.freeze(treeData);

            // 替换新的查询搜索
            setTimeout(() => {
              if (this.filterText) {
                this.searchFilterText(this.filterText);
              }
            }, 200);
          }
          this.loading = false;
          this.$emit('CheckedNodesUpdate', this.$refs.tree.getCheckedNodes());
        }).catch(msg => {
          this.loading = false;
        });
      } else {
        this.customTreeApi().then(treeData => {
          if (JSON.stringify(treeData) !== JSON.stringify(this.data)) {
            this.defaultCheckedKeys = this.$refs.tree.getCheckedKeys(true);
            this.data = Object.freeze(treeData);
            setTimeout(() => {
              if (this.filterText) {
                this.searchFilterText(this.filterText);
              }
            }, 200);
          }
          this.loading = false;
          this.$emit('CheckedNodesUpdate', this.$refs.tree.getCheckedNodes());
        }).catch(msg => {
          this.loading = false;
        });
      }
    }
  }

};

</script>

<style lang="less" scoped>
  @import "../../../assets/less/variables.less";
  .width-set{
    min-width:64px
  }
  .over-hidden{
    width: 450px;
    overflow: hidden;
  }
  .xh-select-component-collapse-item /deep/.el-collapse-item__content{
    padding-bottom: 0;
    padding-top: 0;
  }
  .xh-dropDown-invisible{
    width: 300px;
  }
  .xh-tree-live-map{
    // height: calc(100vh - 2 * @logoBarHeight -  @xhSpacingBase - 3*@tagsViewHeight) !important;
    // 车辆列表标题高度/车辆目录标题高度(36px) // 搜索条件高度(40px) // 车辆状态选择高度(70px) // 车辆总数高度(21px) // 绝对定位上下偏移(8px)
    height: calc(100vh - 2 * 36px -  40px - 70px - 21px - 8px) !important;
  }
  .custom-tree-node{
    width: 100%;
  }
  .custom-tree-node:hover{
    .tree-icon{
      display: inline-block;
    }
  }
  .tree-icon{
    padding-left: 5px;
    display: none;
  }
  .label-green{
    color: rgb(118, 191, 49);
  }
  .sign-green{
    background: rgb(118, 191, 49);
  }
  .label-gray{
    color: rgb(177, 177, 177);
  }
  .sign-gray{
    background: rgb(177, 177, 177);
  }
  .vehicle-tree-container, .vehicle-tree-content, .xh-select-component-collapse-item, .xh-select-component-collapse-item /deep/ .el-collapse-item__content, .easy-tree, .easy-tree /deep/ .vue-recycle-scroller{
    height: 100% !important;
  }
  .vehicle-tree-content, .xh-select-component-collapse-item {
    display: flex;
    flex-direction: column;
  }
  .vehicle-tree-content-collapse, .xh-select-component-collapse-item /deep/ .el-collapse-item__wrap{
    flex: 1;
  }
  .vehicle-tree-content-collapse{
    overflow: auto;
  }
  .easy-tree /deep/ .vue-recycle-scroller__item-wrapper{
    overflow: visible;
  }
  .header {
    background-color: #f3f6f8;
    height: 54px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .car-filter-container {
      display: flex;
      align-items: center;
      border: 1px solid #bfbfbf;
      background-color: #ffffff;
      padding: 0 4px;
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;

      ::v-deep .el-input__inner {
        border: none !important;
        box-shadow: none !important;
      }

      .el-icon-search, .el-icon-refresh {
        font-size: 18px;
        color: #c0c0c0;
      }

      .el-icon-refresh {
        cursor: pointer;
      }
    }
  }
</style>
