<template>
  <div
    v-loading="loading"
    class="vehicle-tree-container"
  >
    <div
      ref="selectComponentContainerDom"
      class="vehicle-tree-content"
    >
      <div
        ref="headerDom"
        class="xh-select-component-header"
      >
        <span class="xh-select-component-header-title">终端列表</span>
        <span
          v-if="pageSrc === 'rm'"
          title="介绍"
          class="xh-select-component-header-refreshBtn"
          @click="$emit('questionHandle')"
        >
          <i class="el-icon-question"/>
        </span>
      </div>
      <div
        ref="firstCollapseItemComponent"
        class="header"
      >
        <div class="car-filter-container">
          <i class="el-icon-search"/>
          <div class="search-input-wrapper">
            <el-input
              v-model="filterText"
              clearable
              size="small"
              placeholder="搜索终端名称/终端类型/机构"
              class="car-filter"
              @input="handleSearchInput"
              @focus="handleSearchFocus"
              @blur="handleSearchBlur"
            />
            <!-- 搜索结果下拉框 -->
            <div
              v-if="showSearchDropdown && searchResults.length > 0"
              class="search-dropdown"
              @scroll="handleSearchScroll"
            >
              <div
                v-for="item in searchResults"
                :key="item.id"
                class="search-dropdown-item"
                @click="handleSearchResultClick(item)"
              >
                <div class="search-item-name">{{ item.name }}</div>
<!--                <div class="search-item-path">{{ item.path }}</div>-->
              </div>
              <!-- 加载更多提示 -->
              <div
                v-if="searchPagination.hasMore"
                class="search-dropdown-item search-load-more"
              >
                <div class="search-item-name">
                  <i
                    v-if="searchLoadingMore"
                    class="el-icon-loading"
                  />
                  {{ searchLoadingMore ? '加载中...' : '滚动加载更多' }}
                </div>
              </div>
              <!-- 没有更多数据提示 -->
              <div
                v-else-if="searchResults.length > 0 && searchPagination.total > searchResults.length"
                class="search-dropdown-item search-no-more"
              >
                <div class="search-item-name">已显示全部结果</div>
              </div>
            </div>
            <!-- 搜索无结果提示 -->
            <div
              v-if="showSearchDropdown && searchResults.length === 0 && !searchLoading && filterText"
              class="search-dropdown search-no-result"
            >
              <div class="search-dropdown-item">
                <div class="search-item-name">无搜索结果</div>
              </div>
            </div>
            <!-- 搜索加载状态 -->
            <div
              v-if="showSearchDropdown && searchLoading"
              class="search-dropdown search-loading"
            >
              <div class="search-dropdown-item">
                <div class="search-item-name">
                  <i class="el-icon-loading"/> 搜索中...
                </div>
              </div>
            </div>
          </div>
          <i
            class="el-icon-refresh"
            @click="handleRefresh"
          />
          <el-button
            size="small"
            class="search-button"
            @click="searchFilterText(filterText)"
          >
            查询
          </el-button>
        </div>
      </div>
      <el-collapse
        v-model="activeNames"
        class="vehicle-tree-content-collapse"
        @change="handleCollapseChange"
      >
        <el-collapse-item
          name="secondItem"
          class="xh-select-component-collapse-item"
        >
          <!--车辆标题插槽-->
          <template slot="title">
            <div class="collapse-item-title-slot">
              终端目录
            </div>
          </template>
          <!--树渲染-->
          <vue-easy-tree
            ref="tree"
            :key="treeKey"
            :data="data"
            node-key="id"
            class="easy-tree"
            show-checkbox
            :lazy="lazy"
            :props="defaultProps"
            :load="loadTreeNode"
            :style="tableStyle"
            :height="treeMaxHeight"
            :default-checked-keys="defaultCheckedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :indent="10"
            :default-expand-all="defaultExpand"
            @check="handleCheck"
            @node-click="handleNodeClick"
            @node-expand="handleNodeExpand"
            @node-collapse="handleNodeCollapse"
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <span v-if="data.type === 'dept'">
                <svg-icon
                  :icon-class="getTreeIconClass(data)"
                />
                <span>
                  {{ data.name }}({{ data.onlineNum }}/{{ data.total }})
                </span>
              </span>
              <span v-if="data.type === 'device_type'">
                {{ data.name }}({{ data.onlineNum }}/{{ data.total }})
              </span>
              <span v-if="data.type === 'target' || data.type === 'device'">
                <svg-icon
                  :icon-class="getStaffIconClass(data)"
                  class="svg-icon-vehicle"
                />
                <span>{{ data.name }}</span>
                <svg-icon :icon-class="getIconByterminal(data)"/>
                <span
                  title="历史轨迹查询"
                  class="tree-icon"
                  @click="toHistoryTrack(node, data)"
                >
                  <svg-icon icon-class="trajectory"/>
                </span>
              </span>
            </span>
          </vue-easy-tree>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import crudVehicle from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  name: 'VehicleMultiSelect',
  components: {},
  directives: {
    drag(el, bindings, vnode) { // 横向拖拽tree
      el.onmousedown = bindings.value ? function (e) {
        let w = el.clientWidth || 230;
        let x = e.clientX;
        document.onmousemove = function (e) {
          let k = w + e.clientX - x;
          if (k >= 230 && k <= 660) {
            el.style.width = k + 'px';
            vnode.context.$emit('setTableWidth', k);
          }
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      } : null;
    }
  },
  props: {
    pageSrc: {
      type: String,
      default: ''
    },
    customTreeApi: {
      type: Function,
      default: null
    },
    isShow: {
      type: Boolean,
      default: false
    },
    monitorCarObj: {
      type: Object,
      default: () => {
        return {};
      }
    },
    fromCe: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeNames: ['secondItem'],
      tableStyle: '',
      data: [],
      loading: false,
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      treeMaxHeight: '0px',
      filterText: '',
      searchText: '', // 用来手动清除filterText值但未点击查询按钮的情况下搜索整个树节点使用
      treeKey: 0, // 车辆树的key(用来更新树)
      defaultProps: {
        isLeaf: 'leaf'
      },
      terminalTreeData: [],
      lazy: true,
      defaultExpand: false,
      treeCheckedKeys: [],
      filterCheckedKeys: [],
      // 搜索相关
      searchResults: [], // 搜索结果列表
      showSearchDropdown: false, // 是否显示搜索下拉框
      searchLoading: false, // 搜索加载状态
      searchTimer: null, // 搜索防抖定时器
      // 分页相关
      searchPagination: {
        current: 1, // 当前页
        size: 10, // 每页大小
        total: 0, // 总数
        hasMore: false // 是否还有更多数据
      },
      searchLoadingMore: false, // 加载更多状态
      currentSearchKeyword: '' // 当前搜索关键词
    };
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            if (this.$store.state.vehicle.tree?.length) {
              this.terminalTreeData = Object.freeze(this.$store.state.vehicle.tree);
              this.data = (this.$store.state.vehicle.tree || []).map(item => ({...item, children: []}));
            } else {
              this.onRefresh('store');
            }
          });
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.updateStyle();
      this.$nextTick(() => {
        if (this.$route.query.deviceId) {
          this.setSelectedVehicle(this.$route.query);
        }
      });
    });
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.deviceId) {
        this.setSelectedVehicle(this.$route.query);
      }
    });
  },
  beforeDestroy() {
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },
  methods: {
    // 点击行
    handleNodeClick(data, node) {
      this.$emit('treeNodeClick', {
        ...data,
        deviceType: data.type === undefined ? Number(node.parent.data.id.slice(-1)) : undefined
      });
    },
    // 修改终端树节点状态
    updateStateTerminal(data) {
      // 实测修改2000个终端状态, 执行需要两秒多, 期间会导致页面阻塞, 因此加个限制<500
      if (data && data.length && data.length < 500) {
        console.time();
        data.forEach(element => {
          this.updateNodeById(this.terminalTreeData, element.id, (node) => {
            node.fusionState = element.fusionState;
          });
        });
        const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
        if (this.lazy) {
          this.data = (this.terminalTreeData || []).map(item => ({...item, children: []}));
        } else {
          const list = this.filterNestedArray(this.terminalTreeData, this.searchText);
          this.data = list;
        }
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
        });
        console.timeEnd();
      }
    },
    // 查找对应的对象修改其内容
    updateNodeById(tree, id, updateFn) {
      for (let node of tree) {
        if (node.id === id) {
          updateFn(node);
          return true;
        }
        if (node.children && node.children.length > 0) {
          const found = this.updateNodeById(node.children, id, updateFn);
          if (found) {
            return true;
          }
        }
        if (node.tags && node.tags.length > 0) {
          const found = this.updateNodeById(node.tags, id, updateFn);
          if (found) {
            return true;
          }
        }
      }
      return false;
    },
    toHistoryTrack(node, data) {
      const trackInfo = JSON.stringify({
        deviceId: data.id,
        deviceType: Number(node.parent.data.id.at(-1)),
        targetName: data.name,
        id: data.id
      });
      localStorage.setItem('TRACK_INFO', trackInfo);
      this.$router.push({
        path: `/monitoring/trackInfo/${this.fromCe ? 'indexCE' : 'index'}`,
        query: {
          isRouter: this.$route.fullPath
        }
      });
    },
    /**
     * 点击刷新按钮
     */
    handleRefresh() {
      // 先清除地图上的车辆图标
      this.$emit('clearAll');
      this.onRefresh();
    },
    // 加载子节点
    loadTreeNode(node, resolve) {
      // 如果是根节点，直接返回空（根节点数据在onRefresh中加载）
      if (node.level === 0) {
        return resolve([]);
      }

      // 获取当前节点的ID
      const nodeId = node.data.id;
      this.defaultExpandedKeys.push(nodeId);

      // 调用动态树接口，传递当前节点ID作为展开节点
      crudVehicle.dynamicTree({expandedNodeIds: this.defaultExpandedKeys}).then(treeData => {
        if (JSON.stringify(treeData) !== JSON.stringify(this.terminalTreeData)) {
          // console.log('crudVehicle.tree-->', treeData);
          // 直接使用default-checked-keys传入keys可能会出现不可预估的错误, 因此使用setCheckedKeys设置keys
          const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
          this.terminalTreeData = Object.freeze(treeData);
          // if (this.lazy) {
          //   this.data = (treeData || []).map(item => ({...item, children: []}));
          // } else {
          //   const list = this.filterNestedArray(this.terminalTreeData, this.searchText);
          //   this.data = list;
          // }
          this.$nextTick(() => {
            this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
          });
        }
        // 在完整树数据中查找当前节点
        const currentNode = this.findInNestedArray(treeData, item => item.id === nodeId);

        if (currentNode) {
          // 获取子节点数据，优先使用tags，其次使用children
          const children = currentNode.children || [];

          // 处理子节点数据，确保格式统一
          const processedChildren = children.map(child => ({
            ...child,
            // 如果有tags属性，将其重命名为children以保持一致性
            children: child.children || [],
            // 标记是否为叶子节点（没有子节点的设备节点）
            leaf: !(child.type === 'dept' || child.type === 'device_type' || child.children.length !== 0)
          }));

          // 更新terminalTreeData中对应节点的children
          this.updateNodeById(this.terminalTreeData, nodeId, (targetNode) => {
            targetNode.children = processedChildren;
            if (targetNode.tags) {
              targetNode.tags = processedChildren;
            }
          });

          resolve(processedChildren);

        } else {
          resolve([]);
        }
      }).catch(error => {
        console.error('加载子节点失败:', error);
        this.$notify({
          title: '加载子节点失败',
          message: error.message || '网络错误',
          type: 'error'
        });
        resolve([]);
      });
    },
    findInNestedArray(array, condition) {
      for (let item of array) {
        if (condition(item)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = this.findInNestedArray(item.children, condition);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    /**
     * 刷新
     */
    onRefresh(type) {
      this.loading = true;
      this.onClear();
      if (!this.customTreeApi) {
        // 使用新的动态树接口，传递当前展开的节点ID
        crudVehicle.dynamicTree({expandedNodeIds: this.defaultExpandedKeys}).then(treeData => {
          this.terminalTreeData = Object.freeze(treeData);
          this.data = (treeData || []).map(item => ({...item, children: []}));
          this.loading = false;
          if (type === 'store') {
            this.$store.commit('SET_TREE', treeData || []);
          }
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      } else {
        this.customTreeApi().then(treeData => {
          this.terminalTreeData = Object.freeze(treeData);
          this.data = (treeData || []).map(item => ({...item, children: []}));
          this.loading = false;
          if (type === 'store') {
            this.$store.commit('SET_TREE', treeData || []);
          }
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      }
      // 滚动后刷新接口无法恢复到初始状态, 因此手动将scrollTop设为0
      this.$nextTick(() => {
        if (this.$refs?.tree.$children[0]?.$el) {
          this.$refs.tree.$children[0].$el.scrollTop = 0;
        }
      });
    },

    searchFilterText(val, isLoadMore = false) {
      if (!val || !val.trim()) {
        this.showSearchDropdown = false;
        this.searchResults = [];
        this.resetSearchPagination();
        return;
      }

      // 如果是新搜索，重置分页和结果
      if (!isLoadMore) {
        this.searchResults = [];
        this.resetSearchPagination();
        this.currentSearchKeyword = val.trim();
      }

      this.searchLoading = !isLoadMore;
      this.searchLoadingMore = isLoadMore;
      this.showSearchDropdown = true;

      // 构建分页参数
      const params = {
        keyword: this.currentSearchKeyword,
        current: this.searchPagination.current,
        size: this.searchPagination.size
      };

      // 使用新的 /tree/search 接口进行搜索
      crudVehicle.treeSearch(params).then(res => {
        this.searchLoading = false;
        this.searchLoadingMore = false;

        if (res && res.records && Array.isArray(res.records)) {
          // 处理分页数据结构，搜索结果在 records 数组中
          const newResults = res.records.map(item => ({
            id: item.id,
            name: item.name,
            // path: this.buildNodePath(item),
            originalData: item
          }));

          // 如果是加载更多，追加结果；否则替换结果
          if (isLoadMore) {
            this.searchResults = [...this.searchResults, ...newResults];
          } else {
            this.searchResults = newResults;
          }

          // 更新分页信息
          this.searchPagination.total = res.total || 0;
          this.searchPagination.current = res.current || 1;
          this.searchPagination.hasMore = this.searchResults.length < this.searchPagination.total;
        } else {
          if (!isLoadMore) {
            this.searchResults = [];
          }
          this.searchPagination.hasMore = false;
        }
      }).catch(error => {
        this.searchLoading = false;
        this.searchLoadingMore = false;
        if (!isLoadMore) {
          this.searchResults = [];
        }
        console.error('搜索失败:', error);
        this.$message.error('搜索失败，请稍后重试');
      });
    },

    /**
     * 重置搜索分页信息
     */
    resetSearchPagination() {
      this.searchPagination = {
        current: 1,
        size: 10,
        total: 0,
        hasMore: false
      };
    },

    /**
     * 处理搜索下拉框滚动事件
     */
    handleSearchScroll(event) {
      const {target} = event;
      const {scrollTop, scrollHeight, clientHeight} = target;

      // 当滚动到底部附近时（距离底部20px），触发加载更多
      if (scrollTop + clientHeight >= scrollHeight - 20) {
        this.loadMoreSearchResults();
      }
    },

    /**
     * 加载更多搜索结果
     */
    loadMoreSearchResults() {
      if (this.searchLoadingMore || !this.searchPagination.hasMore) {
        return;
      }

      // 增加页码
      this.searchPagination.current += 1;

      // 加载更多数据
      this.searchFilterText(this.currentSearchKeyword, true);
    },

    /**
     * 构建节点路径信息
     */
    buildNodePath(item) {
      // 接口返回的数据没有 path 字段，需要构建路径信息
      // 根据 type 字段判断节点类型
      if (item.type === 'target') {
        // 终端节点，显示类型信息
        return `终端 - ${item.name}`;
      } else if (item.type === 'dept') {
        // 部门节点
        return `部门 - ${item.name}`;
      } else {
        // 其他类型节点
        return item.name || '';
      }
    },

    /**
     * 处理搜索输入
     */
    handleSearchInput(val) {
      // 防抖处理，避免频繁请求
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        if (val && val.trim()) {
          this.searchFilterText(val, false); // 新搜索，不是加载更多
        } else {
          this.showSearchDropdown = false;
          this.searchResults = [];
          this.resetSearchPagination();
        }
      }, 300);
    },

    /**
     * 处理搜索框获得焦点
     */
    handleSearchFocus() {
      if (this.filterText && this.searchResults.length > 0) {
        this.showSearchDropdown = true;
      }
    },

    /**
     * 处理搜索框失去焦点
     */
    handleSearchBlur() {
      // 延迟隐藏下拉框，以便点击下拉项
      setTimeout(() => {
        this.showSearchDropdown = false;
      }, 200);
    },

    /**
     * 处理搜索结果点击
     */
    handleSearchResultClick(item) {
      this.showSearchDropdown = false;
      this.filterText = item.name;

      // 根据选中的节点ID，重新查询树结构
      this.expandToNode(item.id);
    },

    /**
     * 展开到指定节点
     */
    expandToNode(nodeId) {
      // 查找节点的所有父级ID
      const ancestorIds = this.findAncestorsById(this.terminalTreeData, nodeId);

      if (ancestorIds && ancestorIds.length > 0) {
        // 将父级ID添加到展开列表中
        this.defaultExpandedKeys = [...new Set([...this.defaultExpandedKeys, ...ancestorIds, nodeId])];

        // 重新查询树结构
        this.loading = true;
        crudVehicle.dynamicTree({expandedNodeIds: this.defaultExpandedKeys}).then(treeData => {
          this.terminalTreeData = Object.freeze(treeData);
          this.data = (treeData || []).map(item => ({...item, children: []}));
          this.loading = false;

          // 等待树渲染完成后，高亮并选中目标节点
          this.$nextTick(() => {
            this.highlightAndSelectNode(nodeId);
          });
        }).catch(error => {
          this.loading = false;
          console.error('查询树结构失败:', error);
        });
      } else {
        // 如果找不到父级，直接尝试选中节点
        this.$nextTick(() => {
          this.highlightAndSelectNode(nodeId);
        });
      }
    },

    /**
     * 高亮并选中节点
     */
    highlightAndSelectNode(nodeId) {
      if (this.$refs.tree) {
        // 设置当前节点
        this.$refs.tree.setCurrentKey(nodeId);

        // 滚动到节点位置
        const node = this.$refs.tree.getNode(nodeId);
        if (node && node.$el) {
          node.$el.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
      }
    },

    // 查找指定id的节点及其所有父级和祖宗级的id
    findAncestorsById(data, targetId) {
      const ancestors = [];
      return this.findRecursive(data, targetId, ancestors);
    },
    findRecursive(items, targetId, path = []) {
      for (let item of items) {
        if (item.id === targetId) {
          return [...path];
        }
        if (item.children && item.children.length > 0) {
          const result = this.findRecursive(item.children, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
        if (item.tags && item.tags.length > 0) {
          const result = this.findRecursive(item.tags, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },
    filterNestedArray(array, filterValue) {
      const result = [];
      result.push(...this.recursiveFilter(array, filterValue));
      return result;
    },
    recursiveFilter(items, filterValue) {
      return items.reduce((acc, item) => {
        if (item.name.includes(filterValue)) {
          acc.push(this.renameTagsToChildren({...item}));
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.recursiveFilter(item.children, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({...item, children: filteredChildren});
          }
        } else if (item.tags && item.tags.length > 0) {
          const filteredChildren = this.recursiveFilter(item.tags, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({...item, children: filteredChildren});
          }
        }
        return acc;
      }, []);
    },
    // 将所有的tags属性值替换为children, 保持统一便组件使用
    renameTagsToChildren(item) {
      if (item.tags) {
        item.children = item.tags;
      }
      if (item.children) {
        item.children = item.children.map(child => this.renameTagsToChildren(child));
      }
      return item;
    },
    /**
     * 勾选-处理多个
     * @param node
     * @param options
     * @param options.checkedNodes 勾选的节点
     * @param options.checkedKeys 勾选节点的key
     * @param options.halfCheckedNodes
     * @param options.halfCheckedKeys
     */
    handleCheck(node, options) {
      console.log('node', node);
      console.log('options', options);
      let checkedKeys = options.checkedKeys;
      let checkedNode = node;
      let treeData = !this.lazy ? this.data : this.terminalTreeData;
      checkedNode.checked = checkedKeys.includes(node.id);
      if (!this.lazy && checkedNode.checked) {
        this.filterCheckedKeys.push(node.id);
      } else if (!this.lazy && !checkedNode.checked) {
        this.filterCheckedKeys = this.filterCheckedKeys.filter(item => item !== node.id);
        this.treeCheckedKeys = this.treeCheckedKeys.filter(item => item !== node.id);
      }
      let checkedList = [];
      // 勾选
      if (checkedNode.checked) {
        // 勾选机构或分组时
        if (checkedNode.type !== undefined) {
          const result = this.findInNestedArray(treeData, item => item.id === checkedNode.id);
          if (result) {
            checkedList = this.getAllTerminal([result]);
          }
        } else {
          const nodeData = this.$refs.tree.getNode(checkedNode.id);
          // 勾选终端时
          checkedList = [{
            ...checkedNode,
            deviceType: Number(nodeData.parent.data.id.slice(-1))
          }];
        }
      } else { // 取消勾选
        // 取消勾选机构或分组时
        if (checkedNode.type !== undefined) {
          const result = this.findInNestedArray(treeData, item => item.id === checkedNode.id);
          if (result) {
            checkedList = this.getDelTerminal([result]);
          }
        } else {
          const nodeData = this.$refs.tree.getNode(checkedNode.id);
          // 取消勾选终端时
          checkedList = [{
            ...checkedNode,
            deviceType: Number(nodeData.parent.data.id.slice(-1))
          }];
        }
      }
      this.$emit('checkedVehiclesChange', checkedNode, checkedList);
    },
    // 获取勾选节点下的所有终端(已显示的不在获取), 返回一个终端数组
    getAllTerminal(data) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.tags && element.tags.length) {
          for (let k = 0; k < element.tags.length; k++) {
            const item = element.tags[k];
            if (!this.monitorCarObj[item.id]) {
              arr.push({
                ...item,
                deviceType: Number(element.id.slice(-1))
              });
            }
          }
          // arr = arr.concat(element.tags.filter(v => !this.monitorCarObj[v.id]));
        }
        if (element.children && element.children.length) {
          const result = this.getAllTerminal(element.children);
          arr = arr.concat(result);
        }
      }
      return arr;
    },
    // 获取勾选节点下的所有终端(已删除的不在获取), 返回一个终端数组
    getDelTerminal(data) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.tags && element.tags.length) {
          for (let k = 0; k < element.tags.length; k++) {
            const item = element.tags[k];
            if (this.monitorCarObj[item.id]) {
              arr.push({
                ...item,
                deviceType: Number(element.id.slice(-1))
              });
            }
          }
          // arr = arr.concat(element.tags.filter(v => this.monitorCarObj[v.id]));
        }
        if (element.children && element.children.length) {
          const result = this.getDelTerminal(element.children);
          arr = arr.concat(result);
        }
      }
      return arr;
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass(data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      let category = data.category.toString();
      if (vehicleModel.includes(category)) {
        vehicleIcon = this.colorStaffType(data, 'vehicle'); // 车辆
      } else if (materialsModel.includes(category)) {
        vehicleIcon = this.colorStaffType(data, 'materials'); // 物资
      } else if (personnelModel.includes(category)) {
        vehicleIcon = this.colorStaffType(data, 'personnel'); // 人员
      } else if (shortMessageModel.includes(category)) {
        vehicleIcon = this.colorStaffType(data, 'shortMessage'); // 短报文终端
      } else if (timeServiceModel.includes(category)) {
        vehicleIcon = this.colorStaffType(data, 'timeService'); // 授时终端
      } else if (monitorModel.includes(category)) {
        vehicleIcon = this.colorStaffType(data, 'monitor'); // 监测终端
      } else if (category === '0') {
        vehicleIcon = this.colorStaffType(data, 'other'); // 其他
      }
      return vehicleIcon;
    },
    colorStaffType(val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass(data) {
      let treeIcon = '';
      if (data.onlineNum > 0) {
        treeIcon = 'treeOnline';
      } else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 获取定位终端/视频终端
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getIconByterminal(data) {
      if (data.isVideo === 0) {
        return 'positioningTerminal';
      } else if (data.isVideo === 1) {
        return 'videoTerminal';
      }
    },
    /**
     * 设置keys
     * @param keys
     */
    setCheckedKeys(keys) {
      this.$refs.tree.setCheckedKeys(keys);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 收缩状态改变
     * @param val
     */
    handleCollapseChange(val) {
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 更新样式
     */
    updateStyle() {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      let firstCollapseItemDomHeight = this.$refs['firstCollapseItemComponent'].clientHeight; // 搜索条件高度
      let headerDomHeight = this.$refs['headerDom'].clientHeight; // 车辆列表标题高度/车辆目录标题高度(两个高度一致)
      let maxHeight = selectComponentDomHeight - firstCollapseItemDomHeight - 2 * headerDomHeight - 8;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        requestAnimationFrame(this.updateStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear() {
      this.filterText = '';
      this.searchText = '';
      // 清除勾选和展开节点的状态
      this.defaultCheckedKeys = [];
      this.defaultExpandedKeys = [];
      this.treeCheckedKeys = [];
      this.filterCheckedKeys = [];
      this.lazy = true;
      this.defaultExpand = false;
      // 修改lazy后需要手动更新tree
      this.treeKey += 1;
    },
    /**
     * 清空用户选择
     * @public
     */
    clear() {
      this.$refs.tree.setCheckedAll(false);
    },
    /**
     * 设置选中的车辆并搜索
     * @param {String} val 车牌号
     */
    setSelectedVehicle(val) {
      if (this.loading) {
        setTimeout(() => {
          this.setSelectedVehicle(val);
        }, 1000);
      } else {
        // 替换新的查询搜索
        this.filterText = val.targetName;
        this.searchFilterText(this.filterText);
        this.$nextTick(() => {
          // 搜索后勾选
          this.$refs.tree.setCheckedKeys([val.deviceId], false);
          const {data: node, parent} = this.$refs.tree.getNode(val.deviceId);
          node.checked = true;
          this.filterCheckedKeys.push(node.id);
          let checkedList = [];
          // 勾选机构或分组时
          if (node.type !== undefined) {
            const result = this.findInNestedArray(this.data, item => item.id === node.id);
            if (result) {
              checkedList = this.getAllTerminal([result]);
            }
          } else {
            // 勾选终端时
            checkedList = [{
              ...node,
              deviceType: Number(parent.data.id.slice(-1))
            }];
          }
          // 勾选后下发事件
          this.$emit('checkedVehiclesChange', node, checkedList);
        });
      }
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand(data, node) {
      // this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse(data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
      // 收起父节点时同时收起子节点
      if (node.childNodes.length > 0) {
        let list = this.flattenKeyList(node.childNodes);
        for (let i = 0; i < list.length; i++) {
          const element = list[i];
          const key = this.defaultExpandedKeys.findIndex(item => item === element);
          if (key !== -1) {
            this.defaultExpandedKeys.splice(key, 1);
          }
        }
      }
    },
    flattenKeyList(data) {
      let result = [];
      data.forEach((item) => {
        if (item.data.type === 'dept' || item.data.type === 'device_type') {
          result.push(item.data.id);
          if (item.childNodes && item.childNodes.length > 0 && item.data.type === 'dept') {
            let childId = this.flattenKeyList(item.childNodes);
            result = result.concat(childId);
          }
        }
      });
      return result;
    },
    /**
     * 更新数据，保留之前的状态
     * @see defaultCheckedKeys
     * @see defaultExpandedKeys
     */
    onUpdate() {
      // console.log("刷新树");
      // // this.loading = true;
      // if (!this.customTreeApi) {
      //   // 使用新的动态树接口，传递当前展开的节点ID
      //   crudVehicle.dynamicTree({ expandedNodeIds: this.defaultExpandedKeys }).then(treeData => {
      //     if (JSON.stringify(treeData) !== JSON.stringify(this.terminalTreeData)) {
      //       // console.log('crudVehicle.tree-->', treeData);
      //       // 直接使用default-checked-keys传入keys可能会出现不可预估的错误, 因此使用setCheckedKeys设置keys
      //       const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
      //       this.terminalTreeData = Object.freeze(treeData);
      //       if (this.lazy) {
      //         this.data = (treeData || []).map(item => ({...item, children: []}));
      //       } else {
      //         const list = this.filterNestedArray(this.terminalTreeData, this.searchText);
      //         this.data = list;
      //       }
      //       this.$nextTick(() => {
      //         this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
      //       });
      //     }
      //     // this.loading = false;
      //     this.$emit('CheckedNodesUpdate');
      //   }).catch(msg => {
      //     // this.loading = false;
      //   });
      // } else {
      //   this.customTreeApi().then(treeData => {
      //     if (JSON.stringify(treeData) !== JSON.stringify(this.terminalTreeData)) {
      //       // 直接使用default-checked-keys传入keys可能会出现不可预估的错误, 因此使用setCheckedKeys设置keys
      //       const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
      //       this.terminalTreeData = Object.freeze(treeData);
      //       if (this.lazy) {
      //         this.data = (treeData || []).map(item => ({...item, children: []}));
      //       } else {
      //         const list = this.filterNestedArray(this.terminalTreeData, this.searchText);
      //         this.data = list;
      //       }
      //       this.$nextTick(() => {
      //         this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
      //       });
      //     }
      //     // this.loading = false;
      //     this.$emit('CheckedNodesUpdate');
      //   }).catch(msg => {
      //     // this.loading = false;
      //   });
      // }
      // 滚动后刷新接口无法恢复到初始状态, 因此手动将scrollTop设为0
      // this.$nextTick(() => {
      //   if (this.$refs?.tree.$children[0]?.$el) {
      //     this.$refs.tree.$children[0].$el.scrollTop = 0;
      //   }
      // });
    }
  }

};

</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";

.xh-select-component-collapse-item /deep/ .el-collapse-item__content {
  padding-bottom: 0;
  padding-top: 0;
}

.custom-tree-node {
  width: 100%;
}

.custom-tree-node:hover {
  .tree-icon {
    display: inline-block;
  }
}

.tree-icon {
  padding-left: 5px;
  display: none;
}

.vehicle-tree-container, .vehicle-tree-content, .xh-select-component-collapse-item, .xh-select-component-collapse-item /deep/ .el-collapse-item__content, .easy-tree, .easy-tree /deep/ .vue-recycle-scroller {
  height: 100% !important;
}

.vehicle-tree-content, .xh-select-component-collapse-item {
  display: flex;
  flex-direction: column;
}

.vehicle-tree-content-collapse, .xh-select-component-collapse-item /deep/ .el-collapse-item__wrap {
  flex: 1;
}

.vehicle-tree-content-collapse {
  overflow: auto;
}

.easy-tree /deep/ .vue-recycle-scroller__item-wrapper {
  overflow: visible;
}

.header {
  background-color: #f3f6f8;
  height: 54px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;

  .car-filter-container {
    display: flex;
    align-items: center;
    border: 1px solid #bfbfbf;
    background-color: #ffffff;
    padding-left: 4px;
    flex: 1;
    box-sizing: border-box;
    overflow: visible;

    .search-input-wrapper {
      position: relative;
      flex: 1;

      .search-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #ffffff;
        border: 1px solid #e4e7ed;
        border-top: none;
        border-radius: 0 0 4px 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;

        .search-dropdown-item {
          padding: 8px 12px;
          cursor: pointer;
          border-bottom: 1px solid #f5f7fa;

          &:hover {
            background-color: #f5f7fa;
          }

          &:last-child {
            border-bottom: none;
          }

          .search-item-name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 2px;
          }

          .search-item-path {
            font-size: 12px;
            color: #909399;
          }
        }

        &.search-no-result,
        &.search-loading {
          .search-dropdown-item {
            cursor: default;
            text-align: center;
            color: #909399;

            &:hover {
              background-color: transparent;
            }
          }
        }

        .search-load-more {
          cursor: default;
          text-align: center;
          color: #909399;
          font-size: 12px;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .search-no-more {
          cursor: default;
          text-align: center;
          color: #c0c4cc;
          font-size: 12px;

          &:hover {
            background-color: transparent;
          }
        }
      }
    }

    ::v-deep .el-input__inner {
      border: none !important;
      box-shadow: none !important;
    }

    .el-icon-search, .el-icon-refresh {
      font-size: 18px;
      color: #c0c0c0;
    }

    .el-icon-refresh {
      padding-right: 4px;
      cursor: pointer;
    }

    .search-button {
      border: none;
      border-left: 1px solid #bfbfbf !important;
      border-radius: 0 !important;
    }
  }
}
</style>
