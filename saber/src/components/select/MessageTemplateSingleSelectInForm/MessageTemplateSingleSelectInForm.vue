<template>
  <div>
    <xh-select
      v-model="obj"
      value-key="id"
      name="content"
      clearable
      filterable
      :filter-method="search"
      style="width: 370px;"
      placeholder="请选择"
      @change="handleChange"
    >
      <el-option
        v-for="item in lastList"
        :key="item.id"
        :label="item.content"
        :value="item"
      >
        <span class="multiSelect-option-name">
          {{ item.content }}
          <span v-if="item.type">
            （{{ getLabel('type') }}：{{ getEnumDictLabel('type', item.type) }}）
          </span>
        </span>
      </el-option>
      <div
        v-if="lastList.length > 0"
        class="divider"
      />
      <el-option
        v-for="item in crud.data"
        :key="item.id"
        :label="item.content"
        :value="item"
      >
        <span class="multiSelect-option-name">
          {{ item.content }}
          <span v-if="item.type">
            （{{ getLabel('type') }}：{{ getEnumDictLabel('type', item.type) }}）
          </span>
        </span>
      </el-option>
      <!--分页-->
      <!--分页组件-->
      <pagination
        ref="paginationComponent"
        layout="total, prev, next, jumper"
      />
      <!--表单渲染-->
      <eForm :message-template-type="dict.messageTemplateType" />
    </xh-select>
    <a
      v-show="showInfo"
      class="message-info"
      @click="addTemplateMessage"
    >
      预设常用信息
    </a>
  </div>
</template>

<script>
import crudMessageTemplate from '@/api/system/messageTemplate';
import CRUD, { presenter, header } from '@/components/Crud/crud';
// import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
// import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
// import eHeader from './module/header';
import StringUtil from '@/utils/helper/StringUtil';
import eForm from './module/form';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('MessageTemplate', 'uniName'), // 车辆
  sort: 'id,desc',
  crudMethod: { ...crudMessageTemplate }
});

export default {
  name: 'MessageTemplateSingleSelectInForm',
  components: { pagination, eForm },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: ['messageTemplateType'],
  props: {
    value: {
      type: String,
      required: true
    },
    showInfo: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      permission: {
        add: ['admin', 'messageTemplate:add'],
        edit: ['admin', 'messageTemplate:edit'],
        del: []
      },
      obj: '',
      // 旧数据
      lastList: []
    };
  },
  watch: {
    value (content) {
      // console.log('content', content);
      // this.updateValue(content);
    }
  },
  mounted () {
    if (this.value) {
      this.updateValue(this.value);
    }
  },
  methods: {
    updateValue (content) {
      // console.log('content', content);
      if (content) {
        this.lastList = [
          {
            content: content,
            id: StringUtil.generateGuid()
          }
        ];
        this.obj = this.lastList[0];
      } else {
        this.lastList = [];
      }
    },

    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('MessageTemplate', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('MessageTemplate', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 搜索
     */
    search (val) {
      this.query.blurry = val;
      crud.toQuery();
    },
    /**
     * 变化
     * @param {Array.<Object>} val
     */
    handleChange (val) {
      // console.log('obj', val)
      if (val && val.content) {
        this.$emit('input', val.content);
      } else {
        this.$emit('input', '');
      }
    },
    /**
     * 添加预设信息
     */
    addTemplateMessage () {
      crud.toAdd();
    }
  }
};
</script>

<style lang="less" scoped>
  @import "../../../assets/less/variables.less";

  .multiSelect-option-name{
    float: left;
  }
  .multiSelect-option-id{
    float: right;
    margin-right: 30px;
    font-size: @xhFontSizeContent1
  }
  .divider{
    border-bottom: solid 1px @xhTextLineColor4;
  }
  .message-info{
    margin-left: 10px;
    color: var(--gn-color);
  }
</style>
