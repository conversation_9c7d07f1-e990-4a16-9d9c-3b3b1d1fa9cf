<template>
  <el-dialog
    v-dialog-drag
    append-to-body
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="crud.status.title"
    width="500px"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="80px"
    >
      <el-form-item
        :label="getLabel('content')"
        prop="content"
      >
        <el-input
          v-model="form.content"
          type="textarea"
          :autosize="{ minRows: 5}"
          maxlength="40"
          show-word-limit
          :placeholder="getPlaceholder('content')"
        />
      </el-form-item>
      <!-- <el-form-item
        :label="getLabel('type')"
        prop="type"
      >
        <xh-select
          v-model="form.type"
          clearable
          :placeholder="getPlaceholder('type')"
        >
          <el-option
            v-for="item in messageTemplateType"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          />
        </xh-select>
      </el-form-item> -->
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
// import { getEnabledDepts } from '@/api/base/dept';
// import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import contentValidate from '@/validate/messageTemplate/content';
import typeValidate from '@/validate/messageTemplate/type';
// import DateTimeStampPicker from '@/components/select/DateTimeStampPicker/DateTimeStampPicker';

const defaultForm = {
  id: null,
  content: '',
  type: null
};
export default {
  // components: { Treeselect, DateTimeStampPicker },
  mixins: [form(defaultForm)],
  props: {
    messageTemplateType: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      depts: [],
      rules: {
        content: { required: true, validator: contentValidate.defaultValidate, trigger: 'blue' }
        // type: { required: true, validator: typeValidate.defaultValidate, trigger: 'change' }
      }
    };
  },
  methods: {
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('MessageTemplate', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('MessageTemplate', value);
    },
    [CRUD.HOOK.beforeToAdd] () {
      // this.form.content = '';
      this.$set(this.form, 'content', '');
      this.form.type = null;
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
