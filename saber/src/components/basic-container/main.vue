<template>
  <div class="basic-container"
       :style="styleName"
       :class="{'basic-container--block':block}">
    <el-card class="basic-container__card">
      <slot></slot>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "basicContainer",
  props: {
    radius: {
      type: [String, Number],
      default: 0
    },
    background: {
      type: String
    },
    block: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    styleName () {
      return {
        borderRadius: this.setPx(this.radius),
        background: this.background,
      }
    }
  }
};
</script>

<style lang="scss">
.basic-container {
  // padding: 4px;
  box-sizing: border-box;
  height: 100%;
  &--block {
    height: 100%;
    .basic-container__card {
      height: 100%;
    }
  }
  .basic-container__card.el-card.is-always-shadow {
    box-shadow: none;
  }
  &__card {
    width: 100%;
  }
  // &:first-child {
  //   padding-top: 0;
  // }
  .basic-container__card {
    border-radius: 0;
    height: 100%;
    border: none;
    box-shadow: none;
    .el-card__body {
      height: 100%;
      padding: 12px 16px; // TODO 去除!important, 防止其他个性化样式被覆盖
    }
  }
}
</style>
