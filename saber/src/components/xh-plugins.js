// import XhFormHeader from './XhFormHeader';
// import XhFormConfirm from './XhFormConfirm';
// import XhFormCancel from './XhFormCancel';
// import XhFormReset from './XhFormReset';
// import XhFormEdit from './XhFormEdit';
import xhImage from './xhPlugins/image/src/main';
// import xhImg from './xhPlugins/img/xhImage';
import xhTree from './xhPlugins/tree/tree';
import xhSelect from './xhPlugins/select/select';

function plugins (Vue) {
  // Vue.component('xh-form-header', XhFormHeader);
  // Vue.component('xh-form-button-confirm', XhFormConfirm);
  // Vue.component('xh-form-button-cancel', XhFormCancel);
  // Vue.component('xh-form-button-reset', XhFormReset);
  // Vue.component('xh-form-button-edit', XhFormEdit);
  Vue.component('xh-image', xhImage);
  // Vue.component('xh-img', xhImg);
  Vue.component('xh-tree', xhTree);
  Vue.component('xh-select', xhSelect);
}

export default plugins;
