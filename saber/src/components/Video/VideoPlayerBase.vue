<template>
  <div>
    <div ref="VideoPlayerContainer" class="xh-VideoPlayerComponent-container"></div>
  </div>
</template>

<script>
  import VideoPlayer from '../../Modules/Widgets/ResizableVideoPlayer/VideoPlayerRTMP'
  import defaultValue from "@/utils/core/defaultValue";
  export default {
    name: "video-player-base",
    mounted() {
      this.$nextTick(()=>{
        let param = {
          container: this.$refs['VideoPlayerContainer'],
        };
        // 视频播放器
        this._videoPlayer = new VideoPlayer(param);
      })
    },
    props: {
      ignoreGb28181ForJanusWebRTC: {
        type: Boolean,
        default: () => true
      },
      defaultStream: {
        type: String,
        default: () => '第三码流'
      },
      playerType: {
        type: String,
        default: () => 'VideoPlayerRTMP'
      },
      forceUseVideoPlayer: {
        type: String,
        default: () => ''
      },

    },
    beforeDestroy(){

    },
    methods: {
      /**
       * 分屏模式切换
       * @param {Object} mode 模式
       * @param {String} mode.name 名字
       */
      switchMode(mode){
        if(this._videoPlayerGallery){
          this._videoPlayerGallery.switchMode(mode);
        }
      },

      /**
       * 获取视频组合器
       * @return {VideoPlayerGallery|*}
       * @return Promise<VideoPlayerGallery>
       */
      getVideoPlayer(){
        return new Promise((resolve, reject)=> {
          if(this._videoPlayer){
            resolve(this._videoPlayer);
          }else{
            let temp = setInterval(()=>{
              if(this._videoPlayer){
                resolve(this._videoPlayer);
                clearInterval(temp)
              }
            }, 200)
          }
        })
      },

      /**
       * 激活播放器
       */
      activateVideoPlayer(){
        if(this.playerType === 'VideoPlayerRTMP'){
          if(!this._videoPlayer){
            let param = {
              container: this.$refs['VideoPlayerContainer'],
            };
            // 视频播放器
            this._videoPlayer = new VideoPlayer(param);
          }
        }else{
          if(!this._videoPlayer){
            let param = {
              container: this.$refs['VideoPlayerContainer'],
            };
            // 视频播放器
            this._videoPlayer = new VideoPlayer(param);
          }
          this.getVideoPlayer().then(videoPlayer=>{
              videoPlayer.playAll()
          }).catch(msg=>{console.log(msg)})
        }
      },

      /**
       * 使失活播放器
       */
      deactivateVideoPlayer(){
        this.getVideoPlayer().then(videoPlayer=>{
          if(this.playerType === 'VideoPlayerRTMP'){
            if(this._videoPlayer){
              videoPlayer.destroy();
              this._videoPlayer = null
            }
          }else{
            videoPlayer.clear();
          }
        }).catch(msg=>{console.log(msg)})
      }

    }
  }
</script>

<style scoped>
  .xh-VideoPlayerComponent-container{
    width: 100%;
    height: 100%;
  }
</style>
