<template>
  <el-autocomplete
    v-model="TerminalIDObj"
    size="mini"
    placeholder="请输入序列号"
    :clearable="clearable"
    :fetch-suggestions="querySearch"
    :highlight-first-item="true"
    value-key="label"
    @select="handleSelect"
    @clear="clearSelect"
  />
</template>

<script>
import { searchVehicle as searchTerminal } from '@/api/base/vehicle.js';
export default {
  name: 'TerminalSearch',
  components: {},
  props: {
    clearable: {
      type: Boolean,
      default: true
    },
    terminal: {
      type: [String, Number, Object],
      default: ''
    }
  },
  data () {
    return {
      TerminalIDObj: '',
      CarIDOpts: [],
      externalCar: undefined
    };
  },
  watch: {
    TerminalIDObj (_val) {
      this.$emit('update:TerminalID', _val);
    },
    terminal (val) {
      console.log('c', val);
      this.TerminalIDObj = val;
    },
    externalCar: {
      handler (newValue) {
        console.log('！！！', newValue);
        if (newValue) {
          this.handleSelect({
            value: newValue.id,
            label: newValue.licencePlate
          });
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async getOptions (_val) {
      let optionsReturn = [];
      let parme = {
        phone: _val + ''
      };
      await searchTerminal(parme, 'id').then((req) => {
        let opts = [];
        for (let i = 0; i < req.length && i <= 20; i++) {
          const element = req[i];
          opts.push({
            value: element.id,
            label: element.phone,
            licencePlate: element.licencePlate
          });
        }
        optionsReturn = opts;
      });
      return optionsReturn;
    },
    querySearch (_val, _callback) {
      this.getOptions(_val).then(req => {
        _callback(req);
      });
    },
    handleSelect (item) {
      this.TerminalIDObj = item.label;
      this.$emit('terminalSelected', {
        id: item.value,
        terminal: item.label,
        licencePlate: item.licencePlate
      });
    },
    clearSelect () {
      this.TerminalIDObj = '';
      this.$emit('terminalSelected', this.TerminalIDObj);
    }
  }
};
</script>
