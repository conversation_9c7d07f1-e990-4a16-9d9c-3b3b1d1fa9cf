<template>
  <div class="select-content">
    <div
      v-if="!disabled"
      class="mask-operate"
      @click="handleTableShow"
    />
    <el-input
      v-model="value"
      readonly
      :disabled="disabled"
      placeholder="请点击选择赋码编号"
    >
      <el-button
        slot="append"
        icon="el-icon-search"
        :style="{cursor: disabled ? 'default' : 'pointer'}"
        type="info"
      />
    </el-input>
    <el-dialog
      :visible.sync="isShowTable"
      append-to-body
      title="出库终端列表"
      custom-class="device-num-select-dialog"
      :fullscreen="false"
      width="90%"
    >
      <div class="xh-container">
        <!--工具栏-->
        <div class="head-container">
          <HeadCommon
            :dict="dict"
            :head-config="headConfig"
            label-width="120px"
          />
        </div>
        <div class="xh-crud-table-container">
          <el-table
            ref="table"
            v-loading="crud.loading"
            :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
            :data="crud.data"
            :cell-style="{'text-align':'center'}"
            style="width: 100%;height: calc(100% - 27px)"
            :highlight-current-row="true"
            @current-change="currentChange"
          >
            <el-table-column
              label="赋码编号"
              :show-overflow-tooltip="true"
              min-width="100"
              prop="deviceNum"
            />
            <el-table-column
              prop="uniqueId"
              min-width="100"
              label="序列号"
            />
            <el-table-column
              prop="model"
              label="终端型号"
            />
            <el-table-column
              label="IMEI"
              prop="imei"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              prop="bdChipSn"
              label="北斗芯片序列号"
              min-width="120"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              prop="vendorName"
              label="生产厂商"
              min-width="120"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              prop="categoryName"
              label="终端类型"
              min-width="120"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              prop="deptName"
              label="所属机构"
              :show-overflow-tooltip="true"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
          <!--分页组件-->
          <pagination/>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="cancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="submit()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import HeadCommon from '@/components/formHead/headCommon.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import equipmentManage from '@/api/equipmentLedger/equipmentManage';
import pagination from '@/components/Crud/Pagination';
// crud交由presenter持有
const crud = CRUD({
  title: '',
  props: {
    searchToggle: true
  },
  queryOnPresenterCreated: false,
  crudMethod: { ...equipmentManage }
});
export default {
  name: 'DeviceNumSelect',
  components: {
    HeadCommon,
    pagination
  },
  mixins: [presenter(crud)],
  props: {
    // 绑定值
    value: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 默认查询的所属机构
    deptId: {
      type: String,
      default: '',
      required: true
    },
    // 原始表单 用于自动设置表单值
    sourceForm: {
      type: Object,
      default: null
    },
    // 终端类型的字典值 5类型终端的children字典
    categoryDict: {
      type: Object,
      default: null,
      required: true
    },
    // 终端类别(大类)字典值 用于后端进行列表类别筛选
    typeDictValue: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      isShowTable: false,
      headConfig: {
        item: {
          1: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum'
          },
          2: {
            name: '序列号',
            type: 'input',
            value: 'uniqueId'
          },
          3: {
            name: 'IMEI',
            type: 'input',
            value: 'imei'
          },
          4: {
            name: '终端类型',
            type: 'select',
            value: 'category',
            dictOptions: 'categoryDict'
          },
          5: {
            name: '生产厂商',
            type: 'input',
            value: 'vendor'
          },
          6: {
            name: '终端型号',
            type: 'input',
            value: 'model'
          }
        },
        button: {}
      },
      selectRow: {},
      dict: {
        dict: {}
      }
    };
  },
  watch: {
    isShowTable(val) {
      if (val) {
        this.crud.toQuery();
      }
    },
    categoryDict: {
      handler(val) {
        if (val) {
          this.dict.dict.categoryDict = val;
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleTableShow() {
      if (!this.deptId) {
        this.$message.closeAll();
        this.$message.error('请先选择所属机构');
        return false;
      }
      this.isShowTable = true;
    },
    [CRUD.HOOK.beforeRefresh]() {
      // 默认设置所属机构和出库状态的查询条件
      this.crud.query.deviceType = this.typeDictValue;
      this.crud.query.userDeptId = this.deptId;
      this.crud.query.storageState = 1;
      this.crud.query.status = 1;
    },
    cancel() {
      this.isShowTable = false;
      this.crud.resetQuery();
    },
    submit() {
      if (Object.keys(this.selectRow).length === 0) {
        this.$message.error('请选择终端');
      }
      else {
        if (this.sourceForm) {
          // 代理处理原始表单, 减少终端页面操作代码
          const {
            deviceNum,
            vendor,
            model,
            category,
            imei,
            uniqueId,
            bdChipSn
          } = this.selectRow;
          this.sourceForm.deviceNum = deviceNum;
          this.sourceForm.vendor = vendor;
          this.sourceForm.model = model;
          this.sourceForm.category = `${category}`;
          this.sourceForm.imei = imei;
          this.sourceForm.uniqueId = uniqueId;
          this.sourceForm.bdChipSn = bdChipSn;
        }
        else {
          // 不需要代理操作的提交数据
          this.$emit('handleChange', this.selectRow);
        }
        this.isShowTable = false;
      }
    },
    currentChange(row) {
      this.selectRow = row || {};
    },
    getCategoryDictLabel(value) {
      let label = '';
      for (const item of this?.categoryDict) {
        if (Number(item.value) === value) {
          label = item.label;
          break;
        }
      }
      return label;
    }
  }
};
</script>
<style scoped lang="less">
.select-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.mask-operate {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 1;
}

</style>
<style lang="scss">
.device-num-select-dialog {
  color: #ffffff;
  .el-dialog__body {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    height: 70vh;
    .el-table__row.current-row > td {
      background-color: rgba(var(--gn-color-rgb), .4) !important;
    }
  }
}
</style>
