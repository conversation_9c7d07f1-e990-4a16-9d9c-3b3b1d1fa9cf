<template>
  <div
    v-if="crud.props.searchToggle"
    class="xh-header"
  >
    <el-form
      ref="form"
      :label-width="labelWidth"
    >
      <el-row
        :span="24"
      >
        <div class="xh-header-content">
          <div
            v-for="(item,index) in defaultList"
            :key="index"
            class="el-col el-col-6 el-col-xs-24 el-col-sm-12 el-col-md-6 xh-header-content-item"
          >
            <el-form-item :label="item.name + ':'">
              <HeadCommonInput
                v-if="item.type !== 'extra'"
                :crud="crud"
                :set="item"
                :dict="dict"
                :sp-data="spData"
                @selectChange="selectChange"
              />
              <HeadExtra
                v-else-if="item.type==='extra'"
                ref="HeadExtra"
                :crud="crud"
                :set="item"
                :dict="dict"
                :sp-data="spData"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-6 el-col-md-6 no-print xh-crud-search">
            <el-form-item>
              <div
                class="xh-search-btn gn-button-group"
                :class="{ 'xh-search-btn-near': morelist.length === 0 }"
              >
                <el-tooltip
                  v-if="morelist.length > 0"
                  content="更多筛选项"
                  placement="top"
                >
                  <el-button
                    class="more-btn"
                    :icon="moreSearch ? 'el-icon-d-arrow-left': 'el-icon-d-arrow-right'"
                    size="small"
                    @click="moreSearch=!moreSearch"
                  >
                    {{ moreSearch ? '收起' : '展开' }}
                  </el-button>
                </el-tooltip>
                <slot name="search-left"/>
                <el-button
                  v-if="headConfig.button.query !== false"
                  class="filter-item"
                  size="small"
                  type="primary"
                  icon="el-icon-search"
                  :disabled="searchAbleHandle()"
                  @click="searchClick"
                >查 询
                </el-button>
                <slot name="center"/>
                <el-button
                  v-if="headConfig.button.clear !== false"
                  class="filter-item"
                  size="small"
                  icon="el-icon-refresh-right"
                  @click="clearClick"
                >重 置
                </el-button>

              </div>
            </el-form-item>
          </div>
          <div
            v-show="moreSearch"
            style="width: 100%; margin-top: 6px; padding-right: 5px;"
          >
            <div
              v-for="(item,index) in morelist"
              :key="index"
              class="el-col el-col-6 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-6 xh-header-content-item"
            >
              <el-form-item :label="item.name + ':'">
                <HeadCommonInput
                  v-if="item.type !== 'extra'"
                  :crud="crud"
                  :set="item"
                  :dict="dict"
                  :sp-data="spData"
                  @selectChange="selectChange"
                />
                <HeadExtra
                  v-else-if="item.type==='extra'"
                  ref="HeadExtra"
                  :crud="crud"
                  :set="item"
                  :dict="dict"
                  :sp-data="spData"
                />
              </el-form-item>
            </div>
          </div>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { header } from '@/components/Crud/crud';
import rrOperation from '@/components/Crud/RR.operation';
import HeadExtra from './headExtra.vue';
import HeadCommonInput from './headCommonInput.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    HeadExtra,
    rrOperation,
    HeadCommonInput
  },
  mixins: [header()],
  props: {
    permission: {
      type: [
        Object,
        Array
      ],
      default: () => {
        return {};
      }
    },
    headConfig: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    spData: {
      type: [
        Object,
        Array
      ],
      default: () => {
        return {};
      }
    },
    propsValue: {
      type: [
        Object,
        Array,
        null
      ],
      default: () => {
        return null;
      }
    },
    labelWidth: {
      type: String,
      default: '80px'
    },
    isSpread: {
      type: Boolean,
      default: false
    },
    canSetRemoveTabs: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      moreSearch: false,
      morelist: [],
      defaultList: [],
      pagePath: '',
      canSetRemoveTabsFlag: true
    };
  },
  computed: {
    ...mapGetters(['removeTags'])
  },
  watch: {
    propsValue: {
      handler(newValue) {
        if (newValue) {
          this.setPropsValue(newValue);
        }
      },
      immediate: true,
      deep: true
    },
    query: {
      handler(newValue) {
        this.$parent.headQuery && this.$parent.headQuery(newValue);
      },
      immediate: true,
      deep: true
    },
    headConfig: {
      handler(newValue) {
        const queryList = Object.values(newValue.item);
        this.defaultList = queryList.slice(0, 3);
        this.morelist = queryList.slice(3);
        if (!newValue.noReset) {
          this.setDefaultValue();
        }
      },
      deep: true,
      immediate: true
    },
    isSpread: {
      handler(newValue) {
        if (newValue) {
          this.moreSearch = newValue;
        }
      },
      immediate: true
    },
    dict: {
      handler(newValue) {
      },
      immediate: true,
      deep: true
    },
    $route: {
      handler(val) {
        this.pagePath = val.path;
      },
      deep: true
    }
  },
  created() {
    // this.setDefaultValue();
    this.canSetRemoveTabsFlag = this.canSetRemoveTabs;
  },
  activated() {
    if (this.canSetRemoveTabsFlag) {
      const path = this.$route.fullPath;
      const removeIndex = this.removeTags.indexOf(path);
      if (removeIndex !== -1) {
        this.removeTags.splice(removeIndex, 1);
        this.$store.commit('SET_REMOVE_TAGS', this.removeTags);
        this.setDefaultValue();
        // 触发setDefaultValue方法时会判断initQuery字段是否为true, 为true则触发查询, 因此这里判断为true时不触发查询
        if (!this.headConfig.initQuery) {
          this.crud.toQuery();
        }
        // 触发页面其他需要重新加载的事件
        this.$nextTick(() => {
          this.$emit('reloadPage');
        });
      }
    }
  },
  methods: {
    searchClick() {
      if (this.headConfig.callback && this.headConfig.callback.search) {
        this.headConfig.callback.search(this);
      }
      this.crud.toQuery();
    },
    // defaultValue、defaultFn存在时赋初始值
    setDefaultValue() {
      this.crud.resetQuery(false);
      for (let i in this.headConfig.item) {
        if (this.headConfig.item[i].defaultValue || this.headConfig.item[i].defaultValue === 0) {
          this.$set(this.crud.query, this.headConfig.item[i].value, this.headConfig.item[i].defaultValue);
        }
        else if (this.headConfig.item[i].defaultFn) {
          this.$set(this.crud.query, this.headConfig.item[i].value, this.getDefaultFn(this.headConfig.item[i].defaultFn));
        }
      }
      this.headConfig.initQuery && this.crud.toQuery();
    },
    setPropsValue(obj) {
      for (let i in obj) {
        this.$set(this.crud.query, i, obj[i]);
      }
    },
    getDefaultFn(type) {
      if (type === 'getMonth') { // 获取月份时间
        return this.$moment().format('YYYY-MM');
      }
      else if (type === 'stDE') {
        return this.$moment().startOf('day').valueOf();
      }
      else if (type === '3DS') {
        return this.$moment().startOf('day').valueOf() - 2 * (24 * 60 * 60 * 1000);
      }
      else if (type === '7DS') {
        return this.$moment().startOf('day').valueOf() - 6 * (24 * 60 * 60 * 1000);
      }
      else if (type === 'toDE') {
        return this.$moment().endOf('day').valueOf();
      }
      else if (type === '1MS') {
        console.log('');
      }
    },
    clearClick() {
      this.$emit('handleClear');
      if (this.$refs?.HeadExtra) { // TODO 待修改
        for (let index = 0; index < this.$refs.HeadExtra.length; index++) {
          this.$refs.HeadExtra[index].clearAll();
        }
      }
      this.crud.resetQuery();
      this.crud.toQuery();
    },
    searchAbleHandle() {
      // 搜索项有必填时，判断disable
      let getRequired = false;
      for (let i in this.headConfig.item) {
        if (this.headConfig.item[i].required && (!this.query[this.headConfig.item[i].value] && this.query[this.headConfig.item[i].value] !== 0)) {
          getRequired = true;
        }
      }
      return getRequired;
    },
    // 空字符串报错，转化null
    selectChange(item) {
      this.$emit('selectChange', item);
    }
  }
};
</script>
<style lang="less" scoped>
.xh-header {
  ::v-deep .el-select, ::v-deep .layout, ::v-deep .el-date-editor, ::v-deep .el-input__inner, ::v-deep .el-autocomplete {
    width: 100%;
  }

  ::v-deep .el-form-item {
    //margin-bottom: 6px;
  }

  ::v-deep .el-form-item__content, ::v-deep .el-form-item__label {
    height: 32px;
    line-height: 32px;
  }

  ::v-deep .el-select__caret {
    line-height: 32px;
  }

  .xh-crud-search {
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  @media only screen and (max-width: 780px) {
    .xh-crud-search {
      ::v-deep .xh-search-btn {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>
