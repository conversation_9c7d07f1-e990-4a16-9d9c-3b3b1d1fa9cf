<template>
  <div>
    <el-input
      v-if="set.type === 'input'"
      v-model="crud.query[set.value]"
      clearable
      size="small"
      :placeholder="'请输入' + set.name"
      @keyup.native="handleInput(set)"
      @keyup.enter.native="crud.toQuery"
      @clear="handleInputClear(set)"
    />
    <el-date-picker
      v-else-if="set.type === 'datetime' || set.type === 'date'"
      v-model="crud.query[set.value]"
      size="small"
      :type="set.type"
      :default-time="getDefaultTime(set)"
      :format="getDefaultFormat(set)"
      :value-format="getDefaultValueFormat(set)"
      :placeholder="'请选择' + set.name"
      :picker-options="getDefaultPickerOptions(set)"
      @blur="judge"
    />
    <el-date-picker
      v-else-if="set.type === 'month'"
      v-model="crud.query[set.value]"
      type="month"
      value-format="yyyy-MM"
      size="small"
      :placeholder="'请选择' + set.name"
    />
    <el-select
      v-else-if="set.type === 'select'"
      v-model="crud.query[set.value]"
      :placeholder="'请选择' + set.name"
      :filterable="set.filterable"
      clearable
      :multiple="set.isMultiple"
      :collapse-tags="set.isMultiple"
      size="small"
      @change="selectChange($event, set)"
    >
      <el-option
        v-for="(subItem, subIndex) in getOptions(set)"
        :key="subIndex"
        :label="subItem.label"
        :value="subItem.value"
      />
    </el-select>
    <el-cascader
      v-else-if="set.type === 'cascader'"
      v-model="crud.query[set.value]"
      :placeholder="'请选择' + set.name"
      :options="getOptions(set)"
      clearable
      size="small"
    />
  </div>
</template>

<script>
import zh from '@/lang/zh';
export default {
  components: {},
  props: {
    set: {
      type: Object,
      required: true,
    },
    crud: {
      type: Object,
      required: true,
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      },
    },
    spData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {

    };
  },
  methods:{
    handleInput(set) {
      if (set.keyup === 'number') {
        this.crud.query[set.value] = this.crud.query[set.value].replace(/[^\d]/g,'');
      }
    },
    // 手动清除后赋值为undefined, 避免参数为空字符串时影响后台查询
    handleInputClear(set) {
      this.crud.query[set.value] = undefined;
    },
    getHolder (item) {
      if (item.name) {
        return '请输入' + this.$t(`${item.name}`);
      } else if (item.holder) {
        return this.$t(`holder.${item.holder}`);
      } else if (!zh.holder[item.value]) {
        return '请输入' + this.$t(`${item.value}`);
      } else {
        return this.$t(`holder.${item.value}`);
      }
    },
    getDefaultTime (item) {
      if (item.value.indexOf('end') > -1) {
        return '23:59:59';
      } else {
        return '00:00:00';
      }
    },
    getDefaultFormat (item) {
      if (item.type === 'datetime') {
        return 'yyyy-MM-dd HH:mm:ss';
      } else if (item.type === 'date') {
        return 'yyyy-MM-dd';
      }
    },
    getDefaultValueFormat (item) {
      if (item.valueFormat) {
        return item.valueFormat;
      } else if (item.type === 'date') {
        return 'yyyy-MM-dd';
      }
    },
    getDefaultPickerOptions(item) {
      if (item.pickerOptions) {
        return item.pickerOptions;
      } else {
        return {};
      }
    },
    judge () {
      if (this.crud.query.startTime) {
        if (
          this.$moment(this.crud.query.startTime).valueOf() >
          this.$moment(this.crud.query.endTime).valueOf()
        ) {
          this.$message({
            type: 'warning',
            message: '开始日期时间要小于结束日期时间'
          });
        }
      }
    },
    // 空字符串报错，转化null
    selectChange (e, item) {
      // 避免浪费性能, 多选时不触发查询
      if (item.isMultiple) {
        return;
      }
      if (!e && e !== 0 && e !== false) {
        this.crud.query[item.value] = null;
      }
      item.auto === true && this.crud.toQuery();
      item.position = e;
      this.$emit('selectChange',item);
      this.$forceUpdate();
    },
    // 获取选择器选项
    getOptions (item) {
      if (item.options) {
        return item.options;
      } else if (item.dictOptions) {
        return this.dict.dict[item.dictOptions];
      } else if (item.spOptions) {
        return this.spData[item.spOptions];
      } else if (item.dictsOptions) {
        return this.dict[item.dictsOptions];
      }
    },
    setProp(item){

    }
  }
};
</script>

<style scoped></style>
