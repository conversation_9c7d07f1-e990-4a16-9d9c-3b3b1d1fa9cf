<template>
  <div>
    <div v-if="set.value==='deptId' || set.value==='deptIdStr' || set.value === 'userDeptId'">
      <DeptFormSingleSelect
        ref="deptIdSingle"
        v-model="crud.query[set.value]"
        :placeholder="'请选择' + set.name"
        :is-show="true"
        clearable
        size="small"
      />
    </div>
    <div v-else-if="set.value === 'deptIdStrMultiple'">
      <DeptSingleSelectMultiple
        ref="deptId"
        v-model="crud.query[set.value]"
        placeholder=""/>
    </div>
    <div v-else-if="set.value==='alarmType' || set.value==='alarmTypeList'">
      <AlarmTypeSelect
        ref="AlarmTypeSelect"
        v-model="crud.query[set.value]"
        :tree-data="dict.alarmType"
      />
    </div>
    <div v-else-if="set.value==='alarmTypeSingle'">
      <AlarmTypeSelectSingle
        ref="AlarmTypeSelectSingle"
        v-model="crud.query.alarmType"
      />
    </div>
    <div v-else-if="set.value==='licencePlate'">
      <MultiPlateSelect
        ref="MultiPlateSelect"
        v-model="crud.query.licencePlate"
      />
    </div>
    <div v-else-if="set.value==='vehicleModel'">
      <Treeselect
        class="tree-select__w-280"
        v-model="crud.query.vehicleModel"
        :options="dict.vehicleModel"
        :normalizer="normalizerVehicleModel"
        :placeholder="$t('holder.vehicleModel')"
        @change="crud.toQuery"
      />
    </div>
    <div v-else-if="set.value==='deptIdDept'">
      <Treeselect
        class="tree-select__w-280"
        ref="deptId"
        v-model="crud.query.deptId"
        :options="spData.depts"
        :normalizer="normalizerDeptId"
        z-index="5000"
        no-results-text="暂无数据"
        clear-value-text="清空"
        placeholder="请选择单位"
        style="width:180px"
      />
      <!-- :append-to-body="true" -->
    </div>
    <div v-else-if="set.value==='vehicleUseType'">
      <!-- <VehicleUseType
        ref="VehicleUseType"
        v-model="crud.query.vehicleUseType"
      /> -->
      <Treeselect
        class="tree-select__w-280"
        v-model="crud.query.vehicleUseType"
        :options="dict.vehicleUseType"
        :normalizer="normalizerVehicleModel"
        :placeholder="$t('holder.vehicleUseType')"
      />
    </div>
    <div v-else-if="set.value==='vehicleUseTypeList'">
      <VehicleUseType
        ref="VehicleUseType"
        v-model="crud.query[set.value]"
        :tree-data="dict.vehicleUseType"
      />
    </div>
    <div v-else-if="set.value==='idList'">
      <VehicleTree
        ref="vehicleTree"
        v-model="crud.query[set.value]"
      />
    </div>
    <div v-else-if="set.value==='deviceObj'">
      <!-- <TerminalSingleFilterTree
        ref="terminalSingleFilterTree"
        v-model="crud.query[set.value]"
      /> -->
      <TerminalFilterSelect
        ref="terminalFilterSelect"
        v-model="crud.query[set.value]"
      />
    </div>
    <div v-else-if="set.value==='deptIdList'">
      <DeptIdsFilter
        ref="deptIdsFilter"
        v-model="crud.query[set.value]"
      />
    </div>
    <div v-else-if="set.value==='warnTypes'">
      <AlarmTypeSelect
        v-model="crud.query[set.value]"
        :tree-data="dict.warnType"
      />
    </div>
    <div v-else-if="set.value==='domicile'">
      <Treeselect
        class="tree-select__w-280"
        v-model="crud.query.domicile"
        :options="dict.domicile"
        :normalizer="normalizerVehicleDomicileAdcode"
        placeholder="请选择注册地"
            />
    </div>
    <div v-else-if="set.value==='terminalType' || set.value==='deviceCate' || set.value==='deviceCategory'">
      <Treeselect
        class="tree-select__w-280"
        v-model="crud.query[set.value]"
        :options="dict['bdmDeviceType']"
        :normalizer="normalizerBindTerminalType"
        placeholder="请选择终端类型"
      >
        <p
          slot="option-label"
          slot-scope="{node}"
          style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;width: 90%;"
          :title="node.label"
        >
          <template> {{ node.label }}</template>
        </p>
      </Treeselect>
    </div>
  </div>
</template>

<script>
import DeptSingleSelect from '@/components/select/DeptSingleSelect/DeptSingleSelectNoDefault';
import DeptSingleSelectMultiple from '@/components/select/DeptSingleSelect/DeptSingleSelectMultiple';
import AlarmTypeSelect from '@/components/addSelect/alarmTypeSearch.vue';
import MultiPlateSelect from '@/components/addSelect/multiPlate.vue';
import AlarmTypeSelectSingle from '@/components/addSelect/alarmType.vue';
import VehicleUseType from '@/components/addSelect/vehicleUseType.vue';
// import VehicleTree from '@/components/addSelect/vehicleTree.vue';
import VehicleTree from '@/components/vehicleFilterTree/index.vue';
import TerminalSingleFilterTree from '@/components/vehicleFilterTree/terminalSingleFilterTree.vue';
import TerminalFilterSelect from '@/components/vehicleFilterTree/terminalFilterSelect.vue';
import DeptIdsFilter from '@/components/select/addSelect/deptIdsFilter.vue';

import Treeselect from '@riophae/vue-treeselect';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
export default {
  components: { DeptFormSingleSelect, DeptSingleSelect, DeptSingleSelectMultiple, AlarmTypeSelect, MultiPlateSelect, AlarmTypeSelectSingle, Treeselect, VehicleUseType, VehicleTree, DeptIdsFilter, TerminalSingleFilterTree, TerminalFilterSelect },
  props: {
    set: {
      type: Object,
      required: true
    },
    crud: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    spData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      normalizerVehicleModel (node) {
        return {
          id: node.value,
          label: node.label,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      // treeSelect自定义键名
      normalizerDeptId (node) {
        return {
          id: node.id,
          label: node.title,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      normalizerVehicleDomicileAdcode (node) {
        return {
          id: node.adcode,
          label: node.name,
          children: node.children || undefined
        };
      },
      normalizerBindTerminalType (node) {
        return {
          id: node.value,
          label: node.label,
          children: node.children || undefined
        };
      },
    };
  },
  methods: {
    clearAll(){
      this.$refs?.vehicleTree?.clearAll();
      // this.$refs?.terminalSingleFilterTree?.clearAll();
      this.$refs?.terminalFilterSelect?.clearAll();
      this.$refs?.deptIdSingle?.clear();
    }
  }
};
</script>

<style scoped>

::v-deep .vue-treeselect{
  height: 28px;
}
::v-deep .vue-treeselect .vue-treeselect__control{
  height: 28px !important;
}
::v-deep .vue-treeselect__placeholder{
  font-size: 13.5px;
}
::v-deep .vue-treeselect input{
  font-size: 13.5px;
}

</style>
