<template>
  <!-- /center/instruction/sendcmd 指令下发-拍照模块-使用的单选加请求通道接口，目前仅此用途 -->
  <!-- TODO 感觉跟历史视频页面高度相似 , 感觉可以直接用历史视频的那个组件 -->
  <div
    :class="{'xh-dropDown-invisible': !isDropDown}"
    class="video-single-select-tree"
  >
    <div
      ref="selectComponentContainerDom"
      v-loading="loading"
      class="video-select-component-container"
    >
      <div
        ref="headerDom"
        class="header"
      >
        <div class="car-filter-container">
          <i class="el-icon-search" />
          <el-input
            v-model="query.filterText"
            clearable
            size="small"
            placeholder="搜索终端名称/终端类型/机构"
            class="car-filter"
            @input="searchFilter"
          />
          <i
            class="el-icon-refresh"
            @click="onRefresh"
          />
        </div>
      </div>
      <vue-easy-tree
        ref="tree"
        :data="data"
        class="xh-tree"
        node-key="id"
        :height="treeMaxHeight"
        :indent="10"
        :filter-node-method="filterHandle"
        :style="tableStyle"
        @node-click="nodeClick"
      >
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node"
          @mouseover="mouseOverNode(node)"
        >
          <span v-if="data.type === 'dept'">
            <svg-icon
              :icon-class="getTreeIconClass(data)"
            />
            {{ node.label }}{{ getCount(data) }}
          </span>
          <span v-if="data.type === 'dept_son'">
            {{ node.label }}{{ getCount(data) }}
          </span>
          <!-- <el-radio
            v-if="data.type === 'vehicle'"
            v-model="currentRow"
            :label="data.originData.id"
            @click.native.prevent="radioClick(data.originData.licencePlate, node, data)"
          > -->
          <span v-if="data.type !== 'dept' && data.type !== 'dept_son' && data.type !== 'channel'">
            <svg-icon
              :icon-class="getStaffIconClass(data)"
              class="svg-icon-vehicle"
            />
            {{ node.label }}
            <!-- <span>{{ getAccState(data) }}</span> -->
          </span>
          <!-- </el-radio> -->
        </span>
      </vue-easy-tree>
      <!-- <div
        v-if="!hiddenToggleButton"
        v-show="!formVideoPage"
        class="xh-select-component-toggle-button"
        @click="toggleDropDown"
      >
        &lt;&lt;
      </div>
      <el-collapse
        v-show="isDropDown"
        v-model="activeNames"
        @change="handleCollapseChange"
      >
        <el-collapse-item
          ref="firstCollapseItemComponent"
          name="firstItem"
          class="xh-select-component-collapse-item"
        >
          <template slot="title">
            <span
              class="xh-select-component-filter-item-key space-set"
            >{{ getLabel('licencePlate') }}</span>
            <el-input
              v-model="query.licencePlate"
              clearable
              size="small"
              :placeholder="getPlaceholder('licencePlate')"
              class="xh-select-component-filter-item-value"
              @input="searchFilter"
            />
          </template>
          <div>
            <span class="xh-select-component-filter-item-key">{{ getLabel('vehicleModel') }}</span>
            <xh-select
              v-model="query.vehicleModel"
              clearable
              size="small"
              :placeholder="getPlaceholder('vehicleModel')"
              class="xh-select-component-filter-item-value"
              @change="searchFilter"
            >
              <el-option
                v-for="item in dict.dict.vehicleModel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </div>
          <div>
            <span class="xh-select-component-filter-item-key">{{ getLabel('vehicleUseType') }}</span>
            <xh-select
              v-model="query.vehicleUseType"
              clearable
              size="small"
              :placeholder="getPlaceholder('vehicleUseType')"
              class="xh-select-component-filter-item-value"
              @change="searchFilter"
            >
              <el-option
                v-for="item in dict.dict.vehicleUseType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </div>
          <div>
            <span class="xh-select-component-filter-item-key">{{ getLabel('deptName4') }}</span>
            <el-input
              v-model="query.deptName"
              clearable
              size="small"
              :placeholder="getPlaceholder('deptName4')"
              class="xh-select-component-filter-item-value"
              @input="searchFilter"
            />
          </div>
          <div>
            <span class="xh-select-component-filter-item-key">{{ getLabel('terminalModel') }}</span>
            <el-input
              v-model="query.terminalModel"
              clearable
              size="small"
              :placeholder="getPlaceholder('terminalModel')"
              class="xh-select-component-filter-item-value"
              @input="searchFilter"
            />
          </div>
          <div>
            <span class="xh-select-component-filter-item-key">{{ getLabel('vehicleState') }}</span>
            <xh-select
              v-model="query.terminalState"
              clearable
              size="small"
              :placeholder="getPlaceholder('vehicleState')"
              class="xh-select-component-filter-item-value"
              @change="searchFilter"
            >
              <el-option
                v-for="item in dict.dict.terminalState"
                :key="item.value"
                :label="item.label"
                :value="Number(item.value)"
              />
            </xh-select>
          </div>
        </el-collapse-item>
        <el-collapse-item
          name="secondItem"
          class="xh-select-component-collapse-item"
        >
          <template slot="title">
            <div
              class="collapse-item-title-slot"
            >
              车辆目录
            </div>
          </template>
          <xh-tree
            ref="tree"
            :data="data"
            node-key="id"
            :filter-node-method="filterNode"
            :style="tableStyle"
          >
            <span
              slot-scope="{ node,data }"
              class="custom-tree-node"
              @mouseover="mouseOverNode(node)"
            >
              <svg-icon
                v-if="data.type === 'dept'"
                :icon-class="getTreeIconClass(data)"
              />
              <span v-if="data.type === 'dept'">{{ node.label }}{{ getCount(data) }}</span>
              <el-radio
                v-if="data.type === 'vehicle'"
                v-model="currentRow"
                :label="data.originData.id"
                @click.native.prevent="radioClick(data.originData.licencePlate, node, data)"
              >
                <svg-icon
                  v-if="data.type === 'vehicle'"
                  :icon-class="getVehicleIconClass(data)"
                />
                <span
                  v-if="data.type === 'vehicle'"
                >
                  {{ node.label }}
                  <span v-if="data.originData.licenceColor">({{ data.originData.licenceColor }})</span>
                  <span>{{ getAccState(data) }}</span>
                </span>
              </el-radio>
            </span>
          </xh-tree>
        </el-collapse-item>
      </el-collapse> -->
    </div>
  </div>
</template>

<script>
import crudVehicle from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import variables from '@/assets/less/variables.js';

export default {
  name: 'VehicleSingleSelectTree',
  components: {},
  dicts: ['vehicleModel', 'terminalState', 'vehicleUseType'],
  props: {
    formVideoPage: {
      type: Boolean,
      default: false
    },
    hiddenToggleButton: {
      type: Boolean,
      default: false
    },
    // 标识来源：vl-videolive vh-videoHistory
    pageSrc: {
      type: String,
      default: ''
    },
    customTreeApi: {
      type: Function,
      default: null
    },
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      name: '',
      permission: {
        add: ['admin', 'vehicle:add'],
        edit: ['admin', 'vehicle:edit'],
        del: []
      },
      activeNames: ['secondItem'],
      tableStyle: '',
      query: {
        licencePlate: '',
        vehicleModel: '',
        vehicleUseType: '',
        terminalState: '',
        deptName: '',
        terminalModel: '',
        filterText: ''
      },
      nextButtonVisible: false,
      data: [],
      // permission: []
      variables: {...variables},
      currentRow: null,
      isDropDown: true,
      vehicleCount: 0,
      loading: false,
      treeMaxHeight: '0px' // TODO
    };
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.$nextTick(()=> {
            this.onRefresh();
          });
        }
      },
      immediate: true
    }
  },
  mounted () {
    if (sessionStorage.getItem('storedVehicleTreeData')) {
      this.dataSource(JSON.parse(sessionStorage.getItem('storedVehicleTreeData')));
    }
    this.$nextTick(() => {
      this.updateStyle();
      // 指令中心不需要
      if (!this.customTreeApi) {
        this.onRefresh(() => {
          if (this.$route.query.id) {
            this.setSelectedVehicle(this.$route.query);
          }
        });
      }
    });
  },
  activated () {
    this.$nextTick(() => {
      if (this.$route.query.id) {
        this.setSelectedVehicle(this.$route.query);
      }
    });
  },
  methods: {
    // 在线/总数车辆
    getCount (data) {
      if (this.pageSrc === 'vh') {
        return `(${data.frontCountOnline}/${data.frontCountTotal})`;
      } else {
        return `(${data.onlineVehicleCount}/${data.vehicleCount})`;
      }
    },
    // data根据用途筛选
    dataSource (data) {
      this.data = this.videoFilter(data).data;
    },
    // 视频页筛选视频终端车辆
    videoFilter (data) {
      let arr = [];
      let flag = false;
      let frontCountTotal = 0;
      let frontCountOnline = 0;
      data.forEach((item) => {
        if (item.type === 'dept' || item.type === 'dept_son') {
          if (item.children) {
            let obj = this.videoFilter(item.children);
            if (obj.flag) {
              flag = true;
              item.children = obj.data;
              item.frontCountTotal = obj.frontCountTotal;
              item.frontCountOnline = obj.frontCountOnline;
              frontCountTotal += obj.frontCountTotal;
              frontCountOnline += obj.frontCountOnline;
              arr.push(item);
            }
          }
        } else if (item.type === 'channel') {
          flag = true;
          arr.push(item);
        } else {
          item.childrenFront = item.children;
          item.children = null;
          if (item.video === 1) {
            frontCountTotal++;
            if (item.originData.terminalState === 1) {
              frontCountOnline++;
            }
            flag = true;
            arr.push(item);
          }
        }
      });
      return {
        flag: flag,
        data: arr,
        frontCountOnline: frontCountOnline,
        frontCountTotal: frontCountTotal
      };
    },
    /**
     * (旧)单选变化
     * @param {Object} val
     * @param {Object} val.originData
     * @param {String} val.originData.id 车辆ID
     * @param {String} val.originData.deptName 车组名称
     * @param {String} val.originData.licenceColor 车组名称
     * @param {String} val.originData.licencePlate 车组名称
     * @param {String} val.originData.simId 车组名称
     * @param {String} val.originData.terminalState 终端状态
     * @param {String} val.originData.vehicleModel 车辆类型
     * @param {String} val.originData.vehicleState 车辆状态
     * @param {String} val.originData.vehicleUseType 车辆行业类型
     */
    handleRadioButtonChange (val) {
      console.log('VehicleSingleSelectTree#setPhotoCar-->', val.originData);
      this.$emit('setPhotoCar', val.originData.licencePlate);
      this.$emit('selectedRow', val.originData.licencePlate);
    },
    /**
     * 节点被点击
     */
    nodeClick (data, node) {
      if (data.type !== 'dept' && data.type !== 'dept_son' && data.type !== 'channel') {
        this.$emit('setPhotoCar', data.label);
        this.$emit('setPhotoCarSingleId', data.originData);
        this.$emit('selectedRow', data.label);
        this.$emit('selectedRowSingle', data.originData);
        this.$emit('getSingleCarOriInfo', data);
        if (data) {
          console.log(data);
          this.$emit('selectedVehicle', data.originData);
        }
      }
    },
    /**
     * (新)单选可取消
     * @param {String} licencePlate
     * @param {Object} node
     * @param {Object} data
     * @param {Object} data.originData
     * @param {Object} data.originData.id 车辆ID
     * @param {Object} data.originData.licencePlate 车牌
     */
    radioClick (licencePlate, node, data) {
      data.originData.id === this.currentRow ? (this.currentRow = '') : (this.currentRow = data.originData.id);
      let Plate = this.currentRow ? licencePlate : null;
      let singleData = this.currentRow ? data.originData : null;
      this.$emit('setPhotoCar', Plate);
      this.$emit('setPhotoCarSingleId', singleData);
      this.$emit('selectedRow', Plate);
      this.$emit('selectedRowSingle', singleData);
      this.$emit('getSingleCarOriInfo', data);
      if (data) {
        console.log(data);
        this.$emit('selectedVehicle', data.originData);
      }
    },
    /**
     * 刷新
     */
    onRefresh (callback) {
      this.loading = true;
      this.onClear();
      if (!this.customTreeApi) {
        crudVehicle.treeWithChannel().then(treeData => {
          console.log('crudVehicle.tree-->', treeData);
          // localStorage.setItem('storedVehicleTreeData', JSON.stringify(treeData));
          this.dataSource(treeData);
          let vehicleCount = 0;
          for (let i = 0; i < treeData.length; i++) {
            vehicleCount += treeData[i].vehicleCount;
          }
          this.vehicleCount = vehicleCount;
          this.nextButtonVisible = false;
          this.loading = false;
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        }).then(() => {
          callback && callback();
        });
      } else {
        this.customTreeApi().then(treeData => {
          this.dataSource(treeData);
          let vehicleCount = 0;
          for (let i = 0; i < treeData.length; i++) {
            vehicleCount += treeData[i].vehicleCount;
          }
          this.vehicleCount = vehicleCount;
          this.nextButtonVisible = false;
          this.loading = false;
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        }).then(() => {
          callback && callback();
        });
      }
    },
    /**
     * 下一个
     */
    onNext () {
      this.$refs.tree.filter(this.query, {
        ignoreLeafType: 'dept',
        expandNew: true
      });
      this.nextButtonVisible = true;
    },
    /**
     * 节点过滤
     */
    searchFilter () {
      if (this.query.licencePlate === '' &&
        this.query.terminalState === '' &&
        this.query.deptName === '' &&
        this.query.vehicleUseType === '' &&
        this.query.vehicleModel === '' &&
        this.query.terminalModel === '' &&
        this.query.filterText === '') {
        this.$refs.tree.filter(this.query);
        this.nextButtonVisible = false;
      } else {
        this.$refs.tree.filter(this.query, {
          ignoreLeafType: 'dept',
          expandNew: false
        });
        this.nextButtonVisible = true;
      }
    },
    /**
     * 节点搜索过滤函数
     * @param value
     * @param {String} value.licencePlate
     * @param {Int} value.terminalState
     * @param {String} value.deptName
     * @param {Object} data
     * @param {Object} node
     * @param data
     */
    filterNode (value, data, node) {
      // console.log('filterNode-->', data.type);
      if (data.type === 'dept' && data.originData.name.indexOf(value.deptName) === -1) {
        return false;
      }
      if (data.type === 'vehicle') {
        // 判断如果子节点的单位和目标单位不一致，则不显示
        if (value.deptName !== '' && !this.recursionHandle(node, value.deptName)) {
          return false;
        }
        if (data.originData.licencePlate.indexOf(value.licencePlate) === -1) {
          return false;
        } else if ((value.terminalState === 0 || value.terminalState === 1) && data.originData.terminalState !== value.terminalState) {
          return false;
        } /* else if (value.vehicleModel !== '' && data.originData.vehicleModel !== value.vehicleModel) {
          return false;
        } else if (value.vehicleUseType !== '' && data.originData.vehicleUseType !== value.vehicleUseType) {
          return false;
        } */
        if (value.terminalModel !== '' && data.originData.terminalModel.indexOf(value.terminalModel) === -1) {
          return false;
        }
      }
      return true;
    },
    /**
     * 节点搜索过滤函数(替换新的查询搜索)
     */
    filterHandle (value, data, node) {
      let parentNode = node.parent; // 父级node
      let labels = [node.label]; // 当前node的名字
      let level = 1; // 层级
      while (level < node.level) {
        labels = [...labels, parentNode.label]; // 当前node名字，父级node的名字
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(d => d.indexOf(value.filterText) !== -1);
    },
    // 递归寻找父级车组名
    recursionHandle (node, value) {
      if (!node.parent || !node.parent.parent) {
        return false;
      } else if (node.parent.data.label.indexOf(value) > -1) {
        return true;
      } else {
        return this.recursionHandle(node.parent, value);
      }
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass (data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'vehicle'); // 车辆
      } else if (materialsModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'materials'); // 物资
      } else if (personnelModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'personnel'); // 人员
      } else if (shortMessageModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'shortMessage'); // 短报文终端
      } else if (timeServiceModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'timeService'); // 授时终端
      } else if (monitorModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'monitor'); // 监测终端
      } else if (data.originData.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(data.originData, 'other'); // 其他
      }
      return vehicleIcon;
    },
    /**
     * 获取车辆类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @param {Number} data.originData.terminalState
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getVehicleIconClass (data) {
      // console.log(data);
      /* switch (data.originData.terminalState) {
        case terminalStateEnum.ONLINE: {
          return 'truckOnline';
        }
        case terminalStateEnum.OFFLINE: {
          return 'truckOffline';
        }
      }
      return ''; */
      let passengerModel = ['10', '11', '12', '13', '14', '15', '16'];
      let truckModel = ['20', '21', '22', '23'];
      let vehicleIcon = '';
      if (passengerModel.includes(data.originData.vehicleModel)) {
        // vehicleIcon = data.originData.terminalState ? 'busOnline' : 'busOffline';
        vehicleIcon = this.colorType(data.originData, 'passenger');
      } else if (truckModel.includes(data.originData.vehicleModel)) {
        vehicleIcon = this.colorType(data.originData, 'vehicle');
        // vehicleIcon = data.originData.terminalState ? 'truckOnline' : 'truckOffline';
      } else if (data.originData.vehicleModel === '66') {
        vehicleIcon = this.colorType(data.originData, 'bus'); // 公交车
      } else if (data.originData.vehicleModel === '73' || data.originData.vehicleModel === '33') {
        vehicleIcon = this.colorType(data.originData, 'coldChain'); // 冷链车
      } else if (data.originData.vehicleModel === '35' || data.originData.vehicleModel === '40') {
        vehicleIcon = this.colorType(data.originData, 'oilTank'); // 油罐车
      } else if (data.originData.vehicleModel === '70' || data.originData.vehicleModel === '71') {
        vehicleIcon = this.colorType(data.originData, 'rentOut'); // 出租车
      } else {
        vehicleIcon = this.colorType(data.originData, 'otherOffline');
        // vehicleIcon = data.originData.terminalState ? 'otherCarOnline' : 'otherCarOffline';
      }
      return vehicleIcon;
    },
    colorStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    colorType (val, type) {
      // 0-离线 1-行驶 2-停驶-ACC关 3-告警 4-未定位 5-停驶-ACC开
      // 离线(灰色) 行驶(蓝色) 停驶-ACC关(紫色) 告警(红色) 未定位(黄色) 停驶-ACC开(绿色)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}`;
        break;
      case 1:
        vehicleIcon = `${type}Blue`;
        break;
      case 2:
        vehicleIcon = `${type}Purple`;
        break;
      case 3:
        vehicleIcon = `${type}Red`;
        break;
      case 4:
        vehicleIcon = `${type}Yellow`;
        break;
      case 5:
        vehicleIcon = `${type}Green`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass (data) {
      let treeIcon = '';
      if (data.onlineVehicleCount > 0) {
        treeIcon = 'treeOnline';
      } else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 鼠标滑过某个节点
     * @param node
     */
    mouseOverNode (node) {
      if (node.data.type === 'channel') {
        let data = {
          licencePlate: node.parent.data.originData.licencePlate,
          channel: node.data.originData.channel
        };
        this.$emit('mouseOverNode', data);
      } else if (node.data.type === 'vehicle') {
        let data = {
          licencePlate: node.data.originData.licencePlate
        };
        this.$emit('mouseOverNode', data);
      } else {
        this.$emit('mouseOverNode', {});
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 切换状态
     */
    toggleDropDown () {
      this.isDropDown = !this.isDropDown;
      this.$emit('getPanelStatus', this.isDropDown);
    },
    /**
     * 切换状态
     */
    changeCarList () {
      this.$emit('changeCarList');
    },
    /**
     * 收缩状态改变
     * @param val
     */
    handleCollapseChange (val) {
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 更新样式
     */
    updateStyle () {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      // let firstCollapseItemDomHeight = this.$refs['firstCollapseItemComponent'].$el.clientHeight;
      let headerDomHeight = this.$refs['headerDom'].clientHeight;
      // const xhSpacingBase = variables.xhSpacingBase;// @xhSpacingBase
      // const collapseItemTitleHeight = variables.tagsViewHeight; // @tagsViewHeight
      // let maxHeight = selectComponentDomHeight - firstCollapseItemDomHeight - headerDomHeight - collapseItemTitleHeight - 4 * xhSpacingBase;
      let maxHeight = selectComponentDomHeight - headerDomHeight;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        requestAnimationFrame(this.updateStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear () {
      this.query = {
        licencePlate: '',
        vehicleModel: '',
        vehicleUseType: '',
        terminalState: '',
        deptName: '',
        terminalModel: '',
        filterText: ''
      };
    },

    /**
     * 清空选择
     */
    clear () {
      this.currentRow = '';
      this.updateStyle();
    },
    /**
     * 设置选中的车辆
     * @param {String} val 车牌号
     */
    setSelectedCar (val) {
      this.currentRow = val;
    },
    /**
     * 获取车辆总数
     * TODO 修改函数名
     * @return {Number}
     */
    getCarNumber () {
      return this.vehicleCount;
    },
    /**
     * 设置下拉
     * @param {Boolean} val
     */
    setDropDown (val) {
      this.isDropDown = val;
    },
    setSelectedVehicle (val) {
      if (this.loading) {
        setTimeout(() => {
          this.setSelectedVehicle(val);
        }, 1000);
      }
      else {
        this.query.filterText = val.targetName;
        this.searchFilter();
        // 模拟点击节点
        this.$nextTick(()=>{
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(val.id);
            const node = this.$refs.tree.getNode(val.id);
            if (node) {
              this.nodeClick(node.data);
            }
          }
        });
      }
    },
    getAccState (data) {
      // acc_state ACC状态 0-关，1-开
      return data.originData.accState === 1 ? '[ACC开]' : '[ACC关]';
    }
  }
};
</script>

<style lang="less" scoped>
  @import "../../assets/less/variables.less";
  .custom-tree-node{
    .el-radio{
      margin-right: 4px;
    }
  }
  .space-set{
    white-space: nowrap
  }
  .video-single-select-tree {
    height: 100%;
  .header {
    background-color: #f3f6f8;
    height: 54px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  .car-filter-container {
    display: flex;
    align-items: center;
    border: 1px solid #bfbfbf;
    background-color: #ffffff;
    padding: 0 4px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;

    ::v-deep .el-input__inner {
      border: none !important;
      box-shadow: none !important;
    }

    .el-icon-search, .el-icon-refresh {
      font-size: 18px;
      color: #c0c0c0;
    }

    .el-icon-refresh {
      cursor: pointer;
    }
  }

  .online-select {
    width: 70px;
    margin-left: 4px;

    ::v-deep .el-input__inner {
      border-radius: 0;
      border-color: #bfbfbf;
      color: #4d83c9;
    }
  }

  ::v-deep .el-tree-node__content {
    .el-checkbox {
      margin-right: 4px !important;
    }
  }
}
.video-select-component-container {
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  transition: @xhTransitionFast;
  display: flex;
  flex-direction: column;
  .xh-tree {
    flex: 1;
    ::v-deep .vue-recycle-scroller {
      height: 100% !important;
    }
  }
}
</style>
