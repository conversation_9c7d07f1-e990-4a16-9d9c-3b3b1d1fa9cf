<template>
  <div class="crud-opts gn-button-group">
    <span class="crud-opts-left">
      <!--左侧插槽-->
      <slot name="left" />
      <el-button
        v-if="crud.optShow.add"
        v-permission="permission.add"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="crud.toAdd"
      >
        新 增
      </el-button>
      <!-- <el-button
        v-if="crud.optShow.edit && showEdit"
        v-permission="permission.edit"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="crud.selections.length !== 1"
        @click="crud.toEdit(crud.selections[0])"
      >
        修改
      </el-button> -->
      <el-button
        v-if="crud.optShow.del"
        slot="reference"
        v-permission="permission.del"
        :loading="crud.delAllLoading"
        class="filter-item"
        icon="el-icon-delete"
        size="small"
        @click="toDelete(crud.selections)"
      >
        删 除
      </el-button>
      <el-button
        v-if="crud.optShow.download"
        :loading="crud.downloadLoading"
        :disabled="!crud.data || !crud.data.length"
        class="filter-item cyan-btn"
        size="small"
        icon="el-icon-download"
        @click="handleExport"
      >导 出</el-button>
      <!--右侧-->
      <slot name="right" />
    </span>
    <span
      v-if="toolShow"
      class="crud-opts-right"
    >
      <!-- <el-tooltip
        content="隐藏搜索框"
        placement="top-start"
      >
        <el-button
          size="small"
          icon="el-icon-search"
          @click="toggleSearch()"
        />
      </el-tooltip>
      <el-tooltip
        content="刷新"
        placement="top-start"
      >
        <el-button
          size="small"
          icon="el-icon-refresh"
          @click="crud.refresh()"
        />
      </el-tooltip> -->
      <el-tooltip
        v-if="hasToggleCol"
        content="隐藏列名"
        placement="top-start"
      >
        <el-popover
          placement="bottom-end"
          :width="width"
          trigger="click"
          popper-class="popper-container"
        >
          <el-button
            slot="reference"
            size="small"
            icon="el-icon-setting"
            class="crud-opts-right-setting"
          />
          <el-checkbox
            v-model="allColumnsSelected"
            :indeterminate="allColumnsSelectedIndeterminate"
            @change="handleCheckAllChange"
          >
            全选
          </el-checkbox>
          <el-checkbox
            v-for="item in crud.props.tableColumns"
            :key="item.label"
            v-model="item.visible"
            @change="handleCheckedTableColumnsChange(item)"
          >
            {{ item.label }}
          </el-checkbox>
        </el-popover>
      </el-tooltip>
    </span>
  </div>
</template>
<script>
import CRUD, { crud } from './crud';
export default {
  mixins: [crud()],
  props: {
    permission: {
      type: Object,
      default: null
    },
    download: {
      type: Boolean,
      default: true
    },
    showEdit: {
      type: Boolean,
      default: true
    },
    toolShow: {
      type: Boolean,
      default: true
    },
    hasToggleCol: {
      type: Boolean,
      default: true
    },
    width: {
      type: Number,
      default: 150
    }
  },
  data () {
    return {
      allColumnsSelected: false,
      allColumnsSelectedIndeterminate: true
    };
  },
  watch: { // 部分页面没有导出功能(命令列表)
    download: {
      handler (newVal) {
        this.crud.optShow.download = newVal;
      },
      immediate: true
    }
  },
  created () {
    this.crud.updateProp('searchToggle', true);
    this.$nextTick(() => {
      const result = Object.values(this.crud.props.tableColumns).some(item => !item.visible);
      this.allColumnsSelected = !result;
      this.allColumnsSelectedIndeterminate = result;
    });
  },
  methods: {
    handleExport () {
      this.crud.toQuery();
      const ids = this.crud.selections?.length ? this.crud.selections.map( item => item.id) : null;
      this.crud.doExport(ids);
    },
    toDelete (datas) {
      if (datas.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm(`确定将选择数据删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.crud.delAllLoading = true;
        this.crud.doDelete(datas);
      }).catch(() => {
      });
    },
    handleCheckAllChange (val) {
      if (val === false) {
        this.allColumnsSelected = true;
        return;
      }
      for (const key in this.crud.props.tableColumns) {
        this.crud.props.tableColumns[key].visible = val;
      }
      this.allColumnsSelected = val;
      this.allColumnsSelectedIndeterminate = false;
      this.$nextTick(()=>{
        this.crud.doLayoutTable();
      });
    },
    handleCheckedTableColumnsChange (item) {
      let totalCount = 0;
      let selectedCount = 0;
      for (const key in this.crud.props.tableColumns) {
        ++totalCount;
        selectedCount += this.crud.props.tableColumns[key].visible ? 1 : 0;
      }
      if (selectedCount === 0) {
        this.crud.notify('请至少选择一列', CRUD.NOTIFICATION_TYPE.WARNING);
        this.$nextTick(function () {
          item.visible = true;
        });
        return;
      }
      this.allColumnsSelected = selectedCount === totalCount;
      this.allColumnsSelectedIndeterminate = selectedCount !== totalCount && selectedCount !== 0;
      this.$nextTick(()=>{
        this.crud.doLayoutTable();
      });
    },
    toggleSearch () {
      this.crud.props.searchToggle = !this.crud.props.searchToggle;
    }
  }
};
</script>

<style lang="less">
  .crud-opts {
    padding: 0 0 15px;
    display: -webkit-flex;
    display: flex;
    justify-content: space-between;
    width: 100%;
    min-height: 36px;
    /*align-items: center;*/
  }
  .popper-container{
    max-height: 500px;
    overflow-y: scroll;
    overflow-x: hidden;
    .el-checkbox__label {
      color: rgba(0, 0, 0, 0.85) !important;
    }
  }
  .crud-opts-right-setting {
    font-size: 16px;
    border: none;
    i {
      color: rgba(0, 0, 0, 0.75);
    }
  }
</style>
