<!--搜索与重置-->
<template>
  <div class="xh-search-btn gn-button-group">
    <slot name="left" />
    <el-button
      class="filter-item"
      size="small"
      type="primary"
      icon="el-icon-search"
      @click="crud.toQuery"
    >查 询</el-button>
    <slot name="center" />
    <el-button
      v-if="clearShow"
      class="filter-item clear-item"
      size="small"
      icon="el-icon-refresh-left"
      @click="clearClick"
    >重 置</el-button>
    <slot name="right" />
  </div>
</template>
<script>
export default {
  props: {
    crud: {
      type: Object,
      required: true
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    clearShow: {
      type: Boolean,
      default: true
    },
    clearQuery: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    clearClick () {
      this.$emit('handleClear');
      this.crud.resetQuery();
      this.crud.toQuery();
    }
  }
};
</script>
