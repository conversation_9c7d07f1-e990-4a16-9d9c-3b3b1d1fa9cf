import { setToken, setRefreshToken, removeToken, removeRefreshToken } from '@/util/auth';
import { Message } from 'element-ui';
import { setStore, getStore } from '@/util/store';
import { isURL, validatenull } from '@/util/validate';
import { deepClone } from '@/util/util';
import website from '@/config/website';
import {
  loginByUsername,
  loginBySocial,
  loginBySso,
  getUserInfo,
  logout,
  refreshToken,
  getButtons,
  gnToken,
  getGNButtons
} from '@/api/user';
import { getTopMenu, getRoutes } from '@/api/system/menu';
import md5 from 'js-md5';
let isCE = window.localStorage.hasOwnProperty("cedi__Access-Token");

function addPath(ele, first) {
  const menu = website.menu;
  const propsConfig = menu.props;
  const propsDefault = {
    label: propsConfig.label || 'name',
    path: propsConfig.path || 'path',
    icon: propsConfig.icon || 'icon',
    children: propsConfig.children || 'children'
  };
  const icon = ele[propsDefault.icon];
  ele[propsDefault.icon] = validatenull(icon) ? menu.iconDefault : icon;
  const isChild = ele[propsDefault.children] && ele[propsDefault.children].length !== 0;
  if (!isChild) ele[propsDefault.children] = [];
  if (!isChild && first && !isURL(ele[propsDefault.path])) {
    ele[propsDefault.path] = ele[propsDefault.path] + '/index';
  }
  else {
    ele[propsDefault.children].forEach(child => {
      addPath(child);
    });
  }

}

const user = {
  state: {
    tenantId: getStore({ name: 'tenantId' }) || '',
    userInfo: getStore({ name: 'userInfo' }) || [],
    permission: isCE ? {} : getStore({ name: 'permission' }) || {},
    roles: [],
    menuId: {},
    menu: getStore({ name: 'menu' }) || [],
    menuAll: getStore({ name: 'menuAll' }) || [],
    token: getStore({ name: 'token' }) || '',
    refreshToken: getStore({ name: 'refreshToken' }) || ''
  },
  actions: {
    //根据用户名登录
    LoginByUsername({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        loginByUsername(userInfo.tenantId, userInfo.deptId, userInfo.roleId, userInfo.username, md5(userInfo.password), userInfo.type, userInfo.key, userInfo.code).then(res => {
          const data = res.data;
          if (data.error_description) {
            Message({
              message: data.error_description,
              type: 'error'
            });
          }
          else {
            commit('SET_TOKEN', data.access_token);
            commit('SET_REFRESH_TOKEN', data.refresh_token);
            commit('SET_TENANT_ID', data.tenant_id);
            commit('SET_USER_INFO', data);
            commit('DEL_ALL_TAG');
            commit('CLEAR_LOCK');
          }
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },
    //根据手机号登录
    LoginByPhone({ commit }, userInfo) {
      return new Promise((resolve) => {
        loginByUsername(userInfo.phone, userInfo.code).then(res => {
          const data = res.data.data;
          commit('SET_TOKEN', data);
          commit('DEL_ALL_TAG');
          commit('CLEAR_LOCK');
          resolve();
        });
      });
    },
    //根据第三方信息登录
    LoginBySocial({ commit }, userInfo) {
      return new Promise((resolve) => {
        loginBySocial(userInfo.tenantId, userInfo.source, userInfo.code, userInfo.state).then(res => {
          const data = res.data;
          if (data.error_description) {
            Message({
              message: data.error_description,
              type: 'error'
            });
          }
          else {
            commit('SET_TOKEN', data.access_token);
            commit('SET_REFRESH_TOKEN', data.refresh_token);
            commit('SET_USER_INFO', data);
            commit('SET_TENANT_ID', data.tenant_id);
            commit('DEL_ALL_TAG');
            commit('CLEAR_LOCK');
          }
          resolve();
        });
      });
    },
    //根据单点信息登录
    LoginBySso({ commit }, userInfo) {
      return new Promise((resolve) => {
        loginBySso(userInfo.state, userInfo.code).then(res => {
          const data = res.data;
          if (data.error_description) {
            Message({
              message: data.error_description,
              type: 'error'
            });
          }
          else {
            commit('SET_TOKEN', data.access_token);
            commit('SET_REFRESH_TOKEN', data.refresh_token);
            commit('SET_USER_INFO', data);
            commit('SET_TENANT_ID', data.tenant_id);
            commit('DEL_ALL_TAG');
            commit('CLEAR_LOCK');
          }
          resolve();
        });
      });
    },
    //获取用户信息
    GetUserInfo({ commit }) {
      return new Promise((resolve, reject) => {
        getUserInfo().then((res) => {
          const data = res.data.data;
          commit('SET_ROLES', data.roles);
          resolve(data);
        }).catch(err => {
          reject(err);
        });
      });
    },
    //获取用户信息
    GetGNInfo({ commit }, token) {
      return new Promise((resolve, reject) => {
        gnToken(token).then((res) => {
          const data = res.data;
          if (data.error_description) {
            Message({
              message: data.error_description,
              type: 'error'
            });
          }
          else {
            commit('SET_TOKEN', data.access_token);
            commit('SET_REFRESH_TOKEN', data.refresh_token);
            commit('SET_TENANT_ID', data.tenant_id);
            commit('SET_USER_INFO', data);
            commit('DEL_ALL_TAG');
            commit('CLEAR_LOCK');
          }
          resolve();
        }).catch(err => {
          reject(err);
        });
      });
    },
    //刷新token
    refreshToken({
      state,
      commit
    }, userInfo) {
      window.console.log('handle refresh token');
      return new Promise((resolve, reject) => {
        refreshToken(state.refreshToken, state.tenantId,
          !validatenull(userInfo) ? userInfo.deptId : state.userInfo.dept_id,
          !validatenull(userInfo) ? userInfo.roleId : state.userInfo.role_id
        ).then(res => {
          const data = res.data;
          commit('SET_TOKEN', data.access_token);
          commit('SET_REFRESH_TOKEN', data.refresh_token);
          commit('SET_USER_INFO', data);
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 登出
    LogOut({ commit }) {
      return new Promise((resolve, reject) => {
        logout().then(() => {
          commit('SET_TOKEN', '');
          commit('SET_MENU', []);
          commit('SET_MENU_ALL_NULL', []);
          commit('SET_ROLES', []);
          commit('SET_TAG_LIST', []);
          commit('DEL_ALL_TAG');
          commit('CLEAR_LOCK');
          commit('SET_DEPT_PER', null);
          commit('SET_DEPT', null);
          removeToken();
          removeRefreshToken();
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },
    //注销session
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '');
        commit('SET_MENU_ALL_NULL', []);
        commit('SET_MENU', []);
        commit('SET_ROLES', []);
        commit('SET_TAG_LIST', []);
        commit('DEL_ALL_TAG');
        commit('CLEAR_LOCK');
        removeToken();
        removeRefreshToken();
        resolve();
      });
    },
    //获取顶部菜单
    GetTopMenu() {
      return new Promise(resolve => {
        getTopMenu().then((res) => {
          const data = res.data.data || [];
          resolve(data);
        });
      });
    },
    //获取系统菜单
    GetMenu({
      commit,
      dispatch
    }, topMenuId) {
      return new Promise(resolve => {
        if (!sessionStorage.getItem('saber-integration')) {
          dispatch('GetButtons');
        }
        getRoutes(topMenuId).then((res) => {
          const data = res.data.data;
          let menu = deepClone(data);
          menu.forEach(ele => {
            addPath(ele, true);
          });
          commit('SET_MENU_ALL', menu);
          commit('SET_MENU', menu);
          resolve(menu);
        });
      });
    },
    //获取系统按钮
    GetButtons({ commit }) {
      return new Promise((resolve) => {
        let isCE = window.localStorage.hasOwnProperty("cedi__Access-Token");
        if (isCE) {
          const data = JSON.parse(window.sessionStorage.getItem('LOGIN_USER_BUTTON_AUTH'));
          // 底座的sessionStorage有权限集合
          if (data) {
            const formatData = [];
            data.forEach(item => {
              if (item.systemCode === 'bdswz') {
                formatData.push({
                  code: item.action,
                  name: item.describe
                });
              }
            });
            console.log('-> 国能sessionStorage获取到的权限', formatData);
            commit('SET_PERMISSION', formatData);
            resolve();
          } else {
            getGNButtons().then((res) => {
              const formatData = [];
              res.data.data.forEach(item => {
                formatData.push({
                  code: item.componentUrl, //国能提的接口中，componentUrl存放的是按钮code
                  name: item.componentName
                });
              });
              console.log('-> 国能接口获取到的权限', formatData);
              commit('SET_PERMISSION', formatData);
              resolve();
            });
          }
        } else {
          getButtons().then(res => {
            const data = res.data.data;
            commit('SET_PERMISSION', data);
            resolve();
          });
        }
      });
    }
  },
  mutations: {
    SET_TOKEN: (state, token) => {
      setToken(token);
      state.token = token;
      setStore({
        name: 'token',
        content: state.token
      });
    },
    SET_MENU_ID(state, menuId) {
      state.menuId = menuId;
    },
    SET_MENU_ALL: (state, menuAll) => {
      let menu = state.menuAll;
      menuAll.forEach(ele => {
        if (!menu.find(item => item.label === ele.label && item.path === ele.path)) {
          menu.push(ele);
        }
      });
      state.menuAll = menu;
      if (!sessionStorage.getItem('saber-integration')) {
        setStore({
          name: 'menuAll',
          content: state.menuAll
        });
      }
    },
    SET_MENU_ALL_NULL: (state) => {
      state.menuAll = [];
      setStore({
        name: 'menuAll',
        content: state.menuAll
      });
    },
    SET_MENU: (state, menu) => {
      state.menu = menu;
      if (!sessionStorage.getItem('saber-integration')) {
        setStore({
          name: 'menu',
          content: state.menu
        });
      }
    },
    SET_REFRESH_TOKEN: (state, refreshToken) => {
      setRefreshToken(refreshToken);
      state.refreshToken = refreshToken;
      setStore({
        name: 'refreshToken',
        content: state.refreshToken
      });
    },
    SET_TENANT_ID: (state, tenantId) => {
      state.tenantId = tenantId;
      setStore({
        name: 'tenantId',
        content: state.tenantId
      });
    },
    SET_USER_INFO: (state, userInfo) => {
      if (validatenull(userInfo.avatar)) {
        userInfo.avatar = '/img/bg/img-logo.png';
      }
      state.userInfo = userInfo;
      setStore({
        name: 'userInfo',
        content: state.userInfo
      });
      localStorage.setItem('user', JSON.stringify(userInfo)); // 放到缓存中方便api模块调用
    },
    SET_USER_INFO_INTEGRATION: (state, userInfo) => {
      state.userInfo = userInfo;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSION: (state, permission) => {
      let result = [];
      function getCode(list) {
        list.forEach(ele => {
          if (typeof (ele) === 'object') {
            const chiildren = ele.children;
            const code = ele.code;
            if (chiildren) {
              getCode(chiildren);
            }
            else {
              result.push(code);
            }
          }
        });
      }
      getCode(permission);
      state.permission = {};
      result.forEach(ele => {
        state.permission[ele] = true;
      });
      // 取消所有权限缓存
      //是否是国能平台

      // 国能平台不做缓存
      if (!isCE) {
        setStore({
          name: 'permission',
          content: state.permission,
        });
      }
    }
  }

};
export default user;
