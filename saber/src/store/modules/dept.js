// import { getDeptSuperior } from '@/api/base/dept';
import { getDept } from '@/api/user';
import { flattenListDept } from '@/api/utils/share';

const dept = {
  state: {
    dept: null,
    selectedDeptId: '',
    // 第一次加载菜单时用到
    loadMenus: false,
    deptPer: null,
  },

  mutations: {
    SET_DEPT: (state, dept) => {
      state.dept = dept;
    },
    SET_DEPT_ID: (state, deptId) => {
      state.selectedDeptId = deptId;
      window.selectedDeptId = deptId;// 这个参数的优先级在window.user.dept.id之上 @see getParamDeptId
    },
    SET_LOAD_DEPTS: (state, loadMenus) => {
      state.loadMenus = loadMenus;
    },
    SET_DEPT_PER: (state, data) => {
      state.deptPer = data;
    }
  },

  actions: {
    // 获取用户可见的单位树
    GetDept () {
      return new Promise((resolve, reject) => {
        getDept().then(res => {
          let deptList = flattenListDept(JSON.parse(JSON.stringify(res.data.data)));
          localStorage.setItem(`dept-recursion`, JSON.stringify(deptList));
          resolve(res.data);
        }).catch(error => {
          console.error('getDeptSuperior-->', error);
          reject(error);
        });
      });
    },
    updateLoadDepts ({ commit }) {
      return new Promise((resolve, reject) => {
        commit('SET_LOAD_DEPTS', false);
      });
    },
    // 设置
    SetSelectedDeptId ({ commit }, deptId) {
      return new Promise((resolve, reject) => {
        commit('SET_DEPT_ID', deptId);
      });
    }
  }
};

export default dept;
