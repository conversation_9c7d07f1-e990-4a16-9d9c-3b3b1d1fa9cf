<template>
  <!--  设备赋码页面取消  此代码暂时无效-->
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :permission="permission"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        >
          <template slot="left">
            <el-button
              v-permission="permission.batch"
              class="filter-item"
              size="small"
              icon="el-icon-edit"
              @click="handleBatch"
            >
              批 赋 码
            </el-button>
            <el-button
              v-permission="permission.imp"
              class="filter-item"
              size="small"
              icon="el-icon-upload2"
              @click="batchvisible = true"
            >
              导 入
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          row-key="id"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
            :selectable="checkSelectable"
            reserve-selection
          />
          <el-table-column
            v-permission="['admin','terminalAssignment:batch']"
            width="60"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="left">
                  <el-button
                    v-permission="permission.batch"
                    type="text"
                    size="small"
                    :disabled="scope.row.codeMachine !== 'CE01'"
                    @click="toDetail(scope.row)"
                  >
                    赋码
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 序列号 -->
          <el-table-column
            v-if="columns.visible('deviceNo')"
            :label="getLabel('deviceNo')"
            prop="deviceNo"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 批次 -->
          <el-table-column
            v-if="columns.visible('batchNo')"
            :label="getLabel('batchNo')"
            prop="batchNo"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.batchNo }}
              </span>
            </template>
          </el-table-column>
          <!-- IMEI -->
          <el-table-column
            v-if="columns.visible('imei')"
            :label="getLabel('imei')"
            prop="imei"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 设备厂商 -->
          <el-table-column
            v-if="columns.visible('manufacturer')"
            :label="getLabel('manufacturer')"
            prop="manufacturer"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 设备类型 -->
          <el-table-column
            v-if="columns.visible('deviceType')"
            :label="getLabel('deviceType')"
            prop="deviceType"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 设备型号 -->
          <el-table-column
            v-if="columns.visible('deviceModel')"
            :label="getLabel('deviceModel')"
            prop="deviceModel"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 协议 -->
          <el-table-column
            v-if="columns.visible('protocol')"
            :label="getLabel('protocol')"
            prop="protocol"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 送检时间 -->
          <el-table-column
            v-if="columns.visible('sendDate')"
            :label="getLabel('sendDate')"
            prop="sendDate"
            width="130"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ parseTime(scope.row.sendDate, '{y}-{m}-{d}') }}
              </span>
            </template>
          </el-table-column>
          <!-- 送检企业 -->
          <el-table-column
            v-if="columns.visible('companyName')"
            :label="getLabel('companyName')"
            prop="companyName"
            min-width="140"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 北斗芯片序列号 -->
          <el-table-column
            v-if="columns.visible('chipSeq')"
            :label="getLabel('chipSeq')"
            prop="chipSeq"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 赋码编号 -->
          <el-table-column
            v-if="columns.visible('deviceNum')"
            :label="getLabel('deviceNum')"
            prop="deviceNum"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 赋码机编号 -->
          <el-table-column
            v-if="columns.visible('codeMachine')"
            :label="getLabel('codeMachine')"
            prop="codeMachine"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 终端状态 -->
          <!-- <el-table-column
            v-if="columns.visible('deviceState')"
            :label="getLabel('deviceState')"
            prop="deviceState"
            min-width="100"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.deviceState === '0' ? '离线' : scope.row.deviceState === '1' ? '在线' : '' }}
            </template>
          </el-table-column> -->
          <!-- 赋码结果 -->
          <el-table-column
            v-if="columns.visible('codeState')"
            :label="getLabel('codeState')"
            prop="codeState"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 上次赋码成功时间 -->
          <el-table-column
            v-if="columns.visible('codeTime')"
            :label="getLabel('codeTime')"
            prop="codeTime"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ parseTime(scope.row.codeTime) }}
              </span>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column
            v-if="columns.visible('codeResMessage')"
            :label="getLabel('codeResMessage')"
            prop="codeResMessage"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 入网状态 -->
          <el-table-column
            v-if="columns.visible('formalStr')"
            :label="getLabel('formalStr')"
            prop="formalStr"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 创建时间 -->
          <el-table-column
            v-if="columns.visible('createTime')"
            :label="getLabel('createTime')"
            prop="createTime"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ parseTime(scope.row.createTime) }}
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :device-no="deviceNo"
        :protocol="protocol"
        :dialog-visible.sync="dialogVisible"
        @handleRefresh="handleRefresh"
      />
      <BatchImport
        class="batch-container"
        :visible="batchvisible"
        :range="1"
        tips="注: 该功能仅用于赋码机赋码导入"
        :is-template="false"
        mod="terminal"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <MsgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
    </div>
  </basic-container>
</template>

<script>
import crudTerminalAssignment from '@/api/code/terminalAssignment';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import { parseTime } from '@/api/utils/share';
import { allCompanyName } from '@/api/bdTest/report';
import { pagination as allManufacturer } from '@/api/bdTest/manuFactor';
import BatchImport from '@/components/upload/batchImport.vue';
import MsgDialog from '@/components/importErr/index.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('TerminalAssignment', 'uniName'), // 设备赋码
  crudMethod: { ...crudTerminalAssignment }
});

export default {
  name: 'TerminalAssignment',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon, BatchImport, MsgDialog },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'testDeviceType',
    'codeResult'
  ],
  data () {
    return {
      permission: {
        add: ['admin', 'terminalAssignment:add'],
        edit: ['admin', 'terminalAssignment:edit'],
        del: ['admin', 'terminalAssignment:del'],
        view: ['admin', 'terminalAssignment:view'],
        batch: ['admin', 'terminalAssignment:batch'],
        imp: ['admin', 'terminalAssignment:imp']
      },
      headConfig: {
        item: {
          1: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum',
          },
          2: {
            name: '序列号',
            type: 'input',
            value: 'deviceNo',
          },
          3: {
            name: '送检企业',
            type: 'select',
            value: 'companyId',
            options: []
          },
          5: {
            name: '赋码结果',
            type: 'select',
            value: 'codeResult',
            dictOptions: 'codeResult'
          },
          6: {
            name: '赋码机编号',
            type: 'input',
            value: 'codeMachine'
          },
          7: {
            name: '入网状态',
            type: 'select',
            value: 'formal',
            options: [
              {
                label: '未正式入网',
                value: 0
              },
              {
                label: '已正式入网',
                value: 1
              }
            ]
          },
          8: {
            name: '设备厂商',
            type: 'select',
            value: 'manufacturer',
            options: []
          },
          9: {
            name: '设备型号',
            type: 'input',
            value: 'deviceModel'
          },
          10: {
            name: '上次赋码日期',
            type: 'datetime',
            value: 'codeTime',
            valueFormat: 'yyyy-MM-dd HH:mm:ss'
          }
        },
        button: {
        }
      },
      dialogVisible: false,
      deviceNo: null,
      protocol: null,
      // 批量引入相关
      batchvisible: false,
      msgData: [], // 批量导入提示消息
      numKey: undefined, // 递归中继值
      dataType: { // excel里文字对应的key
        '赋码机编号': 'codeMachine',
        '赋码号': 'deviceNum',
        '设备类型': 'deviceType',
        '设备厂商编号': 'manufacturer',
        '设备型号': 'deviceModel',
        '设备序列号': 'deviceSeq',
        'IMEI号': 'imei',
        '北斗芯片序列号': 'chipSeq',
        '赋码签名': 'deviceNumSign',
        '赋码时间': 'codeTime'
      },
      // 时间类型，统一转换时间
      typeTime: [],
      // 必填项
      typeRequired: [
        'codeMachine', 'deviceNum', 'deviceType', 'manufacturer', 'deviceModel', 'deviceSeq', 'imei', 'chipSeq', 'deviceNumSign', 'codeTime'
      ],
      // 字符串项
      typeString: [

      ],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        deviceType: 'testDeviceType'
      },
      tipsKey: [] // 提示点集合
    };
  },
  created() {
    // 获取送检企业列表
    this.getCompanyList();
    // 获取设备厂商列表
    this.getManufacturerList();
  },
  methods: {
    [CRUD.HOOK.beforeRefresh] () {
      // if(this.crud.selections?.length) {
      //   this.$refs.table?.clearSelection()
      // }
    },
    /** 刷新 - 之后 */
    [CRUD.HOOK.afterRefresh] () {
      const allCancel = this.crud.selections.filter(item => item.codeMachine !== 'CE01');
      // 当table只选择了一行时使用toggleRowSelection取消勾选不生效, 暂时不知道什么原因, 因此直接使用clearSelection
      if (allCancel.length === 1 && this.crud.selections.length === 1) {
        this.$refs.table.clearSelection();
      } else {
        allCancel.forEach((item) => {
          this.$refs.table.toggleRowSelection(item, false);
        });
      }
    },
    // 批量赋码
    handleBatch () {
      if (!this.crud.selections.length) {
        this.$message.warning('请勾选需要操作的终端');
        return;
      }
      const result = this.crud.selections.some((item) => item.formal === 1);
      if (result) {
        this.$confirm(`已入网的设备重新赋码需重新注册入网，请确认是否重新赋码？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.setBatch();
        });
      } else {
        this.setBatch();
      }
    },
    setBatch() {
      const query = {
        deviceNoList: this.crud.selections.map(item => item.deviceNo)
      };
      crudTerminalAssignment.batchCode(query).then(res => {
        this.$message.success(res.msg);
        this.crud.refresh();
      }).catch(err => {

      });
    },
    // 表格禁止选中
    checkSelectable (row) {
      if (row.codeMachine === 'CE01') return true;
      else return false;
    },
    // 刷新表格
    handleRefresh () {
      this.crud.refresh();
    },
    getCompanyList() {
      allCompanyName().then(res => {
        this.headConfig['item'][3].options = res.data.map(item => {
          return {
            label: item.companyName,
            value: item.id
          };
        });
      });
    },
    getManufacturerList() {
      const query = {
        page: 0,
        size: 9999
      };
      allManufacturer(query).then(res => {
        this.headConfig['item'][8].options = res.data.content.map(item => {
          return {
            label: item.name,
            value: item.code
          };
        });
      });
    },
    // 详情
    toDetail (data) {
      if (data.formal === 1) {
        this.$confirm(`已入网的设备重新赋码需重新注册入网，请确认是否重新赋码？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dialogVisible = true;
          this.deviceNo = data.deviceNo;
          this.protocol = data.protocol;
        });
      } else {
        this.dialogVisible = true;
        this.deviceNo = data.deviceNo;
        this.protocol = data.protocol;
      }
    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData(returnValue) {
      let data = returnValue.data;
      let reversalObj = {};
      for (let k in this.dataType) {
        reversalObj[this.dataType[k]] = k;
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.typeTimeTurn(obj);
        this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index, reversalObj);
        this.typeStringTurn(obj);
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = [];
            item.forEach(v => {
              errList.push(v);
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      }
      else {
        this.addbatchPost(arr);
        returnValue.close();
      }
    },
    // 转化字符串
    typeStringTurn(obj, dataType = this.typeString) {
      dataType.forEach(v => {
        if (obj[v]) {
          obj[v] = obj[v] + '';
        }
      });
    },
    // 必填项判断
    typeRequiredTurn(obj, k, reversalObj, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, reversalObj, v.required);
        }
        else if (!obj[v] && obj[v] !== 0) {
          if (!this.tipsKey[k]) {
            this.tipsKey[k] = [reversalObj[v]];
          }
          else {
            this.tipsKey[k].push(reversalObj[v]);
          }
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    // 递归找key/id
    treeTurn (tree, word,str, iValue = 'value', iLabel = 'label', iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue,iLabel, iChildren);
        }
      });
    },
    // 时间转化
    typeTimeTurn(obj) {
      for (let k in obj) {
        this.typeTime.forEach(v => {
          if (k === v && obj[k]) {
            obj[k] = this.timeTurn(obj[k]);
          }
        });
      }
    },
    // excel表里时间转化时间戳
    timeTurn (numb) {
      const t = new Date(numb).getTime() / 1000;
      return t;
    },
    // 提交请求
    addbatchPost(arr) {
      crudTerminalAssignment.addbatch({list: arr}).then(res => {
        this.$message({
          showClose: true,
          message: '导入成功',
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('TerminalAssignment', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('TerminalAssignment', value);
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.xh-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: #fcf0c1;
}
.batch-container {
  /deep/ .el-dialog__body {
    padding: 20px;
  }
}
</style>
