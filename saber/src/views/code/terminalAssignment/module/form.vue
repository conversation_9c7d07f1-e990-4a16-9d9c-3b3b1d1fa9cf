<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    title="参数"
    append-to-body
    width="40%"
    @closed="closed"
  >
    <div
      v-if="isTerminalCode === 1"
      class="dialog-container"
    >
      <div class="dialog-header">
        <el-button
          type="primary"
          @click="getCodeData"
        >获取赋码参数</el-button>
        <el-button
          v-if="isCheckout"
          type="primary"
          @click="handleCheckoutClick"
        >检验</el-button>
      </div>
      <div
        v-loading="tableLoading"
        class="dialog-table"
      >
        <el-table
          v-if="isCheckout"
          :data="tableData"
        >
          <el-table-column
            prop="label"
            label="参数"
            show-overflow-tooltip
          />
          <el-table-column
            prop="value"
            label="值"
            show-overflow-tooltip
          />
          <el-table-column
            prop="result"
            label="校验结果"
            width="80px"
          >
            <template slot-scope="scope">
              <el-result
                v-if="scope.row.result === 1"
                icon="success"
              />
              <el-result
                v-else-if="scope.row.result === 0"
                icon="error"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            label="备注"
            show-overflow-tooltip
          />
        </el-table>
      </div>
      <div
        class="dialog-footer"
      >
        <el-button
          v-if="isGenerate"
          type="primary"
          size="small"
          @click="handleGenerateClick"
        >生成赋码</el-button>
      </div>
    </div>
    <div
      v-if="isTerminalCode === 2"
      class="dialog-container"
    >
      <div class="dialog-content">
        <div>
          <div>
            <span>赋码编号：</span>
            <span>{{ deviceNum }}</span>
          </div>
          <div>
            <span>赋码签名：</span>
            <span>{{ deviceNumSign }}</span>
          </div>
        </div>
      </div>
      <div
        class="dialog-footer"
      >
        <el-button
          type="primary"
          size="small"
          @click="handleTerminalClick"
        >设备赋码</el-button>
      </div>
    </div>
    <div
      v-if="isTerminalCode === 3"
      v-loading="terminalLoading"
      class="dialog-container"
    >
      <div class="dialog-content">
        <el-result
          v-if="result === '成功'"
          icon="success"
          title="赋码成功"
        />
        <el-result
          v-if="result === '失败'"
          icon="error"
          title="赋码失败"
        />
      </div>
      <div
        class="dialog-footer-result"
      >
        <el-button
          size="small"
          @click="isTerminalCode = 2"
        >上一步</el-button>
        <el-button
          type="primary"
          size="small"
          @click="$emit('update:dialogVisible', false)"
        >确认</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { queryTerminalParam, paramCheck, createCode, queryTerminalParamMqtt, deliver } from '@/api/code/terminalAssignment';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    dict: {
      type: Object,
      required: true
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    deviceNo: {
      type: String,
      default: ''
    },
    protocol: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      tableData: [
        {
          label: '设备类型标识',
          value: null,
          name: 'deviceType',
          result: null,
          checkName: 'deviceType',
          mqttName: 'deviceCategory',
          remark: ''
        },
        {
          label: '生产厂商编号',
          value: null,
          name: 'deviceProducer',
          result: null,
          checkName: 'manufacturer',
          mqttName: 'manufacturerId',
          remark: ''
        },
        {
          label: '产品型号',
          value: null,
          name: 'deviceModel',
          result: null,
          checkName: 'deviceModel',
          mqttName: 'productModel',
          remark: ''
        },
        {
          label: '产品序列号',
          value: null,
          name: 'deviceSerial',
          result: null,
          checkName: 'deviceSeq',
          mqttName: 'productSerialNumber',
          remark: ''
        },
        {
          label: 'IMEI号',
          value: null,
          name: 'deviceImei',
          result: null,
          checkName: 'imei',
          mqttName: 'imei',
          remark: ''
        },
        {
          label: '北斗芯片序列号',
          value: null,
          name: 'deviceChipId',
          result: null,
          checkName: 'chipSeq',
          mqttName: 'bdChipSerialNumber',
          remark: ''
        }
      ],
      isCheckout: false,
      tableLoading: false,
      terminalData: {},
      isTerminalCode: 1,
      deviceNum: null,
      deviceNumSign: null,
      result: null,
      terminalLoading: false
    };
  },
  computed: {
    isGenerate () {
      return this.tableData.every(item => item.result === 1);
    }
  },
  methods: {
    // 获取赋码参数
    getCodeData () {
      if (this.protocol === 'MQTT') {
        let query = {
          deviceNo: this.deviceNo,
          cmdFlag: 'query_cmd'
        };
        this.tableLoading = true;
        queryTerminalParamMqtt(query).then(res => {
          this.isCheckout = true;
          Object.keys(res.data).forEach(key => {
            let index = this.tableData.findIndex(item => item.mqttName === key);
            if (index >= 0) {
              this.tableData[index].value = res.data[key];
            }
          });
          this.terminalData = {
            deviceType: res.data.deviceCategory,
            deviceSeq: res.data.productSerialNumber,
            imei: res.data.imei,
            manufacturer: res.data.manufacturerId,
            deviceModel: res.data.productModel,
            chipSeq: res.data.bdChipSerialNumber
          };
          console.log('-> this.terminalData', this.terminalData);
        }).finally(() => {
          this.tableLoading = false;
        });
      } else {
        // 设备类型标识(65530)、生产厂商编号(65531)、产品型号(65532)、产品序列号(65533)、IMEI号(65534)、北斗芯片序列号(65535)
        let query = {
        // deviceNo: '8008100097',
          deviceNo: this.deviceNo,
          paramIds: [65530, 65531, 65532, 65533, 65534, 65535]
        };
        this.tableLoading = true;
        queryTerminalParam(query).then(res => {
          this.isCheckout = true;
          Object.keys(res.data.Params).forEach(key => {
            let index = this.tableData.findIndex(item => item.name === key);
            if (index || index === 0) {
              this.tableData[index].value = res.data.Params[key];
            }
          });
          this.terminalData = {
            deviceType: res.data.Params.deviceType,
            deviceSeq: res.data.Params.deviceSerial,
            imei: res.data.Params.deviceImei,
            manufacturer: res.data.Params.deviceProducer,
            deviceModel: res.data.Params.deviceModel,
            chipSeq: res.data.Params.deviceChipId
          };
        }).finally(() => {
          this.tableLoading = false;
        });
      }

    },
    // 检验设备参数
    handleCheckoutClick () {
      let query = {
        // deviceNo: '8008100097',
        deviceNo: this.deviceNo,
        ...this.terminalData
      };
      paramCheck(query).then(res => {
        if (res?.data) {
          Object.keys(res.data).forEach(key => {
            let index = this.tableData.findIndex(item => item.checkName === key);
            if (index || index === 0) {
              this.tableData[index].result = res.data[key].code;
              this.tableData[index].remark = res.data[key].msg;
            }
          });
        }
      }).catch(err => {
        console.log('检验设备参数报错', err);
      });
    },
    // 生成赋码
    handleGenerateClick () {
      let query = {
        // deviceNo: '8008100097',
        deviceNo: this.deviceNo,
        ...this.terminalData
      };
      createCode(query).then(res => {
        this.isTerminalCode = 2;
        this.deviceNum = res.data.deviceNum;
        this.deviceNumSign = res.data.deviceNumSign;
      }).catch(err => {
        console.log('生成赋码报错', err);
      });
    },
    // 设备赋码
    handleTerminalClick () {
      this.isTerminalCode = 3;
      this.terminalLoading = true;
      const query = {
        deviceNo: this.deviceNo,
        deviceNumSign: this.deviceNumSign,
        deviceNum: this.deviceNum
      };
      deliver(query).then(res => {
        this.terminalLoading = false;
        if (res.code === 200) {
          this.result = '成功';
        } else {
          this.result = '失败';
        }
      }).catch(err => {
        this.terminalLoading = false;
        this.result = '失败';
        console.log('设备赋码报错', err);
      });
    },
    // 监听关闭事件
    closed () {
      this.isCheckout = false;
      this.terminalData = {};
      this.isTerminalCode = 1;
      this.deviceNum = null;
      this.deviceNumSign = null;
      this.result = null;
      this.tableLoading = false;
      this.terminalLoading = false;
      for (let index = 0; index < this.tableData.length; index++) {
        const element = this.tableData[index];
        element.value = null;
        element.result = null;
        element.remark = '';
      }
      this.$emit('handleRefresh');
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('TerminalAssignment', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('TerminalAssignment', value);
    }
  }
};
</script>

<style lang="less" scoped>
.dialog-container {
  min-height: 372px;
  display: flex;
  flex-direction: column;
}
.dialog-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
}
.dialog-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
}
.dialog-table {
  min-height: 200px;
  ::v-deep .el-result {
    padding: 0;
    .el-result__icon {
      line-height: 0;
      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: end;
  padding-top: 20px;
}
.dialog-footer-result {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}
</style>
