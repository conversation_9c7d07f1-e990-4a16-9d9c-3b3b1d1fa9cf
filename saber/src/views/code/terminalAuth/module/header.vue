<template>
  <div
    v-if="crud.props.searchToggle"
    class="xh-custom-header"
  >
    <el-row
      :gutter="20"
      style="width: 100%;margin: 0;"
    >
      <el-col :span="4">
        <span>序列号:</span>
        <el-input
          v-model="query.deviceId"
          size="small"
          placeholder="请输入序列号"
        />
      </el-col>
      <el-col :span="4">
        <span>终端类型:</span>
        <xh-select
          v-model="query.deviceType"
          clearable
          size="small"
          placeholder="请选择终端类型"
          @change="crud.toQuery"
        >
          <el-option
            v-for="item in dict.bdmDeviceType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-col>
      <el-col :span="4">
        <span>赋码编号:</span>
        <el-input
          v-model="query.deviceNum"
          size="small"
          placeholder="请输入赋码编号"
        />
      </el-col>
      <el-col :span="4">
        <span>认证结果:</span>
        <xh-select
          v-model="query.deviceType"
          clearable
          size="small"
          placeholder="请选择认证结果"
          @change="crud.toQuery"
        >
          <el-option
            v-for="item in dict.bdmDeviceType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-col>
      <el-col :span="4">
        <span>设备类型:</span>
        <xh-select
          v-model="query.deviceType"
          clearable
          size="small"
          placeholder="请选择设备类型"
          @change="crud.toQuery"
        >
          <el-option
            v-for="item in dict.bdmDeviceType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-col>
      <el-col :span="4">
        <rrOperation
          :crud="crud"
          :clear-query="false"
          @handleClear="handleClear"
        >
<!--          <el-tooltip-->
<!--            slot="left"-->
<!--            content="更多筛选项"-->
<!--            placement="top"-->
<!--            effect="light"-->
<!--          >-->
<!--            <el-button-->
<!--              class="more-btn"-->
<!--              :icon="moreSearch ? 'el-icon-d-arrow-left': 'el-icon-d-arrow-right'"-->
<!--              size="small"-->
<!--              @click="moreSearch=!moreSearch"-->
<!--            />-->
<!--          </el-tooltip>-->
        </rrOperation>
      </el-col>
    </el-row>
<!--    <el-row-->
<!--      v-show="moreSearch"-->
<!--      :gutter="20"-->
<!--      style="width: 100%;margin: 20px 0 0 0;"-->
<!--    >-->
<!--      <el-col :span="4">-->
<!--        <span>设备类型:</span>-->
<!--        <xh-select-->
<!--          v-model="query.deviceType"-->
<!--          clearable-->
<!--          size="small"-->
<!--          placeholder="请选择设备类型"-->
<!--          @change="crud.toQuery"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="item in dict.bdmDeviceType"-->
<!--            :key="item.value"-->
<!--            :label="item.label"-->
<!--            :value="item.value"-->
<!--          />-->
<!--        </xh-select>-->
<!--      </el-col>-->
<!--    </el-row>-->
  </div>
</template>

<script>
import CRUD, { header } from '@/components/Crud/crud';
import rrOperation from '@/components/Crud/RR.operation';

export default {
  components: {
    rrOperation,
  },
  mixins: [header()],
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      moreSearch: false,
      testResArr: [
        {
          label: '合格',
          value: 1,
        },
        {
          label: '不合格',
          value: 0
        }
      ],
      testProgressArr: [
        {
          label: '进行中',
          value: 0
        },
        {
          label: '已完成',
          value: 1,
        },
      ],
      companyList: [
        {
          label: '测试企业',
          value: 1
        },
        {
          label: '海格星航',
          value: 2,
        },
      ]
    };
  },
  created() {

  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {

    },
    handleClear() {

    },
  }
};
</script>
<style scoped>
</style>
