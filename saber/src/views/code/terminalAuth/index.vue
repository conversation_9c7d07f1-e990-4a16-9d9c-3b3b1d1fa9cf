<template>
  <div
    class="app-container terminal-auth"
    style="padding: 0;"
  >
    <basic-container style="padding-bottom: 6px;">
      <div class="xh-container">
        <!--工具栏-->
        <div class="head-container">
          <eHeader
            :dict="dict"
          />
        </div>
        <div class="xh-crud-table-container">
          <crudOperation :download="false"/>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            :data="crud.data"
            :max-height="tableMaxHeight"
            style="width: 100%;height: calc(100% - 47px);"
            :cell-style="{'text-align':'center'}"
          >
            <el-table-column
              label="操作"
              width="60"
              align="center"
              fixed="right"
              :resizable="false"
            >
              <template slot-scope="scope">
                <div class="operation">
                  <el-tooltip
                    content="查看详情"
                    placement="top"
                    :enterable="false"
                  >
                    <el-button
                      size="small"
                      type="primary"
                      icon="el-icon-view"
                      @click="showDetail(scope.row.id)"
                    />
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="序列号"
              :show-overflow-tooltip="true"
              :resizable="false"
            />
            <el-table-column
              prop="deptName"
              label="终端类型"
              :resizable="false"
            />
            <el-table-column
              prop="deptName"
              label="赋码编号"
              :resizable="false"
            />
            <el-table-column
              prop="deptName"
              label="认证结果"
              :resizable="false"
            />
            <el-table-column
              prop="deptName"
              label="设备类型"
              :resizable="false"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
        </div>
        <!--分页组件-->
        <pagination/>
      </div>
    </basic-container>
  </div>
</template>

<script>
import terminalAuth from '@/api/code/terminalAuth.js';
import eHeader from './module/header.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';

// crud交由presenter持有
const crud = CRUD({
  title: '终端鉴权',
  optShow: {
    add: false,
    edit: false,
    download: false
  },
  crudMethod: { ...terminalAuth }
});

export default {
  name: 'TerminalAuth',
  components: {
    eHeader,
    crudOperation,
    pagination,
  },
  mixins: [presenter(crud)],
  dicts: [
    'bdmDeviceType'
  ],
  data() {
    return {
    };
  },
  methods: {

  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.terminal-auth {
  border: 1px solid #e5e7e9;
  box-shadow: 1px 1px 5px #88888850;
}
</style>
