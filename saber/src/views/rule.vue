<template>
  <div class="rule">
    <basic-container class="rule-tree">
      <el-input
        v-model="filterText"
        placeholder="输入关键字进行过滤"
        size="small"
        class="filter-input"
      />
      <el-tree
        ref="tree"
        class="filter-tree"
        :data="treeData"
        node-key="id"
        :props="defaultProps"
        :default-expand-all="true"
        :current-node-key="2"
        :filter-node-method="filterNode"
        @node-click="handleClick"
      />
    </basic-container>
    <basic-container class="rule-content">
      <FatigueRule
        v-if="nodeData.name === '疲劳规则'"
        ref="fatigueRule"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <OverspeedRule
        v-if="nodeData.name === '超速规则'"
        ref="overspeedRule"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <ProhibitRule
        v-if="nodeData.name === '禁行规则'"
        ref="prohibitRule"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <NightRestrictRule
        v-if="nodeData.name === '夜间限速规则'"
        ref="nightRestrictRule"
        :nodeData="nodeData"
        :dict="dict"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <SubsectionRestrictRule
        v-if="nodeData.name === '分段限速规则'"
        ref="subsectionRestrictRule"
        :nodeData="nodeData"
        :dict="dict"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <AnomalousRule
        v-if="nodeData.name === '报停异动规则'"
        ref="anomalousRule"
        :nodeData="nodeData"
        :dict="dict"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <NightSteerRule
        v-if="nodeData.name === '夜间异动规则'"
        ref="nightSteerRule"
        :nodeData="nodeData"
        :dict="dict"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <ElectronicFenceRule
        v-if="nodeData.name === '电子围栏规则'"
        ref="electronicFenceRule"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <PathExcursion
        v-if="nodeData.name === '路线偏航规则'"
        ref="pathExcursion"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <ApproachRegion
        v-if="nodeData.name === '接近区域规则'"
        ref="approachRegion"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <RoadwayRestrict
        v-if="nodeData.name === '道路限速规则'"
        ref="roadwayRestrict"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <OfflineNotification
        v-if="nodeData.name === '离线通知规则'"
        ref="offlineNotification"
        :nodeData="nodeData"
        :dict="dict"
        :interRegionList="interRegionList"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <DriverIdentity
        v-if="nodeData.name === '驾驶员身份识别'"
        ref="driverIdentity"
        :nodeData="nodeData"
        :dict="dict"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
      <OfflineDisplacement
        v-if="nodeData.name === '离线位移规则'"
        ref="offlineDisplacement"
        :nodeData="nodeData"
        :dict="dict"
        @setRuleAllot="setRuleAllot"
        @setRuleLogs="setRuleLogs"
        @setRuleVehicle="setRuleVehicle"
        @getRuleTips="getRuleTips"
      />
    </basic-container>
    <RuleAllot
      :dialogVisible.sync="dialogAllotVisible"
      :node-data="nodeData"
      @refresh="refresh"
    />
    <RuleLogs
      ref="ruleLogs"
      :dialogVisible.sync="dialogLogsVisible"
      :ruleQuery="ruleQuery"
      :interRegionList="interRegionList"
      @compareDetails="compareDetails"
      @closeHandle="closeHandle"
    />
    <RuleVehicle
      ref="ruleVehicle"
      :dialogVisible.sync="dialogVehicleVisible"
      :dict="dict"
    />
    <el-dialog title="规则说明"
               v-dialog-drag
               append-to-body
               :visible.sync="isShowRuleTips"
               width="800px">
      <div class="rule-tips-content">
        <el-row>
          <el-col :span="24" style="display: flex;align-items: center;">
            <el-select
              v-model="activeRuleTypeId"
              placeholder="请选择规则"
              @change="changeRule"
              size="small"
            >
              <el-option
                v-for="item in dict.alarmRule"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-tooltip
              placement="right"
            >
              <div slot="content">
                <div class="tips-explain"></div>
                <p>说明须知：</p>
                <p>1、平台上每个规则都可以自定义名称便于区分，新建好规则之后，需要将规则分配给 需要的车辆，规则才可以生效。</p>
                <p>2、规则中产生的照片，都可以在‘数据中心-照片查看’这个菜单功能中查找到。</p>
                <p>3、同一类型规则不可出现相同规则名称。</p>
                <p>4、若规则有开始时间和结束时间，需填写且两者不可相等。</p>
                <p>5、有拍照的规则，每分钟最多拍两张（摄像头个数除以拍照间隔不大于2），拍照间隔不为零。</p>
                <p>6、语音提示填写内容时，需填写语音提示间隔和语音提示次数。</p>
              </div>
              <i class="el-icon-question rule-tips-icon"></i>
            </el-tooltip>
          </el-col>
          <el-col :span="24">
            <el-row type="flex" v-loading="ruleDocLoading">
              <el-col>
                <p class="tips-content" v-html="ruleDocContent.richText"></p>
              </el-col>
              <!--后端未配置图片 暂时设置不显示-->
              <el-col :span="12" v-if="false">
                <img class="tips-img" :src="ruleDocContent.img" alt="">
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="isShowRuleTips = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { get, ruledoc } from '@/api/rule'
import RuleAllot from './rules/ruleAllot.vue'
import FatigueRule from './rules/fatigue/fatigueRule/index.vue'
import OverspeedRule from './rules/overspeedLimit/overspeedRule/index.vue'
import ProhibitRule from './rules/overspeedLimit/prohibitRule/index.vue'
import NightRestrictRule from './rules/overspeedLimit/nightRestrictRule/index.vue'
import SubsectionRestrictRule from './rules/overspeedLimit/subsectionRestrictRule/index.vue'
import NightSteerRule from './rules/overspeedLimit/nightSteerRule/index.vue'
import ElectronicFenceRule from './rules/regionalRoute/electronicFenceRule/index.vue'
import PathExcursion from './rules/regionalRoute/pathExcursion/index.vue';
import ApproachRegion from './rules/regionalRoute/approachRegion/index.vue';
import RoadwayRestrict from './rules/regionalRoute/roadwayRestrict/index.vue'
import OfflineDisplacement from './rules/rests/offlineDisplacement/index.vue'
import DriverIdentity from './rules/identityRecognition/driverIdentity/index.vue'
import AnomalousRule from './rules/anomalous/anomalousRule/index.vue'
import OfflineNotification from './rules/notification/offlineNotification/index.vue'
import RuleLogs from './rules/ruleLogs.vue'
import RuleVehicle from './rules/ruleVehicle.vue'
import { regionGroups } from '@/api/base/region';

const visibleList = [
  {
    label: '疲劳规则',
    value: 'fatigueRule'
  },
  {
    label: '超速规则',
    value: 'overspeedRule'
  },
  {
    label: '禁行规则',
    value: 'prohibitRule'
  },
  {
    label: '夜间限速规则',
    value: 'nightRestrictRule'
  },
  {
    label: '分段限速规则',
    value: 'subsectionRestrictRule'
  },
  {
    label: '夜间异动规则',
    value: 'nightSteerRule'
  },
  {
    label: '报停异动规则',
    value: 'anomalousRule'
  },
  {
    label: '电子围栏规则',
    value: 'electronicFenceRule'
  },
  {
    label: '路线偏航规则',
    value: 'pathExcursion'
  },
  {
    label: '接近区域规则',
    value: 'approachRegion'
  },
  {
    label: '道路限速规则',
    value: 'roadwayRestrict'
  },
  {
    label: '离线通知规则',
    value: 'offlineNotification'
  },
  {
    label: '驾驶员身份识别',
    value: 'driverIdentity'
  },
  {
    label: '离线位移规则',
    value: 'offlineDisplacement'
  }
]
export default {
  components: {
    FatigueRule,
    RuleAllot,
    RuleLogs,
    RuleVehicle,
    OverspeedRule,
    ProhibitRule,
    NightRestrictRule,
    SubsectionRestrictRule,
    NightSteerRule,
    ElectronicFenceRule,
    PathExcursion,
    ApproachRegion,
    OfflineDisplacement,
    DriverIdentity,
    OfflineNotification,
    RoadwayRestrict,
    AnomalousRule
  },
  dicts: [
    'ruleManage',
    'alarmLevel',
    'alarmRule',
    'targetType'
  ],
  data() {
    return {
      filterText: '',
      val: true,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      nodeData: {},
      dialogAllotVisible: false,
      dialogLogsVisible: false,
      dialogVehicleVisible: false,
      ruleQuery: {
        ruleTypeId: undefined,
        ruleId: undefined
      },
      interRegionList: [],
      isShowRuleTips: false,
      activeRuleTypeId: '',
      ruleDocContent: {},
      ruleDocLoading: false,
      pageData: {} // 从其他页面跳转过来携带的参数
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    'dict.alarmRule'(val) {
      if (val) {
        this.ruleTypeId
      }
    }
  },
  mounted() {
    this.getRuleData(this.toRuleDetails);
    this.getTreeData();
  },
  activated() {
    if (localStorage.getItem('RULE_DATA')) {
      let data = localStorage.getItem('RULE_DATA');
      this.pageData = JSON.parse(data);
      this.toRuleDetails();
      localStorage.removeItem('RULE_DATA');
    }
  },
  methods: {
    // 其他页跳转打开对应的规则详情
    toRuleDetails() {
      if (this.pageData.ruleTypeId) {
        for (let index = 0; index < this.treeData.length; index++) {
          const element = this.treeData[index];
          const data = element.children && element.children.find((item) => item.id === this.pageData.ruleTypeId);
          if (data) {
            this.nodeData = data; // 查找到赋值打开相对应的表格
            this.$refs.tree.setCurrentKey(data.id); // 改变当前选中节点
            this.$nextTick(() => {
              let obj = visibleList.find((item)=>item.label === this.nodeData.name); // 获取对应的表格ref
              this.$refs[obj.value].toDetails(this.pageData.ruleId);
            });
            break;
          }
        }
      }
    },
    closeHandle(type) {
      let obj = visibleList.find((item) => item.label === type)
      this.$refs[obj.value].closeHandle()
    },
    compareDetails(data) {
      let obj = visibleList.find((item) => item.label === data.ruleTypeName)
      this.$refs[obj.value].compareDetails(data)
    },
    getTreeData() {
      regionGroups().then((res) => {
        this.interRegionList = res.data
        this.processingList(this.interRegionList)
      })
    },
    processingList(list) {
      list.forEach(element => {
        if (element.type === 1) {
          element.id = 'm-' + element.id
        }
        if (element.children && element.children.length > 0) {
          this.processingList(element.children)
        }
      })
    },
    refresh() {
      let name = this.nodeData.name
      this.$delete(this.nodeData, 'name')
      this.$nextTick(() => {
        this.$set(this.nodeData, 'name', name) // TODO
      })
    },
    setRuleVehicle(data) {
      let query = {
        ruleType: this.nodeData.id,
        ruleId: data.id
      }
      this.$refs.ruleVehicle.getData(query)
      this.dialogVehicleVisible = true
    },
    // 打开规则日志弹窗
    setRuleLogs(data) {
      if (data.id) {
        this.ruleQuery.ruleTypeId = this.nodeData.id
        this.ruleQuery.ruleId = data.id
        this.$nextTick(() => {
          this.$refs.ruleLogs.getData()
        })
      }
      else {
        this.ruleQuery = {}
      }
      this.dialogLogsVisible = true
    },
    // 打开规则分配弹窗
    setRuleAllot() {
      this.dialogAllotVisible = true
    },
    // 树节点点击事件
    handleClick(data) {
      if (!data.children) {
        this.nodeData = data
      }
    },
    // 获取规则树
    getRuleData(callback) {
      get({ parentId: 0 }).then(res => {
        this.treeData = res.data.content
        this.nodeData = this.treeData[0].children[0]
        this.$nextTick(() => {
          callback && callback()
        })
      })
    },
    // tree查询
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    changeRule(ruleTypeId) {
      this.getRuleTipsContent(ruleTypeId)
    },
    getRuleTips(ruleTypeId) {
      this.activeRuleTypeId = ruleTypeId
      this.getRuleTipsContent(ruleTypeId)
      this.isShowRuleTips = true
    },
    getRuleTipsContent(ruleTypeId) {
      this.ruleDocLoading = true
      ruledoc(ruleTypeId).then(res => {
        this.ruleDocContent = res.data
        // this.ruleDocContent.img = 'https://pic.616pic.com/photoone/00/03/74/618ce3d196a7d6996.jpg'
        this.ruleDocLoading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
.basic-container {
  padding: 0;
}

.rule {
  display: flex;
  justify-content: space-between;
  height: 100%;
  // padding: 0 4px 4px 4px;
}

.rule-tree {
  width: 240px;
  height: 100%;
  background-color: #ffffff;
}

.rule-content {
  width: calc(100% - 244px);
}

.filter-input {
  margin: 5px 0;
}

.filter-tree {
  height: calc(100% - 40px);
}

.rule-tips-icon {
  font-size: 24px;
  margin-left: 8px;
  color: #409eff;
}
.tips-content {
  padding: 0 8px 10px 4px;
  box-sizing: border-box;
  height: 580px;
  overflow: auto;
}
.tips-img {
  width: 100%;
  object-fit: contain;
}
.tips-explain {
    width: 500px;
}
</style>
