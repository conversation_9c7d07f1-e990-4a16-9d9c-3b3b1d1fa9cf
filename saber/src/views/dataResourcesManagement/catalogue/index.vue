<template>
  <basic-container>
    <div class="main">
      <div
        v-dompurify-html="mdContent"
        class="markdown-body"
        data-theme="light"
      />
    </div>
  </basic-container>
</template>
<script>
import { marked } from 'marked';

export default {
  name: 'DataResourcesCatalogue',
  data() {
    return {
      mdContent: null
    };
  },
  created() {
    fetch('/bdsplatform/dataResource/catalogue.md').then(response => response.text())
      .then(markdown => {
        console.log(markdown);
        this.mdContent = marked(markdown);
      });
  }
};
</script>

<style scoped lang="less">
.main {
  height: 100%;
  // padding: 16px;
  overflow-y: auto;
}
</style>
