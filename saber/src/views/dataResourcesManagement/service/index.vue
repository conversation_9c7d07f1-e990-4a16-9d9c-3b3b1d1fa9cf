<template>
  <basic-container>
    <el-tabs
      v-model="activePage"
      type="border-card"
      class="tab-container"
      tab-position="left"
      @tab-click="handleTabClick"
    >
      <el-tab-pane
        v-for="(item, index) in serviceList"
        :key="index"
        :name="item.name"
        :label="item.label"
      />
      <div class="tab-container-content">
        <div class="tab-container-content-title">
          <h1>
            {{ form.label }}
          </h1>
        </div>
        <div class="tab-container-content-search">
          <el-input
            v-model="form.url"
            readonly
            placeholder="请输入请求地址"
            @input="handleUrlInput"
          >
            <el-select
              slot="prepend"
              v-model="form.method"
              placeholder="请选择"
              disabled
              class="method-select"
            >
              <el-option
                v-for="item in methodList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-input>
          <el-button
            type="primary"
            class="search-btn"
            icon="el-icon-search"
            size="small"
            @click="handleQueryClick"
          >发送</el-button>
        </div>
        <div class="tab-container-content-param">
          <el-tabs
            v-model="activeParam"
            type="card"
          >
            <el-tab-pane
              label="Header"
              name="1"
            >
              <template slot="label">
                <span>Header<el-badge
                  v-if="headerConfigNum"
                  :value="headerConfigNum"
                  type="primary"
                /></span>
              </template>
              <div class="main">
                <div class="item_label main_size">
                  参数名
                </div>
                <div class="item_label">
                  参数值
                </div>
                <div class="item_label">
                  是否必填
                </div>
                <div class="item_label">
                  类型
                </div>
                <div class="item_label">
                  描述
                </div>
                <!-- <div class="item_label">
                  <span
                    class="add_item"
                    @click="addHandle('headerConfig')"
                  >添加</span>
                </div> -->
              </div>
              <div
                v-for="(item,index) in form.headerConfig"
                :key="index"
                class="main"
              >
                <div class="main_item main_size">
                  <!-- <el-input
                    v-model="form.headerConfig[index].label"
                    readonly
                    placeholder="请输入参数名"
                  /> -->
                  {{ form.headerConfig[index].label }}
                </div>
                <div class="main_item">
                  <!-- <el-input
                    v-model="form.headerConfig[index].value"
                    readonly
                    placeholder="请输入参数值"
                  /> -->
                  {{ form.headerConfig[index].value }}
                </div>
                <div class="main_item">
                  {{ form.headerConfig[index].required }}
                </div>
                <div class="main_item">
                  {{ form.headerConfig[index].type }}
                </div>
                <div class="main_item">
                  {{ form.headerConfig[index].desc }}
                </div>
                <!-- <div class="main_item">
                  <div
                    class="reduce_item"
                  >
                    <i
                      class="el-icon-remove-outline reduce_icon"
                      @click="reduceHandle('headerConfig', index)"
                    />
                  </div>
                </div> -->
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="Query"
              name="2"
            >
              <template slot="label">
                <span>Query<el-badge
                  v-if="paramConfigNum"
                  :value="paramConfigNum"
                  type="primary"
                /></span>
              </template>
              <div class="main">
                <div class="item_label main_size">
                  参数名
                </div>
                <div class="item_label">
                  参数值
                </div>
                <div class="item_label">
                  是否必填
                </div>
                <div class="item_label">
                  类型
                </div>
                <div class="item_label">
                  描述
                </div>
                <!-- <div class="item_label">
                  <span
                    class="add_item"
                    @click="addHandle('paramConfig')"
                  >添加</span>
                </div> -->
              </div>
              <div
                v-for="(item,index) in form.paramConfig"
                :key="index"
                class="main"
              >
                <div class="main_item main_size">
                  <!-- <el-input
                    v-model="form.paramConfig[index].label"
                    placeholder="请输入参数名"
                    readonly
                    @input="handleParamInput"
                  /> -->
                  {{ form.paramConfig[index].label }}
                </div>
                <div class="main_item">
                  <!-- <el-input
                    v-model="form.paramConfig[index].value"
                    placeholder="请输入参数值"
                    @input="handleParamInput"
                  /> -->
                  <el-input
                    v-model="form.paramConfig[index].value"
                    placeholder="请输入参数值"
                  />
                </div>
                <div class="main_item">
                  {{ form.paramConfig[index].required }}
                </div>
                <div class="main_item">
                  {{ form.paramConfig[index].type }}
                </div>
                <div class="main_item">
                  {{ form.paramConfig[index].desc }}
                </div>
                <!-- <div class="main_item">
                  <div
                    class="reduce_item"
                  >
                    <i
                      class="el-icon-remove-outline reduce_icon"
                      @click="reduceHandle('paramConfig', index)"
                    />
                  </div>
                </div> -->
              </div>
            </el-tab-pane>
            <!-- <el-tab-pane
              label="Body"
              name="3"
            >
              <el-input
                v-model="form.params"
                type="textarea"
                :rows="8"
                placeholder="请输入内容"
              /></el-tab-pane> -->
          </el-tabs>
        </div>
        <div class="tab-container-content-result">
          <el-tabs
            v-model="activeResult"
            type="card"
          >
            <el-tab-pane
              label="实时响应"
              name="1"
            >
              <div class="result-content">
                <pre>{{ contentResult }}</pre>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="请求头"
              name="2"
            >
              <div class="result-content">
                <pre>{{ headerResult }}</pre>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="响应头"
              name="3"
            >
              <div class="result-content">
                <pre>{{ requestResult }}</pre>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="返回字段描述"
              name="4"
            >
              <div class="main">
                <div class="item_label main_size">
                  参数名
                </div>
                <!-- <div class="item_label">
                  参数值
                </div> -->
                <div class="item_label">
                  类型
                </div>
                <div class="item_label">
                  描述
                </div>
              </div>
              <div
                v-for="(item, index) in form.resultConfig"
                :key="index"
                class="main"
              >
                <div class="main_item">
                  {{ form.resultConfig[index].label }}
                </div>
                <!-- <div class="main_item">
                  <el-tooltip
                    :content="form.resultConfig[index].value"
                    placement="top"
                  >
                    <span>{{ form.resultConfig[index].value }}</span>
                  </el-tooltip>
                </div> -->
                <div class="main_item">
                  {{ form.resultConfig[index].type }}
                </div>
                <div class="main_item">
                  {{ form.resultConfig[index].desc }}
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-tabs>
  </basic-container>
</template>

<script>
import axios from 'axios';
import moment from 'moment';
import { defaultDeviceNum } from '@/api/statistics/location.js';
const JSONbig = require('json-bigint');
export default {
  name: "DataResourcesService",
  components: {},
  data() {
    return {
      activePage: '0',
      serviceList: [
        {
          label: '定位查询服务',
          name: '0',
          url: '/vdm-statistic/static/locations/list',
          method: 'post',
          params: '',
          paramConfig: [
            { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: true },
            { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: true },
            { label: 'deviceNum', value: '', type: 'String', required: '选填', desc: '赋码编号', isUrl: false },
            { label: 'startTime', value: moment().startOf('day').unix() - 6 * 86400, type: 'Number', required: '选填', desc: '开始时间', isUrl: false },
            { label: 'endTime', value: moment().endOf('day').unix(), type: 'Number', required: '选填', desc: '结束时间', isUrl: false },
            { label: 'targetName', value: '', type: 'String', required: '选填', desc: '监控对象名称', isUrl: false }
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.countId', value: '', type: 'String', desc: '-' },
            { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
            { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
            { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
            { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
            { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
            { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
            { label: 'data.records.deviceNum', value: 'NVF4524090700000', type: 'String', desc: '赋码编号' },
            { label: 'data.records.deviceType', value: 1, type: 'Number', desc: '终端类别' },
            { label: 'data.records.targetName', value: '粤B65432', type: 'String', desc: '监控对象名称' },
            { label: 'data.records.longitude', value: 113.447001, type: 'Number', desc: '经度' },
            { label: 'data.records.latitude', value: 23.156438, type: 'Number', desc: '纬度' },
            { label: 'data.records.address', value: '广东省广州市黄埔区南翔二路68-31号正东方向60米唐克士', type: 'String', desc: '地址' },
            { label: 'data.records.altitude', value: 0, type: 'Number', desc: '高程(米)' },
            { label: 'data.records.speed', value: 0, type: 'Number', desc: '速度(km)' },
            { label: 'data.records.bearing', value: 247, type: 'Number', desc: '方向(度)' },
            { label: 'data.records.time', value: 1725693809, type: 'Number', desc: '定位时间' },
            { label: 'data.records.recvTime', value: 1725693810, type: 'Number', desc: '接收时间' },
            { label: 'data.records.batch', value: 0, type: 'Number', desc: '上传类型' },
            { label: 'data.records.valid', value: 1, type: 'Number', desc: '定位有效性' },
            { label: 'data.records.alarm', value: '', type: 'Number', desc: '终端告警' },
            { label: 'data.records.auxiliary', value: '', type: 'String', desc: '定位附加信息' },
            { label: 'data.records.correction', value: 0, type: 'Number', desc: '是否纠正点, 0-未纠正，1-纠正点' },
            { label: 'data.records.deviceId', value: '', type: 'String', desc: '设备id' },
            { label: 'data.records.id', value: '', type: 'String', desc: '主键' },
            { label: 'data.records.mileage', value: '', type: 'Number', desc: '行驶里程：单位：km' },
            { label: 'data.records.posSys', value: '', type: 'Number', desc: '定位系统' },
            { label: 'data.records.status', value: '', type: 'Number', desc: '终端状态' },
            { label: 'data.records.targetId', value: '15532826604666881', type: 'String', desc: '监控对象id' },
            { label: 'data.records.targetType', value: '', type: 'Number', desc: '监控对象类型' },
            { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
            { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
            { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
            { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
        {
          label: '轨迹查询服务',
          name: '1',
          url: '/monitorcars-wrapper/monitorcars/vehicle/querytracking',
          method: 'post',
          params: '',
          paramConfig: [
            { label: 'start_time', value: moment().startOf('day').unix(), type: 'Number', required: '必填', desc: '开始时间', isUrl: false },
            { label: 'end_time', value: moment().endOf('day').unix(), type: 'Number', required: '必填', desc: '结束时间', isUrl: false },
            { label: 'device_id', value: undefined, type: 'BigInt', required: '必填', desc: '终端设备ID', isUrl: false },
            { label: 'device_type', value: undefined, type: 'Number', required: '必填', desc: '终端类别', isUrl: false },
            { label: 'target_id', value: undefined, type: 'BigInt', required: '必填', desc: '目标ID', isUrl: false },
            { label: 'target_type', value: undefined, type: 'Number', required: '必填', desc: '目标类型', isUrl: false },
            { label: 'invalid', value: undefined, type: 'Number', required: '选填', desc: '无效(值为1则查询无效点)', isUrl: false },
            { label: 'batch', value: undefined, type: 'Number', required: '选填', desc: '补报(值为1则为查询补报点)', isUrl: false },
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.device_type', value: 1, type: 'Number', desc: '终端类别' },
            { label: 'data.device_category', value: 104, type: 'Number', desc: '终端类型' },
            { label: 'data.device_unique_id', value: '8000075009105001', type: 'String', desc: '序列号' },
            { label: 'data.target_type', value: 1, type: 'Number', desc: '目标类型' },
            { label: 'data.target_name', value: '粤A35N03', type: 'String', desc: '监控对象' },
            { label: 'data.dept_id', value: '1123598813738675201', type: 'String', desc: '组织机构ID' },
            { label: 'data.dept_name', value: '海格通信', type: 'String', desc: '组织机构' },
            { label: 'data.device_id', value: '19409496296128513', type: 'String', desc: '设备id' },
            { label: 'data.device_model', value: 'ZHT2105', type: 'String', desc: '设备型号' },
            { label: 'data.device_no', value: '', type: 'String', desc: '互联网卡号' },
            { label: 'data.device_num', value: 'NCE0124091300000', type: 'String', desc: '设备编号' },
            { label: 'data.specificity', value: 2, type: 'Number', desc: '设备' },
            { label: 'data.target_id', value: 132, type: 'Number', desc: '目标类型ID' },
            { label: 'data.locations', value: '[]', type: 'Array', desc: '数据列表' },
            { label: 'data.locations.time', value: 1725504976, type: 'Number', desc: '定位时间' },
            { label: 'data.locations.speed', value: '5.2', type: 'String', desc: '速度(km/h)' },
            { label: 'data.locations.mileage', value: '487.80002', type: 'String', desc: '总里程(km)' },
            { label: 'data.locations.longitude', value: '113.447616', type: 'String', desc: '经度' },
            { label: 'data.locations.latitude', value: '23.157508', type: 'String', desc: '纬度' },
            { label: 'data.locations.bearing', value: 70, type: 'Number', desc: '方向' },
            { label: 'data.locations.loc_addr', value: '广东省广州市黄埔区南翔二路23号广东南方海岸科技服务有限公司北16米', type: 'String', desc: '地址' },
            { label: 'data.locations.gnss_num', value: 0, type: 'Number', desc: '定位卫星' },
            { label: 'data.locations.wireless', value: 24, type: 'Number', desc: '通信信号' },
            { label: 'data.locations.io_state', value: 0, type: 'Number', desc: '休眠状态' },
            { label: 'data.locations.batch', value: 0, type: 'Number', desc: '上传类型' },
            { label: 'data.locations.alarm', value: 32768, type: 'Number', desc: '终端告警' },
            { label: 'data.locations.altitude', value: 41, type: 'Number', desc: '地理高程，单位：米' },
            { label: 'data.locations.auxiliary', value: '', type: 'String', desc: '定位附加信息' },
            { label: 'data.locations.correction', value: 0, type: 'Number', desc: '是否纠正点, 0-未纠正，1-纠正点' },
            { label: 'data.locations.id', value: 0, type: 'Number', desc: '主键' },
            { label: 'data.locations.pos_sys', value: 0, type: 'Number', desc: '定位系统' },
            { label: 'data.locations.recv_time', value: 1726281639, type: 'Number', desc: '平台接收定位的时间，单位：秒' },
            { label: 'data.locations.status', value: 524291, type: 'Number', desc: '终端状态' },
            { label: 'data.locations.status_dsc', value: 'acc开', type: 'String', desc: '终端状态名称' },
            { label: 'data.locations.valid', value: 1, type: 'Number', desc: '定位有效性，0-无效，1-有效' },
            { label: 'msg', value: 'ok', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
        {
          label: '告警查询服务',
          name: '2',
          url: '/vdm-alarm/alarm/page',
          method: 'post',
          params: '',
          paramConfig: [
            { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: true },
            { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: true },
            { label: 'start_time', value: moment().startOf('day').unix() - 6 * 86400, type: 'Number', required: '选填', desc: '开始时间', isUrl: false },
            { label: 'end_time', value: moment().endOf('day').unix(), type: 'Number', required: '选填', desc: '结束时间', isUrl: false },
            { label: 'device_id', value: undefined, type: 'BigInt', required: '选填', desc: '终端设备ID', isUrl: false },
            { label: 'device_type', value: undefined, type: 'Number', required: '选填', desc: '终端类别', isUrl: false },
            { label: 'alarm_type_list', value: undefined, type: 'Array', required: '选填', desc: '告警类型', isUrl: false },
            { label: 'alarm_level_list', value: undefined, type: 'Array', required: '选填', desc: '告警等级', isUrl: false },
            { label: 'dept_id', value: '', type: 'String', required: '选填', desc: '所属机构', isUrl: false },
            { label: 'alarm_source', value: '', type: 'String', required: '选填', desc: '告警来源', isUrl: false },
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.countId', value: '', type: 'String', desc: '-' },
            { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
            { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
            { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
            { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
            { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
            { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
            { label: 'data.records.target_name', value: '测试手表', type: 'String', desc: '监控对象' },
            { label: 'data.records.device_type_name', value: '北斗穿戴式终端', type: 'String', desc: '终端类别' },
            { label: 'data.records.device_cate', value: '北斗智能手表/环', type: 'String', desc: '终端类型' },
            { label: 'data.records.device_num', value: 'MCE0124082900024', type: 'String', desc: '赋码编号' },
            { label: 'data.records.unique_id', value: '04B01022D0137017', type: 'String', desc: '序列号' },
            { label: 'data.records.alarm_type', value: '区域告警（手表）', type: 'String', desc: '告警类型' },
            { label: 'data.records.alarm_level', value: '0级告警"', type: 'String', desc: '告警等级' },
            { label: 'data.records.alarm_source', value: '终端', type: 'String', desc: '告警来源' },
            { label: 'data.records.start_time', value: '2024-09-04 14:11:37', type: 'String', desc: '开始时间' },
            { label: 'data.records.end_time', value: '2024-09-04 17:05:42', type: 'String', desc: '结束时间' },
            { label: 'data.records.start_addr', value: '广东省广州市黄埔区南翔一路88正南方向60米达安基因南198米', type: 'String', desc: '开始地址' },
            { label: 'data.records.end_addr', value: '广东省广州市黄埔区南翔一路88正南方向60米达安基因南198米', type: 'String', desc: '结束地址' },
            { label: 'data.records.rule_name', value: '', type: 'String', desc: '告警规则' },
            { label: 'data.records.handle_measures', value: '紧急处理', type: 'String', desc: '处理措施' },
            { label: 'data.records.handle_content', value: '测试处理', type: 'String', desc: '处理内容' },
            { label: 'data.records.handle_time', value: '2024-08-28 16:12:38', type: 'String', desc: '处理时间' },
            { label: 'data.records.handler', value: '超级管理员', type: 'String', desc: '处理人员' },
            { label: 'data.records.dept_name', value: '测试单位', type: 'String', desc: '所属机构' },
            { label: 'data.records.alarm_complete', value: 1, type: 'Number', desc: '告警结束状态' },
            { label: 'data.records.device_id', value: '19409496296128513', type: 'String', desc: '终端设备id' },
            { label: 'data.records.device_type', value: 1, type: 'Number', desc: '终端类别' },
            { label: 'data.records.handle_state', value: '待处理', type: 'String', desc: '处理状态' },
            { label: 'data.records.id', value: '1834776651762896896', type: 'String', desc: '主键' },
            { label: 'data.records.iot_protocol', value: 1, type: 'Number', desc: '物联网协议' },
            { label: 'data.records.numAttach', value: '0/0', type: 'String', desc: '仅导出使用' },
            { label: 'data.records.num_attach_expect', value: 0, type: 'Number', desc: '预期附件数' },
            { label: 'data.records.num_attach_real', value: 0, type: 'Number', desc: '实际附件数' },
            { label: 'data.records.target_id', value: '17751117150617600', type: 'String', desc: '监控对象id' },
            { label: 'data.records.target_type', value: 2, type: 'Number', desc: '监控对象类型' },
            { label: 'data.records.target_type_name', value: '人员', type: 'String', desc: '监控对象类型名称' },
            { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
            { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
            { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
            { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
        {
          label: '设备状态查询服务',
          name: '3',
          url: '/vdm-statistic/statistic/deviceStatus/device/status',
          method: 'get',
          params: '',
          paramConfig: [
            { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: true },
            { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: true },
            { label: 'startTime', value: moment().startOf('day').unix() - 6 * 86400, type: 'Number', required: '选填', desc: '开始时间', isUrl: true },
            { label: 'endTime', value: moment().endOf('day').unix(), type: 'Number', required: '选填', desc: '结束时间', isUrl: true },
            { label: 'deviceNum', value: '', type: 'String', required: '选填', desc: '赋码编号', isUrl: true },
            { label: 'targetName', value: '', type: 'String', required: '选填', desc: '监控对象名称', isUrl: true },
            { label: 'runningStatus', value: '', type: 'String', required: '选填', desc: '运行状态', isUrl: true },
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.countId', value: '', type: 'String', desc: '-' },
            { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
            { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
            { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
            { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
            { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
            { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
            { label: 'data.records.deviceType', value: 1, type: 'Number', desc: '终端类别' },
            { label: 'data.records.deviceNum', value: 'NCE0124083000005', type: 'String', desc: '赋码编号' },
            { label: 'data.records.uniqueId', value: '8000075009105001', type: 'String', desc: '序列号' },
            { label: 'data.records.targetName', value: '粤A35N03', type: 'String', desc: '监控对象名称' },
            { label: 'data.records.action', value: 0, type: 'Number', desc: '在线状态' },
            { label: 'data.records.actionTime', value: '2024-09-05 10:01:12', type: 'String', desc: '最近一次上线/下线时间' },
            { label: 'data.records.faultCount', value: 0, type: 'Number', desc: '终端告警数量' },
            { label: 'data.records.deviceId', value: '16092200997814273', type: 'String', desc: '终端设备id' },
            { label: 'data.records.id', value: 103, type: 'Number', desc: '主键' },
            { label: 'data.records.targetId', value: '16092250389938177', type: 'String', desc: '监控对象id' },
            { label: 'data.records.targetType', value: 1, type: 'Number', desc: '监控对象类型' },
            { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
            { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
            { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
            { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
        {
          label: '车辆信息查询服务',
          name: '4',
          url: '/vdm-base-info/baseinfo/vehicle/list',
          method: 'post',
          params: '',
          paramConfig: [
            { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: false },
            { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: false },
            { label: 'number', value: '', type: 'String', required: '选填', desc: '车辆编号', isUrl: false },
            { label: 'category', value: '', type: 'String', required: '选填', desc: '车辆类型', isUrl: false },
            { label: 'deptId', value: '', type: 'String', required: '选填', desc: '所属机构', isUrl: false },
            { label: 'uniqueId', value: '', type: 'String', required: '选填', desc: '绑定序列号', isUrl: false },
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.countId', value: '', type: 'String', desc: '-' },
            { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
            { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
            { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
            { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
            { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
            { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
            { label: 'data.records.number', value: '京RB7294-蓝色', type: 'String', desc: '车辆编号' },
            { label: 'data.records.deptName', value: '海格通信', type: 'String', desc: '所属机构' },
            { label: 'data.records.categoryName', value: '公务车', type: 'String', desc: '车辆类型' },
            { label: 'data.records.terminalCategories', value: '室内外多源融合通导一体终端', type: 'String', desc: '绑定终端类型' },
            { label: 'data.records.uniqueId', value: '681B16A1C123739C', type: 'String', desc: '绑定序列号' },
            { label: 'data.records.createTime', value: '2024-09-04 15:59:47', type: 'String', desc: '创建时间' },
            { label: 'data.records.category', value: '5', type: 'String', desc: '车辆类型' },
            { label: 'data.records.deleted', value: 0, type: 'Number', desc: '删除状态' },
            { label: 'data.records.deptId', value: '1123598813738675201', type: 'String', desc: '所属机构' },
            { label: 'data.records.id', value: '19406320167813121', type: 'String', desc: '主键' },
            { label: 'data.records.manufacturer', value: '', type: 'String', desc: '制造商' },
            { label: 'data.records.maxPower', value: 0, type: 'Number', desc: '最大马力，单位kw' },
            { label: 'data.records.model', value: '', type: 'String', desc: '车辆型号' },
            { label: 'data.records.ratedLoad', value: 0, type: 'Number', desc: '额定载重，单位t' },
            { label: 'data.records.targetType', value: '1', type: 'String', desc: '目标类别' },
            { label: 'data.records.targetTypeName', value: '车辆', type: 'String', desc: '目标类别名称' },
            { label: 'data.records.updateTime', value: '', type: 'String', desc: '修改时间' },
            { label: 'data.records.vin', value: '', type: 'String', desc: '车架号' },
            { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
            { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
            { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
            { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
        {
          label: '人员信息查询服务',
          name: '5',
          url: '/vdm-base-info/person/worker/list',
          method: 'post',
          params: '',
          paramConfig: [
            { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: false },
            { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: false },
            { label: 'name', value: '', type: 'String', required: '选填', desc: '人员姓名', isUrl: false },
            { label: 'wkno', value: '', type: 'String', required: '选填', desc: '工号', isUrl: false },
            { label: 'terminalType', value: '', type: 'String', required: '选填', desc: '绑定终端类型', isUrl: false },
            { label: 'terminalId', value: '', type: 'String', required: '选填', desc: '绑定序列号', isUrl: false },
            { label: 'industry', value: '', type: 'String', required: '选填', desc: '从业类型', isUrl: false },
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.countId', value: '', type: 'String', desc: '-' },
            { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
            { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
            { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
            { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
            { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
            { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
            { label: 'data.records.name', value: '飞念梦', type: 'String', desc: '人员姓名' },
            { label: 'data.records.wkno', value: '759570000000', type: 'String', desc: '工号' },
            { label: 'data.records.industryName', value: '煤炭', type: 'String', desc: '从业类型' },
            { label: 'data.records.postName', value: '管理员', type: 'String', desc: '岗位类型' },
            { label: 'data.records.deptName', value: '广州华维天梁', type: 'String', desc: '所属机构' },
            { label: 'data.records.phone', value: '16722460983', type: 'String', desc: '手机号码' },
            { label: 'data.records.terminalCategories', value: '北斗+UWB融合定位终端', type: 'String', desc: '绑定终端类型' },
            { label: 'data.records.deptId', value: '1824365648831123457', type: 'String', desc: '所属机构' },
            { label: 'data.records.id', value: '17751117150617600', type: 'String', desc: '主键' },
            { label: 'data.records.industry', value: '1', type: 'String', desc: '从业类型' },
            { label: 'data.records.post', value: '104', type: 'String', desc: '岗位类型' },
            { label: 'data.records.targetType', value: 2, type: 'Number', desc: '目标类别' },
            { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
            { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
            { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
            { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
        {
          label: '基础设施查询服务',
          name: '6',
          url: '/vdm-base-info/base/facility/list',
          method: 'post',
          params: '',
          paramConfig: [
            { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: false },
            { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: false },
            { label: 'category', value: '', type: 'String', required: '选填', desc: '设施类型', isUrl: false },
            { label: 'name', value: '', type: 'String', required: '选填', desc: '设施名称', isUrl: false },
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.countId', value: '', type: 'String', desc: '-' },
            { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
            { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
            { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
            { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
            { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
            { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
            { label: 'data.records.code', value: '粤A675YT', type: 'String', desc: '编号' },
            { label: 'data.records.categoryName', value: '煤炭', type: 'String', desc: '设施类型' },
            { label: 'data.records.name', value: '海格通信', type: 'String', desc: '设施名称' },
            { label: 'data.records.terminalCategories', value: '北斗基准站接收机', type: 'String', desc: '绑定终端类型' },
            { label: 'data.records.address', value: '广东省珠海市香洲区狮山街道狮山路福祥小区(狮山路)', type: 'String', desc: '设施地址' },
            { label: 'data.records.deptName', value: '海格通信', type: 'String', desc: '所属机构' },
            { label: 'data.records.category', value: '3', type: 'String', desc: '设施类型' },
            { label: 'data.records.deptId', value: '1123598813738675201', type: 'String', desc: '所属机构' },
            { label: 'data.records.geometry', value: '113.320077 23.158243 113.319862 23.151042', type: 'String', desc: '点串坐标，描述基础设施范围' },
            { label: 'data.records.id', value: '19181654857744385', type: 'String', desc: '主键' },
            { label: 'data.records.targetType', value: 3, type: 'Number', desc: '目标类别' },
            { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
            { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
            { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
            { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
        {
          label: '设备信息查询服务',
          name: '7',
          url: '/vdm-base-info/device/rnssDevice/list',
          method: 'post',
          params: '',
          paramConfig: [
            { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: false },
            { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: false },
            { label: 'category', value: '', type: 'String', required: '选填', desc: '终端类型', isUrl: false },
            { label: 'uniqueId', value: '', type: 'String', required: '选填', desc: '序列号', isUrl: false },
            { label: 'deviceNum', value: '', type: 'String', required: '选填', desc: '赋码编号', isUrl: false },
            { label: 'imei', value: '', type: 'String', required: '选填', desc: 'IMEI', isUrl: false },
            { label: 'number', value: '', type: 'String', required: '选填', desc: '物联网卡号', isUrl: false },
            { label: 'specificity', value: '', type: 'String', required: '选填', desc: '设备', isUrl: false },
          ],
          headerConfig: [
            { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
          ],
          resultConfig: [
            { label: 'code', value: 200, type: 'Number', desc: '状态码' },
            { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
            { label: 'data.countId', value: '', type: 'String', desc: '-' },
            { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
            { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
            { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
            { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
            { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
            { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
            { label: 'data.records.categoryName', value: '北斗车载终端', type: 'String', desc: '终端类型' },
            { label: 'data.records.vendorName', value: '海格通信', type: 'String', desc: '厂商名称' },
            { label: 'data.records.model', value: 'Z/HT-7', type: 'String', desc: '终端型号' },
            { label: 'data.records.uniqueId', value: '4443B59317A39214', type: 'String', desc: '序列号' },
            { label: 'data.records.specificityName', value: '新设备', type: 'String', desc: '设备' },
            { label: 'data.records.gnssModeName', value: '单北斗', type: 'String', desc: '定位模式' },
            { label: 'data.records.deviceNum', value: 'NXQ0124090431425', type: 'String', desc: '赋码编号' },
            { label: 'data.records.imei', value: '86575844131425202', type: 'String', desc: 'IMEI' },
            { label: 'data.records.numbers', value: '1619823504167', type: 'String', desc: '物联网卡' },
            { label: 'data.records.bdChipSn', value: 'CNBDCS75240904131426', type: 'String', desc: '北斗芯片序列号' },
            { label: 'data.records.deptName', value: '广州华成天权', type: 'String', desc: '所属机构' },
            { label: 'data.records.channelNum', value: 0, type: 'Number', desc: '视频通道数' },
            { label: 'data.records.activated', value: 0, type: 'Number', desc: '激活状态' },
            { label: 'data.records.category', value: '101', type: 'String', desc: '终端类型' },
            { label: 'data.records.createTime', value: '', type: 'String', desc: '创建时间' },
            { label: 'data.records.deleted', value: '', type: 'Number', desc: '删除状态' },
            { label: 'data.records.deptId', value: '1123598813738675201', type: 'String', desc: '所属机构' },
            { label: 'data.records.deviceType', value: 1, type: 'Number', desc: '终端类别' },
            { label: 'data.records.domain', value: 1, type: 'Number', desc: '应用方向/领域' },
            { label: 'data.records.gnssMode', value: '3', type: 'String', desc: '定位模式' },
            { label: 'data.records.id', value: '19610988244369409', type: 'String', desc: '主键' },
            { label: 'data.records.installdate', value: '', type: 'String', desc: '安装日期' },
            { label: 'data.records.iotProtocol', value: 1, type: 'Number', desc: '物联网协议' },
            { label: 'data.records.scenario', value: 5111, type: 'Number', desc: '应用场景' },
            { label: 'data.records.specificity', value: '2', type: 'String', desc: '设备' },
            { label: 'data.records.targetId', value: 0, type: 'Number', desc: '监控对象id' },
            { label: 'data.records.targetName', value: '', type: 'String', desc: '监控对象名称' },
            { label: 'data.records.targetType', value: 0, type: 'Number', desc: '监控对象类型' },
            { label: 'data.records.terminalId', value: '', type: 'String', desc: '终端编号' },
            { label: 'data.records.updateTime', value: '', type: 'String', desc: '修改时间' },
            { label: 'data.records.vendor', value: '0750', type: 'String', desc: '厂商名称' },
            { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
            { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
            { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
            { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
            { label: 'success', value: true, type: 'Boolean', desc: '状态' },
          ]
        },
      ],
      form: {
        method: 'post',
        url: '/vdm-statistic/static/locations/list',
        label: '定位查询服务',
        params: '',
        paramConfig: [
          { label: 'current', value: 1, type: 'Number', required: '必填', desc: '当前页码', isUrl: true },
          { label: 'size', value: 10, type: 'Number', required: '必填', desc: '页面条数', isUrl: true },
          { label: 'deviceNum', value: '', type: 'String', required: '选填', desc: '赋码编号', isUrl: false },
          { label: 'startTime', value: moment().startOf('day').unix() - 6 * 86400, type: 'Number', required: '选填', desc: '开始时间', isUrl: false },
          { label: 'endTime', value: moment().endOf('day').unix(), type: 'Number', required: '选填', desc: '结束时间', isUrl: false },
          { label: 'targetName', value: '', type: 'String', required: '选填', desc: '监控对象名称', isUrl: false },
        ],
        headerConfig: [
          { label: 'Content-Type', value: 'application/json', type: 'String', required: '必填', desc: '类型' }
        ],
        resultConfig: [
          { label: 'code', value: 200, type: 'Number', desc: '状态码' },
          { label: 'data', value: '{}', type: 'Object', desc: '结构体' },
          { label: 'data.countId', value: '', type: 'String', desc: '-' },
          { label: 'data.current', value: 1, type: 'Number', desc: '页码' },
          { label: 'data.maxLimit', value: '', type: 'Number', desc: '最大分页大小' },
          { label: 'data.optimizeCountSql', value: true, type: 'Boolean', desc: '是否自动优化分页sql' },
          { label: 'data.orders', value: '[]', type: 'Array', desc: '排序字段信息' },
          { label: 'data.pages', value: 831, type: 'Number', desc: '总页数' },
          { label: 'data.records', value: [], type: 'Array', desc: '数据列表' },
          { label: 'data.records.deviceNum', value: 'NVF4524090700000', type: 'String', desc: '赋码编号' },
          { label: 'data.records.deviceType', value: 1, type: 'Number', desc: '终端类别' },
          { label: 'data.records.targetName', value: '粤B65432', type: 'String', desc: '监控对象名称' },
          { label: 'data.records.longitude', value: 113.447001, type: 'Number', desc: '经度' },
          { label: 'data.records.latitude', value: 23.156438, type: 'Number', desc: '纬度' },
          { label: 'data.records.address', value: '广东省广州市黄埔区南翔二路68-31号正东方向60米唐克士', type: 'String', desc: '地址' },
          { label: 'data.records.altitude', value: 0, type: 'Number', desc: '高程(米)' },
          { label: 'data.records.speed', value: 0, type: 'Number', desc: '速度(km)' },
          { label: 'data.records.bearing', value: 247, type: 'Number', desc: '方向(度)' },
          { label: 'data.records.time', value: 1725693809, type: 'Number', desc: '定位时间' },
          { label: 'data.records.recvTime', value: 1725693810, type: 'Number', desc: '接收时间' },
          { label: 'data.records.batch', value: 0, type: 'Number', desc: '上传类型' },
          { label: 'data.records.valid', value: 1, type: 'Number', desc: '定位有效性' },
          { label: 'data.records.alarm', value: '', type: 'Number', desc: '终端告警' },
          { label: 'data.records.auxiliary', value: '', type: 'String', desc: '定位附加信息' },
          { label: 'data.records.correction', value: 0, type: 'Number', desc: '是否纠正点, 0-未纠正，1-纠正点' },
          { label: 'data.records.deviceId', value: '', type: 'String', desc: '设备id' },
          { label: 'data.records.id', value: '', type: 'String', desc: '主键' },
          { label: 'data.records.mileage', value: '', type: 'Number', desc: '行驶里程：单位：km' },
          { label: 'data.records.posSys', value: '', type: 'Number', desc: '定位系统' },
          { label: 'data.records.status', value: '', type: 'Number', desc: '终端状态' },
          { label: 'data.records.targetId', value: '15532826604666881', type: 'String', desc: '监控对象id' },
          { label: 'data.records.targetType', value: '', type: 'Number', desc: '监控对象类型' },
          { label: 'data.searchCount', value: true, type: 'Boolean', desc: '是否自动查询数据总条数' },
          { label: 'data.size', value: 10, type: 'Number', desc: '条数' },
          { label: 'data.total', value: 8308, type: 'Number', desc: '总条数' },
          { label: 'msg', value: '操作成功', type: 'String', desc: '提示语' },
          { label: 'success', value: true, type: 'Boolean', desc: '状态' },
        ]
      },
      methodList: [
        { label: 'get', value: 'get' },
        { label: 'post', value: 'post' },
        { label: 'put', value: 'put' },
        { label: 'delete', value: 'delete' }
      ],
      activeParam: '1',
      activeResult: '1',
      contentResult: '',
      headerResult: '',
      requestResult: ''
    };
  },
  computed: {
    paramConfigNum () {
      return this.form.paramConfig.filter(item => item.label && item.value).length;
    },
    headerConfigNum () {
      return this.form.headerConfig.filter(item => item.label && item.value).length;
    }
  },
  created () {
    this.getDefaultDeviceNum();
  },
  methods: {
    getDefaultDeviceNum () {
      defaultDeviceNum().then(res => {
        if (res.data) {
          let index = this.form.paramConfig.findIndex(item => item.label === 'deviceNum');
          this.$set(this.serviceList[0].paramConfig[index], 'value', res.data);
          this.$set(this.form.paramConfig[index], 'value', res.data);
        }
      });
    },
    handleParamInput () {
      const urlObj = new URL(window.location.origin + this.form.url);
      const paramList = this.form.paramConfig.filter(item => item.label && item.value);
      const urlParams = new URLSearchParams('');
      paramList.forEach(item => {
        urlParams.set(item.label, item.value);
      });
      urlObj.search = urlParams.toString();
      if (this.form.url.indexOf('?') === -1) {
        this.form.url += urlObj.search;
      } else {
        this.form.url = this.form.url.replace(/\?.*/, urlObj.search);
      }
    },
    handleUrlInput () {
      const urlParams = new URLSearchParams(new URL(window.location.origin + this.form.url).search);
      if (urlParams.size) {
        this.form.paramConfig = [];
        urlParams.forEach((value, key) => {
          this.form.paramConfig.push({
            label: key,
            value: value
          });
        });
      } else {
        this.form.paramConfig = [
          { label: '', value: '' }
        ];
      }
    },
    addHandle(config) {
      let obj = {
        label: '',
        value: ''
      };
      this.form[config].push(obj);
    },
    reduceHandle (config, index) {
      this.form[config].splice(index, 1);
      if (config === 'paramConfig') {
        this.handleParamInput();
      }
    },
    handleTabClick () {
      this.form = { ...this.serviceList[this.activePage] };
      this.contentResult = '';
      this.headerResult = '';
      this.requestResult = '';
    },
    handleQueryClick () {
      const result = this.form.paramConfig.some(item => item.required === '必填' && !item.value);
      if (result) {
        return this.$message.warning('请填写必填项');
      }
      // 获取拼接在url后的请求参数
      const queryParams = this.form.paramConfig.reduce((obj, item) => {
        if (item.label && item.value && item.isUrl) {
          obj[item.label] = item.value;
        }
        return obj;
      }, {});
      // 获取请求头的参数
      const headerParams = this.form.headerConfig.reduce((obj, item) => {
        if (item.label && item.value) {
          obj[item.label] = item.value;
        }
        return obj;
      }, {});
      // 获取body的请求参数
      const data = this.form.paramConfig.reduce((obj, item) => {
        if (item.label && item.value && !item.isUrl) {
          if (item.type === 'BigInt') {
            obj[item.label] = BigInt(item.value);
          } else if (item.type === 'Number') {
            obj[item.label] = Number(item.value);
          } else if (item.type === 'Array') {
            obj[item.label] = this.parseArray(item.value);
          } else {
            obj[item.label] = item.value;
          }
        }
        return obj;
      }, {});
      // if (this.form.params && !this.isJSONString(this.form.params)) {
      //   this.$message.warning('请输入正确的JSON字符串');
      //   return;
      // }
      // const data = this.form.params ? JSON.parse(this.form.params) : {};
      this.contentResult = '';
      this.headerResult = '';
      this.requestResult = '';
      let params = this.form.method === 'get' ? { params: queryParams } : JSONbig.stringify(data);
      axios[this.form.method](this.form.url, params, {
        params: queryParams,
        headers: headerParams
      }).then(res => {
        this.contentResult = JSON.stringify(res.data, null, 4);
        this.headerResult = JSON.stringify(res.headers, null, 4);
        this.requestResult = JSON.stringify(res.request, null, 4);
      }).catch((error) => {
        this.contentResult = error;
      });
    },
    parseArray(str) {
      // 去掉开头和结尾的方括号
      str = str.slice(1, -1);
      // 使用逗号分隔字符串，并去除引号
      return str.split(',').map(item => item.trim().slice(1, -1));
    },
    isJSONString(str) {
      try {
        const result = JSON.parse(str);
        // 检查解析结果是否为对象或数组
        if (typeof result === 'object' && result !== null) {
          return true;
        }
      } catch (e) {
        // 如果发生错误，则不是有效的 JSON 字符串
        return false;
      }
      // 如果结果不是对象或数组，也不是有效的 JSON 字符串
      return false;
    }
  },
};
</script>

<style lang="less" scoped>
.tab-container {
  height: 100%;
  /deep/ .el-tabs--left .el-tabs__item.is-left {
    text-align: center;
  }
  /deep/ .el-tabs__content {
    height: 100%;
  }
  .method-select {
    width: 120px;
  }
  &-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    &-title {
      text-align: center;
    }
    &-search {
      display: flex;
    }
    &-param {
      margin: 20px 0;
      height: 300px;
    }
    &-result {
      flex: 1;
      overflow: auto;
    }
  }
  .search-btn {
    margin-left: 10px;
  }
}
.tab-container-content-result, .tab-container-content-param {
  /deep/ .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs__content {
      flex: 1;
      overflow: auto;
    }
  }
}
.main {
    display: flex;
    width: 85%;
    text-align: center;
  }

  .main_item {
    flex: 1;
    height: 45px;
    line-height: 45px;
    border: 1px solid #c1c9da;
    padding: 0 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    /deep/ .el-input {
      width: 100%;
      .el-input__inner {
        border: none !important;
      }
    }
  }

  .main_size {
    flex: 1 !important;
  }

  .item_label {
    flex: 1;
    height: 40px;
    line-height: 40px;
    background-color: #e1e5ee;
    border: 1px solid #c1c9da;
  }

  .add_item {
    border: 1px solid #aebac5;
    padding: 3px 5px;
    background-color: #ffffff;
    margin: 5px;
    cursor: pointer;
  }
  .result-content {
    white-space: pre-line;
  }
</style>
