<template>
  <div class="video-live">
    <section
      v-show="showCarList"
      class="left"
    >
      <div class="car-container chunk">
        <TableTitleSlot title="语音对讲中心"/>
        <div style="height: calc(100% - 40px);">
          <VideoMultiSelectTree
            ref="VideoMultiSelect"
            style="height: 100%;"
            :is-voice="true"
            @mouseOverNode="highlightPlayers"
            @checkedChannelsChange="handleTreeCheckedChannelsChange"
          />
        </div>
        <el-card
          v-if="intercom"
          class="box-card"
        >
          <div
            slot="header"
            class="clearfix"
          >
            <span>{{ operate === 1 ? '双向对讲' : operate === 2 ? '监听' : '' }} — {{
              intercomOriginData.targetName
            }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="closeIntercom"
            >
              关闭
            </el-button>
          </div>
          <VideoPlayerGalleryComponent
            ref="intercomGalleryComponent"
            class="intercom-container"
            :default-mode="{
              videoNumber: 1,
              templateName: 'videoPlayer-template-1big-full',
              name: '1big-full',
              displayName: '单主屏'
            }"
            :max-video-count="1"
            player-type="VideoPlayerFLV"
            force-use-video-player=""
            :subscribe-click-switch-player-event="false"
            :custom-setup="{customMsg:'正在连接'}"
          />
        </el-card>
      </div>
    </section>
    <section
      v-show="showMap"
      class="right"
    >
      <div class="map-container chunk">
        <MapWidget
          ref="MapWidget"
          class="map-widget"
          :show-fold-button="true"
          :tool-config="toolConfig"
          @changeMap="changeMap"
          @customEvent="handleMarkerClick"
        />
      </div>
    </section>
  </div>
</template>

<script>
import MapWidget from '@/components/map/MapWidgetAMap';
import VideoPlayerGalleryComponent from '@/components/Video/VideoPlayerGalleryBase';
import { playRealtimeVideo, stopRealtimeVideo } from '@/api/video/realtimeVideo.js';
import VideoMultiSelectTree from '@/components/select/VideoMultiSelect/VideoMultiSelectTreeNew';
import Recorder from 'recorder-core';
import 'recorder-core/src/engine/pcm.js';
import 'recorder-core/src/engine/wav.js';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
import { terminalDetails } from '@/api/video/realtimeVideo';
import { startGBChannel, channellist } from '@/api/video/gbVideo';
import { getSystemParam } from '@/api/user';

export default {
  name: 'VideoLive',
  components: {
    TableTitleSlot,
    MapWidget,
    VideoPlayerGalleryComponent,
    VideoMultiSelectTree
  },
  data() {
    return {
      selectLayout: 4,
      selectLayoutOptions: [
        {
          value: 1,
          label: '单屏1'
        },
        {
          value: 4,
          label: '分屏4'
        }
      ],
      currentVideoNumber: 9,
      // videoModeArray: [
      //   {
      //     videoNumber: 1,
      //     templateName: 'videoPlayer-template-1big-full',
      //     name: '1big-full',
      //     displayName: '单主屏'
      //   },
      //   {
      //     videoNumber: 4,
      //     templateName: 'videoPlayer-template-4balance-leftTop',
      //     name: '4balance-leftTop',
      //     displayName: '4分屏'
      //   },
      //   {
      //     videoNumber: 6,
      //     templateName: 'videoPlayer-template-1big5small-leftTop',
      //     name: '1big5small-leftTop',
      //     displayName: '6分屏'
      //   },
      //   {
      //     videoNumber: 7,
      //     templateName: 'videoPlayer-template-3big4small-leftTop',
      //     name: '3big4small-leftTop',
      //     displayName: '7分屏'
      //   },
      //   {
      //     videoNumber: 8,
      //     templateName: 'videoPlayer-template-1big7small-leftTop',
      //     name: '1big7small-leftTop',
      //     displayName: '8分屏'
      //   },
      //   {
      //     videoNumber: 9,
      //     templateName: 'videoPlayer-template-9balance-leftTop',
      //     name: '9balance-leftTop',
      //     displayName: '9分屏'
      //   },
      //   {
      //     videoNumber: 16,
      //     templateName: 'videoPlayer-template-16balance-leftTop',
      //     name: '16balance-leftTop',
      //     displayName: '16分屏'
      //   }
      // ],
      videoModeList: [
        {
          videoNumber: 1,
          templateName: 'videoPlayer-template-1big-full',
          name: '1big-full',
          displayName: '单主屏'
        },
        {
          videoNumber: 4,
          templateName: 'videoPlayer-template-4balance-leftTop',
          name: '4balance-leftTop',
          displayName: '4分屏'
        },
        {
          videoNumber: 6,
          templateName: 'videoPlayer-template-1big5small-leftTop',
          name: '1big5small-leftTop',
          displayName: '6分屏'
        },
        {
          videoNumber: 7,
          templateName: 'videoPlayer-template-3big4small-leftTop',
          name: '3big4small-leftTop',
          displayName: '7分屏'
        },
        {
          videoNumber: 8,
          templateName: 'videoPlayer-template-1big7small-leftTop',
          name: '1big7small-leftTop',
          displayName: '8分屏'
        },
        {
          videoNumber: 9,
          templateName: 'videoPlayer-template-9balance-leftTop',
          name: '9balance-leftTop',
          displayName: '9分屏'
        },
        {
          videoNumber: 16,
          templateName: 'videoPlayer-template-16balance-leftTop',
          name: '16balance-leftTop',
          displayName: '16分屏'
        }
      ],
      videoModeValue: 9,
      showCarList: true,
      showMap: true,
      calcVideoWidth: false,
      carNumber: 0,
      selectedCar: [],
      lastCars: [],
      playFlag: false,
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: true, // 路况
        layerSelectShow: true, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: true // 工具栏
      }, // 控制工具按钮
      intercom: false,
      rec: null,
      ws: null,
      passOptions: [
        {
          value: 1,
          label: '子码流'
        },
        {
          value: 0,
          label: '主码流'
        }
      ],
      timeOptions: [
        {
          value: 0.5,
          label: '30秒'
        },
        {
          value: 1,
          label: '60秒'
        },
        {
          value: 2,
          label: '120秒'
        },
        {
          value: 3,
          label: '180秒'
        }
      ],
      passValue: 1,
      localUserConfig: {
        videoLiveTime: 3
      },
      operate: 1,
      autoUpDateTimer: null,
      videoMapCar: [] // 当前选择的车辆
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.intercomGalleryComponent?.activateVideoPlayerGallery();
      this.bingVideoPlayerGalleryAndVideoPlayerGalleryModeControl();
    });
    // if (this.$route.query.licencePlate) {
    //   this.$refs.VideoMultiSelect.setSelectedCar(this.$route.query.licencePlate, 'fromVideo');
    // }
    let config = JSON.parse(localStorage.getItem(`localUserConfig`));
    if (config && config.videoLiveTime) {
      this.localUserConfig.videoLiveTime = config.videoLiveTime;
    }
  },
  activated() {
    if (!this.autoUpDateTimer) {
      this.autoUpDateTimer = setInterval(() => {
        if (this.videoMapCar.length) {
          this.$refs.MapWidget.drawRegionMarkers(this.videoMapCar, true);
        }
      }, 30 * 1000);
    }
  },
  deactivated() {
    clearInterval(this.autoUpDateTimer);
    this.autoUpDateTimer = null;
  },
  methods: {
    // 点击marker的自定义事件
    handleMarkerClick(data) {
      // 点击marker后 定时器刷新默认展示该marker
      if (this.videoMapCar.indexOf(data.vehicleId) !== -1) {
        this.videoMapCar.splice(this.videoMapCar.indexOf(data.vehicleId), 1);
        this.videoMapCar.push(data.vehicleId);
      }
    },
    // 全屏展示
    handleConfig() {
      const element = this.$refs['intercomGalleryComponent'].$el;
      if (element.requestFullscreen) {
        element.requestFullscreen();
      }
      else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      }
      else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      }
      else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    // 关闭视频后请求最新的车辆marker和详情弹窗
    getVehicleData(data) {
      let ids = [];
      data.forEach((item) => {
        const list = item.split('-');
        ids.push(Number(list[list.length - 1]));
      });
      this.videoMapCar = ids;
      this.$refs.MapWidget.drawRegionMarkers(ids, true);
    },
    /**
     * 设置视频的分屏模式
     * @param {Object} videoMode
     * @param {String} videoMode.videoNumber 视频数量
     * @param {String} videoMode.templateName 模板名称
     * @param {String} videoMode.name 模板名字
     * @param {String} videoMode.displayName 显示名称
     * @example setVideoMode({
     videoNumber: 8,
     templateName: 'videoPlayer-template-1big7small-leftTop',
     name: '1big7small-leftTop',
     displayName: '8分屏'
     });
     */
    setVideoMode(videoMode) {
      if (this._videoPlayerGallery) {
        this._videoPlayerGallery.switchMode(videoMode);
        this.currentVideoNumber = videoMode.videoNumber;
      }
    },
    /**
     * 绑定视频和分屏控制器
     */
    bingVideoPlayerGalleryAndVideoPlayerGalleryModeControl() {
      this.$refs.intercomGalleryComponent?.getVideoPlayerGallery().then(videoPlayerGallery => {
        this._videoPlayerGallery = videoPlayerGallery;
        // 鼠标悬浮在单个播放器的时候，高亮相关的播放器
        this._videoPlayerGallery.subscribeOnHoverEvent(function (event, channelID) {
          if (channelID) {
            videoPlayerGallery.highlightPlayers(channelID);
          }
          else {
            videoPlayerGallery.highlightPlayers();
          }
        });

        // 监听视频组件变化返回相应的
        this._videoPlayerGallery.subscribeChannelIdChangeEvent(list => {
          if (!this.treeChecking) {
            let keys = [];
            for (let i = 0; i < list.length; i++) {
              if (list[i].channelID) { // TODO customData无法获取到最新的播放视频数, 因此改为channelID
                keys.push(list[i].customData['VEHICLETREE'].treeNodeKey);
              }
            }
            console.log('list->', keys);
            this.$refs.VideoMultiSelect.setCheckedKeys(keys);
            // 关闭视频后请求最新的车辆marker和详情弹窗
            this.getVehicleData(keys);
          }
          // else {
          //   console.log('treeChecking#ignore');
          // }
        });
      }).catch(errorMessage => {
        this.$message({
          type: 'error',
          message: errorMessage
        });
      });
    },
    /**
     * 高亮播放器
     * @param data
     * @param [data.licencePlate] 车牌号
     * @param [data.channel] 通道号
     */
    highlightPlayers(data) {
      if (this.$refs?.intercomGalleryComponent) {
        this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
        // node.data.originalData;
          let licensePlate = data.licencePlate;
          let channel = data.channel;
          let channelIDFuzzy = '';
          if (licensePlate) {
            if (channel) {
              channelIDFuzzy = licensePlate + '_' + channel;
            }
            else {
              channelIDFuzzy = licensePlate;
            }
          }
          // console.log('highlightPlayers-->', data, channelIDFuzzy);
          videoPlayerGallery.highlightPlayers(channelIDFuzzy);
        }).catch(errorMessage => {
          this.$message({
            type: 'error',
            message: errorMessage
          });
        });
      }
    },
    /**
     * 勾选树结构
     * @param data
     * @param {Array.<Object>} data.checkedChannels
     * @param data.checkedChannels[].licencePlate 车牌号
     * @param data.checkedChannels[].channel 通道号
     * @param data.checkedChannels[].treeNodeKey 树结构节点id
     */
    handleTreeCheckedChannelsChange(data) {
      console.log('-> data', data)
      this.treeChecking = true;
      let checkedChannels = data.checkedChannels;
      this.videoMapCar = data.videoMapCar; // 将当前选中的车辆保存下来
      this.$refs.MapWidget.drawRegionMarkers(data.videoMapCar, true);
      this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
        // 如果格子不够，打开更多的视频格子
        if (checkedChannels.length > this.currentVideoNumber) {
          for (let i = 0; i < this.videoModeList.length; i++) {
            if (this.videoModeList[i].videoNumber > checkedChannels.length) {
              this.setVideoMode(this.videoModeList[i]);
              break;
            }
          }
        }
        // 播放视频
        let dataArray = [];
        for (let i = 0; i < checkedChannels.length; i++) {
          dataArray.push({
            channelID: checkedChannels[i].targetName + '_' + checkedChannels[i].channel,
            deviceId: checkedChannels[i].deviceId,
            deviceType: checkedChannels[i].deviceType,
            customData: {
              'VEHICLETREE': {
                treeNodeKey: checkedChannels[i].treeNodeKey
              }
            },
            streamType: this.passValue,
            phone: checkedChannels[i].phone
          });
        }
        videoPlayerGallery.reloadChannelIDsIfAvailable(dataArray);
        this.treeChecking = false;
      }).catch(errorMessage => {
        this.$message({
          type: 'error',
          message: errorMessage
        });
        this.treeChecking = false;
      });
    },
    /**
     * 显示视频播放器的编号
     * @param {Boolean} flag
     */
    showVideoPlayerIndexWidget(flag) {
      this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
        videoPlayerGallery.showVideoPlayerIndexWidget(flag);
      }).catch(errorMessage => {
        this.$message({
          type: 'error',
          message: errorMessage
        });
      });
    },
    changeMap() {
      this.showMap = !this.showMap;
    },
    handleMonitorSelect(intercomOriginData) {
      let that = this;
      this.operate = 2;
      this.intercomOriginData = intercomOriginData;
      this.intercom = true;
      this.$nextTick(() => {
        that.$refs.intercomGalleryComponent.activateVideoPlayerGallery();
      });
      console.log('-> intercomOriginData1', intercomOriginData)
      let parme = {
        'deviceType': intercomOriginData.deviceType,
        'deviceId': intercomOriginData.deviceId,
        // 'vehicleId': intercomOriginData.id,
        'channel': 1,
        // 数据类型（默认0） 0：音视频，1：视频，2：双向对讲，3：监听，4：中心广播，5：透传
        'dataType': 3,
        // 0：主码流，1：子码流
        'streamType': 1
      };
      playRealtimeVideo(parme).then(res => {
        if (res.code === 200) {
          let url = res.data.flvUrl;
          this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
            let videoPlayer = videoPlayerGallery.get(0);
            videoPlayer.reload({
              videoUrl: url,
              hasAudio: true,
              hasVideo: false,
              fromIntercom: 2,
              muted: false,
              callback: function () {
                videoPlayer.videoPlayerToolBarVisible = true;
              }
            });
          }).catch(errorMessage => {
            that.$message({
              type: 'error',
              message: errorMessage
            });
          });
        }
      });
    },
    handleMonitorStop() {
      let that = this;
      // 关闭双向对讲窗口
      this.intercom = false;
      this.$refs.VideoMultiSelect.updateStyle();
      // 停止推流
      let parme = {
        // 'licencePlate': this.intercomOriginData.licencePlate,
        // 'vehicleId': this.intercomOriginData.id,
        'deviceId': this.intercomOriginData.deviceId,
        'deviceType': this.intercomOriginData.deviceType,
        'channel': 1,
        'cmd': 0,
        'closeType': 0,
        'streamType': 1
      };
      stopRealtimeVideo(parme).then(res => {
        if (res.code === 200) {
          console.log(res.data);
        }
      });
      this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
        let videoPlayer = videoPlayerGallery.get(0);
        videoPlayer.clearStream();
      }).catch(errorMessage => {
        that.$message({
          type: 'error',
          message: errorMessage
        });
      });
    },
    async handleTalkbackSelect(intercomOriginData) {
      console.log('-> intercomOriginData', intercomOriginData);
      let that = this;
      this.operate = 1;
      this.intercomOriginData = intercomOriginData;
      this.intercom = true;
      this.$nextTick(() => {
        that.$refs.intercomGalleryComponent.activateVideoPlayerGallery();
      });
      Recorder.BufferSize = 2048;
      const reader = new window.FileReader();
      this.rec = new Recorder({
        type: 'pcm',
        sampleRate: 8000,
        bitRate: 16,
        onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate) {
          // console.log(buffers, bufferSampleRate);
          // 录音实时回调
          let chunkInfo = Recorder.SampleData([buffers[buffers.length - 1]], bufferSampleRate, that.rec.set.sampleRate);
          const blob = new Blob([chunkInfo.data], { type: 'audio/pcm' });
          reader.readAsDataURL(blob);
          reader.onloadend = () => {
            const readerData = reader.result;
            const base64 = readerData.split(',')[1];
            that.ws.send(base64);
          };
        }
      });
      const { code: terminalCode, data: terminalData } = await terminalDetails(intercomOriginData.deviceId);
      if (terminalCode !== 200 || !terminalData || !terminalData.channelIds) {
        this.$message.error('终端信息获取失败');
        return;
      }
      const requestResult = await getSystemParam('gbsRequestUrl');
      const { code: gbsRequestCode, data: gbsRequestData } = requestResult.data;
      if (gbsRequestCode !== 200 || !gbsRequestData) {
        this.$message.error('获取接口前缀失败');
        return;
      }
      // 使用ws地址播放视频就用gbsStreamingUrl参数
      const streamingResult = await getSystemParam('gbsStreamingUrl');
      const { code: gbsStreamingCode, data: gbsStreamingData } = streamingResult.data;
      if (gbsStreamingCode !== 200 || !gbsStreamingData) {
        this.$message.error('获取流媒体前缀失败');
        return;
      }
      const { ChannelList: channelData } = await channellist({serial: terminalData.channelIds, url: gbsRequestData});
      if (!channelData || !channelData.length) {
        this.$message.error('视频通道编号获取失败');
        return;
      }
      const result = await getSystemParam('talkbackUrl');
      const { data: wsLocation, code: talkbackCode } = result.data;
      if (talkbackCode !== 200 || !wsLocation) {
        return;
      }
      this.rec.open(function () {
        that.rec.start(); // 开始录音
        // 开始录音同时连接websocket
        const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
        that.ws = new WebSocket(`${protocol}${wsLocation}/wsgbs/v1/control/ws-talk/${terminalData.channelIds}/${channelData[0].ID}?token=${localStorage.getItem('QS_TOKEN') || ''}`);
        that.ws.onopen = function (evt) {
          console.log('Connection open ...', evt);
        };
        that.ws.onmessage = function (evt) {
          console.log('Received Message:' + evt.data);
        };
        that.ws.binaryType = 'arraybuffer';
      });
      startGBChannel({
        serial: terminalData.channelIds,
        code: channelData[0].ID,
        url: gbsRequestData,
        prefix: gbsStreamingData
      }).then(json => {
        this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
          let videoPlayer = videoPlayerGallery.get(0);
          videoPlayer.reload({
            videoUrl: json,
            hasAudio: true,
            hasVideo: false,
            fromIntercom: 1,
            muted: false,
            callback: function () {
              videoPlayer.videoPlayerToolBarVisible = true;
            }
          });
        }).catch(errorMessage => {
          that.$message({
            type: 'error',
            message: errorMessage
          });
        });
      });
    },
    handleSelect(intercomOriginData) {
      console.log('-> intercomOriginData', intercomOriginData)
      let that = this;
      this.operate = 1;
      this.intercomOriginData = intercomOriginData;
      this.intercom = true;
      this.$nextTick(() => {
        that.$refs.intercomGalleryComponent.activateVideoPlayerGallery();
      });
      Recorder.BufferSize = 2048;
      this.rec = new Recorder({
        type: 'wav',
        sampleRate: 8000,
        bitRate: 16,
        onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate) {
          // console.log(buffers, bufferSampleRate);
          // 录音实时回调
          let chunkInfo = Recorder.SampleData([buffers[buffers.length - 1]], bufferSampleRate, that.rec.set.sampleRate);
          // 释放占用的内存，但是录音结束时不可以调用stop，直接close可关闭
          /* if (that.rec.set.type) {
            buffers[buffers.length - 3] = null
          } */
          // console.log(chunkInfo, chunkInfo.data);
          // 将chunkInfo给到websocket
          that.ws.send(chunkInfo.data);
        }
      });

      let port = location.port;
      let wsad;
      // TODO 暂时只有一个59.41.7.83:20420的测试环境, 研发和正式等后面有了再更换
      if (port === '20413') { // 研发
        wsad = '59.41.7.83:20420';
      }
      else if (port === '30122' || port === '1888') { // 测试
        wsad = '59.41.7.83:20420';
      }
      else {
        wsad = '59.41.7.83:20420';
      }
      this.rec.open(function () {
        that.rec.start(); // 开始录音
        // 开始录音同时连接websocket
        const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
        that.ws = new WebSocket(protocol + wsad + '/realtime/audio?tel=' + intercomOriginData.phone);
        that.ws.onopen = function (evt) {
          console.log('Connection open ...', evt);
        };
        that.ws.onmessage = function (evt) {
          console.log('Received Message:' + evt.data);
        };
        that.ws.binaryType = 'arraybuffer';
      });
      let parme = {
        'deviceType': intercomOriginData.deviceType,
        'deviceId': intercomOriginData.deviceId,
        'channel': 1,
        // 数据类型（默认0） 0：音视频，1：视频，2：双向对讲，3：监听，4：中心广播，5：透传
        'dataType': 2,
        // 0：主码流，1：子码流
        'streamType': 1
      };
      playRealtimeVideo(parme).then(res => {
        if (res.code === 200) {
          let url = res.data.flvUrl;
          this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
            let videoPlayer = videoPlayerGallery.get(0);
            videoPlayer.reload({
              videoUrl: url,
              hasAudio: true,
              hasVideo: false,
              fromIntercom: 1,
              muted: false,
              callback: function () {
                videoPlayer.videoPlayerToolBarVisible = true;
              }
            });
          }).catch(errorMessage => {
            that.$message({
              type: 'error',
              message: errorMessage
            });
          });
        }
      });
    },
    handleStop(intercomOriginData) {
      // 停止双向对讲
      this.stopIntercom();
    },
    stopIntercom() {
      let that = this;
      // 关闭双向对讲窗口
      this.intercom = false;
      this.$refs.VideoMultiSelect.updateStyle();
      if (this.intercomOriginData.treeCategory !== '202') {
        // 停止推流
        let parme = {
          // 'licencePlate': this.intercomOriginData.licencePlate,
          // 'vehicleId': this.intercomOriginData.id,
          'deviceId': this.intercomOriginData.deviceId,
          'deviceType': this.intercomOriginData.deviceType,
          'channel': 1,
          'cmd': 4,
          'closeType': 0,
          'streamType': 1
        };
        stopRealtimeVideo(parme).then(res => {
          if (res.code === 200) {
            console.log(res.data);
          }
        });
      }
      this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
        let videoPlayer = videoPlayerGallery.get(0);
        videoPlayer.clearStream();
      }).catch(errorMessage => {
        that.$message({
          type: 'error',
          message: errorMessage
        });
      });
      // 停止录音
      this.rec.stop(function (blob, duration) {
        console.log(blob, window.URL.createObjectURL(blob), '时长:' + duration + 'ms');
        that.rec.close();
        that.rec = null;
        // 打印
        /* blob.arrayBuffer().then(res => {
          console.log(res);
        });
        // 播放
        var audio = document.createElement('audio');
        audio.controls = true;
        document.body.appendChild(audio);
        audio.src = window.URL.createObjectURL(blob);
        audio.crossOrigin = 'Anonymous';
        audio.play();
        // 下载
        var link = document.createElement('a');
        link.download = that.$moment().format('YYYYMMDDHHmmss') + '.pcm';
        link.style.display = 'none';
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link); */
      }, function (msg) {
        console.log('录音失败:' + msg);
        that.rec.close();
        that.rec = null;
      });
      // 停止webSocket
      this.ws.onclose = function (evt) {
        console.log('Connection closed!', evt);
      };
    },
    closeIntercom() {
      if (this.operate === 1) {
        this.stopIntercom();
      }
      else if (this.operate === 2) {
        this.handleMonitorStop();
      }
    },
    // 关闭所有通道
    closeAllHandle() {
      console.log('111');
      this.$refs.VideoMultiSelect.clearAll();
    },
    // 关闭单个通道（treeNodeKey）
    closeSingleChannel(value) {
      this.$refs.intercomGalleryComponent.getVideoPlayerGallery().then(videoPlayerGallery => {
        videoPlayerGallery.stopSingle(value);
      }).then(() => {
        this.$nextTick(() => {
          this.$refs.VideoMultiSelect.$refs.tree.setChecked(value.treeNodeKey, false);
        });
      });
    },
    drawVideoMapMarker() {
      console.log('drawVideoMapMarker');
    },
    // 延时时长
    handleTimeChange() {
      localStorage.setItem('localUserConfig', JSON.stringify(this.localUserConfig));
    },
    // 延时时长
    videoLiveTimeChange(val) {
      val = val ? +val : 3;
      if (val > 0) {
        localStorage.setItem('localUserConfig', JSON.stringify(this.localUserConfig));
      }
    },
    videoLiveTimeKeyup(val) {
      val = +val;
      if (val > 0 && val <= 60) {
      }
      else if (!val) {
      }
      else {
        this.localUserConfig.videoLiveTime = 3;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.video-live {
  width: 100%;
  height: 100%;
  padding: 0 4px 4px;
  display: flex;
  min-height: 800px;

  @space: 4px;
  @collapseBtnSize: 90px;
  @sideSectionWidth: 400px;

  .collapse-btn-base {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
    background-color: #b0b3b8;
    color: #ffffff;

    i {
      font-weight: bold;
    }
  }

  section {
    .chunk {
      border: 1px solid #e1e5e8;
      background-color: #ffffff;
    }
  }

  .left {
    width: 450px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;

    .car-container {
      flex: 1;
      overflow: hidden;
    }
  }

  .middle {
    flex: 1;
    min-width: 50vw;
    display: flex;
    flex-direction: column;

    .video-container {
      flex: 1;
      position: relative;
    }
  }

  .right {
    flex: 1;
    .map-container {
      height: 100%;
    }
  }

  .collapse-vertical {
    width: @space;
    height: 100%;
    display: flex;
    align-items: center;

    .collapse-btn {
      width: 100%;
      height: @collapseBtnSize;
      .collapse-btn-base
    }
  }

}

.video-up {
  display: flex;
  height: @videoSelectHeight;
  width: 100%;
  background: #f0f2f5;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 0px 5px #888888;
}

.carList {
  position: absolute;
  left: 20px;
  cursor: pointer;
}

.mapList {
  position: absolute;
  right: 40px;
  cursor: pointer;
}

.video-down {
  display: flex;
  width: 100%;
  height: calc(100% - @videoSelectHeight);
}

.video-left {
  width: @selectComponentContainerWidth;
  padding: 5px;
}

.video-right {
  width: @selectComponentContainerWidth;
  padding: 5px;
}

.video-middle {
  flex: auto;
}

.videoPlayer-container-allopen {
  position: absolute;
  height: calc(100% - @videoSelectHeight);
  width: 100%;
  bottom: 1px;
}

.videoPlayer-container-half {
  position: absolute;
  height: calc(100% - @videoSelectHeight);
  // width: calc(100% - 1 * @selectComponentContainerWidth - 2 * @xhSpacingBase);
  width: 100%;
  bottom: 1px;
}

.videoPlayer-container-allclose {
  position: absolute;
  height: calc(100% - @videoSelectHeight);
  width: calc(100% - 0 * @selectComponentContainerWidth - 2 * @xhSpacingBase);
  bottom: 1px;
}

.video-mode-switch-button {
  margin-right: @xhSpacingBase;
  background-color: @xhUIColorMain;
  border-radius: @xhBorderRadiusBase;
  color: @xhTextColor4;
  padding: @xhSpacingBase;
  cursor: pointer;
  text-align: center;
  line-height: @xhLineHigh1;
  height: @xhLineHigh1;
  width: @xhLineHigh1;
}

.video-mode-switch-button-active {
  background-color: @xhUIColorSuccess;
}

.box-card {
  position: absolute;
  z-index: 100000;
  height: 300px;
  width: 270px;
  left: 15px;
  bottom: 135px;
}

.intercom-container {
  z-index: 100000;
  height: 300px;
  width: 270px;
}

.el-card /deep/ .el-card__header {
  padding: 1px;
}

.el-card /deep/ .el-card__body {
  padding: 1px;
}

.video-option-switch-close, .video-option-switch-pass {
  position: absolute;
  left: 110px;
  top: 0;
  line-height: calc(1 * @videoSelectHeight - 10px);
  height: calc(1 * @videoSelectHeight - 10px);
  width: 100px;
  margin: 5px;
}

.video-option-set-time {
  position: absolute;
  left: 220px;
  top: 0;
  line-height: calc(1 * @videoSelectHeight - 10px);
  height: calc(1 * @videoSelectHeight - 10px);
  width: 100px;
  margin: 5px;
}

.video-option-switch-close {
  left: 0;
  border: 1px solid #dcdfe6;
  text-align: center;
  cursor: pointer;
  background-color: #ffffff;
  border-radius: 4px;
}

.video-option-switch-close:hover {
  border: 1px solid #c0c4cc;
}

.video-config {
  height: 40px;
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}

.video-config-left {
  color: #838383;
  display: flex;
  align-items: center;

  /deep/ .el-select {
    width: 90px;

    .el-input__inner {
      height: 32px;
      line-height: 32px;
      border: 1px solid #787878;
      border-radius: 0;
    }

    .el-input__icon {
      line-height: 32px;
    }
  }
}

.video-config-close {
  display: inline-block;
  width: 90px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border: 1px solid #787878;
  cursor: pointer;
}

.video-config-pass {
  margin: 0 7px;
}

.video-config-right {
  color: #1d6dcf;
  display: flex;
  align-items: center;

  /deep/ .el-select {
    width: 90px;

    .el-input__inner {
      height: 32px;
      line-height: 32px;
      border: 1px solid #1d6dcf;
      border-radius: 0;
    }

    .el-input__icon {
      line-height: 32px;
    }
  }
}

.video-config-btn {
  text-align: center;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border: 1px solid #1d6dcf;
  margin-left: 5px;
  cursor: pointer;
}
</style>
