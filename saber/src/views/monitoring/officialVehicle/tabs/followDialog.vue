<template>
  <div class="follow-container">
    <div class="head-title">
      跟踪车辆列表
    </div>
    <div
      v-for="(item, index) in followCar"
      :key="index"
      class="follow-item"
    >
      <div>
        <span class="follow-item-label">{{ item.targetName }}</span>
        <i
          class="el-icon-error follow-item-icon"
          @click="handleCease(item)"
        />
      </div>
    </div>
    <div
      class="close-btn"
      @click="followCloseAll"
    >
      <i class="el-icon-error"/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FollowDialog',
  props: {
    followCar: {
      type: Array,
      default: ()=>{ return []; }
    }
  },
  data() {
    return{

    };
  },
  methods: {
    handleCease(data) {
      this.$emit('setFollowClose', data);
    },
    followCloseAll() {
      this.$emit('followCloseAll');
    },
  }
};
</script>

<style lang="less" scoped>
.follow-container{
    padding: 8px 15px;
    background-color: #E1E5EE;
    border: 1px solid #bfbfbf;
    position: relative;
}
.head-title{
    font-size: 16px;
    color: #3f97f3;
}
.follow-item-label{
    padding-right: 10px;
    width: 20px;
}
.follow-item-icon{
    font-size: 15px;
    cursor: pointer;
    color: #3F97F3;
}
.close-btn{
    position: absolute;
    top: -17px;
    right: -11px;
    font-size: 22px;
    color: #3F97F3;
    cursor: pointer;
}
</style>