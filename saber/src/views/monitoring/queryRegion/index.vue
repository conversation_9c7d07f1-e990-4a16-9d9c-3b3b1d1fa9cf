<template>
  <div class="back_box">
    <section
      v-show="showCarList"
      ref="left"
      class="left"
    >
      <RegionList
        ref="regionList"
        :region-name-option="regionNameOption"
        :show-draw="showDraw"
        @addNewRegion="addNewRegion"
        @removeRegions="removeRegions"
        @drawCarMarkers="drawCarMarkers"
        @clearMarkers="clearMarkers"
        @trajectory="trajectory"
        @toPoint="toPoint"
      />
    </section>
    <div
      v-drag
      class="collapse-vertical"
    >
      <span
        class="collapse-btn"
        @click="showCarList = !showCarList"
      >
        <i :class="`el-icon-arrow-${showCarList ? 'left':'right'}`"/>
      </span>
    </div>
    <section class="right">
      <div
        class="map-container chunk"
      >
        <MapWidget
          ref="MapWidget"
          :map-show-fold="false"
          :map-show-zone-tool="false"
          :from-query-region="true"
          :tool-config="toolConfig"
          @customEvent="editRowChange"
        />
      </div>
    </section>
  </div>
</template>
<script>
import MapWidget from '@/components/map/MapWidgetAMap';
import RegionList from './module/regionList.vue';
import {pagination as getVehiclestates} from '@/api/monitoring/info.js';
import { queryTracking } from '@/api/monitoring/track.js';
import { parseTime } from '@/api/utils/share';
import {getCarStatus} from '@/utils/getCarStatus';

export default {
  name: 'QueryRegion',
  components: {
    MapWidget: MapWidget,
    RegionList
  },
  directives: {
    drag (el, bindings, vnode) { // 横向拖拽
      el.onmousedown = function (e) {
        let x = e.clientX;
        let xel = vnode.context.$refs.left;
        let xelDefault = xel.style.width || '350px';
        xelDefault = +xelDefault.substring(0, xelDefault.length - 2);
        xelDefault = xelDefault > 600 ? 600 : xelDefault;
        xelDefault = xelDefault < 350 ? 350 : xelDefault;
        document.onmousemove = function (res) {
          let ylong = xelDefault + res.clientX - x;
          ylong = ylong > 600 ? 600 : ylong;
          ylong = ylong < 350 ? 350 : ylong;
          xel.style.width = ylong + 'px';
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      };
    }
  },
  data () {
    return {
      selectedCar: [],
      CarMarkers: [],
      AMap: null,
      map: null,
      mousetool: null,
      index: 0,
      regionNameOption: [],
      text: '',
      showDraw: true,
      trackPointGroups: null,
      trackPointGroupsSub: null,
      marker: null,
      polyline: null,
      signalGroups: null,
      timeSpeedGroups: null,
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: true, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: true, // 路况
        layerSelectShow: true, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: true // 工具栏
      }, // 控制工具按钮
      showCarList: true,
      markerList: []
    };
  },
  methods: {
    editRowChange(data) {
      this.$refs['regionList'].editRowChange(data);
    },
    // 视角移动marker
    toPoint(row) {
      const { data } = row;
      this.map.setZoomAndCenter(18, [data.longitude, data.latitude]);
      const marker = this.markerList.find(item => item.vehicleId === data.vehicleId);
      if (marker) {
        this.$refs.MapWidget.$refs.CarMarkerManage.editVehicleData(marker);
      }
    },
    initMap () {
      let mapObject = this.$refs.MapWidget.getMapInit();
      this.AMap = mapObject.AMap;
      this.map = mapObject.map;
      this.mousetool = mapObject.mousetool;
    },
    /**
     * 绘制轨迹数据
     */
    trajectory (data) {
      if (this.polyline) {
        this.map.remove(this.polyline);
        this.map.remove(this.trackPointGroups);
        this.map.remove(this.trackPointGroupsSub);
        this.map.remove(this.timeSpeedGroups);
        this.map.remove(this.marker);
        this.clearSignalPointText();
      }
      let parme = {
        licencePlate: data.licencePlate,
        licenceColor: data.licenceColor,
        startTime: this.$moment(data.startTime).valueOf() / 1000,
        endTime: this.$moment(data.endTime).valueOf() / 1000,
        is_filter: 0
      };
      queryTracking(parme).then(res => { // 查询绘制轨迹
        const data = res.data || [];
        let coors = this.$refs.MapWidget.formatPathData(data);
        this.polyline = this.$refs.MapWidget.getPath(coors);
        this.map.add(this.polyline);
        this.setTrackPoint(data);
        this.setTimeSpeed(data);
        let status = getCarStatus();
        this.marker = this.$refs.MapWidget.getTrackCarMarker({
          status: status[5 + data[0].running],
          position: coors[0],
          label: data[0].licencePlate,
          angle: data[0].bearing
        });
        this.map.add(this.marker);
      });
    },
    /**
     * 时间速度加载
     */
    setTimeSpeed (currentData) {
      this.timeSpeedGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: true
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let timeSpeedText = new this.AMap.LabelMarker({
          position: position,
          text: {
            content: this.parseTime(currentData[i].locTime) + ', ' + currentData[i].speed + 'km/h',
            direction: 'right',
            offset: [6, -18],
            style: {
              'fontSize': 12,
              'backgroundColor': 'rgba(255, 255, 255, 0.1)',
              'fillColor': '#036eb8',
              'strokeColor': 'white',
              'strokeWidth': 4
            }
          }
        });
        this.timeSpeedGroups.add(timeSpeedText);
      }
      this.map.add(this.timeSpeedGroups);
    },
    /**
     * 定位点加载
     */
    setTrackPoint (currentData) {
      this.trackPointGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: true
      });
      this.trackPointGroupsSub = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      [0, currentData.length - 1].forEach((i, index) => {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyleSub(index === 0 ? 's' : 'e');
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [28, 36]
          }
        });
        pointMarker.on('click', (e) => {
          this.setSinglePointText([currentData[i]]);
        });
        this.trackPointGroupsSub.add(pointMarker);
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = require('@/assets/images/car/track-run.png');
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [11, 12]
          }
        });
        pointMarker.on('click', (e) => {
          // 处理定位
          // this.setSinglePointText([currentData[i]]);
        });
        this.trackPointGroups.add(pointMarker);
      }
      this.map.add(this.trackPointGroups);
      this.map.add(this.trackPointGroupsSub);
      this.$refs.MapWidget.setFitView();
    },
    setSinglePointText (currentData) {
      this.clearSignalPointText();
      let markerCollection = [];
      let infoWindow = null;
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        var info = [];
        info.push("<div class='input-card content-window-card' style='width: 435px;>");
        info.push("<div style='padding:7px 0px 15px 0px;><h4>" + `${currentData[i].licencePlate}` + '</h4>');
        info.push("<p class='input-item' style='margin: 10px'>定位信息：" + `${currentData[i].speed}` + 'km/h,' + `${currentData[i].locAccuracy}` + ',' + `${this.getDirection(currentData[i].bearing)}` + ',' + `${currentData[i].longitude}` + ',' + `${currentData[i].latitude}`);
        info.push("<p class='input-item' style='margin: 10px'>总里程：" + `${currentData[i].mileage}` + 'km');
        info.push("<p class='input-item' style='margin: 10px'>状态：" + `${currentData[i].locStateStr}`);
        info.push("<p class='input-item' style='margin: 10px'>定位时间：" + `${this.parseTime(currentData[i].locTime)}`);
        info.push("<p class='input-item' style='margin: 10px'>地址：" + `${currentData[i].locAddr}`);
        infoWindow = new this.AMap.InfoWindow({
          position: position,
          anchor: 'bottom-center',
          content: info.join(''),
          offset: new this.AMap.Pixel(0, -30)
        });
        markerCollection.push([infoWindow]);
      }
      this.signalGroups = new this.AMap.OverlayGroup(markerCollection);
      this.map.add(this.signalGroups);
    },
    getIconStyleSub (i) {
      let icon;
      if (i === 's') {
        icon = require('@/assets/images/car/track-start.png');
      } else if (i === 'e') {
        icon = require('@/assets/images/car/track-end.png');
      }
      return icon;
    },
    clearSignalPointText () {
      if (this.signalGroups) {
        this.signalGroups.clearOverlays();
        this.map.remove(this.signalGroups);
        this.signalGroups = null;
      }
    },
    /**
     * 获取车辆列表数据
     */
    selectedCarInfo (val) {
      // 删除地图中车辆图标
      this.clearAll();
      this.selectedCar.push(val.licencePlate);
      // 地图中车辆定位
      this.showCarMarker(val);
    },
    /**
     * 进行地图中车辆定位
     */
    showCarMarker (carInfo) {
      this.$refs.MapWidget.setMarkerFromVideo(carInfo);
    },
    /**
     *删除地图中车辆图标
     */
    clearAll () {
      this.$refs.MapWidget.deleteMarkerFromVideo();
      this.selectedCar = [];
    },
    createText (position) {
      var text = new this.AMap.Text({
        position: position,
        anchor: 'bottom-center',
        text: this.text,
        style: {'background-color': 'red'}
      });
      this.map.add(text);
    },
    getDirection (bearing) {
      if (bearing === 0) {
        return '正北';
      }
      if (bearing) {
        switch (true) {
        case bearing === 90:
          return '正东';
        case bearing === 180:
          return '正南';
        case bearing === 270:
          return '正西';
        case bearing > 0 && bearing < 90:
          return '东北';
        case bearing > 90 && bearing < 180:
          return '东南';
        case bearing > 180 && bearing < 270:
          return '西南';
        case bearing > 270 && bearing < 360:
          return '西北';
        default:
          return '无';
        }
      }
    },
    addNewRegion (type) {
      this.flag = true;
      this.initMap();
      if (type === 'rectangle') {
        // 用鼠标工具画矩形
        this.mousetool.rectangle();
        // 添加事件
        this.mousetool.on('draw', this.handleRectangleDraw);
      }else {
        // 用鼠标工具画圆形
        this.mousetool.circle();
        // 添加事件
        this.mousetool.on('draw', this.handleCircleDraw);
      }

    },
    handleRectangleDraw (e) {
      this.mousetool.close();
      // 用于记录绘制的矩形数据
      this.index++;
      // this.text = '自定义区域' + this.index;
      let regionData = {
        value: [e.obj.getBounds().getNorthEast(), e.obj.getBounds().getSouthWest()]
      };
      // for (let i = 0; i < this.regionNameOption.length; i++) {
      //   if (JSON.stringify(this.regionNameOption[i].value[0]) === JSON.stringify(regionData.value[0]) &&
      // JSON.stringify(this.regionNameOption[i].value[1]) === JSON.stringify(regionData.value[1])) {
      //     this.flag = false;
      //     return;
      //   }
      // }
      if (this.flag) {
        this.regionNameOption.push(regionData);
        // this.createText(e.obj.getBounds().getCenter());
        this.mousetool.off('draw', this.handleRectangleDraw);
      }
      this.showDraw = false;
      this.$refs['regionList'].queryCars();
    },
    handleCircleDraw (e) {
      this.mousetool.close();
      // 用于记录绘制的圆形数据
      this.index++;
      let circleParam = e.obj.getCenter();
      let regionData = {
        value: {
          point: {
            longitude: circleParam.lng,
            latitude: circleParam.lat
          },
          radius: e.obj.getRadius()
        }
      };
      this.regionNameOption.push(regionData);
      this.mousetool.off('draw', this.handleCircleDraw);
      this.showDraw = false;
      this.$refs['regionList'].queryCars();
    },
    removeRegions () {
      this.map.clearMap();
      this.index = 0;
      this.regionNameOption = [];
      this.$refs.MapWidget.clearClustererMarkers();
      this.$refs.MapWidget.$refs.CarMarkerManage.closeDialog();
      this.showDraw = true;
      if (this.polyline) {
        this.map.remove(this.polyline);
        this.map.remove(this.trackPointGroups);
        this.map.remove(this.trackPointGroupsSub);
        this.map.remove(this.timeSpeedGroups);
        this.clearSignalPointText();
      }
    },
    drawCarMarkers (carsInMap) {
      console.log(carsInMap);
      let vehicles = [];
      this.clearMarkers();
      carsInMap.forEach(item => {
        vehicles.push(item.vehicleId);
      });
      getVehiclestates({ids: vehicles}).then(res=>{
        const { content } = res.data;
        this.markerList = content;
        this.$refs.MapWidget.drawMarkers(this.markerList);
      });
    },
    clearMarkers () {
      this.$refs.MapWidget.clearAll();
      if (this.polyline) {
        this.map.remove(this.polyline);
        this.map.remove(this.trackPointGroups);
        this.map.remove(this.trackPointGroupsSub);
        this.map.remove(this.timeSpeedGroups);
        this.clearSignalPointText();
      }
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
  .back_box {
    width: 100%;
    height: 100%;
    padding: 0 4px 4px;
    display: flex;

    @space: 4px;
    @collapseBtnSize: 90px;
    @sideSectionWidth: 350px;

    .collapse-btn-base {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      cursor: pointer;
      background-color: #b0b3b8;
      color: #ffffff;

      i {
        font-weight: bold;
      }
    }

  .collapse-vertical {
    width: @space;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: e-resize;
    .collapse-btn {
      width: 100%;
      height: @collapseBtnSize;
      .collapse-btn-base
    }
  }

  .collapse {
    height: @space;
    display: flex;
    justify-content: center;

    .collapse-btn {
      height: 100%;
      width: @collapseBtnSize;
      overflow: hidden;
      margin: 0 10px;
      .collapse-btn-base
    }
  }

  section {
    .chunk {
      border: 1px solid #e1e5e8;
      background-color: #ffffff;
    }
  }

  .left {
    width: @sideSectionWidth;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    flex-shrink: 0;
    background-color: #FFFFFF;
  }

  .right {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .map-container {
      flex: 1;
    }
  }

}
</style>
