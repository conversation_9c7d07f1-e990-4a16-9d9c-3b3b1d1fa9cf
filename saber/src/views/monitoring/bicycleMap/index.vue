<template>
  <div class="page-container">
    <div class="top row">
      <div class="top-left-section">
        <el-form
          label-width="auto"
          :model="formInline"
          inline
          size="small"
          style="padding: 10px 0 0 10px;"
        >
          <el-form-item label="选择终端:">
            <!-- <TerminalSingleFilterTree
              ref="terminalSingleFilterTree"
              v-model="formInline.terminal"
              :is-filter="true"
              class="terminal-tree"
            /> -->
            <TerminalFilterSelect
              ref="terminalFilterSelect"
              v-model="formInline.terminal"
              :is-track="true"
              class="terminal-tree"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              :loading="btnLoading"
              type="primary"
              size="small"
              style="position:relative;top: -1px"
              @click="onSubmit"
            >查询
            </el-button>
          </el-form-item>
        </el-form>
        <div class="line"/>
        <el-scrollbar style="height: calc(100% - 50px)">
          <div class="info-main">
            <TableTitleSlot title="监控对象信息"/>
            <el-descriptions
              column="1"
              class="info-container"
              label-class-name="info-label"
            >
              <el-descriptions-item label="监控对象">
                <span style="color: #409efe">{{ baseInfo.targetName | nullValueStr }}</span></el-descriptions-item>
              <el-descriptions-item
                label="所属机构"
                :span="24"
              >{{
                baseInfo.deptName | nullValueStr
              }}
              </el-descriptions-item>

              <el-descriptions-item label="目标类型">
                {{ getEnumDictLabel('targetType', baseInfo.targetType)| nullValueStr }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="line"/>
          <div class="info-main">
            <TableTitleSlot title="终端实时运行数据"/>
            <el-descriptions
              column="1"
              class="info-container"
              label-class-name="info-label"
            >
              <el-descriptions-item label="速度">{{
                (baseInfo.speed === 0 || baseInfo.speed) && baseInfo.speed + 'km/h' | nullValueStr
              }}
              </el-descriptions-item>
              <el-descriptions-item
                label="终端状态"
                :span="24"
              >{{
                baseInfo.terminalState | nullValueStr
              }}
              </el-descriptions-item>
              <el-descriptions-item label="经度">
                {{ baseInfo.longitude ? handlePosition(baseInfo.longitude) : nullValueStr }}
              </el-descriptions-item>
              <el-descriptions-item label="纬度">
                {{ baseInfo.latitude ?handlePosition(baseInfo.latitude) : nullValueStr }}
              </el-descriptions-item>
              <el-descriptions-item
                :span="24"
                label="位置"
              >
                <el-tooltip :content="baseInfo.position">
                  <span class="address-content">{{ baseInfo.position | nullValueStr }}</span>
                </el-tooltip>
              </el-descriptions-item>
              <el-descriptions-item label="上报时间">{{ getReportTime }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-scrollbar>
      </div>
      <div class="top-right-section">
        <div class="trace-btn">
          <el-checkbox
            v-model="isTrace"
            :disabled="!marker"
          >跟踪终端</el-checkbox>
        </div>
        <div id="container"/>
      </div>
    </div>
    <div class="bottom row">
      <div class="bottom-l-section">
        <TableTitleSlot title="实时速度情况 (Km/h)"/>
        <chartsBox
          v-if="alarmOptions.series[0].data.length > 0"
          style="width: 100%; height: calc(100% - 40px);"
          class="chart-container"
          :options="alarmOptions"
          :lazyUpdate="true"
          :onlyUpDateSeries="true"
        />
        <el-empty
          v-if="alarmOptions.series[0].data.length === 0"
          :image="require('@/assets/images/nodata.png')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getAuthCode, trackcontrol } from '@/api/monitoring/bicycleMap';
import chartsBox from '@/components/chartsBox';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
import { debounce } from '@/api/utils/share';
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';
import { dateFormat } from '@/util/date';
import AMapUtil from '@/components/map/AMapUtil';
import { vehicleAddress, terminalStates } from '@/api/monitoring/info.js';
import { getWebsocketParam } from '@/api/user';
// import TerminalSingleFilterTree from '@/components/vehicleFilterTree/terminalSingleFilterTree.vue';
import TerminalFilterSelect from '@/components/vehicleFilterTree/terminalFilterSelect.vue';
import jsonToHump from '@/utils/helper/jsonToHump';
import JSONbig from 'json-bigint';

export default {
  name: 'BicycleMap',
  dicts: [
    'targetType'
  ],
  components: {
    TableTitleSlot,
    chartsBox,
    // TerminalSingleFilterTree,
    TerminalFilterSelect
  },
  data() {
    return {
      AMap: null,
      map: null,
      formInline: {
        terminal: ''
      },
      options: [],
      baseInfo: {},
      alarmOptions: {
        animation: false,
        grid: {
          top: 15,
          left: 50,
          right: 40,
          bottom: 40
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          max: 180,
          min: 0,
          splitNumber: 8
        },
        tooltip: {
          trigger: 'axis'
        },
        series: [
          {
            type: 'line',
            connectNulls: false,
            smooth: true,
            data: []
          }
        ]
      },
      lineArr: [],
      marker: null,
      passedPolyline: null,
      maxChartsNum: 100,
      isTrace: false,
      sendInterval: null,
      wsTimeout: null,
      wsVehicleStateTimeout: null,
      wsMessageBox: null,
      vehicleStateArr: ['离线', '静止', '移动'],
      labelTypeData: {},
      btnLoading: false
    };
  },
  computed: {
    getReportTime() {
      if (!this.baseInfo.locTime) {
        return '-';
      } else {
        return dateFormat(new Date(this.baseInfo.locTime * 1000));
      }
    }
  },
  watch: {
    isTrace(val) {
      if (val) {
        this.initWebSocket();
        this.isInitWS = true;
        this.passedPolyline = new this.AMap.Polyline({
          map: this.map,
          showDir: true,
          strokeColor: '#2288ff', //线颜色
          strokeWeight: 6 //线宽
        });
      } else {
        this.getTrackControl(0);
        this.closeWebsocket();
        // this.clearSendInterval()
        if (this.passedPolyline) {
          this.map.remove(this.passedPolyline);
          this.lineArr = [];
        }
      }
    }
  },
  activated () {
    this.mapReload();
  },
  mounted() {
    this.initMap();
    this.alarmOptions.dataZoom = [
      {
        type: 'slider',
        start: 0, //默认开始位置（百分比）
        end: 100
      }
    ];
  },
  deactivated() {
    this.isTrace = false;
    this.wsMessageBox?.close();
  },
  methods: {
    mapReload () {
      let canvas = document.getElementsByClassName('amap-layer')[0];
      if (canvas) {
        console.log(canvas); // 打印绘制的canvas
        let canvasContent = canvas.getContext('webgl'); // 因为高德地图是通过webGl来绘制的，所以通过getContext(‘webgl’)才能获取到内容
        console.log(canvasContent);
        if (canvasContent?.drawingBufferHeight < 10 && canvasContent?.drawingBufferWidth < 10) {
          this.initMap();
          this.isTrace = false;
          this.wsMessageBox?.close();
        }
      }
    },
    // 逆地理编码
    async getAddress() {
      const query = {
        lon: Number(this.baseInfo.longitude),
        lat: Number(this.baseInfo.latitude)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          this.$set(this.baseInfo, 'position', res.data);
        }
      });
    },
    initMap() {
      AMapUtil.loadAMap(AMap => {
        this.AMap = AMap;
        this.$nextTick(() => {
          this.map = new AMap.Map('container', {
            //设置地图容器id
            viewMode: '3D', //是否为3D地图模式
            zoom: 8, //初始化地图级别
            zooms: [2, 26],
            center: [
              105.602725,
              37.076636
            ] //初始化地图中心点位置
          });
          this.AMap.plugin([
            'AMap.Driving',
            'AMap.MoveAnimation',
            'AMap.Scale',
            'AMap.ToolBar'
          ], () => {
            const scale = new AMap.Scale({
              visible: true
            });
            const toolBar = new AMap.ToolBar({
              visible: true
            });
            this.map.addControl(scale);
            this.map.addControl(toolBar);
          });
        });
      });
    },
    onSubmit() {
      if (!this.formInline.terminal) {
        this.$message.error('请选择要查询的终端');
        return false;
      }
      this.isTrace = false;
      if (this.marker) {
        this.map.remove(this.marker);
      }
      this.getVehicleStates();
      this.alarmOptions.series[0].data = [];
      this.alarmOptions.xAxis.data = [];
    },
    // 临时位置跟踪控制
    async getTrackControl(duration = 3) {
      const params = {
        deviceId: BigInt(this.baseInfo.deviceId),
        deviceType: this.baseInfo.deviceType,
        duration,
        expire: 5 * 60 * 60 // 取消5分钟时间限制, 暂时定为5小时
      };
      const {
        code,
        data
      } = await trackcontrol(params);
      if (code === 200) {
        this.labelTypeData.bgUrl = '/bdsplatform/static/images/pic/static.png';
        const content = `<div style="position: relative;">
                            <div class="follow-marker-bg" style="position: absolute; width: 100%; height: 100%; background-image: url(${this.labelTypeData.bgUrl}); background-size: 100%;"></div>
                            <img src="${this.labelTypeData.iconUrl}" style="display: block; width: ${this.labelTypeData.iconWidth}px; height: ${this.labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                          </div>`;
        this.marker.setContent(content);
        console.log('-> 开启临时位置跟踪', data);
      }
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon (val) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/vehicle.png`; // 车辆
      } else if (materialsModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/materials.png`; // 物资
      } else if (personnelModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/personnel.png`; // 人员
      } else if (shortMessageModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/shortMessage.png`; // 短报文终端
      } else if (timeServiceModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/timeService.png`; // 授时终端
      } else if (monitorModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/monitor.png`; // 监测终端
      } else if (val.treeCategory === '0') {
        vehicleIcon = `/bdsplatform/static/images/pic/other.png`; // 其他
      }
      return vehicleIcon;
    },
    judgeBackgroundIcon (val) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/offline.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/static.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/move.png`;
        break;
      }
      return vehicleIcon;
    },
    // 多车辆实施状态
    async getVehicleStates() {
      const params = {
        deviceIds: [BigInt(this.formInline.terminal.deviceId)]
      };
      this.btnLoading = true;
      const {
        code,
        data
      } = await terminalStates(params);
      this.btnLoading = false;
      if (code == 200 && data) {
        this.alarmOptions.series[0].data = [];
        let terminalObj = data?.content[0] || {};
        const locationData = this.$utils.wgs84togcj02(terminalObj.longitude, terminalObj.latitude);
        this.baseInfo = {
          ...terminalObj,
          treeCategory: String(terminalObj.deviceCategory),
          fusionState: terminalObj.status,
          locTime: terminalObj.time,
          deviceUniqueId: terminalObj.uniqueId,
          longitudeGcj: locationData[0],
          latitudeGcj: locationData[1]
        };
        // 0-离线 1-静止 2-移动
        this.baseInfo.terminalState = this.vehicleStateArr[this.baseInfo.fusionState];
        this.getAddress();
        if (this.passedPolyline) {
          this.map.remove(this.passedPolyline);
        }
        if (this.baseInfo.longitudeGcj && this.baseInfo.latitudeGcj) {
          this.createMarker([
            this.baseInfo.longitudeGcj,
            this.baseInfo.latitudeGcj
          ]);
          this.map.setZoomAndCenter(17, [
            this.baseInfo.longitudeGcj,
            this.baseInfo.latitudeGcj
          ]);
        } else {
          this.$notify({
            title: `本车辆没有定位点`,
            type: 'warning'
          });
        }
      }
    },
    initWebSocket() {
      if (typeof WebSocket === 'undefined') {
        console.log('您的浏览器不支持WebSocket');
      }
      this.getTrackControl();
      getAuthCode().then(res => {
        const socketCode = res.data;
        // const wsLocation = process.env.NODE_ENV === 'development' ? '**********:20845' : '**********:20845'
        getWebsocketParam().then(res => {
          const wsLocation = res.data.data;
          const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
          const wsUrl = `${protocol}${wsLocation}/ws/locationTrack/push/${socketCode}`;
          this.websock = new ReconnectingWebSocket(wsUrl);
          this.websock.onopen = () => {
            console.log('成功');
            const query = {
              device_type: this.baseInfo.deviceType,
              device_id: this.baseInfo.deviceId
            };
            this.websock.send(JSON.stringify(query));
          };
          this.websock.onmessage = (e) => {
            console.log('-> 单车跟踪ws推送数据', JSON.parse(e.data));
            if (this.wsTimeout) {
              clearTimeout(this.wsTimeout);
              this.wsTimeout = null;
            }
            if (this.wsVehicleStateTimeout) {
              clearTimeout(this.wsVehicleStateTimeout);
              this.wsVehicleStateTimeout = null;
            }
            this.wsVehicleStateTimeout = setTimeout(() => {
              console.log('35秒未推送设置静止');
              this.baseInfo.terminalState = '静止';
              this.baseInfo.speed = 0;
            }, 35 * 1000);
            this.wsTimeout = setTimeout(() => {
              this.wsMessageBox = this.$message({
                showClose: true,
                message: '数据传输已断开, 请重新跟踪',
                type: 'warning',
                duration: 0
              });
              this.isTrace = false;
            }, 60 * 1000);
            const data = jsonToHump(JSONbig({ storeAsString: true }).parse(e.data));
            const dStr = `${this.baseInfo.deviceId}-${this.baseInfo.deviceType}`;
            const wsStr = `${data.deviceId}-${data.deviceType}`;
            if(dStr !== wsStr) {
              return false;
            }
            const locationData = this.$utils.wgs84togcj02(data.longitude, data.latitude);
            data.longitudeGcj = locationData[0];
            data.latitudeGcj = locationData[1];
            const position = [
              data.longitudeGcj,
              data.latitudeGcj
            ];
            this.baseInfo.terminalState = data.speed ? '移动' : '静止';
            if (this.isInitWS) {
              this.isInitWS = false;
              this.map.setCenter(position);
            }
            this.lineArr.push(position);
            this.debounceDraw(data);
            this.labelTypeData.bgUrl = data.speed ? '/bdsplatform/static/images/pic/move.png' : '/bdsplatform/static/images/pic/static.png';
            const content = `<div style="position: relative;">
                            <div class="follow-marker-bg" style="position: absolute; width: 100%; height: 100%; background-image: url(${this.labelTypeData.bgUrl}); background-size: 100%;"></div>
                            <img src="${this.labelTypeData.iconUrl}" style="display: block; width: ${this.labelTypeData.iconWidth}px; height: ${this.labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                          </div>`;
            this.marker.setContent(content);
          };
          this.websock.onerror = () => {
            console.log('数据传输已断开, 正在尝试重新连接');
            // this.$message.error('数据传输已断开, 正在尝试重新连接');
          };
        });
      });
      this.$once('hook:deactivated', () => {
        this.closeWebsocket();
      });
    },
    closeWebsocket(resetMap = false) {
      this.websock?.close();
      if (this.wsTimeout) {
        clearTimeout(this.wsTimeout);
        this.wsTimeout = null;
      }
      if (resetMap && this.marker) {
        this.map.remove(this.marker);
      }
    },
    // 每一次获取到新数据 更新lineArr并且绘制轨迹
    debounceDraw: debounce(function (data) {
      if (this.lineArr.length === 1) {
        this.marker.setPosition([
          Number(data.longitudeGcj),
          Number(data.latitudeGcj)
        ]);
      }
      // this.checkSetMapPosition(data)
      const cachePosition = this.baseInfo.position;
      this.baseInfo = {
        ...this.baseInfo,
        longitude: data.longitude,
        latitude: data.latitude,
        speed: Number(data.speed).toFixed(2),
        position: cachePosition,
        locTime: data.locTime || data.recvTime
      };
      this.getAddress();
      const notEmptyList = this.alarmOptions.series[0].data.filter(item => item !== '');
      const notEmptyXList = this.alarmOptions.xAxis.data.filter(item => item !== '');
      if (notEmptyList.length < this.maxChartsNum) {
        const placeholderArr = new Array(this.maxChartsNum - notEmptyList.length - 1).fill('');
        notEmptyList.push(Number(data.speed).toFixed(2));
        notEmptyXList.push(this.$dayjs(this.baseInfo.locTime * 1000).format('HH:mm:ss'));
        this.alarmOptions.series[0].data = notEmptyList.concat(placeholderArr);
        this.alarmOptions.xAxis.data = notEmptyXList.concat(placeholderArr);
      }
      else {
        this.alarmOptions.series[0].data.push(Number(data.speed).toFixed(2));
        this.alarmOptions.xAxis.data.push(this.$dayjs(this.baseInfo.locTime * 1000).format('HH:mm:ss'));
      }
      if (this.lineArr.length >= 2 && this.isTrace) {
        this.debounceMove(data);
      }
    }, 500, true),
    debounceMove: debounce(function (data) {
      this.marker.moveAlong(
        [
          this.lineArr[this.lineArr.length - 2],
          this.lineArr[this.lineArr.length - 1]
        ],
        {
          duration: 3000,
          autoRotation: true
        }
      );
      this.checkSetMapPosition(data);
    }, 500, true),
    /**
     * 判断marker是否即将超出地图 改变地图中心点位置
     */
    checkSetMapPosition(data) {
      const bounds = this.map.getBounds();
      const NorthEast = bounds.getNorthEast();
      const SouthWest = bounds.getSouthWest();
      const SouthEast = [NorthEast.lng, SouthWest.lat];
      const NorthWest = [SouthWest.lng, NorthEast.lat];
      const path = [[NorthEast.lng, NorthEast.lat], SouthEast, [SouthWest.lng, SouthWest.lat], NorthWest];
      const isPointInRing = this.AMap.GeometryUtil.isPointInRing([data.longitudeGcj, data.latitudeGcj], path);
      if (!isPointInRing) {
        this.map.setCenter([
          data.longitudeGcj,
          data.latitudeGcj
        ]);
      }
    },
    createMarker(position) {
      this.labelTypeData = {
        iconUrl: this.judgeTerminalIcon(this.baseInfo),
        bgUrl: this.judgeBackgroundIcon(this.baseInfo),
        iconWidth: 50,
        iconHeight: 50
      };
      this.marker = new this.AMap.Marker({
        map: this.map,
        position,
        offset: new this.AMap.Pixel(-20, -20),
        content: `<div style="position: relative;">
                      <div class="follow-marker-bg" style="position: absolute; width: 100%; height: 100%; background-image: url(${this.labelTypeData.bgUrl}); background-size: 100%; transform: rotate(${this.baseInfo.bearing}deg);"></div>
                      <img src="${this.labelTypeData.iconUrl}" style="display: block; width: ${this.labelTypeData.iconWidth}px; height: ${this.labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`
      });
      this.marker.dom.classList.add("follow-marker");
      this.passedPolyline = new this.AMap.Polyline({
        map: this.map,
        showDir: true,
        strokeColor: '#2288ff', //线颜色
        strokeWeight: 6 //线宽
      });
      this.marker.on('moving', (e) => {
        const markerDom = e.target.dom;
        let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
        bgDom.style.transform = `rotate(${e.target['_style'].rotate}deg)`;
        // 假设lineArr有A点、B点、C点, 这里只是刚准备从B点走到C点, 此时不需要绘制到C点的轨迹线
        // 所以删除C点, 也就是数组最后一个经纬度
        let lineData = this.lineArr.slice(0, this.lineArr.length - 1);
        lineData.push([e.passedPath[1].lng, e.passedPath[1].lat]);
        this.passedPolyline.setPath(lineData);
      });
      this.map.setFitView([this.marker], true);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {number|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.page-container {
  // padding: 0 4px;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 1100px;

  @baseBorder: 1px solid #e1e5e8;
  @sectionPadding: 0;

  .base-content-border {
    border: @baseBorder;
    box-sizing: border-box;
  }

  .top, .bottom {
    & > [class$="-section"] {
      background-color: #ffffff;
      .base-content-border;
    }
  }

  .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
  }

  .line {
    width: 100%;
    height: 1px;
    border-bottom: @baseBorder;
  }

  .top {
    height: 70%;
    // min-height: 500px;

    .top-left-section {
      margin-right: 4px;
      width: 350px;
      padding: @sectionPadding;
      .terminal-tree {
        width: 180px;
        ::v-deep .input-decorate-content {
          line-height: 31px;
        }
      }

      .info-main {
        padding: 0 6px;
      }

      .info-container {
        padding: 0;
      }

      ::v-deep .info-label {
        width: 76px;
        justify-content: flex-end;
      }
    }

    .top-right-section {
      position: relative;

      .trace-btn {
        background-color: #ffffff;
        padding: 4px 10px;
        border: 1px solid #dadada;
        border-radius: 4px;
        position: absolute;
        top: 4px;
        left: 4px;
        z-index: 9999;
      }

      flex: 1;
    }
  }

  .bottom {
    height: calc(30% - 4px);
    // min-height: 200px;

    .bottom-l-section {
      flex: 1;
      padding: @sectionPadding @sectionPadding @sectionPadding 6px;

      .chart-container {
        width: 100%;
        height: 280px;
      }
    }
  }

  #container {
    height: 100%;
  }

  ::v-deep .amap-icon {
    position: absolute !important;
    overflow: inherit !important;
    opacity: 1;
    width: auto !important;
    height: auto !important;

    img {
      width: auto;
      height: auto;
    }
  }

  ::v-deep .el-form {
    .el-form-item {
      margin-bottom: 6px;
    }
    .el-form-item:last-child {
      margin-right: 0;
    }
  }

  ::v-deep .amap-icon img {
    transform: rotateZ(90deg);
  }
  .address-content {
    height: 40px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
.follow-marker {
    transform: translate(-25px, -25px) scale(1) !important;
  }
</style>
