<template>
  <el-dialog
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="110px"
    >
      <el-row span="24">
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('alarmType')"
            prop="alarmType"
          >
            <AlarmTypeSingle
              ref="AlarmTypeSingle"
              v-model="form.alarmType"
              :alarm-type="dict.alarmType"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('reminderWay')"
            prop="reminderWay"
          >
            <xh-select
              v-model="form.reminderWay"
              :placeholder="getPlaceholder('reminderWay')"
              clearable
            >
              <el-option
                v-for="item in dict.reminderWay"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('reminderWay')"
            prop="reminderWay"
          >
            <el-input v-model="form.alarmContent"/>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('alarmContent')"
            prop="alarmContent"
          >
            <el-input
              v-model="form.alarmContent"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('alarmContent')"
              type="textarea"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('alarmLevel')"
            prop="alarmLevel"
          >
            <el-radio-group
              v-model="form.alarmLevel"
            >
              <el-radio-button
                v-for="(item,index) in dict.alarmLevel"
                :key="index"
                :label="item.dictCode"
              >
                {{ item.dictName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('alarmSound')"
            prop="alarmSound"
          >
            <el-radio-group
              v-model="form.alarmSound"
            >
              <el-radio-button :label="true">
                是
              </el-radio-button>
              <el-radio-button :label="false">
                否
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isOpen')"
          >
            <el-radio-group
              v-model="form.isOpen"
            >
              <el-radio-button :label="true">
                是
              </el-radio-button>
              <el-radio-button :label="false">
                否
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('autoDeal')"
          >
            <el-radio-group
              v-model="form.autoDeal"
            >
              <el-radio-button :label="true">
                是
              </el-radio-button>
              <el-radio-button :label="false">
                否
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import AlarmTypeSingle from '@/components/addSelect/alarmTypeSingle.vue';

const defaultForm = {
  key: null,
  alarmType: null,
  reminderWay: null,
  alarmLevel: 1,
  alarmContent: '',
  autoDeal: false,
  alarmSound: false,
  isOpen: true
};
export default {
  components: { AlarmTypeSingle },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      depts: [],
      rules: {
        alarmType: { required: true, message: '请选择告警类型', trigger: 'change' },
        reminderWay: { required: true, message: '请选择提醒方式', trigger: 'change' },
        alarmContent: { required: true, message: '请输入告警播放内容', trigger: 'blur' }
      }
    };
  },
  methods: {
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /**
     * 提交前的验证
     */
    [CRUD.HOOK.afterValidateCU] () {
      // let form = this.form;
      // form.userId = this.$store.state.user.user.id;// 自动补充userId
      // form.autoDeal = false;// 强制改变
      return true;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('NormalAlarmSetting', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('NormalAlarmSetting', value);
    },
    /**
     * 关闭对话框时清除所有内容
     */
    closed () {
      this.form.alarmType = null;
      this.form.reminderWay = null;
      this.form.alarmContent = null;
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
