<template>
  <div class="alram-main">
    <TableTitleSlot title="持续中的告警" >
      <div slot="center">持续告警(条): <span class="strong-text">{{ tableData.length }}</span></div>
    </TableTitleSlot>
    <u-table
      ref="table"
      v-loading="tableLoading"
      use-virtual
      :header-cell-style="{background:'#f3f9ff','text-align':'center', 'font-weight': 600}"
      :data="tableData"
      :border="false"
      row-height="54"
      height="420"
      style="width: 100%"
    >
      <!--      <u-table-column-->
      <!--        type="index"-->
      <!--        label="#"-->
      <!--        width="50"-->
      <!--      />-->
      <u-table-column
        label="操作"
        min-width="50"
        fixed="right"
        :resizable="false"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="toDeal(scope.row)"
          >
            处理
          </el-button>
        </template>
      </u-table-column>
      <u-table-column
        prop="serverState"
        label="处理状态"
        min-width="80"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <div
            :title="getEnumDictLabel('serverState', scope.row.serverState)"
            class="xh-table-cell-same-line"
          >
            <el-tag
              :type="getStateType(scope.row.serverState)"
              effect="light"
              size="small"
            >
              {{ getEnumDictLabel('serverState', scope.row.serverState) }}
            </el-tag>
          </div>
        </template>
      </u-table-column>
      <u-table-column
        prop="alarmType"
        label="告警类型"
        min-width="150"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <div
            :title="getEnumDictLabel('alarmType', scope.row.alarmType)"
            class="xh-table-cell-same-line"
          >
            {{ getEnumDictLabel("alarmType", scope.row.alarmType) }}
          </div>
        </template>
      </u-table-column>
      <u-table-column
        prop="alarmLevel"
        label="告警等级"
        min-width="80"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <div>
            <el-tag
              :class="getLevelType(scope.row.alarmLevel)"
              effect="plain"
              size="small"
            >
              {{ getEnumDictLabel('alarmLevel', scope.row.alarmLevel) }}
            </el-tag>
          </div>
        </template>
      </u-table-column>
      <u-table-column
        prop="licencePlate"
        label="车牌号码"
        min-width="100"
        show-overflow-tooltip
        :resizable="false"
      />
      <u-table-column
        prop="licenceColor"
        label="车牌颜色"
        min-width="80"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <div
            :title="getEnumDictLabel('licenceColor', scope.row.licenceColor)"
            class="xh-table-cell-same-line"
          >
            {{ getEnumDictLabel("licenceColor", scope.row.licenceColor) }}
          </div>
        </template>
      </u-table-column>
      <u-table-column
        prop="deptFullName"
        label="企业名称"
        min-width="150"
        show-overflow-tooltip
        :resizable="false"
      />
      <u-table-column
        prop="speed"
        label="速度(km/h)"
        min-width="110"
        show-overflow-tooltip
        :resizable="false"
      />
      <u-table-column
        prop="maxSpeed"
        label="最高速度"
        min-width="80"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.maxSpeed ? scope.row.maxSpeed : '-' }}</span>
        </template>
      </u-table-column>
      <u-table-column
        prop="limitSpeed"
        label="限速"
        min-width="80"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.limitSpeed ? scope.row.limitSpeed : '-' }}</span>
        </template>
      </u-table-column>
      <u-table-column
        prop="alarmTime"
        label="开始时间"
        min-width="160"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <div class="table-date-td">
            {{ parseTime(scope.row.alarmTime) }}
          </div>
        </template>
      </u-table-column>
      <u-table-column
        prop="alarmEndTime"
        label="结束时间"
        min-width="160"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <div class="table-date-td">
            {{ parseTime(scope.row.alarmEndTime) }}
          </div>
        </template>
      </u-table-column>
      <u-table-column
        label="持续时间"
        prop="duration"
        min-width="100"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <span>{{ parseDuration(scope.row.alarmTime, scope.row.alarmEndTime) }}</span>
        </template>
      </u-table-column>
      <u-table-column
        prop="alarmAddress"
        label="开始地址"
        min-width="180"
        show-overflow-tooltip
        :resizable="false"
      />
      <u-table-column
        prop="alarmEndAddress"
        label="结束地址"
        min-width="180"
        show-overflow-tooltip
        :resizable="false"
      />
      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </u-table>
  </div>
</template>

<script>

import { sustainPagination } from '@/api/monitoring/realTimeMonitoring';
import { parseTime, parseDeptName } from '@/api/utils/share';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
export default {
  components: { TableTitleSlot },
  mixins: [],
  props: {
    dict: {
      type: Object,
      default: ()=>{ return {};}
    }
  },
  data () {
    return {
      tableData: [],
      tableLoading: false
    };
  },
  created () {
  },
  methods: {
    toDeal(data) {
      this.$emit('toDeal', data);
    },
    getStateType(val) {
      let str = '';
      switch (val) {
      case 0:
        str = 'danger';
        break;
      case 1:
        str = 'success';
        break;
      case 2:
        str = 'warning';
        break;
      }
      return str;
    },
    // 告警等级颜色
    getLevelType(val) {
      let str = '';
      switch (val) {
      case 0:
        str = 'table-alarmLevel-success';
        break;
      case 1:
        str = 'table-alarmLevel-default';
        break;
      case 2:
        str = 'table-alarmLevel-warning';
        break;
      case 3:
        str = 'table-alarmLevel-severity';
        break;
      case 4:
        str = 'table-alarmLevel-danger';
        break;
      case 5:
        str = 'table-alarmLevel-critical';
        break;
      }
      return str;
    },
    toQuery(data){
      let query = JSON.parse(JSON.stringify(data));
      this.tableLoading = true;
      sustainPagination(query).then(res=>{
        this.tableLoading = false;
        this.tableData = res.data;
      });
    },
    // 计算持续时间
    parseDuration (start, end) {
      const startTime = new Date(start * 1000);
      const endTime = new Date(end * 1000);
      // 计时间差（单位毫秒）
      const timeDiff = endTime - startTime;
      // 将时间差转换为天、小时、分钟秒
      const seconds = Math.floor(timeDiff / 1000) % 60;
      const minutes = Math.floor(timeDiff / (1000 * 60)) % 60;
      const hours = Math.floor(timeDiff / (1000 * 60 * 60)) % 24;
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      let str = '';
      if (days) str += days + '天';
      if (hours) str += hours + '时';
      if (minutes) str += minutes + '分';
      if (seconds) str += seconds + '秒';
      return str || '-';
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    parseTime,
    parseDeptName
  }
};
</script>
<style lang="less" scoped>
::v-deep .plTableBox .el-table td, ::v-deep .plTableBox .el-table th{
  padding: 8px 0;
}
.alram-main {
  padding: 4px 6px;
  border: 1px #E1E5E8 solid;
  background-color: #fff;
  height: 470px;
  ::v-deep .el-table td{
    height: 38px !important;
  }
}
.table-btn{
  width: 22px;
  height: 22px;
  padding: 0;
  ::v-deep .el-icon-edit{
    font-size: 16px;
  }
}
::v-deep .el-table__row {
  height: 37.5px !important;
}
</style>
