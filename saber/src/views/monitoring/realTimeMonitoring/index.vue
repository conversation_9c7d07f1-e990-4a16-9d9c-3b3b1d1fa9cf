<template>
  <div class="main">
<!--    <HeadTabs-->
<!--      ref="headTabs"-->
<!--      :alarm-list="alarmList"-->
<!--      :pending-list="pendingList"-->
<!--      :dict="dict"-->
<!--      :head-loading="headLoading"-->
<!--      @toQuery="toQuery"-->
<!--    />-->
    <basic-container>
      <TableTitleSlot title="动态监控告警">
<!--        <template slot="center">-->
<!--          <div class="dynamic-num">-->
<!--            <div style="margin-right: 40px;">-->
<!--              <span>告警终端: </span><span class="strong-text">{{ numCar }}</span>-->
<!--            </div>-->
<!--            <div>-->
<!--              <span>告警次数: </span><span class="strong-text">{{ totalAlarm }}</span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </template>-->
        <template slot="right">
          <el-checkbox
            v-model="isRefresh"
            class="checkbox"
            title="自动刷新"
            @change="handleRefresh"
          >
            自动刷新
          </el-checkbox>
        </template>
      </TableTitleSlot>
      <div class="xh-container">
        <div class="head-container">
          <HeadCommon
            :dict="dict"
            :permission="permission"
            :head-config="headConfig"
            label-width="80px"
            @handleClear="handleClear"
          />
        </div>
        <!--工具栏-->
        <div class="xh-crud-table-container">
          <crudOperation
            :permission="permission"
            width="190"
          >
            <template slot="left">
              <el-button
                class="filter-item"
                size="small"
                icon="el-icon-edit"
                @click="disposeAll"
              >
                批 处 理
              </el-button>
              <el-button
                class="filter-item"
                size="small"
                icon="el-icon-thumb"
                @click="misinformationAll(crud.selections,$event)"
              >
                批 误 报
              </el-button>
            </template>
            <template
              v-if="false"
              slot="right"
            >
              <el-dropdown style="margin-left: 14px;">
                <el-button
                  type="primary"
                  class="filter-item"
                  size="small"
                  icon="el-icon-arrow-down"
                >
                  打 印
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>处罚单</el-dropdown-item>
                  <el-dropdown-item>处罚通告</el-dropdown-item>
                  <el-dropdown-item>处罚决定书</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </crudOperation>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            :data="crud.data"
            :cell-style="{'text-align':'center'}"
            :max-height="tableMaxHeight"
            style="width: 100%; height: calc(100% - 47px);"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              type="selection"
              width="50"
              :selectable="checkCanHandle"
            />
            <el-table-column
              v-permission="['admin', 'realTimeMonitoring:edit', 'realTimeMonitoring:del']"
              label="操作"
              width="110"
              fixed="right"
              :resizable="false"
            >
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                >
                  <template slot="right">
                    <el-button
                      type="text"
                      size="small"
                      :disabled="!checkCanHandle(scope.row)"
                      @click="toDeal(scope.row)"
                    >
                      处理
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      @click="misinformationAll([scope.row],$event)"
                    >
                      误报
                    </el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('id')"
              :label="getLabel('id')"
              prop="id"
              width="200"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('handleState')"
              :label="getLabel('handleState')"
              prop="handleState"
              width="100"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="getStateType(scope.row.handleState)"
                  effect="light"
                  size="small"
                >
                  {{ scope.row.handleState }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('targetName')"
              :label="getLabel('targetType')"
              prop="targetName"
              show-overflow-tooltip
              width="130"
              :resizable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.targetName || $utils.emptymap.targetName }}
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('deviceTypeName')"
              :label="getLabel('deviceTypeName')"
              prop="deviceTypeName"
              width="120"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('deviceCateName')"
              :label="getLabel('deviceCateName')"
              prop="deviceCateName"
              width="210"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('deviceNum')"
              :label="getLabel('deviceNum')"
              prop="deviceNum"
              width="180"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('uniqueId')"
              :label="getLabel('uniqueId')"
              prop="uniqueId"
              width="160"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('alarmType')"
              :label="getLabel('alarmType')"
              prop="alarmType"
              width="320"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('alarmLevel')"
              :label="getLabel('alarmLevel')"
              prop="alarmLevel"
              width="100"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                <el-tag
                  :class="getLevelType(scope.row.alarmLevel)"
                  effect="light"
                  size="small"
                >
                  {{ scope.row.alarmLevel }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('alarmSource')"
              :label="getLabel('alarmSource')"
              prop="alarmSource"
              width="100"
              show-overflow-tooltip
              :resizable="false"
            />

            <el-table-column
              v-if="columns.visible('startTime')"
              :label="getLabel('startTime')"
              prop="startTime"
              width="180"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                <span class="table-date-td">
                  {{ scope.row.startTime }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('endTime')"
              :label="getLabel('endTime')"
              prop="endTime"
              width="180"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                <span class="table-date-td">
                  {{ scope.row.alarmComplete === 1 ? scope.row.endTime : '持续中' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('numAttach')"
              :label="getLabel('numAttach')"
              prop="numAttach"
              width="170"
              show-overflow-tooltip
              :resizable="false"
            >
              <template
                v-if="columns.visible('numAttach')"
                slot-scope="scope"
              >
                <span
                  class="active-label"
                  @click="handleAttachments(scope.row)"
                >{{ scope.row.numAttachReal || 0 }}/{{ scope.row.numAttachExpect || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('startAddr')"
              :label="getLabel('startAddr')"
              prop="startAddr"
              width="450"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('endAddr')"
              :label="getLabel('endAddr')"
              prop="endAddr"
              width="450"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.alarmComplete === 1 ? scope.row.endAddr : '定位中' }}
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('ruleName')"
              :label="getLabel('ruleName')"
              prop="ruleName"
              width="150"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('handleMeasures')"
              :label="getLabel('handleMeasures')"
              prop="handleMeasures"
              show-overflow-tooltip
              width="200"
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('handleContent')"
              :label="getLabel('handleContent')"
              prop="handleContent"
              width="250"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('handleTime')"
              :label="getLabel('handleTime')"
              prop="handleTime"
              show-overflow-tooltip
              width="180"
              :resizable="false"
            >
              <template slot-scope="scope">
                <span class="table-date-td">
                  {{ scope.row.handleTime }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('handler')"
              :label="getLabel('handler')"
              prop="handler"
              width="130"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
        </div>
        <!--分页组件-->
        <pagination/>
        <!--表单渲染-->
        <eForm
          :dict="dict"
        />
      </div>
    </basic-container>
    <!-- 处理 -->
    <AlarmSomeDialog
      service-role="1"
      :alarm-some-data="alarmSomeData"
      :dialog-visible="alarmSomeDialogShow"
      :is-alarm-details="dialogVisibleAlarm"
      :dict="dict"
      @closeDialog="alarmSomeDialogShow = false"
      @refresh="refresh"
      @selectionChange="selectionChange"
    />
    <!-- 告警详情 -->
    <AlarmDetails
      ref="alarmDetails"
      :dialog-visible.sync="dialogVisibleAlarm"
      :alarmData="alarmData"
      :dict="dict"
      @alarmHandle="alarmHandle"
      @misinformationAll="misinformationAll"
    />
    <!-- 附件弹窗 -->
    <AttachmentsDialog
      :dialog-visible.sync="dialogVisibleAttachments"
      :dict="dict"
      :alarm-point-list="alarmPointList"
      :attachments-data="attachmentsData"
    />
  </div>
</template>

<script>
import crudRealTimeMonitoring from '@/api/monitoring/realTimeMonitoring';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import HeadCommon from '@/components/formHead/headCommon.vue';
import HeadTabs from '@/components/pageHead/headTabs.vue';
// import ContinuousAlarm from './module/continuousAlarm.vue';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
import AlarmSomeDialog from '@/components/AlarmDialog/indexSomeNew.vue';
import AlarmDetails from '@/components/alarmDetails/index.vue';
import AttachmentsDialog from '@/components/AlarmDialog/attachmentsDialog.vue';
import { parseTime } from '../../../utils';

// crud交由presenter持有
const crud = CRUD({
  title: '', // 车辆
  crudMethod: { ...crudRealTimeMonitoring },
  queryOnPresenterCreated: false
});

export default {
  name: 'RealTimeMonitoring',
  components: {
    eForm,
    crudOperation,
    pagination,
    udOperation,
    HeadCommon,
    HeadTabs,
    // ContinuousAlarm,
    TableTitleSlot,
    AlarmSomeDialog,
    AlarmDetails,
    AttachmentsDialog
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [
    'alarmType',
    'alarmLevel',
    'serverState',
    'alarmTypeSpecial',
    'bdmDeviceType',
    'targetType'
  ],
  data() {
    return {
      permission: {
        add: [
          'admin',
          'realTimeMonitoring:add'
        ],
        edit: [
          'admin',
          'realTimeMonitoring:edit'
        ],
        del: [
          'admin',
          'realTimeMonitoring:del'
        ],
        export: [
          'admin',
          'realTimeMonitoring:export'
        ]
      },
      elTableColumnWidth: 150,
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: '监控对象',
            type: 'extra',
            value: 'deviceObj'
          },
          2: {
            name: '开始日期',
            type: 'datetime',
            value: 'startTime',
            defaultFn: 'stDE'
          },
          3: {
            name: '结束日期',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE',
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() > Date.now();
              }
            }
          },
          4: {
            name: '处理状态',
            type: 'select',
            value: 'handleState',
            defaultValue: '0',
            dictOptions: 'serverState'
          },
          5: {
            name: '告警类型',
            type: 'extra',
            value: 'alarmTypeList'
          },
          6: {
            name: '告警等级',
            type: 'select',
            value: 'alarmLevelList',
            dictOptions: 'alarmLevel',
            isMultiple: true
          },
          7: {
            name: '序列号',
            type: 'input',
            value: 'uniqueId'
          },
          8: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum'
          },
          9: {
            name: '终端类型',
            type: 'extra',
            value: 'deviceCate',
            dictOptions: 'bdmDeviceType'
          },
          // 10: {
          //   name: '处理人员',
          //   type: 'input',
          //   value: 'handler'
          // }
        },
        button: {}
      },
      alarmSomeData: [],
      alarmSomeDialogShow: false,
      dialogVisibleAlarm: false,
      alarmList: [],
      pendingList: [],
      numCar: null,
      totalAlarm: null,
      alarmData: {},
      isRefresh: false, // 自动刷新
      timer: null, // 定时器
      headLoading: false,
      dialogVisibleAttachments: false,
      alarmPointList: [],
      attachmentsData: {},
      visibleForm: [
        'handleState',
        'targetName',
        'deviceTypeName',
        'deviceCateName',
        'deviceNum',
        'uniqueId',
        'alarmType',
        'alarmLevel',
        'alarmSource',
        'startTime',
        'endTime',
        'numAttach',
        'startAddr',
        'endAddr',
        'ruleName',
        'handleMeasures',
        'handleContent',
        'handleTime',
        'handler'
      ]
    };
  },
  computed: {
    alarmTypeList() {
      return this.crud.query.alarmTypeList;
    }
  },
  watch: {
    alarmTypeList: {
      handler(newVal) {
        if (newVal) {
          if (typeof newVal === 'string') {
            this.$set(this.crud.query, 'specialAlarmType', 1);
          }
          else if (typeof newVal === 'object') {
            this.$set(this.crud.query, 'specialAlarmType', 0);
            this.$refs.headTabs.defaultActiveIndex();
          }
        }
        else {
          this.$set(this.crud.query, 'specialAlarmType', null);
        }
      }
    }
  },
  activated() {
    // 进入页面缓一会再打开详情, 不然性能差的电脑有可能会卡顿
    setTimeout(() => {
      if (localStorage.getItem('ROUTER_QUERY')) {
        const data = localStorage.getItem('ROUTER_QUERY');
        this.toDeal(JSON.parse(data));
        localStorage.removeItem('ROUTER_QUERY');
      }
    }, 1000);
    if (this.isRefresh) {
      this.autoRefresh();
    }
    this.$EventBus.$on('alarmDealDialog', (data) => {
      this.toDeal(JSON.parse(data));
    });
  },
  deactivated() {
    this.$EventBus.$off('alarmDealDialog');
  },
  methods: {
    // 点击"重置"时的操作
    handleClear() {
      this.$refs.headTabs.defaultActiveIndex();
    },
    // 点击附件数
    handleAttachments(data) {
      this.dialogVisibleAttachments = true;
      this.attachmentsData = data;
      // 请求告警轨迹点
      const query = {
        ids: [data.id]
      };
      crudRealTimeMonitoring.attachmentList(query).then(res => {
        if (res.data && res.data.length) {
          this.alarmPointList = res.data;
        }
        else {
          this.alarmPointList = [];
        }
      });
    },
    parseTime,
    /** 导出 - 之前 */
    [CRUD.HOOK.beforeExport]() {
      if (!this.crud.query.startTime || !this.crud.query.endTime) {
        this.crud.downloadLoading = false;
        this.$message.warning('必须选择开始时间和结束时间');
        return false;
      }
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    handleRefresh(val) {
      if (val) {
        this.autoRefresh();
      }
      else {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    // 轮询接口
    autoRefresh() {
      this.timer = setInterval(() => {
        this.crud.toQuery();
      }, 10000);
      this.$once('hook:deactivated', () => {
        clearInterval(this.timer);
        this.timer = null;
      });
    },
    // 跳转规则管理页面
    toRule(data) {
      const ruleData = JSON.stringify({
        ruleTypeId: data.ruleTypeId,
        ruleId: data.ruleId
      });
      localStorage.setItem('RULE_DATA', ruleData);
      this.$router.push({
        path: '/rule/index',
        query: { isRouter: this.$route.fullPath }
      });
    },
    // 告警等级颜色
    getLevelType(val) {
      let str = '';
      switch (val) {
      case '0级告警':
        str = 'table-alarmLevel-success';
        break;
      case '1级告警':
        str = 'table-alarmLevel-default';
        break;
      case '2级告警':
        str = 'table-alarmLevel-warning';
        break;
      case '3级告警':
        str = 'table-alarmLevel-severity';
        break;
      case '4级告警':
        str = 'table-alarmLevel-danger';
        break;
      case '5级告警':
        str = 'table-alarmLevel-critical';
        break;
      }
      return str;
    },
    getStateType(val) {
      let str = '';
      switch (val) {
      case '待处理':
        str = 'danger';
        break;
      case '已处理':
        str = 'success';
        break;
      case '误报':
        str = 'warning';
        break;
      }
      return str;
    },
    // 误报操作
    misinformationAll(datas, event) {
      console.log(event);
      let target = event.target;
      if (target.nodeName === 'I') {
        target = event.target.parentNode;
      }
      target.blur();
      if (datas.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      const result = datas.find((item) => item.isWrong === 1);
      if (result) {
        this.$message.warning('请选择未误报的数据');
        return;
      }
      this.$confirm(`确定将选择数据进行误报处理?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const list = datas.map(item => {
          return item.id;
        });
        const query = {
          ids: list,
          handleState: 2 // 2 误报
        };
        crudRealTimeMonitoring.dealAlarms(query).then((res) => {
          this.$message.success(res.msg);
          this.refresh();
        });
      }).catch(() => {
      });
    },
    toDeal(data) {
      this.dialogVisibleAlarm = true;
      this.alarmData = JSON.parse(JSON.stringify(data));
    },
    getAlarmDetails() {
      this.$refs.alarmDetails.getAlarmDetails();
    },
    alarmHandle(data) {
      this.alarmSomeData = data;
      this.alarmSomeDialogShow = true;
    },
    refresh() {
      this.crud.toQuery();
      if (this.dialogVisibleAlarm) {
        this.getAlarmDetails();
      }
    },
    toQuery(type) {
      this.$set(this.crud.query, 'alarmTypeList', type);
      this.$nextTick(() => {
        this.refresh();
      });
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh]() {
      if (!this.crud.query.startTime || !this.crud.query.endTime) {
        this.$message.warning('必须选择开始时间和结束时间');
        return false;
      }
      // this.$nextTick(() => {
      //   this.getAlarmData();
      // });
      return true;
    },
    /** 刷新 - 之后 */
    [CRUD.HOOK.afterRefresh]() {
      this.crud.data.forEach((item) => {
        item.duration = this.parseDuration(item.startTime, item.endTime);
      });
    },
    // 计算持续时间
    parseDuration(start, end) {
      const startTime = new Date(start * 1000);
      const endTime = new Date(end * 1000);
      // 计时间差（单位毫秒）
      const timeDiff = endTime - startTime;
      // 将时间差转换为天、小时、分钟秒
      const seconds = Math.floor(timeDiff / 1000) % 60;
      const minutes = Math.floor(timeDiff / (1000 * 60)) % 60;
      const hours = Math.floor(timeDiff / (1000 * 60 * 60)) % 24;
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      let str = '';
      if (days) str += days + '天';
      if (hours) str += hours + '时';
      if (minutes) str += minutes + '分';
      if (seconds) str += seconds + '秒';
      return str || '-';
    },
    async getAlarmData() {
      const query = {
        startTime: this.crud.query.startTime,
        endTime: this.crud.query.endTime
      };
      this.headLoading = false; // 加载中
      let alarmList = [], pendingList = [];
      // 告警类型的未处理总数
      await crudRealTimeMonitoring.pendingCount().then((res) => {
        // 1001 动态监控告警
        if (res.data[1001]) {
          res.data[1001].forEach((item) => {
            item.numPending = item.numAlarm;
          });
          alarmList = res.data[1001];
        }
        // 1002 主动安全告警
        if (res.data[1002]) {
          res.data[1002].forEach((item) => {
            item.numPending = item.numAlarm;
          });
          pendingList = res.data[1002];
        }
      }).catch(() => {
        this.headLoading = true;
      });
      // 告警类型的告警总数
      crudRealTimeMonitoring.alarmCount().then((res) => {
        if (res.data[1001]) {
          res.data[1001].forEach((item, index) => {
            alarmList[index].numAlarm = item.numAlarm;
          });
        }
        if (res.data[1002]) {
          res.data[1002].forEach((item, index) => {
            pendingList[index].numAlarm = item.numAlarm;
          });
        }
        this.alarmList = alarmList;
        this.pendingList = pendingList;
        this.headLoading = true;
      }).catch(() => {
        this.headLoading = true;
      });
      // 告警次数
      crudRealTimeMonitoring.realtimeCount(JSON.parse(JSON.stringify(query))).then((res) => {
        this.totalAlarm = res.data;
      });
      // 告警终端数量
      crudRealTimeMonitoring.realtimeCarCount(JSON.parse(JSON.stringify(query))).then((res) => {
        this.numCar = res.data;
      });
    },
    findAlarmName(id, data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].value === id) {
          return data[i].label;
        }
        else {
          for (var j = 0; j < data[i].children.length; j++) {
            if (data[i].children[j].value === id) {
              return data[i].children[j].label;
            }
          }
        }
      }
    },
    // 批量处理
    disposeAll() {
      if (this.crud.selections.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      // const result = this.crud.selections.find((item) => item.serverState !== 0);
      // if (result) {
      //   this.$message.warning('请选择未处理的数据');
      //   return;
      // }
      this.alarmSomeData = this.crud.selections;
      this.alarmSomeDialogShow = true;
    },
    // 拼接处理措施
    getServerState(dictName, val) {
      const valList = val.split(',');
      let labelList = valList.map(element => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][element]) {
          return this.dict.dict[dictName][element].label;
        }
      });
      let str = labelList.toString();
      return str;
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('RealTimeMonitoring', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    selectionChange(list) {
      this.$refs.table.clearSelection();
      list.forEach(item => {
        this.$refs.table.toggleRowSelection(item, true);
      });
    },
    checkCanHandle(row) {
      // 暂定808才可以处理报警
      return row.iotProtocol === 1;
    }
  }
};
</script>

<style lang="less" scoped>
.avue-view {
  height: 100%;
  // padding: 0 6px 4px;
}

.head-tabs {
  margin-bottom: 4px;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1
}

.main {
  display: flex;
  flex-direction: column;
  height: 100%;
  // padding: 0 6px 4px;
}

.basic-container {
  height: calc(100% - 0px);
}

.xh-container {
  height: calc(100% - 40px);
  // border: 1px solid #ebeef5;
  padding-bottom: 4px;

  .xh-crud-table-container {
    border: none;
  }
}

::v-deep .el-table__row {
  td {
    box-sizing: border-box;
    height: 42px;
    padding: 0;
  }
}

/*滚动条中间滑动部分*/
::v-deep ::-webkit-scrollbar-thumb {
  background-color: rgba(125, 125, 125, 0.5);
}

.rule-label {
  color: blue;
  cursor: pointer;
}

// 覆盖公共样式, 防止alarmType搜索框被隐藏
::v-deep .el-card {
  overflow: inherit;
}

.checkbox {
  // zoom: 200%;
  // margin-right: 20px;
}

</style>
