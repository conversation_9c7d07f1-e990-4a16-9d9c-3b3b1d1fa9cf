<template>
  <div class="container">
    <div class="top-container">
      <div class="top-content-item">
        <div><svg-icon
          icon-class="accessTotal"
        /><span>终端接入数</span></div>
        <div class="top-content-item-label">{{ accessTotal }}</div>
      </div>
      <!-- <div class="top-content-item">
        <div><svg-icon
          icon-class="todayOnlineTotal"
        /><span>今日上线数</span></div>
        <div class="top-content-item-label">{{ todayOnlineTotal }}</div>
      </div> -->
      <div class="top-content-item">
        <div><svg-icon
          icon-class="onlineTotal"
        /><span>当前在线数</span></div>
        <div class="top-content-item-label">{{ onlineTotal }}</div>
      </div>
    </div>
    <div
      class="show-table-btn"
      @click="isShowTable = !isShowTable"
    >
      <el-button
        round
        plain
      >
        终端类型统计
        <i :class="isShowTable ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"/>
      </el-button>
    </div>
    <div
      v-show="isShowTable"
      class="left-container"
    >
      <div class="left-container-header">
        <div class="left-container-header-item">
          <el-select
            v-model="provincial"
            placeholder="请选择省"
            clearable
            size="medium"
            @change="provincialChangeHandle"
          >
            <el-option
              v-for="item in provincialOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
          <el-select
            v-model="city"
            placeholder="请选择市"
            class="header-item-select"
            clearable
            size="medium"
            @change="cityChangeHandle"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
          <el-select
            v-model="county"
            placeholder="请选择区/县"
            clearable
            size="medium"
            @change="getTerminalCount"
          >
            <el-option
              v-for="item in countyOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </div>
        <div class="left-container-header-item">
          <el-select
            v-model="query.scenario"
            clearable
            size="medium"
            placeholder="请选择"
            @change="scenarioChangeHandle"
          >
            <el-option
              v-for="item in scenarioOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <DeptFormSingleSelect
            ref="deptIdRef"
            v-model="query.deptId"
            class="header-item-select"
            popper-class="home-dept-select-tree"
            placeholder="请选择所属机构"
            :dept-list="deptOptions"
            clearable
            size="medium"
          />
          <el-select
            v-model="query.usage"
            clearable
            size="medium"
            placeholder="请选择"
            @change="getTerminalCount"
          >
            <el-option
              v-for="item in usageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-input
          v-model="query.keyword"
          clearable
          size="medium"
          placeholder="请输入型号或名称进行查询"
          class="input-filter"
        >
          <el-button
            slot="append"
            size="medium"
            icon="el-icon-search"
            @click="getTerminalCount"
          />
        </el-input>
      </div>
      <div class="table-info">
        <el-table
          :header-cell-style="{'text-align':'center'}"
          :data="tableData"
          :cell-style="{'text-align':'center'}"
          style="height: 100%;overflow-x:hidden;"
          empty-text=" "
        >
          <el-table-column
            label="类型"
            prop="deviceUsage"
            width="105"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <svg-icon
                :icon-class="getUsageIcon(scope.row.deviceUsage)"
              />
              <span
                v-if="scope.row.deviceUsage"
                class="table-usage-label"
                :style="getUsageStyle(scope.row.deviceUsage)"
              >
                {{ getEnumDictLabel("bdmDeviceUsage", scope.row.deviceUsage) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="型号"
            prop="modelCode"
            width="85"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            label="名称"
            prop="modelName"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            label="接入数"
            prop="accessNum"
            width="65"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            label="在线数"
            prop="onlineNum"
            width="65"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
    </div>
    <div
      class="show-state-btn"
      @click="isShowStateSection = !isShowStateSection"
    >
      <el-button
        :icon="isShowStateSection ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
        round
        plain
      >
        终端运行状态
      </el-button>
    </div>
    <div
      class="terminal-state-section"
      :style="{height : isShowStateSection? 'calc(100% - 120px)': '0'}"
    >
      <div class="ts-title">
        <span>状态列表</span>
      </div>
      <div
        ref="tsList"
        class="ts-list"
        @mouseenter="stopDeviceStateScroll = true"
        @mouseleave="stopDeviceStateScroll = false"
      >
        <div
          v-for="item in deviceStateList"
          :key="`${item.deviceType}-${item.deviceNum}-${item.time || item.startTime}`"
          class="ts-item"
        >
          <template v-if="item.onOffLine !== undefined">
            <!-- <span
              class="device-type-tag"
              :style="{'--border-color': colorConfig[item.deviceType].numColor}"
            >
              {{ getEnumDictLabel('bdmDeviceType', item.deviceType) }}
            </span> -->
            <b class="device-num-tag">【{{ item.uniqueId }} - {{ item.targetName }}】</b>
            <b class="device-time-tag">于&ensp;{{ formatTime(item.time) }}&ensp;</b>
            <b :style="{color: item.onOffLine === 1? '#7c7a7a' : '#53c60c'}">{{ getOnOffLineStr(item.onOffLine) }}</b>
          </template>
          <template v-else>
            <!-- <span
              class="device-type-tag"
              :style="{'--border-color': colorConfig[item.deviceType].numColor}"
            >
              {{ getEnumDictLabel('bdmDeviceType', item.deviceType) }}
            </span> -->
            <b class="device-num-tag">【{{ item.uniqueId }} - {{ item.targetName }}】</b>
            <b class="device-time-tag">于&ensp;{{ formatTime(item.startTime, item.level) }}&ensp;发生&ensp;</b>
            <b style="color: #dd5353;">{{ getEnumDictLabel('alarmType', item.type) }}</b>
          </template>
        </div>
      </div>
    </div>
    <div class="map-container">
      <div
        ref="mapContainer"
        class="map"
      />
    </div>
  </div>
</template>
<script>
import AMapUtil from '@/components/map/AMapUtil';
import StringUtil from '@/utils/helper/StringUtil';
import configMap from '@/config/configMap';
import { queryTerminalLocation, lastStatusData, queryTerminalCount, queryOldTerminalCount, queryAllRegion, querySpecialTerminalCount, industryQueryDept } from '@/api/monitoring/main';
import crudBicycleMap from '@/api/monitoring/bicycleMap';
import { getWebsocketParam } from '@/api/user';
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';

const cacheStateArrLength = 200;
const splitStateArrLength = 150;

let __map = null;
let __AMap = null;
let __massMarks = null;
export default {
  components: { DeptFormSingleSelect },
  dicts: [
    'bdmDeviceType',
    'alarmType',
    'bdmDeviceUsage',
    'industrialSector'
  ],
  data() {
    return {
      toolConfig: {
        routeRegionEdit: false, // 跳转区域编辑
        routePolylineEdit: false, // 跳转路线编辑
        routePointEdit: false, // 跳转标注点编辑
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false // 工具栏
      }, // 控制工具按钮
      isShowStateSection: true,
      isShowTable: true,
      colorConfig: {
        '0': {
          titleColor: '#64d123',
          numColor: '#64d123'
        },
        '1': {
          titleColor: '#1a71bd',
          numColor: '#1a71bd'
        },
        '2': {
          titleColor: '#cf681f',
          numColor: '#cf681f'
        },
        '3': {
          titleColor: '#1ac1c1',
          numColor: '#1ac1c1'
        },
        '4': {
          titleColor: '#af4ef3',
          numColor: '#af4ef3'
        },
        '5': {
          titleColor: '#dd449b',
          numColor: '#dd449b'
        }
      },
      styles: null,
      markerList: [],
      deviceStateList: [],
      deviceStateWebsock: null,
      map: null,
      stopDeviceStateScroll: false,
      timer: null,
      tableData: [],
      accessTotal: 0,
      todayOnlineTotal: 0,
      onlineTotal: 0,
      provincial: '',
      city: '',
      county: '',
      query: {
        keyword: '',
        district: null,
        scenario: '',
        deptId: null,
        usage: ''
      },
      provincialOptions: [],
      cityOptions: [],
      countyOptions: [],
      scenarioOptions: [],
      usageOptions: [],
      usageIconObj: {
        1: 'home-statistics-people',
        2: 'home-statistics-vehicle',
        3: 'home-statistics-property',
        4: 'home-statistics-security',
        5: 'home-statistics-exploration',
        6: 'home-statistics-emergency',
        7: 'home-statistics-frequency',
        8: 'home-statistics-other'
      },
      usageStyleObj: {
        1: 'color:#EC808D;border-color:#EC808D',
        2: 'color:#0000FF;border-color:#0000FF',
        3: 'color:#169BD5;border-color:#169BD5',
        4: 'color:#FACD91;border-color:#FACD91',
        5: 'color:#2c2c2c;border-color:#2c2c2c',
        6: 'color:#C280FF;border-color:#C280FF',
        7: 'color:#00BFBF;border-color:#00BFBF',
        8: 'color:#BFBFBF;border-color:#BFBFBF'
      },
      deptOptions: []
    };
  },
  watch: {
    $route: {
      handler(to) {
        if (!localStorage.getItem('cedi__Access-Token')) {
          if (to.path === '/wel/index') {
            this.$store.commit('SET_COLLAPSE', true);
          }
          else {
            this.$store.commit('SET_COLLAPSE', false);
          }
        }
      },
      immediate: true,
      deep: true
    },
    'dict.bdmDeviceUsage': {
      handler(val) {
        this.usageOptions = [...val];
        this.usageOptions.unshift({
          value: '',
          label: '全部'
        });
      }
    },
    'dict.industrialSector': {
      handler(val) {
        this.scenarioOptions = [...val];
        this.scenarioOptions.unshift({
          value: '',
          label: '全部'
        });
      }
    },
    'query.deptId': {
      handler() {
        this.getTerminalCount();
      }
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.mapWidget = this.$refs.MapWidget;
    });
    this.initWebSocket();
    await this.init();
    this.getTerminalCount();
    this.monitorAlarmWs();
    this.getLastStatusData();
    this.getAllRegion();
    this.getDeptData('');
    document.addEventListener('visibilitychange', () => {
      if (document.hidden === true) {
        this.stopDeviceStateScroll = true;
      }
      else {
        this.onceScrollDeviceState();
      }
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
    if (__map) {
      //解绑地图的点击事件
      // __map.off("click", clickHandler);
      //销毁地图，并清空地图容器
      __map.destroy();
      //地图对象赋值为null
      __map = null;
      //清除地图容器的 DOM 元素
      document.getElementById(this.mapId)?.remove(); //"container" 为指定 DOM 元素的id
    }
  },
  activated() {
    this.initInterval();
    this.onceScrollDeviceState();
    this.mapReload();
  },
  deactivated() {
    this.stopDeviceStateScroll = true;
    clearInterval(this.timer);
  },
  methods: {
    async mapReload () {
      let canvas = document.getElementsByClassName('amap-layer')[0];
      if (canvas) {
        console.log(canvas); // 打印绘制的canvas
        let canvasContent = canvas.getContext('webgl'); // 因为高德地图是通过webGl来绘制的，所以通过getContext(‘webgl’)才能获取到内容
        console.log(canvasContent);
        if (canvasContent?.drawingBufferHeight < 10 && canvasContent?.drawingBufferWidth < 10) {
          await this.init();
          this.getTerminalCount();
        }
      }
    },
    getUsageStyle (val) {
      if (val) {
        return this.usageStyleObj[val];
      } else {
        return '';
      }
    },
    getUsageIcon (val) {
      if (val) {
        return this.usageIconObj[val];
      } else {
        return '';
      }
    },
    // 选择行业板块时查询所属机构数据
    scenarioChangeHandle () {
      if (!this.query.scenario) {
        this.$refs.deptIdRef?.clear();
        this.getDeptData('');
      }
      const result = this.scenarioOptions.find(item => item.value === this.query.scenario);
      if (result && result.children) {
        let ids = result.children.map(item => item.value).join(',');
        this.getDeptData(ids);
      }
    },
    getDeptData (val) {
      industryQueryDept({
        sectors: val
      }).then(res => {
        if (res.code === 200) {
          this.deptOptions = Object.freeze(res.data) || [];
          this.setDeptData(this.deptOptions);
        }
      });
    },
    setDeptData (data) {
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        element.title = element.orgName;
        if (element.children && element.children.length) {
          this.setDeptData(element.children);
        }
      }
    },
    // 根据市编码查询县
    cityChangeHandle () {
      this.county = '';
      this.countyOptions = [];
      if (this.city) {
        this.getAllRegion(this.city, 'countyOptions');
      }
      this.$nextTick(() => {
        this.getTerminalCount();
      });
    },
    // 根据省编码查询地级市
    provincialChangeHandle () {
      this.city = '';
      this.cityOptions = [];
      this.county = '';
      this.countyOptions = [];
      if (this.provincial) {
        this.getAllRegion(this.provincial, 'cityOptions');
      }
      this.$nextTick(() => {
        this.getTerminalCount();
      });
    },
    // 查询行政区划
    getAllRegion (code = '00', list = 'provincialOptions') {
      queryAllRegion({
        code: code
      }).then(res => {
        if (res.code === 200) {
          this[list] = res.data || [];
          if (list === 'provincialOptions') {
            this[list].unshift({
              code: '',
              name: '全国'
            });
          }
        }
      });
    },
    // 获取各类终端接入量、上线数数据
    getTerminalCount () {
      this.query.district = this.county ? this.county : this.city ? this.city : this.provincial;
      let promiseList;
      if ((!this.query.keyword && !this.query.usage) || this.query.usage === '8') {
        promiseList = [queryTerminalCount(JSON.parse(JSON.stringify(this.query))), this.getOldTerminalCount(), this.getSpecialTerminalCount()];
      } else {
        promiseList = [queryTerminalCount(JSON.parse(JSON.stringify(this.query)))];
      }
      Promise.allSettled(promiseList).then((resList) => {
        let tableData = [];
        let accessTotal = 0;
        let todayOnlineTotal = 0;
        let onlineTotal = 0;
        if (resList[0].value?.code === 200 && resList[0].value?.data?.length) {
          tableData = resList[0].value.data;
          tableData.forEach(element => {
            accessTotal += element.accessNum;
            todayOnlineTotal += element.todayOnlineNum;
            onlineTotal += element.onlineNum;
          });
        }
        if (resList[1]?.status === 'fulfilled' && resList[1]?.value) {
          resList[1].value.deviceUsage = 8;
          tableData.push(resList[1].value);
          accessTotal += resList[1].value.accessNum;
          todayOnlineTotal += resList[1].value.todayOnlineNum;
          onlineTotal += resList[1].value.onlineNum;
        }
        if (resList[2]?.status === 'fulfilled' && resList[2]?.value) {
          resList[2].value.deviceUsage = 8;
          tableData.push(resList[2].value);
          accessTotal += resList[2].value.accessNum;
          todayOnlineTotal += resList[2].value.todayOnlineNum;
          onlineTotal += resList[2].value.onlineNum;
        }
        this.tableData = tableData;
        this.accessTotal = accessTotal;
        this.todayOnlineTotal = todayOnlineTotal;
        this.onlineTotal = onlineTotal;
        this.getAllTerminalInfo();
      });
      // queryTerminalCount(JSON.parse(JSON.stringify(this.query))).then(res => {
      //   this.tableData = [];
      //   this.accessTotal = 0;
      //   this.todayOnlineTotal = 0;
      //   this.onlineTotal = 0;
      //   if (res.code === 200 && res.data?.length) {
      //     this.tableData = res.data;
      //     this.tableData.forEach(element => {
      //       this.accessTotal += element.accessNum;
      //       this.todayOnlineTotal += element.todayOnlineNum;
      //       this.onlineTotal += element.onlineNum;
      //     });
      //   }
      //   if (!this.query.keyword && !this.query.usage) {
      //     this.getOldTerminalCount();
      //     this.getSpecialTerminalCount();
      //   } else if (this.query.usage === '8') {
      //     this.getOldTerminalCount();
      //     this.getSpecialTerminalCount();
      //   }
      // });
    },
    // 获取旧终端接入量、上线数数据
    getOldTerminalCount () {
      this.query.district = this.county ? this.county : this.city ? this.city : this.provincial;
      return new Promise((resolve, reject) => {
        queryOldTerminalCount(JSON.parse(JSON.stringify(this.query))).then(res => {
          if (res.code === 200 && res.data) {
            // res.data.deviceUsage = 8;
            // this.tableData.push(res.data);
            // this.accessTotal += res.data.accessNum;
            // this.todayOnlineTotal += res.data.todayOnlineNum;
            // this.onlineTotal += res.data.onlineNum;
            resolve(res.data);
          } else {
            reject();
          }
        }).catch(()=> {
          reject();
        });
      });
    },
    // 获取特殊终端接入量、上线数数据
    getSpecialTerminalCount () {
      this.query.district = this.county ? this.county : this.city ? this.city : this.provincial;
      return new Promise((resolve, reject) => {
        querySpecialTerminalCount(JSON.parse(JSON.stringify(this.query))).then(res => {
          if (res.code === 200 && res.data) {
            // res.data.deviceUsage = 8;
            // this.tableData.push(res.data);
            // this.accessTotal += res.data.accessNum;
            // this.todayOnlineTotal += res.data.todayOnlineNum;
            // this.onlineTotal += res.data.onlineNum;
            resolve(res.data);
          } else {
            reject();
          }
        }).catch(()=> {
          reject();
        });
      });
    },
    onceScrollDeviceState() {
      const container = this.$refs.tsList;
      container.scrollTo({
        top: 0,
        behavior: 'instant'
      });
      setTimeout(() => {
        this.stopDeviceStateScroll = false;
      }, 1);
    },
    initInterval() {
      this.timer = setInterval(() => {
        this.getTerminalCount();
      }, 1000 * 30);
    },
    async init() {
      return new Promise(resolve => {
        AMapUtil.loadAMap(AMap => {
          __AMap = AMap;
          this.mapId = StringUtil.generateGuid();
          this.$refs.mapContainer.id = this.mapId;
          this.config = configMap.default;
          const massMarkerSize = 14;
          const massMarkerAnchor = massMarkerSize / 2;
          this.$nextTick(() => {
            let center = [
              108.552500,
              35.822700
            ];
            __map = new __AMap.Map(this.mapId, {
              resizeEnable: true, // 是否监控地图容器尺寸变化
              zoom: 4.1, // 初始化地图层级
              center: center, // 初始化地图中心点
              expandZoomRange: true,
              zooms: [
                4,
                18
              ],
              animateEnable: false,
              mapStyle: 'amap://styles/whitesmoke',
              viewMode: '3D',
              // features: [
              //   'bg',
              //   'road'
              // ]
            });
            this.styles = [
              0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8
            ].map(number => {
              return {
                // TODO 此处逻辑修改待终端全类型有数据后再修改 设计图片device-type99不记得了
                url: require(`@/assets/images/gn-home/device-type${number}.png`), //图标地址
                anchor: new AMap.Pixel(massMarkerAnchor, massMarkerAnchor), //图标显示位置偏移量，基准点为图标左上角
                size: new AMap.Size(massMarkerSize, massMarkerSize), //图标的尺寸
                zIndex: number + 1 //每种样式图标的叠加顺序，数字越大越靠前
              };
            });
            __AMap.plugin([
              'AMap.Scale',
              'AMap.MassMarks',
              'AMap.DistrictLayer',
              'AMap.GeoJSON' // 配置行政区查询服务
            ], () => {
              const disCountry = new AMap.DistrictLayer.Country({
                opacity: 0.8,
                // depth: 0,
                SOC: 'CHN', // 国家编码
                styles: {
                  'stroke-width': 1.1, // 描边线宽
                  "nation-stroke": "#84cdec", // 国界线颜色
                  "coastline-stroke": "#84cdec", // 海岸线颜色
                  "province-stroke": "#afd4f3", // 省线颜色
                  // "city-stroke": "#62b4ef",
                  fill: "rgba(214,229,255,0.2)", // 背景填充颜色
                },
              });
              disCountry.setMap(__map);
              __massMarks = new __AMap.MassMarks(
                [],
                {
                  zIndex: 999,
                  zooms: [
                    4,
                    18
                  ],
                  style: this.styles
                }
              );
              __massMarks.setMap(__map);
              const scale = new AMap.Scale({
                visible: true
              });
              __map.addControl(scale);
              resolve();
            });
          });
        });
      });
    },
    async getAllTerminalInfo() {
      const result = this.tableData.filter(item => item.deviceIds && item.deviceIds.length).map(item => ({
        deviceUsage: item.deviceUsage,
        deviceIds: item.deviceIds.map(element => BigInt(element))
      }));
      if (!result.length) {
        __massMarks.clear();
        return;
      }
      const { data, code } = await queryTerminalLocation(result);
      this.markerList = [];
      if (code === 200 && data.resData?.length) {
        const locationData = this.$utils.wgs84togcj02Batch(data.resData);
        this.markerList = locationData.map(item => {
          return {
            lnglat: [
              item.longitude,
              item.latitude
            ],
            style: item.deviceUsage
          };
        });
      }
      this.createMarker();
    },
    createMarker() {
      __massMarks.clear();
      __massMarks.setData(this.markerList);
    },
    // 获取最后推送的十条数据
    getLastStatusData() {
      const params = {
        size: 10,
        endTime: this.$moment().unix(),
        startTime: this.$moment().subtract(3, 'days').unix()
      };
      lastStatusData(params).then(res => {
        if (res.data) {
          this.deviceStateList = res.data.map(item => JSON.parse(item.content));
        }
      });
    },
    monitorAlarmWs() {
      this.$EventBus.$on('realTimeMap', (data) => {
        console.log('-> 首页推送终端告警数据', data);
        this.deviceStateList.unshift(data);
        this.scrollList();
        if (this.deviceStateList.length > cacheStateArrLength) {
          this.spliceDeviceStateArr();
        }
      });
    },
    initWebSocket() {
      if (typeof WebSocket === 'undefined') {
        console.log('您的浏览器不支持WebSocket');
        return false;
      }
      crudBicycleMap.getAuthCode().then(res => {
        const socketCode = res.data;
        getWebsocketParam().then(res => {
          const wsLocation = res.data.data;
          const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
          this.initDeviceStateWebsock(protocol, wsLocation, socketCode);
        });
      });
      this.$once('hook:beforeDestroy', () => {
        this.closeWebsocket();
      });
    },
    initDeviceStateWebsock(protocol, wsLocation, socketCode) {
      const wsUrl = `${protocol}${wsLocation}/ws/deviceOnOff/push/${socketCode}`;
      this.deviceStateWebsock = new ReconnectingWebSocket(wsUrl);
      this.deviceStateWebsock.onopen = () => {
        console.log('终端状态ws连接成功');
      };
      this.deviceStateWebsock.onmessage = (e) => {
        const data = JSON.parse(e.data);
        console.log('-> 首页推送终端状态数据', JSON.parse(e.data));
        this.deviceStateList.unshift(data);
        this.scrollList();
        if (this.deviceStateList.length > cacheStateArrLength) {
          this.spliceDeviceStateArr();
        }
      };
      this.deviceStateWebsock.onerror = () => {
        console.log('终端状态数据传输已断开, 正在尝试重新连接');
      };
    },
    closeWebsocket() {
      this.deviceStateWebsock?.close();
      this.$EventBus.$off('realTimeMap');
    },
    spliceDeviceStateArr() {
      this.deviceStateList = this.deviceStateList.splice(0, splitStateArrLength);
    },
    scrollList() {
      if (!this.stopDeviceStateScroll) {
        this.$nextTick(function () {
          const container = this.$refs.tsList;
          container.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }
    },
    formatTime(time, level) {
      const isAlarm = level || level === 0;
      return this.$moment(isAlarm ? time * 1000 : time).format('HH:mm:ss');
    },
    getOnOffLineStr(onOffLine) {
      return onOffLine === 0 ? '上线' : '下线';
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>
@satelliteTitleHeight: 40px;
.container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  padding: 0 4px 4px;
  position: relative;
}

.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.show-state-btn {
  right: 12px;
  top: 20px;
  position: absolute;
  z-index: 3;
  button {
    border: none;
  }
  button, i {
    color: #555555 !important;
  }
}

.terminal-state-section {
  width: 445px;
  height: calc(100% - 120px);
  background-color: rgb(255, 255, 255);
  position: absolute;
  right: 12px;
  top: 70px;
  z-index: 3;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  padding: 0 10px;

  .ts-title {
    height: 50px;
    line-height: 50px;
    color: #303030;
    font-size: 16px;
    position: relative;
    font-weight: bold;
  }

  .ts-list {
    height: calc(100% - 50px);
    padding: 4px 0;
    box-sizing: border-box;
    overflow: scroll;

    &::-webkit-scrollbar-track {
      display: none;
    }
    &::-webkit-scrollbar-thumb {
      background: var(--gn-color);
    }
  }

  .ts-item {
    font-size: 14px;
    background-color: #ffffff;
    padding: 5px 12px;
    box-sizing: border-box;
    line-height: 24px;
    border-radius: 4px;
    word-break: break-all;
  }

  .ts-item:nth-child(odd) {
    background-color: rgba(245, 245, 245, 0.8);
  }
}

.map {
  width: 100%;
  height: 100%;
}

.top-container {
    font-size: 16px;
    font-weight: 700;
    position: absolute;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    z-index: 3;
    display: flex;
}

.top-content-item {
  text-align: center;
  padding: 15px 15px 10px;
  .svg-icon {
    width: 28px;
    height: 25px;
    vertical-align: middle;
    margin-right: 7px;
  }
  &-label {
    color: var(--gn-color);
  }
}

.show-table-btn {
  left: 12px;
  top: 20px;
  position: absolute;
  z-index: 3;
  button {
    border: none;
  }
  button, i {
    color: #555555 !important;
  }
}

.left-container {
  position: absolute;
  left: 16px;
  top: 70px;
  width: 26vw;
  height: calc(100% - 120px);
  background: rgba(255, 255, 255);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  z-index: 1000;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
}

.left-container-header {
  .input-filter {
    padding-bottom: 10px;
    /deep/ .el-input-group__append {
      text-align: center;
      background-color: white;
      button {
        border: none;
        padding: 0;
        i {
          font-size: 20px;
        }
      }
    }
  }
  &-item {
    display: flex;
    padding-bottom: 7px;
    div {
      flex: 1;
    }
    .header-item-select {
      padding: 0 5px;
    }
  }
}

.table-info {
  flex: 1;
  overflow: hidden;
}

.table-usage-label {
  border: 1px solid;
  margin-left: 3px;
  border-radius: 3px;
  padding-right: 2px;
}

::v-deep .el-table,
.el-table__expanded-cell {
  background-color: transparent;
}

::v-deep .el-table {
  .el-table__header-wrapper {
    tr {
      background-color: rgba(245, 245, 245, 1) !important;
      height: 40px !important;
    }
    th {
      height: 40px !important;
    }
  }
  .el-table__body-wrapper {
    &::-webkit-scrollbar-track {
      display: none;
    }
    &::-webkit-scrollbar-thumb {
      background: var(--gn-color);
    }
  }
}

::v-deep .el-table__body td, ::v-deep .el-table__header th,
.el-table .cell {
  background-color: transparent !important;
}

::v-deep .el-table::before {
  //去除底部白线
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0;
}

//th的样式
::v-deep .el-table__header th {
  font-weight: bold;
  font-size: 14px;
  color: #333333;
}

::v-deep .el-table td.el-table__cell {
  border-bottom: none;
  height: 30px !important;
  font-size: 14px;
  color: #333333;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0 !important;
}

</style>
<style scoped>
.device-type-tag {
  height: 20px;
  padding: 0 8px;
  line-height: 20px;
  background-color: white;
  border-color: hsl(from var(--border-color) h s calc(l * 1.8));
  color: var(--border-color);
  display: inline-block;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  font-size: 12px;
  box-sizing: border-box;
}

.device-num-tag {
  color: #333333;
}

.device-time-tag {
  color: #333333;
}

b {
  font-weight: normal;
}
</style>

<style lang="less">
.home-dept-select-tree {
  width: 300px;
  .vue-recycle-scroller__item-wrapper {
    overflow: visible;
  }
}
</style>
