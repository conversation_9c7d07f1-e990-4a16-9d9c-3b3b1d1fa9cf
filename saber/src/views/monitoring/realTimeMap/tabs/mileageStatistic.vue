<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    title="里程统计"
    append-to-body
    width="80%"
    @close="close"
  >
    <div class="xh-container">
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
        />
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            v-if="columns.visible('enterprise')"
            prop="enterprise"
            :label="getLabel('enterprise')"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            prop="deptName"
            show-overflow-tooltip
            :label="getLabel('deptName')"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('licencePlate')"
            prop="licencePlate"
            show-overflow-tooltip
            :label="getLabel('licencePlate')"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('licenceColor')"
            prop="licenceColor"
            show-overflow-tooltip
            :label="getLabel('licenceColor')"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('vehicleModel')"
            prop="vehicleModel"
            show-overflow-tooltip
            :label="getLabel('vehicleModel')"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('vehicleOwner')"
            prop="vehicleOwner"
            show-overflow-tooltip
            :label="getLabel('vehicleOwner')"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('accessMode')"
            prop="accessMode"
            min-width="120"
            show-overflow-tooltip
            :label="getLabel('accessMode')"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('totalMileage')"
            prop="totalMileage"
            show-overflow-tooltip
            :label="getLabel('totalMileage')"
            :resizable="false"
          />
          <el-table-column
            v-for="(item,index) in dateTime"
            :key="index"
            :label="item"
            min-width="120"
            show-overflow-tooltip
            :prop="item"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
    </div>
    <!--分页组件-->
    <pagination />
  </el-dialog>
</template>

<script>
import crudDayMileage, { arrList } from '@/api/statistics/dayMileage.js';
import HeadCommon from '@/components/formHead/headCommon.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
// crud交由presenter持有
const crud = CRUD({
  title: getLabel('MileageStatistic', 'uniName'),
  crudMethod: { ...crudDayMileage },
  queryOnPresenterCreated: false
});

export default {
  name: 'MileageStatistic',
  components: {
    HeadCommon, pagination, crudOperation
  },
  mixins: [presenter(crud)],
  dicts: ['licenceColor'],
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      permission: {
        add: ['admin', ''],
        edit: ['admin', ''],
        del: ['admin', '']
      },
      dateList: arrList,
      dateTime: [],
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: '车牌号码',
            type: 'input',
            value: 'licencePlate',
          },
          2: {
            name: '车牌颜色',
            type: 'select',
            value: 'licenceColor',
            dictOptions: 'licenceColor'
          },
          3: {
            name: '车组',
            type: 'extra',
            value: 'deptId',
          },
          4: {
            name: '开始时间',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          5: {
            name: '结束时间',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          }
        },
        button: {
        }
      },
      isFirstOpen: true
    };
  },
  // table日期行头
  watch: {
    dateList: {
      handler (val) {
        this.dateTime = val[val.length - 1];
      }
    },
    dialogVisible: {
      handler (newValue) {
        if (newValue && this.isFirstOpen) {
          this.isFirstOpen = false;
          this.initTable();
        }
      }
    }
  },
  methods: {
    initTable () {
      this.$nextTick(() => {
        let columns = {};
        // 兼容u-table获取表格列
        const tableColumns = this.$refs.table.columns || this.$refs.table.getTableColumn();
        tableColumns.forEach(e => {
          if (!e.property || e.type !== 'default') {
            return;
          }
          columns[e.property] = {
            label: e.label,
            visible: true
          };
        });
        this.columns = this.obColumns(columns);
        this.crud.updateProp('tableColumns', columns);
        const element = this.$refs['table'].$el;
        this.tableMaxHeight = element.offsetHeight;
      });
    },
    obColumns(columns) {
      return {
        visible(col) {
          return !columns || !columns[col] ? true : columns[col].visible;
        }
      };
    },
    close () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('MileageStatistic', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
  }
};
</script>

<style lang="less" scoped>
.xh-container{
    height: calc(80vh - 60px - 42px);
}
</style>
