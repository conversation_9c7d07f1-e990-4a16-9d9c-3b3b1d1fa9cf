<template>
  <div class="layout">
    <div class="xh-page-searchItem-content-mini">
      <xh-select
        v-model="query.eventType"
        clearable
        size="small"
        :placeholder="getPlaceholder('eventType')"
        @change="crud.toQuery"
      >
        <el-option
          v-for="item in dict.dict.eventType"
          :key="item.value"
          :label="item.label"
          :value="item.label"
        />
      </xh-select>
    </div>
    <div class="xh-page-searchItem-content-mini">
      <xh-select
        v-model="query.channel"
        clearable
        size="small"
        :placeholder="getPlaceholder('channel')"
        @change="crud.toQuery"
      >
        <el-option
          v-for="item in channelOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </xh-select>
    </div>
  </div>
</template>

<script>
import CRUD, { header } from '@/components/Crud/crud';
import getPlaceholder from '@/utils/getPlaceholder';
export default {
  components: { },
  mixins: [header()],
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      channelOptions: null
    };
  },
  mounted () {
    this.channelOptions = ['一', '二', '三', '四', '五', '六', '七', '八'].map((item, index) => {
      return {
        label: '通道' + item,
        value: index + 1
      };
    });
  },
  methods: {
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('MediaEvent', value);
    }
  }
};
</script>
<style scoped>
.layout{
  display: inline-block;
}
.dept-content{
  vertical-align: top;
}
.xh-page-searchItem-content-mini{
  margin-right: 8px;
  display: inline-block;
  /* margin-bottom: 8px; */
  width: 156px;
}
</style>
