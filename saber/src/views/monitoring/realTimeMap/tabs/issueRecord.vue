<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    title="今日提醒"
    append-to-body
    width="80%"
    @close="close"
  >
    <div class="xh-container">
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :toolShow="false"
        />
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            v-if="columns.visible('licencePlate')"
            prop="licencePlate"
            :label="getLabel('licencePlate')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('licenceColor')"
            prop="licenceColor"
            :label="getLabel('licenceColor')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ getEnumDictLabel('licenceColor', scope.row.licenceColor) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('type')"
            prop="type"
            :label="getLabel('type')"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('param')"
            prop="param"
            :label="getLabel('param')"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.param || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('updateTime')"
            prop="updateTime"
            :label="getLabel('updateTime')"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTime(scope.row.updateTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('resParam')"
            prop="resParam"
            :label="getLabel('resParam')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.resParam ? '未响应' : '已响应' }}</span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
    </div>
    <!--分页组件-->
    <pagination />
  </el-dialog>
</template>

<script>
import crudPlatformRecord from '@/api/center/platformRecord';
import HeadCommon from '@/components/formHead/headCommon.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
// crud交由presenter持有
const crud = CRUD({
  title: getLabel('IssueRecord', 'uniName'),
  crudMethod: { ...crudPlatformRecord },
  queryOnPresenterCreated: false
});

export default {
  name: 'IssueRecord',
  components: {
    HeadCommon, pagination, crudOperation
  },
  mixins: [presenter(crud)],
  dicts: ['licenceColor', 'instructType'],
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      permission: {
        add: ['admin', ''],
        edit: ['admin', ''],
        del: ['admin', '']
      },
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: '车牌号码',
            type: 'input',
            value: 'licencePlate',
          },
          2: {
            name: '车牌颜色',
            type: 'select',
            value: 'licenceColor',
            dictOptions: 'licenceColor'
          },
          3: {
            name: '指令类型',
            type: 'select',
            value: 'type',
            dictOptions: 'instructType'
          },
          // 4: {
          //   name: '开始时间',
          //   type: 'datetime',
          //   value: 'startTime',
          //   defaultFn: 'stDE'
          // },
          // 5: {
          //   name: '结束时间',
          //   type: 'datetime',
          //   value: 'endTime',
          //   defaultFn: 'toDE'
          // }
        },
        button: {
        }
      },
      isFirstOpen: true
    };
  },
  watch: {
    dialogVisible: {
      handler (newValue) {
        if (newValue && this.isFirstOpen) {
          this.isFirstOpen = false;
          this.initTable();
        }
      }
    }
  },
  methods: {
    initTable () {
      this.$nextTick(() => {
        let columns = {};
        // 兼容u-table获取表格列
        const tableColumns = this.$refs.table.columns || this.$refs.table.getTableColumn();
        tableColumns.forEach(e => {
          if (!e.property || e.type !== 'default') {
            return;
          }
          columns[e.property] = {
            label: e.label,
            visible: true
          };
        });
        this.columns = this.obColumns(columns);
        this.crud.updateProp('tableColumns', columns);
        const element = this.$refs['table'].$el;
        this.tableMaxHeight = element.offsetHeight;
      });
    },
    obColumns(columns) {
      return {
        visible(col) {
          return !columns || !columns[col] ? true : columns[col].visible;
        }
      };
    },
    toQuery() {
      this.crud.toQuery();
    },
    [CRUD.HOOK.beforeRefresh]() {
      this.$set(this.crud.query, 'startTime', this.$moment().startOf('day'));
      this.$set(this.crud.query, 'endTime', this.$moment().endOf('day'));
    },
    [CRUD.HOOK.afterRefresh]() {
      this.$emit('editTotal', this.crud.total);
    },
    close () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('IssueRecord', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
  }
};
</script>

<style lang="less" scoped>
.xh-container{
    height: calc(80vh - 60px - 42px);
}
</style>
