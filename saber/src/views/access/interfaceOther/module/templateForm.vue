<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看' : '编辑'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 参数id -->
          <el-form-item
            :label="getLabel('id')"
            prop="id"
          >
            <el-input
              v-model.trim="form.id"
              disabled
              :placeholder="getPlaceholder('id')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 对应接口id -->
          <el-form-item
            :label="getLabel('interfaceManageId')"
            prop="interfaceManageId"
          >
            <el-input
              v-model.trim="form.interfaceManageId"
              disabled
              :placeholder="getPlaceholder('interfaceManageId')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 参数类型 -->
          <el-form-item
            :label="getLabel('paramType')"
            prop="paramType"
          >
            <xh-select
              v-model="form.paramType"
              :placeholder="getPlaceholder('paramType')"
              clearable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in paramTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 参数key -->
          <el-form-item
            :label="getLabel('paramKey')"
            prop="paramKey"
          >
            <el-input
              v-model.trim="form.paramKey"
              :disabled="isDetail"
              :placeholder="getPlaceholder('paramKey')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 参数value -->
          <el-form-item
            :label="getLabel('paramValue')"
            prop="paramValue"
          >
            <el-input
              v-model.trim="form.paramValue"
              :disabled="isDetail"
              :placeholder="getPlaceholder('paramValue')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 备注 -->
          <el-form-item
            :label="getLabel('note')"
            prop="note"
          >
            <el-input
              v-model.trim="form.note"
              :disabled="isDetail"
              :placeholder="getPlaceholder('note')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  interfaceManageId: null,
  paramType: null,
  paramKey: null,
  paramValue: null,
  note: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    paramTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      rules: {
        id: { required: true, message: '请输入参数id', trigger: 'blur' }, // 参数id
        interfaceManageId: { required: true, message: '请输入对应接口id', trigger: 'blur' }, // 对应接口id
        paramType: { required: true, message: '请选择参数类型', trigger: 'change' }, // 参数类型
        paramValue: { required: true, message: '请输入参数value', trigger: 'blur' }, // 参数value
        note: { required: true, message: '请输入备注', trigger: 'blur' } // 备注
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('ParamList', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('ParamList', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
