<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看第三方平台接口' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 接口编码 -->
          <el-form-item
            :label="getLabel('interCode')"
            prop="interCode"
          >
            <el-input
              v-model.trim="form.interCode"
              :disabled="isDetail"
              :placeholder="getPlaceholder('interCode')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 所属平台 -->
          <el-form-item
            :label="getLabel('systemType')"
            prop="systemType"
          >
            <xh-select
              v-model="form.systemType"
              :placeholder="getPlaceholder('systemType')"
              clearable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in systemTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 接口类型 -->
          <el-form-item
            :label="getLabel('interType')"
            prop="interType"
          >
            <xh-select
              v-model="form.interType"
              :placeholder="getPlaceholder('interType')"
              clearable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in interTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 接口功能 -->
          <el-form-item
            :label="getLabel('functionType')"
            prop="functionType"
          >
            <xh-select
              v-model="form.functionType"
              :placeholder="getPlaceholder('functionType')"
              clearable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in functionTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 请求方式 -->
          <el-form-item
            :label="getLabel('requestType')"
            prop="requestType"
          >
            <xh-select
              v-model="form.requestType"
              :placeholder="getPlaceholder('requestType')"
              clearable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in requestTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 协议 -->
          <el-form-item
            :label="getLabel('protocol')"
            prop="protocol"
          >
            <el-input
              v-model.trim="form.protocol"
              :disabled="isDetail"
              :placeholder="getPlaceholder('protocol')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- ip -->
          <el-form-item
            :label="getLabel('ip')"
            prop="ip"
          >
            <el-input
              v-model.trim="form.ip"
              :disabled="isDetail"
              :placeholder="getPlaceholder('ip')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 端口 -->
          <el-form-item
            :label="getLabel('port')"
            prop="port"
          >
            <el-input
              v-model.trim="form.port"
              :disabled="isDetail"
              :placeholder="getPlaceholder('port')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 请求url -->
          <el-form-item
            :label="getLabel('url')"
            prop="url"
          >
            <el-input
              v-model.trim="form.url"
              :disabled="isDetail"
              :placeholder="getPlaceholder('url')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 是否需要token -->
          <el-form-item
            :label="getLabel('isNeedToken')"
            prop="isNeedToken"
          >
            <xh-select
              v-model="form.isNeedToken"
              :placeholder="getPlaceholder('isNeedToken')"
              clearable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in whetherTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.isNeedToken === 'Y'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- token编码 -->
          <el-form-item
            :label="getLabel('tokenInterCode')"
            prop="tokenInterCode"
          >
            <el-input
              v-model.trim="form.tokenInterCode"
              :disabled="isDetail"
              :placeholder="getPlaceholder('tokenInterCode')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  interCode: null,
  systemType: null,
  interType: null,
  functionType: null,
  requestType: null,
  protocol: null,
  ip: null,
  port: null,
  url: null,
  isNeedToken: null,
  tokenInterCode: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    functionTypeOptions: {
      type: Array,
      default: () => []
    },
    interTypeOptions: {
      type: Array,
      default: () => []
    },
    requestTypeOptions: {
      type: Array,
      default: () => []
    },
    whetherTypeOptions: {
      type: Array,
      default: () => []
    },
    systemTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      rules: {
        interCode: { required: true, message: '请输入接口编码', trigger: 'blur' }, // 接口编码
        systemType: { required: true, message: '请选择所属平台', trigger: 'change' }, // 所属平台
        interType: { required: true, message: '请选择接口类型', trigger: 'change' }, // 接口类型
        functionType: { required: true, message: '请选择接口功能', trigger: 'change' }, // 接口功能
        requestType: { required: true, message: '请选择请求方式', trigger: 'change' }, // 请求方式
        protocol: { required: true, message: '请输入协议', trigger: 'blur' }, // 协议
        ip: { required: true, message: '请输入ip', trigger: 'blur' }, // ip
        port: { required: true, message: '请输入端口', trigger: 'blur' }, // 端口
        url: { required: true, message: '请输入请求url', trigger: 'blur' }, // 请求url
        isNeedToken: { required: true, message: '请选择是否需要token', trigger: 'change' }, // 是否需要token
        tokenInterCode: { required: true, message: '请输入token编码', trigger: 'blur' }, // token编码
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('InterfaceOther', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('InterfaceOther', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
