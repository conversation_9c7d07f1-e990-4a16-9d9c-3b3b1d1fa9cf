<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    title="按照模版新增"
    append-to-body
    width="50%"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="180px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 模板类型 -->
          <el-form-item
            :label="getLabel('type')"
            prop="type"
          >
            <xh-select
              v-model="form.type"
              :placeholder="getPlaceholder('type')"
              @change="handleTypeChange"
            >
              <el-option
                v-for="item in DIC.VAILD"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <!-- 模版类型选择时切换显示的内容 -->
        <div
          v-if="form.type === '1'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 是否带有token -->
          <el-form-item
            :label="getLabel('hasToken')"
            prop="hasToken"
          >
            <el-switch
              v-model="form.hasToken"
              active-text="是"
              inactive-text="否"
              active-value="Y"
              inactive-value="N"
              @change="handleHasTokenChange"
            />
          </el-form-item>
        </div>
        <!-- 是否带有token切换时显示的内容 -->
        <div
          v-if="form.hasToken === 'Y'"
          class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-8 el-col-md-8"
        >
          <!-- token位置 -->
          <el-form-item
            :label="getLabel('tokenStation')"
            prop="tokenStation"
            label-width="100px"
          >
            <xh-select
              v-model="form.tokenStation"
              :placeholder="getPlaceholder('tokenStation')"
            >
              <el-option
                v-for="item in DIC.POSTTION"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.hasToken === 'Y'"
          class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-8 el-col-md-8"
        >
          <!-- token key -->
          <el-form-item
            :label="getLabel('tokenKey')"
            prop="tokenKey"
            label-width="100px"
          >
            <el-tooltip
              content="如果token在url中(http://***************:8080/inter/findUser;token=swi2idfjwj4j2wjetwedfs0)，token key应包含分隔符，如 ;token"
            >
              <el-input
                v-model.trim="form.tokenKey"
                :placeholder="getPlaceholder('tokenKey')"
              />
            </el-tooltip>
          </el-form-item>
        </div>
        <div
          v-if="form.hasToken === 'Y'"
          class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-8 el-col-md-8"
        >
          <!-- token 前缀 -->
          <el-form-item
            :label="getLabel('tokenPrefix')"
            prop="tokenPrefix"
            label-width="100px"
          >
            <el-input
              v-model.trim="form.tokenPrefix"
              :placeholder="getPlaceholder('tokenPrefix')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.type !== '2'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- header中是否带有参数 -->
          <el-form-item
            :label="getLabel('hasHeaderParam')"
            prop="hasHeaderParam"
          >
            <el-switch
              v-model="form.hasHeaderParam"
              active-text="是"
              inactive-text="否"
              active-value="Y"
              inactive-value="N"
              @change="form.paramsInHeader = []"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.hasHeaderParam === 'Y'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- header参数信息 -->
          <el-form-item
            :label="getLabel('paramsInHeader')"
            prop="paramsInHeader"
          >
            <el-tooltip
              content="不再包含token设置"
            >
              <div>
                <div class="main">
                  <div class="item_label main_size">
                    参数key
                  </div>
                  <div class="item_label">
                    参数值
                  </div>
                  <div class="item_label">
                    <span
                      class="add_item"
                      @click="handleAddClick('paramsInHeader', 'header')"
                    >添加</span>
                  </div>
                </div>

                <div
                  v-for="(item,index) in form.paramsInHeader"
                  :key="index"
                  class="main"
                >
                  <div class="main_item main_size">
                    <!-- 参数key -->
                    <div>
                      <el-input
                        v-model="form.paramsInHeader[index].headerKey"
                        :placeholder="getPlaceholder('headerKey')"
                        style="width: 90%;"
                      />
                    </div>
                  </div>
                  <!-- 参数值 -->
                  <div class="main_item">
                    <el-input
                      v-model="form.paramsInHeader[index].headerValue"
                      :placeholder="getPlaceholder('headerValue')"
                      style="width: 90%;"
                    />
                  </div>
                  <div class="main_item">
                    <div>
                      <i
                        class="el-icon-remove-outline"
                        @click="reduceHandle('paramsInHeader', index)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-tooltip>
          </el-form-item>
        </div>
        <div
          v-if="form.type !== '2'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- body中是否带有参数 -->
          <el-form-item
            :label="getLabel('hasBodyParam')"
            prop="hasBodyParam"
          >
            <el-switch
              v-model="form.hasBodyParam"
              active-text="是"
              inactive-text="否"
              active-value="Y"
              inactive-value="N"
              @change="form.paramsInBody = []"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.hasBodyParam === 'Y'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- body参数信息 -->
          <el-form-item
            :label="getLabel('paramsInBody')"
            prop="paramsInBody"
          >
            <div class="main">
              <div class="item_label main_size">
                参数key
              </div>
              <div class="item_label">
                参数值
              </div>
              <div class="item_label">
                <span
                  class="add_item"
                  @click="handleAddClick('paramsInBody', 'body')"
                >添加</span>
              </div>
            </div>
            <div
              v-for="(item,index) in form.paramsInBody"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <!-- 参数key -->
                <div>
                  <el-input
                    v-model="form.paramsInBody[index].bodyKey"
                    :placeholder="getPlaceholder('bodyKey')"
                    style="width: 90%;"
                  />
                </div>
              </div>
              <!-- 参数值 -->
              <div class="main_item">
                <el-input
                  v-model="form.paramsInBody[index].bodyValue"
                  :placeholder="getPlaceholder('bodyValue')"
                  style="width: 90%;"
                />
              </div>
              <div class="main_item">
                <div>
                  <i
                    class="el-icon-remove-outline"
                    @click="reduceHandle('paramsInBody', index)"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div
          v-if="form.type !== '2'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- url中是否带有参数 -->
          <el-form-item
            :label="getLabel('hasUrlParam')"
            prop="hasUrlParam"
          >
            <el-switch
              v-model="form.hasUrlParam"
              active-text="是"
              inactive-text="否"
              active-value="Y"
              inactive-value="N"
              @change="form.paramsInUrl = []"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.hasUrlParam === 'Y'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- url参数信息 -->
          <el-form-item
            :label="getLabel('paramsInUrl')"
            prop="paramsInUrl"
          >
            <el-tooltip
              content="不再包含token设置"
            >
              <div>
                <div class="main">
                  <div class="item_label main_size">
                    参数key
                  </div>
                  <div class="item_label">
                    参数值
                  </div>
                  <div class="item_label">
                    <span
                      class="add_item"
                      @click="handleAddClick('paramsInUrl', 'url')"
                    >添加</span>
                  </div>
                </div>
                <div
                  v-for="(item,index) in form.paramsInUrl"
                  :key="index"
                  class="main"
                >
                  <div class="main_item main_size">
                    <!-- 参数key -->
                    <div>
                      <el-input
                        v-model="form.paramsInUrl[index].urlKey"
                        :placeholder="getPlaceholder('urlKey')"
                        style="width: 90%;"
                      />
                    </div>
                  </div>
                  <!-- 参数值 -->
                  <div class="main_item">
                    <el-input
                      v-model="form.paramsInUrl[index].urlValue"
                      :placeholder="getPlaceholder('urlValue')"
                      style="width: 90%;"
                    />
                  </div>
                  <div class="main_item">
                    <div>
                      <i
                        class="el-icon-remove-outline"
                        @click="reduceHandle('paramsInUrl', index)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-tooltip>
          </el-form-item>
        </div>
        <div
          v-if="form.type === '2'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 是否带有认证 -->
          <el-form-item
            :label="getLabel('hasAuth')"
            prop="hasAuth"
          >
            <el-switch
              v-model="form.hasAuth"
              active-text="是"
              inactive-text="否"
              active-value="Y"
              inactive-value="N"
              @change="handleHasAuthChange"
            />
          </el-form-item>
        </div>
        <!-- 是否带有认证切换时显示的内容 -->
        <div
          v-if="form.hasAuth === 'Y'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- username -->
          <el-form-item
            :label="getLabel('username')"
            prop="username"
          >
            <el-input
              v-model.trim="form.username"
              :placeholder="getPlaceholder('username')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.hasAuth === 'Y'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- password -->
          <el-form-item
            :label="getLabel('password')"
            prop="password"
          >
            <el-input
              v-model.trim="form.password"
              :placeholder="getPlaceholder('password')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.type === '4'"
          class="el-col el-col-16 el-col-offset-0 el-col-xs-16 el-col-sm-16 el-col-md-16"
        >
          <!-- 返回token报文中access_token的key -->
          <el-form-item
            :label="getLabel('accessTokenKey')"
            prop="accessTokenKey"
            label-width="245px"
          >
            <el-tooltip
              content="如果为空，则表示返回的报文为token字符串；如果是多个层级，中间使用 : 进行分隔，如 data:access_token"
            >
              <el-input
                v-model.trim="form.accessTokenKey"
                :placeholder="getPlaceholder('accessTokenKey')"
              />
            </el-tooltip>
          </el-form-item>
        </div>
        <div
          v-if="form.type === '4'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-12 el-col-sm-12 el-col-md-12"
        >
          <!-- token的有效期(单位：秒) -->
          <el-form-item
            :label="getLabel('accessTokenDuration')"
            prop="accessTokenDuration"
          >
            <el-tooltip
              content="单位为秒"
            >
              <el-input
                v-model.trim="form.accessTokenDuration"
                :placeholder="getPlaceholder('accessTokenDuration')"
              />
            </el-tooltip>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="$emit('update:dialogVisible', false)"
      >
        取消
      </el-button>
      <el-button
        :loading="submitLoading"
        type="primary"
        size="small"
        @click="handleSubmitClick"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { add, checkHasParam } from '@/api/access/interfaceOthParams';

const defaultForm = {
  id: null,
  type: '1',
  hasToken: 'N',
  hasHeaderParam: 'N',
  hasBodyParam: 'N',
  hasUrlParam: 'N',
  hasAuth: 'N',
  accessTokenKey: null,
  accessTokenDuration: null,
  tokenStation: 'head',
  tokenKey: null,
  tokenPrefix: null,
  username: null,
  password: null,
  paramsInHeader: [],
  paramsInBody: [],
  paramsInUrl: [],
  // 不清楚这两个值是干嘛的, 改造前的代码里包含这两个字段, 但是并没有展示到页面上
  dataStation: 'body',
  dataKey: null
};
export default {
  components: { },
  props: {
    dict: {
      type: Object,
      required: true
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    interfaceManageId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      form: {
        id: null,
        type: '1',
        hasToken: 'N',
        hasHeaderParam: 'N',
        hasBodyParam: 'N',
        hasUrlParam: 'N',
        hasAuth: 'N',
        accessTokenKey: null,
        accessTokenDuration: null,
        tokenStation: 'head',
        tokenKey: null,
        tokenPrefix: null,
        username: null,
        password: null,
        paramsInHeader: [],
        paramsInBody: [],
        paramsInUrl: [],
        // 不清楚这两个值是干嘛的, 改造前的代码里包含这两个字段, 但是并没有展示到页面上
        dataStation: 'body',
        dataKey: null
      },
      rules: {

      },
      submitLoading: false,
      DIC: {
        VAILD: [{
          label: 'HTTP接口',
          value: '1'
        },{
          label: 'WEBSERVICE接口',
          value: '2'
        },{
          label: '请求TOKEN接口',
          value: '4'
        }],
        BOOLEAN_VALUE:[{
          label:"否",
          value:"N"
        },{
          label:"是",
          value:"Y"
        }],
        POSTTION:[{
          label:"head",
          value:"head"
        },{
          label:"body",
          value:"body"
        },{
          label:"url",
          value:"url"
        }],
      }
    };
  },
  watch: {
    interfaceManageId: {
      handler () {
        // 返回参数不符合要求, 需要等后台调整
        // this.handleCheckParam();
      },
      immediate: true
    }
  },
  methods: {

    handleCheckParam () {
      //查询当前接口是否已经含有接口参数配置，如果有的话，进行提示
      checkHasParam(this.interfaceManageId).then(res => {
        if(res.data){
          this.$message.warning('当前接口已经存在参数配置，按照模板新建会清空原有参数配置，请谨慎操作!');
        }
      });
    },
    // 提交表单
    handleSubmitClick () {
      this.$refs?.form?.validate(valid => {
        if (valid) {
          this.$confirm("确定将覆盖原有配置，是否继续?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            this.submitLoading = true;
            this.form.interfaceManageId = this.interfaceManageId;
            add(this.form).then(res => {
              console.log(res, 666);
              this.$emit('handleQuery');
              this.$emit('update:dialogVisible', false);
              this.$message({
                type: 'success',
                message: '新增成功'
              });
            }).finally(() => {
              this.submitLoading = false;
            });
          });
        }
      });
    },
    reduceHandle (val, index) {
      this.form[val].splice(index, 1);
    },
    // 添加子项
    handleAddClick(val, str) {
      let obj = {};
      obj[str + 'Key'] = null;
      obj[str + 'Value'] = null;
      this.form[val].push(obj);
    },
    // 是否带有认证选择时
    handleHasAuthChange () {
      this.form.username = null;
      this.form.password = null;
    },
    // 是否带有token选择时
    handleHasTokenChange () {
      this.form.tokenStation = 'head';
      this.form.tokenKey = null;
      this.form.tokenPrefix = null;
    },
    // 选择模版类型时
    handleTypeChange (val) {
      this.form = {...defaultForm};
      this.$refs?.form?.clearValidate();
      this.form.type = val;
    },
    close () {
      this.form = {...defaultForm};
      this.$refs?.form?.clearValidate();
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('ParamList', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('ParamList', value);
    }
  }
};
</script>

<style lang="less" scoped>
  .main {
    display: flex;
    width: 90%;
    text-align: center;
  }

  .main_item {
    flex: 1;
    height: 45px;
    line-height: 45px;
    border: 1px solid #c1c9da;
  }

  .main_size {
    flex: 1 !important;
  }

  .item_label {
    flex: 1;
    height: 40px;
    line-height: 40px;
    background-color: #e1e5ee;
    border: 1px solid #c1c9da;
  }

  .main_item /deep/ .el-select, .main_item /deep/ .el-input {
    width: 100%;
  }

  .add_item {
    border: 1px solid #aebac5;
    padding: 3px 5px;
    background-color: #ffffff;
    margin: 5px;
    cursor: pointer;
  }
</style>
