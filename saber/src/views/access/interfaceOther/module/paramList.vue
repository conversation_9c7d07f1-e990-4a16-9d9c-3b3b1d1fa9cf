<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    title="编辑接口参数"
    append-to-body
    width="80%"
    @close="close"
  >
    <div class="xh-container">
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="paramPermission"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="permission.add"
              class="filter-item"
              icon="el-icon-plus"
              type="primary"
              size="small"
              @click="dialogTemplateVisible = true"
            >
              按照模版新增
            </el-button>
          </template>
        </crudOperation>
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            v-permission="['admin','paramList:edit','paramList:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 参数id -->
          <el-table-column
            v-if="columns.visible('id')"
            prop="id"
            :label="getLabel('id')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 对应接口id -->
          <el-table-column
            v-if="columns.visible('interfaceManageId')"
            prop="interfaceManageId"
            :label="getLabel('interfaceManageId')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 参数类型 -->
          <el-table-column
            v-if="columns.visible('paramType')"
            prop="paramType"
            :label="getLabel('paramType')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ paramTypeObj[scope.row.paramType] }}
            </template>
          </el-table-column>
          <!-- 参数key -->
          <el-table-column
            v-if="columns.visible('paramKey')"
            prop="paramKey"
            :label="getLabel('paramKey')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 参数value -->
          <el-table-column
            v-if="columns.visible('paramValue')"
            prop="paramValue"
            :label="getLabel('paramValue')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 备注 -->
          <el-table-column
            v-if="columns.visible('note')"
            prop="note"
            :label="getLabel('note')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <TemplateAdd
        ref="templateAdd"
        :dialog-visible.sync="dialogTemplateVisible"
        :interface-manage-id="interfaceManageId"
        @handleQuery="handleQuery"
      />
      <TemplateForm
        ref="templateForm"
        :param-type-options="paramTypeOptions"
        :is-detail.sync="isDetail"
      />
    </div>
    <!--分页组件-->
    <pagination />
  </el-dialog>
</template>

<script>
import crudInterfaceOthParams from '@/api/access/interfaceOthParams';
import HeadCommon from '@/components/formHead/headCommon.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import { queryInterDict } from '@/api/access/interfaceOther';
import TemplateAdd from './templateAdd.vue';
import TemplateForm from './templateForm.vue';
// crud交由presenter持有
const crud = CRUD({
  title: getLabel('ParamList', 'uniName'),
  crudMethod: { ...crudInterfaceOthParams },
  queryOnPresenterCreated: false
});

export default {
  name: 'ParamList',
  components: {
    HeadCommon, pagination, crudOperation, TemplateAdd, TemplateForm, udOperation
  },
  mixins: [presenter(crud)],
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    interfaceManageId: {
      type: String,
      default: ''
    }
  },
  data(){
    return{
      permission: {
        add: ['admin', 'paramList:add'],
        edit: ['admin', 'paramList:edit'],
        del: ['admin', 'paramList:del'],
        view: ['admin', 'paramList:view']
      },
      paramPermission: {
        add: ['admin', ''],
        del: ['admin', '']
      },
      headConfig: {
        item: {
          1: {
            name: '参数类型',
            type: 'select',
            value: 'paramType',
            options: []
          }
        },
        button: {
        }
      },
      dialogTemplateVisible: false,
      dialogFormVisible: false,
      paramTypeOptions: [],
      paramTypeObj: {},
      isDetail: false,
      isFirstOpen: true
    };
  },
  watch: {
    dialogVisible: {
      handler (newValue) {
        if (newValue) {
          this.handleQuery();
          if (this.isFirstOpen) {
            this.isFirstOpen = false;
            this.getInterDictList();
            this.initTable();
          }
        }
      }
    }
  },
  methods: {
    initTable () {
      this.$nextTick(() => {
        let columns = {};
        // 兼容u-table获取表格列
        const tableColumns = this.$refs.table.columns || this.$refs.table.getTableColumn();
        tableColumns.forEach(e => {
          if (!e.property || e.type !== 'default') {
            return;
          }
          columns[e.property] = {
            label: e.label,
            visible: true
          };
        });
        this.columns = this.obColumns(columns);
        this.crud.updateProp('tableColumns', columns);
        const element = this.$refs['table'].$el;
        this.tableMaxHeight = element.offsetHeight;
      });
    },
    obColumns(columns) {
      return {
        visible(col) {
          return !columns || !columns[col] ? true : columns[col].visible;
        }
      };
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      // 避免重置后将interfaceManageId清空, 因此请求前赋值
      this.crud.query.interfaceManageId = this.interfaceManageId;
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    // 查询
    handleQuery () {
      this.crud.toQuery();
    },
    getInterDictList () {
      // 参数类型字典
      queryInterDict('PARAM_TYPE').then(res => {
        this.paramTypeOptions = res.data?.map(item => ({
          label: item.dictValue,
          value: item.dictKey
        }));
        this.paramTypeObj = this.paramTypeOptions.reduce((acc, curr) => {
          acc[curr.value] = curr.label;
          return acc;
        }, {});
        this.headConfig.item['1'].options = this.paramTypeOptions;
      });
    },
    close () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('ParamList', value);
    }
  }
};
</script>

<style lang="less" scoped>
.xh-container{
    height: calc(80vh - 60px - 42px);
}
</style>
