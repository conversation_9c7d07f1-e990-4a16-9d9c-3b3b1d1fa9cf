<template>
  <el-dialog
    v-dialog-drag
    title="绑定告警规则"
    append-to-body
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="650px"
    @closed="closed"
  >
    <div>
      <el-input
        v-model="filterText"
        placeholder="请输入规则搜索"
        size="small"
        style="margin-bottom: 4px"
        @input="(val) => this.$refs.ruleTree.filter(val)"
      />
      <vue-easy-tree
        ref="ruleTree"
        v-loading="treeLoading"
        :data="ruleData"
        show-checkbox
        :props="ruleProps"
        node-key="id"
        :filter-node-method="filterRuleNode"
      />
    </div>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="$emit('update:dialogVisible', false)"
      >取 消</el-button>
      <el-button
        type="primary"
        size="small"
        :loading="btnLoading"
        @click="submitHandle"
      >确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { allRuleSbysystemDept } from "@/api/rule";
import { bindAlarmRule, ruleSbysystem } from "@/api/access/platformAdmin";
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    currentData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      filterText: '',
      ruleProps: {
        label: "name"
      },
      ruleData: [],
      initCheckedNodes: {},
      btnLoading: false,
      treeLoading: false
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.initData();
          });
        }
      }
    }
  },
  methods: {
    submitHandle() {
      let selectedRuleInfos = [];
      let removedRuleInfos = [];
      const checkedNodes = this.$refs.ruleTree?.getCheckedNodes();
      const checkedObj = checkedNodes.reduce((acc, item) => {
        if (item.type || item.type === 0) {
          acc[item.id] = {
            ruleId: item.key,
            ruleType: item.type
          };
        }
        return acc;
      }, {});
      Object.keys(checkedObj).forEach(key => {
        if (!this.initCheckedNodes.hasOwnProperty(key)) {
          selectedRuleInfos.push(checkedObj[key]);
        }
      });
      Object.keys(this.initCheckedNodes).forEach(key => {
        if (!checkedObj.hasOwnProperty(key)) {
          removedRuleInfos.push(this.initCheckedNodes[key]);
        }
      });
      const params = {
        systemId: BigInt(this.currentData.id),
        removedRuleInfos: removedRuleInfos,
        selectedRuleInfos: selectedRuleInfos
      };
      this.btnLoading = true;
      bindAlarmRule(params).then(() => {
        this.$message.success("操作成功！");
        this.$emit('update:dialogVisible', false);
        this.$emit('toQuery');
      }).finally(() => {
        this.btnLoading = false;
      });
    },
    async initData() {
      this.treeLoading = true;
      await allRuleSbysystemDept({ systemId: this.currentData.id }).then((res) => {
        this.ruleData = res.data.content;
        this.processing(this.ruleData);
        this.treeLoading = false;
      });
      ruleSbysystem({ systemId: this.currentData.id }).then(res => {
        if (res.data) {
          this.initCheckedNodes = res.data.reduce((acc, item) => {
            acc[item.ruleId + '-' + item.ruleType] = item;
            return acc;
          }, {});
          this.$refs.ruleTree?.setCheckedKeys(Object.keys(this.initCheckedNodes));
        }
      });
    },
    processing(list) {
      list && list.forEach((element) => {
        if (element.rules) {
          element.children = element.rules.map(item => ({
            id: item.id + '-' + item.type,
            name: item.name,
            type: item.type,
            key: item.id
          }));
        } else {
          this.processing(element.children);
        }
      });
    },
    filterRuleNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    closed() {
      this.filterText = '';
      this.ruleData = [];
      this.$refs.ruleTree?.filter(this.filterText);
      this.$refs.ruleTree?.setCheckedKeys([]);
      this.initCheckedNodes = [];
      this.btnLoading = false;
      this.treeLoading = false;
      this.$emit('update:dialogVisible', false);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-dialog__body {
    height: 500px;
}
</style>