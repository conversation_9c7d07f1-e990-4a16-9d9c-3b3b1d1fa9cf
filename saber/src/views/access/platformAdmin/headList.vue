<template>
  <div>
    <el-form
      ref="form"
      label-width="120px"
      inline
    >
      <el-row :span="24">
        <el-form-item
          v-for="(item, index) in options"
          :key="index"
          :label="item.label"
        >
        <el-input
          v-if="item.type === 'input'"
          v-model="query[item.value]"
          clearable
          size="small"
          :placeholder="'请输入' + item.label"
        />
        <el-checkbox
          v-else-if="item.type === 'checkbox'"
          v-model="query[item.value]"
          :true-label="item.trueLabel"
          :false-label="item.falseLabel"
        />
        <el-select
          v-else-if="['vehicleType', 'vehicleTruckType', 'facilityType', 'bdmWorkerPost', 'selectD'].includes(item.type)"
          v-model="query[item.value]"
          :placeholder="'请选择' + item.label"
          :filterable="item.filterable"
          clearable
          :multiple="item.multiple"
          :collapse-tags="item.collapseTags"
          size="small"
        >
          <el-option
            v-for="(subItem, subIndex) in getOpt(item.type)"
            :key="subIndex"
            :label="subItem.label"
            :value="subItem.value"
          />
        </el-select>
          <DeptFormSingleSelect v-model="query[item.value]" :deptList="deptList" no-req="true" clearable :is-show="true" size="small"  v-else-if="item.type ==='dept'" />
          <Treeselect
              v-else-if="item.type==='bindTerminalType'"
              v-model="query[item.value]"
              :options="dict['bdmDeviceType']"
              :normalizer="normalizerBindTerminalType"
              placeholder="请选择终端类型"
              style="width: 200px;"
            >
            <p
              slot="option-label"
              slot-scope="{node}"
              style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;width: 90%;"
              :title="node.label"
            >
              <template> {{ node.label }}</template>
            </p>
          </Treeselect>
        </el-form-item>
        <el-form-item
        >
        <el-button
          type="primary"
          size="small"
          @click="handleSearch"
        >
          查询
        </el-button>
        <el-button
          size="small"
          @click="reset"
        >
          重置
        </el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import Treeselect from '@riophae/vue-treeselect';
export default {
  components: {
    DeptFormSingleSelect,
    Treeselect
  },
  data () {
    this.initQuery = {
      category: '',
      device_seq: '',
      device_num: '',
      dept_id: null,
      name: '',
      number: '',
      industry: '',
      bind_status: null
    }
    return {
      query: JSON.parse(JSON.stringify(this.initQuery)),
      normalizerBindTerminalType (node) {
        return {
          id: node.value,
          label: node.label,
          children: node.children || undefined
        };
      },
    };
  },
  props: {
    isShowTable: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => []
    },
    dict: {
      type: Object,
      default: () => ({})
    },
    active: {
      type: String,
      default: ''
    },
    deptList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    isShowTable(val) {
      if(!val) {
        this.query =  JSON.parse(JSON.stringify(this.initQuery))
      }
    }
  },
  methods: {
    getOpt(type) {
      if(type === 'selectD') {
        return this.dict.deviceType[this.active] ||[]
      } else {
        return this.dict[type] || []
      }
    },
    handleSearch() {
      this.$emit('getData')
    },
    reset() {
      this.query =  JSON.parse(JSON.stringify(this.initQuery))
      this.$emit('getData')
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .vue-treeselect__control{
  height: 32px;
  line-height: 32px;
}
</style>