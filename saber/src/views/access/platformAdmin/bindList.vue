<template>
  <el-dialog
    v-drag
    :visible.sync="isShowTable"
    append-to-body
    title="绑定业务对象"
    width="90%"
  >
    <el-tabs
      tab-position="left"
      v-model="active"
      @tab-click="tabChange"
    >
      <el-tab-pane
        v-for="item in tabList"
        :label="item.label"
        :key="`${item.value}-${item.catogory}`"
        :name="`${item.value}-${item.catogory}`"
      >
        <headList
          :options="headsOption[item.type]"
          :ref="`head${item.value}-${item.catogory}`"
          :dict="dict"
          :active="active"
          :isShowTable="isShowTable"
          v-bind="$attrs"
          :deptList="activeDeptMap[`${item.value}-${item.catogory}`]"
          @getData="getPage"
        />
        <tableTab
          @getData="getList"
          :ref="`table${item.value}-${item.catogory}`"
          :heads="tableHeads[item.type]"
          :totalPage="totalPages[`${item.value}-${item.catogory}`]"
          :isShowTable="isShowTable"
          :tableData="tableData[`${item.value}-${item.catogory}`]"
          v-model="selectMap[`${item.value}-${item.catogory}`]"
        />
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      v-drag
      width="30%"
      append-to-body
      title="绑定结果如下，请确认是否提交？"
      :visible.sync="innerVisible"
    >
      <div class="confirm-table">
        <div class="list-item head">
          <div class="left">业务对象</div>
          <div class="right">数量</div>
        </div>
        <div
          class="list-item"
          v-for="(key, index) in Object.keys(labelMap)"
          :key="index"
        >
          <div class="left">{{ labelMap[key] }}</div>
          <div class="right">{{ numsAll[key] }}</div>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="cancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="submit"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="handleCancel"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        保存
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import tableTab from './tableTmp.vue'
import headList from './headList.vue'
import { getBindList, handleBind, binded } from "@/api/access/platformAdmin";
import { getDictDetail } from "@/api/security/alarmHistory";
import { deviceType } from '@/api/base/driver.js';
import { getDeptPerInit } from '@/api/base/dept';
export default {
  components: {
    tableTab,
    headList
  },
  props: {
    curInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    const tableHeads = {
      device: [
        { columnName: 'category', columnTitle: '终端类型' },
        { columnName: 'device_seq', columnTitle: '序列号' },
        { columnName: 'device_num', columnTitle: '赋码编号' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
      ],
      vehicle: [
        { columnName: 'number', columnTitle: '车辆编号' },
        { columnName: 'category', columnTitle: '车辆类型' },
        // { columnName: '', columnTitle: '绑定终端类型' },
        // { columnName: '', columnTitle: '绑定序列号' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
      ],
      vehicleTruck: [
        { columnName: 'number', columnTitle: '车辆编号' },
        { columnName: 'category', columnTitle: '车辆类型' },
        // { columnName: '', columnTitle: '绑定终端类型' },
        // { columnName: '', columnTitle: '绑定序列号' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
      ],
      use: [
        { columnName: 'name', columnTitle: '人员姓名' },
        { columnName: 'number', columnTitle: '工号' },
        { columnName: 'industry', columnTitle: '从业类型' },
        { columnName: 'post', columnTitle: '岗位类型' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
        // { columnName: '', columnTitle: '绑定终端类型' },
        // { columnName: '', columnTitle: '绑定终端' },
      ],
      base: [
        { columnName: 'number', columnTitle: '编号' },
        { columnName: 'category', columnTitle: '设施类型' },
        { columnName: 'name', columnTitle: '设施名称' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
        // { columnName: '', columnTitle: '绑定终端类型' },
        // { columnName: '', columnTitle: '绑定终端' },
      ],
      carriage: [
        { columnName: 'number', columnTitle: '车厢编号' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
        // { columnName: '', columnTitle: '绑定终端类型' },
        // { columnName: '', columnTitle: '绑定终端' },
      ],
      precision: [
        { columnName: 'number', columnTitle: '装备编号' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
        // { columnName: '', columnTitle: '绑定终端类型' },
        // { columnName: '', columnTitle: '绑定终端' },
      ],
      ship: [
        { columnName: 'number', columnTitle: '货船编号' },
        { columnName: 'dept_name', columnTitle: '所属机构' },
        // { columnName: '', columnTitle: '绑定终端类型' },
        // { columnName: '', columnTitle: '绑定终端' },
      ],
    }
    const tabList = [
      { label: '定位终端', value: 1, catogory: 'device', type: 'device' },
      { label: '授时终端', value: 5, catogory: 'device', type: 'device' },
      { label: '监测终端', value: 4, catogory: 'device', type: 'device' },
      { label: '穿戴式终端', value: 2, catogory: 'device', type: 'device' },
      { label: '短报文终端', value: 3, catogory: 'device', type: 'device' },
      { label: '车辆', value: 1, catogory: 'target', type: 'vehicle' },
      { label: '人员', value: 2, catogory: 'target',type: 'use' },
      { label: '基础设施', value: 3, catogory: 'target',type: 'base' },
      { label: '铁路货车车厢', value: 8, catogory: 'target',type: 'carriage' },
      { label: '精密装备', value: 9, catogory: 'target',type: 'precision' },
      { label: '货船', value: 7, catogory: 'target',type: 'ship' },
      { label: '外派', value: 5, catogory: 'target',type: 'use' },
      { label: '矿用卡车', value: 10, catogory: 'target',type: 'vehicleTruck' },
    ]
    this.initKey = tabList.reduce((obj, item) => {
      obj[`${item.value}-${item.catogory}`] = []
      return obj
    }, {})
    const headsOption = {
      device: [
        {
          label: '终端类型',
          value: 'category',
          type: 'selectD'
        },
        {
          label: '序列号',
          value: 'device_seq',
          type: 'input'
        },
        {
          label: '赋码号',
          value: 'device_num',
          type: 'input'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
      ],
      vehicle: [
        {
          label: '车辆编号',
          value: 'number',
          type: 'input',
        },
        {
          label: '车辆类型',
          value: 'category',
          type: 'vehicleType'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
        // {
        //   label: '绑定设备序列号',
        //   value: 'device_num',
        //   type: 'input'
        // },
      ],
      vehicleTruck: [
        {
          label: '车辆编号',
          value: 'number',
          type: 'input',
        },
        {
          label: '车辆类型',
          value: 'category',
          type: 'vehicleTruckType'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
        // {
        //   label: '绑定设备序列号',
        //   value: 'device_num',
        //   type: 'input'
        // },
      ],
      use: [
        {
          label: '人员姓名',
          value: 'name',
          type: 'input',
        },
        {
          label: '工号',
          value: 'number',
          type: 'input'
        },
        // {
        //   label: '绑定终端类型',
        //   value: 'device_type',
        //   type: 'bindTerminalType'
        // },
        // {
        //   label: '绑定设备序列号',
        //   value: 'device_num',
        //   type: 'input'
        // },
        {
          label: '从业类型',
          value: 'industry',
          type: 'bdmWorkerPost'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
      ],
      base: [
        {
          label: '设施类型',
          value: 'category',
          type: 'facilityType'
        },
        {
          label: '设施名称',
          value: 'name',
          type: 'input'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
      ],
      carriage: [
        {
          label: '车厢编号',
          value: 'number',
          type: 'input'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
        // {
        //   label: '绑定设备序列号',
        //   value: 'device_num',
        //   type: 'input'
        // },
      ],
      precision: [
        {
          label: '装备编号',
          value: 'number',
          type: 'input'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
        // {
        //   label: '绑定设备序列号',
        //   value: 'device_num',
        //   type: 'input'
        // },
      ],
      ship: [
        {
          label: '货船编号',
          value: 'number',
          type: 'input'
        },
        {
          label: '所属机构',
          value: 'dept_id',
          type: 'dept'
        },
        {
          label: '已绑定业务对象',
          value: 'bind_status',
          type: 'checkbox',
          trueLabel: 1,
          falseLabel: 0
        }
        // {
        //   label: '绑定设备序列号',
        //   value: 'device_num',
        //   type: 'input'
        // },
      ]
    }
    return {
      tableHeads,
      tabList,
      isShowTable: false,
      innerVisible: false,
      active: Object.keys(this.initKey)[0],
      tableData: JSON.parse(JSON.stringify(this.initKey)),
      selectMap: JSON.parse(JSON.stringify(this.initKey)),
      activeDeptMap: JSON.parse(JSON.stringify(this.initKey)),
      headsOption,
      dict: {
        deviceType: {},
        vehicleType: [],
        vehicleTruckType: [],
        bdmWorkerPost: []
      },
      totalPages: {},
      numsAll: {},
      deptOptions: []
    }
  },
  watch: {
    isShowTable (val) {
      if (val) {
        this.getList()
        this.getSelected()
        this.$nextTick(() => {
          if(!this.activeDeptMap[this.active].length) {
            this.activeDeptMap[this.active] = this.deptOptions;
          }
        });
      } else {
        this.tableData = JSON.parse(JSON.stringify(this.initKey))
        this.active = Object.keys(this.initKey)[0]
      }
    }
  },
  created() {
    this.getDictData()
    const obj = {}
    this.tabList.map( item => {
      obj[`${item.value}-${item.catogory}`] = item.label
    })
    this.labelMap = obj
    getDeptPerInit().then( res => {
      this.deptOptions = res.data;
    })
  },
  methods: {
    getDictData() {
      deviceType().then(res => {
        if (res?.data?.[0]?.children) {
          const obj = {}
          this.dict.bdmDeviceType = res.data[0].children
          res.data[0].children.forEach(item => {
            obj[item.value + '-device'] = item.children
          });
          this.dict.deviceType = obj
        }
      });
      getDictDetail("bdm_car_category").then((res) => {
        const list = res.data || [];
        this.dict.vehicleType = list
      });
      getDictDetail("bdm_truck_category").then((res) => {
        const list = res.data || [];
        this.dict.vehicleTruckType = list
      });
      getDictDetail("facility_type").then((res) => {
        const list = res.data || [];
        this.dict.facilityType = list
      });
      getDictDetail("bdm_worker_post").then((res) => {
        const list = res.data || [];
        this.dict.bdmWorkerPost = list
      });
    },
    handleCancel () {
      this.isShowTable = false
    },
    handleSubmit () {
      this.innerVisible = true
      const obj = {}
      const list = Object.keys(this.selectMap)
      list.forEach( key => {
        obj[key] = this.selectMap[key].length
      })
      this.numsAll = obj
    },
    cancel () {
      this.innerVisible = false
    },
    tabChange () {
      if (!this.tableData[this.active].length) {
        this.getList()
      }
      if(!this.selectMap[this.active]) {
        this.getSelected()
      }
      if(!this.activeDeptMap[this.active].length) {
        this.activeDeptMap[this.active] = this.deptOptions
      }
    },
    getPage () {
      const name = `table${this.active}`
      const dom = this.$refs[name]?.[0]
      if(dom) {
        dom.pageChange(1)
      } else {
        this.getList()
      }
    },
    getList () {
      const data = {
        "system_id": this.curInfo.id, // 平台id，必填
        "obj_cate": this.active.split('-')[1] === 'device' ? 'device' : 'target', // 值为target表示目标，device表示设备，必填
        "obj_type": this.active.split('-')[0], // target_type或device_type的值，必填
      }
      const name = `table${this.active}`
      const txt = `head${this.active}`
      const moreData = this.$refs[txt]?.[0].query || {}
      Object.keys(moreData).length && Object.keys(moreData).forEach( key => {
        if(moreData[key] === 0 || moreData) {
          data[key] = moreData[key]
        }
      })
      const params = this.$refs[name]?.[0].query || { size: 10, current: 1 }
      getBindList(params, data).then(res => {
        this.$set(this.tableData, this.active, res.data.data.records)
        this.$set(this.totalPages, this.active, res.data.data.total)
      })
    },
    getSelected() {
      const data = {
        "system_id": BigInt(this.curInfo.id), // 平台id，必填
      }
      binded(data).then(res => {
        const { device, target} = res.data.data
        const des = Object.keys(device) || []
        if(des.length) {
          des.forEach( key => {
            if(device[key]) {
              const name = `${key}-device`
              this.selectMap[name] = device[key]
            }
          })
        }
      const tas = Object.keys(target) || []
      if(tas.length) {
          tas.forEach( key => {
            if(target[key]) {
              const name = `${key}-target`
              this.selectMap[name] = target[key]
            }
          })
        }
      })
    },
    submit () {
      const obj = {
        system_id: BigInt(this.curInfo.id),
        obj_map: {
          device: {},
          target: {}
        }
      }
      const list = Object.keys(this.selectMap)
      list.forEach( key => {
        const [num ,catogory] = key.split('-')
        const arr = this.selectMap[key] || []
        obj.obj_map[catogory][num] = arr.length ? arr.map(it => BigInt(it)) : []
      })
      handleBind(obj).then(res => {
        this.$message.success('操作成功');
        this.cancel()
        this.handleCancel()
      })
    }
  },

};
</script>
<style lang="less" scoped>
.confirm-table {
  width: 100%;
  .list-item {
    width: 100%;
    display: flex;
    &.head {
      .left,
      .right {
        flex: 1;
        text-align: center;
        background-color: #409eff;
        color: #fff;
        padding: 5px 10px;
      }
    }
    .left,
    .right {
      flex: 1;
      text-align: center;
      padding: 5px 10px;
    }
  }
}
</style>
