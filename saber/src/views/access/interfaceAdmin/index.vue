<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','interfaceAdmin:edit','interfaceAdmin:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                del-msg="若该接口已授权，删除该接口将会自动解绑，请确认是否删除该接口?"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 接口编码 -->
          <el-table-column
            v-if="columns.visible('code')"
            :label="getLabel('code')"
            prop="code"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 接口url -->
          <el-table-column
            v-if="columns.visible('url')"
            :label="getLabel('url')"
            prop="url"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 接口名称 -->
          <el-table-column
            v-if="columns.visible('name')"
            :label="getLabel('name')"
            prop="name"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 接口功能描述 -->
          <el-table-column
            v-if="columns.visible('interfaceDesc')"
            :label="getLabel('interfaceDesc')"
            prop="interfaceDesc"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 所属业务服务 -->
          <el-table-column
            v-if="columns.visible('serviceId')"
            :label="getLabel('serviceId')"
            prop="serviceId"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ serviceObj[scope.row.serviceId] }}
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
        :service-list="serviceList"
      />
    </div>
  </basic-container>
</template>

<script>
import crudInterfaceAdmin from '@/api/access/interfaceAdmin';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import { serviceListAll } from '@/api/log/api';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('InterfaceAdmin', 'uniName'), // 平台接口管理
  crudMethod: { ...crudInterfaceAdmin }
});

export default {
  name: 'InterfaceAdmin',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'interfaceAdmin:add'],
        edit: ['admin', 'interfaceAdmin:edit'],
        del: ['admin', 'interfaceAdmin:del'],
        view: ['admin', 'interfaceAdmin:view']
      },
      headConfig: {
        item: {
          1: {
            name: '接口编码',
            type: 'input',
            value: 'code',
          },
          2: {
            name: '接口url',
            type: 'input',
            value: 'url',
          },
          3: {
            name: '所属业务服务',
            type: 'select',
            value: 'serviceId',
            options: []
          }
        },
        button: {
        }
      },
      isDetail: false,
      serviceList: [],
      serviceObj: {}
    };
  },
  created () {
    this.getServiceList();
  },
  methods: {
    getServiceList () {
      serviceListAll().then(res => {
        this.serviceList = res.data.data?.map(item => ({
          label: item.name,
          value: item.id
        }));
        this.serviceObj = this.serviceList.reduce((acc, curr) => {
          acc[curr.value] = curr.label;
          return acc;
        }, {});
        this.headConfig.item['3'].options = this.serviceList;
      });
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('InterfaceAdmin', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('InterfaceAdmin', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
