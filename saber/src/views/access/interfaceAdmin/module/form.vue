<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看平台接口' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 接口编码 -->
          <el-form-item
            :label="getLabel('code')"
            prop="code"
          >
            <el-input
              v-model.trim="form.code"
              :disabled="isDetail"
              :placeholder="getPlaceholder('code')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 接口url -->
          <el-form-item
            :label="getLabel('url')"
            prop="url"
          >
            <el-input
              v-model.trim="form.url"
              :disabled="isDetail"
              :placeholder="getPlaceholder('url')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 接口名称 -->
          <el-form-item
            :label="getLabel('name')"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              :disabled="isDetail"
              :placeholder="getPlaceholder('name')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 接口功能描述 -->
          <el-form-item
            :label="getLabel('interfaceDesc')"
            prop="interfaceDesc"
          >
            <el-input
              v-model.trim="form.interfaceDesc"
              :disabled="isDetail"
              :placeholder="getPlaceholder('interfaceDesc')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 所属业务服务 -->
          <el-form-item
            :label="getLabel('serviceId')"
            prop="serviceId"
          >
            <xh-select
              v-model="form.serviceId"
              :placeholder="getPlaceholder('serviceId')"
              clearable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in serviceList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  code: null,
  url: null,
  name: null,
  interfaceDesc: null,
  serviceId: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    serviceList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      rules: {
        code: { required: true, message: '请输入接口编码', trigger: 'blur' }, // 接口编码
        url: { required: true, message: '请输入接口url', trigger: 'blur' }, // 接口url
        name: { required: true, message: '请输入接口名称', trigger: 'blur' }, // 接口名称
        interfaceDesc: { required: true, message: '请输入接口功能描述', trigger: 'blur' }, // 接口功能描述
        serviceId: { required: true, message: '请选择所属业务服务', trigger: 'change' }, // 所属业务服务
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('InterfaceAdmin', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('InterfaceAdmin', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
