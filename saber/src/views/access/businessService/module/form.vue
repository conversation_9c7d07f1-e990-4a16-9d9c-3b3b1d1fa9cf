<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看业务服务' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 服务编码 -->
          <el-form-item
            :label="getLabel('code')"
            prop="code"
          >
            <el-input
              v-model.trim="form.code"
              :disabled="isDetail"
              maxlength="32"
              show-word-limit
              :placeholder="getPlaceholder('code')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 服务名称 -->
          <el-form-item
            :label="getLabel('name')"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              :disabled="isDetail"
              maxlength="32"
              show-word-limit
              :placeholder="getPlaceholder('name')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 服务功能描述 -->
          <el-form-item
            :label="getLabel('serviceDesc')"
            prop="serviceDesc"
          >
            <el-input
              v-model.trim="form.serviceDesc"
              :disabled="isDetail"
              type="textarea"
              maxlength="256"
              rows="5"
              show-word-limit
              :placeholder="getPlaceholder('serviceDesc')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  code: null,
  name: null,
  serviceDesc: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      rules: {
        code: { required: true, message: '请输入服务编码', trigger: 'blur' }, // 服务编码
        name: { required: true, message: '请输入服务名称', trigger: 'blur' }, // 服务名称
        serviceDesc: { required: true, message: '请输入服务功能描述', trigger: 'blur' }, // 服务功能描述
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('BusinessService', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('BusinessService', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
