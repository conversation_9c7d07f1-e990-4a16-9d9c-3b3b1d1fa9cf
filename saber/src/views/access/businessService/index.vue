<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','businessService:edit','businessService:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 服务编码 -->
          <el-table-column
            v-if="columns.visible('code')"
            :label="getLabel('code')"
            prop="code"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 服务名称 -->
          <el-table-column
            v-if="columns.visible('name')"
            :label="getLabel('name')"
            prop="name"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 服务功能描述 -->
          <el-table-column
            v-if="columns.visible('serviceDesc')"
            :label="getLabel('serviceDesc')"
            prop="serviceDesc"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
      />
    </div>
  </basic-container>
</template>

<script>
import crudBusinessService from '@/api/access/businessService';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('BusinessService', 'uniName'), // 业务服务
  crudMethod: { ...crudBusinessService }
});

export default {
  name: 'BusinessService',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'businessService:add'],
        edit: ['admin', 'businessService:edit'],
        del: ['admin', 'businessService:del'],
        view: ['admin', 'businessService:view']
      },
      headConfig: {
        item: {
          1: {
            name: '服务编码',
            type: 'input',
            value: 'code',
          },
          2: {
            name: '服务名称',
            type: 'input',
            value: 'name',
          }
        },
        button: {
        }
      },
      isDetail: false
    };
  },
  methods: {
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('BusinessService', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('BusinessService', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
