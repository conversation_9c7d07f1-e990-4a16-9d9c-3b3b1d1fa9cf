<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="50%"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="btnShow ? rules : {}"
      size="small"
      label-width="150px"
      class="rewriting-form-disable"
    >
      <el-row type="flex" span="24">
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="赋码机编号" prop="code_machine_num">
            <el-input
              v-model.trim="form.code_machine_num"
              placeholder="必须4位数字、字母组合"
              maxlength="50"
              :disabled="crud.status.title!=='新增'"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          v-if="crud.status.title==='新增'"
        >
          <el-form-item label="密码" prop="password">
            <el-input
              v-model.trim="form.password"
              placeholder="必须8到20位数字、大小写字母、特殊符号组合"
              maxlength="50"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="终端生产厂商" prop="manufacturer">
            <el-select
            v-model="form.manufacturer"
            placeholder="请选择终端生产厂商"
            :disabled="!btnShow"
          >
            <el-option
              v-for="(item, index) in manufacturerList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          v-if="crud.status.title !== '新增'"
        >
          <el-form-item label="创建日期" prop="create_time">
            <el-date-picker
              v-model="form.create_time"
              size="small"
              :disabled="true"
              value-format="yyyy-MM-dd"
              placeholder="请选择创建日期"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          v-if="crud.status.title !== '新增'"
        >
          <el-form-item label="状态" prop="enable">
            <xh-select
              v-model="form.enable"
              clearable
              size="small"
              :disabled="!btnShow"
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in dict.dict.statusType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import { passwordRuleUpdate } from "@/utils/validate";

const defaultForm = {
  code_machine_num: "",
  password: "",
  manufacturer: "",
  create_time: "",
  enable: "",
};
export default {
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      },
    },
    btnShow: {
      type: Boolean,
      default: true,
    },
    manufacturerList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const validator = (rule, value, callback) => {
      if(this.crud.status.title!=='新增' && !value) {
        return callback();
      }
      if (!this.form.id && !passwordRuleUpdate.test(value)) {
        this.$message.closeAll();
        this.$message.warning('密码必须8到20位, 且包含数字、大小写字母、特殊符号');
        callback(new Error("必须8到20位, 且包含数字、大小写字母、特殊符号"));
      } else {
        callback();
      }
    };
    const validateNu = (rule, value, callback) => {
      const reg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{4}$/
      if (!reg.test(value)) {
        this.$message.closeAll();
        this.$message.warning('赋码机编号必须4位数字、字母组合');
        callback(new Error("赋码机编号必须4位数字、字母组合"));
      } else {
        callback();
      }
    };
    return {
      rules: {
        code_machine_num: [{
            required: true,
            trigger: "blur",
          },
          {
            validator: validateNu,
            trigger: "blur",
          },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
          },
          {
            validator,
            trigger: "blur",
          },
        ],
        manufacturer: {
          required: true,
          trigger: "blur",
        },
        enable: {
          required: true,
          trigger: "blur",
        },
      },
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    [CRUD.HOOK.beforeSubmit]() {},
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit("cancelCU");
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU]() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
        }
      });
    },
    /** 提交- 之后 */
    [CRUD.HOOK.afterSubmit]() {
      // this.form = defaultForm;
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU]() {},
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

</style>
