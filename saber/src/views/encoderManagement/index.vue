<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          labelWidth="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{
            background: '#e1e5ee',
            'text-align': 'center',
          }"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%; height: calc(100% - 47px);"
          :cell-style="{ 'text-align': 'center' }"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            label="操作"
            width="150"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                :disabledEdit="scope.row.checkResult > 0"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-permission="permission.pwd"
                    type="text"
                    size="small"
                    @click="updatePwd(scope.row)"
                  >
                    新密码
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('code_machine_num')"
            prop="code_machine_num"
            :label="getLabel('code_machine_num')"
            min-width="120"
            :resizable="false"
          />
          <!-- <el-table-column
              v-if="columns.visible('password')"
              prop="password"
              :label="getLabel('password')"
              min-width="120"
            /> -->
          <el-table-column
            v-if="columns.visible('manufacturer')"
            prop="manufacturer"
            :label="getLabel('manufacturer')"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getLabelItem(scope.row?.manufacturer) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('create_time')"
            prop="create_time"
            :label="getLabel('create_time')"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.create_time }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('enable')"
            prop="enable"
            :label="getLabel('enable')"
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tooltip
                :content="scope.row.enable ? '启用' : '停用'"
                placement="top"
              >
                <el-switch
                  v-model="scope.row.enable"
                  active-color="#409EFF"
                  inactive-color="#ccc"
                  :active-value="1"
                  :inactive-value="0"
                  @change="changeEnabled(scope.row, scope.row.enable)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :manufacturerList="manufacturerList"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
    </div>
  </basic-container>
</template>

<script>
import crudEncoderManagement from "@/api/encoderManagement/index.js";
import CRUD, { presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import getLabel from "@/utils/getLabel";
import HeadCommon from "@/components/formHead/headCommon.vue";
import udOperation from "@/components/Crud/UD.operation.vue";
import eForm from "./module/form";
import { passwordRuleUpdate } from "@/utils/validate";
import { pagination as getList } from '@/api/bdTest/manuFactor';
// crud交由presenter持有
const crud = CRUD({
  title: getLabel("EncoderManagement", "uniName"),
  crudMethod: { ...crudEncoderManagement },
  optShow: {
    add: true,
    edit: false,
    del: false,
    download: false,
  },
});

export default {
  name: "EncoderManagement",
  components: {
    HeadCommon,
    crudOperation,
    pagination,
    udOperation,
    eForm,
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: ["statusType"],
  data() {
    return {
      manufacturerList: [],
      permission: {
        add: ["admin", "encoderManagement:add"],
        edit: ["admin", "encoderManagement:edit"],
        del: ["admin", "encoderManagement:del"],
        pwd: ["admin", "encoderManagement:pwd"],
      },
      headConfig: {
        item: {
          1: {
            name: "赋码机编号",
            type: "input",
            value: "code_machine_num",
          },
          2: {
            name: "终端生产厂商",
            type: "select",
            value: "manufacturer",
            options: []
          },
          3: {
            name: "状态",
            type: "select",
            value: "enable",
            dictOptions: "statusType",
          },
        },
        button: {},
      },
      btnShow: true, // 显示确认取消按钮
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling() {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    },
  },
  mounted() {
    this.getManufacturerList();
  },
  methods: {
    getLabelItem(manufacturer) {
      const item = this.manufacturerList.find( item => item.value === manufacturer);
      return item?.label || '-';
    },
    getManufacturerList() {
      getList({
        page: 0,
        size: 9999
      }).then(res => {
        this.manufacturerList = res.data.content.map(item => {
          return {
            label: item.name,
            value: item.code
          };
        });
        this.headConfig.item['2'].options = this.manufacturerList;
      });
    },
    /**
     * 改变状态
     * @param {Object} data 新的数据
     * @param {Object} val
     */
    changeEnabled(data, val) {
      this.$confirm(`确认切换状态？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          crud.crudMethod
            .edit({
              ...data,
              enable: val,
            })
            .then(() => {
              crud.notify("状态切换成功！", "success");
              crud.refresh();
            })
            .catch((err) => {
              data.enable = data.enable === 1 ? 0 : 1;
            });
        })
        .catch(() => {
          data.enable = data.enable === 1 ? 0 : 1;
        });
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel(value) {
      return getLabel("EncoderManagement", value);
    },
    parseTimes(time) {
      return this.$moment(time).format("YYYY-MM-DD HH:mm:ss");
    },
    toDetails(param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    updatePwd(data) {
      // 必须8到20位, 且包含数字、大小写字母、特殊符号
      this.$prompt('请输入新密码', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern:passwordRuleUpdate,
        inputPlaceholder: "必须8到20位, 且包含数字、大小写字母、特殊符号",
        inputErrorMessage: '必须8到20位, 且包含数字、大小写字母、特殊符号'
      }).then(({ value }) => {
        crud.crudMethod
          .edit({
            ...data,
            password: value,
          })
          .then(() => {
            crud.notify("密码修改成功！", "success");
            crud.refresh();
          });
      }).catch(() => {
      });
    },
    cancel() {
      this.btnShow = true;
      console.log(crud.form);
    },
  },
};
</script>

  <style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
/deep/.el-table th {
  text-align: center;
}
/deep/.el-table td {
  text-align: center;
}
.app-container
  /deep/.el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: #fcf0c1;
}
</style>
