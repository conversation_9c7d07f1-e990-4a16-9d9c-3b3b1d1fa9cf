<template>
  <div class="app-container" style="padding: 0">
    <basic-container style="padding-bottom: 6px">
      <div class="add-page">
        <div class="title">终端信息新增</div>
        <el-tabs v-model="activeName">
          <el-tab-pane label="单个入库" name="SingleImport">
          </el-tab-pane>
          <el-tab-pane label="批量入库" name="BatchImport"></el-tab-pane>
        </el-tabs>
        <component :is="activeName"  class="content" :dict="dict"></component>
      </div>
    </basic-container>
  </div>
</template>

  <script>
import SingleImport from './singleImport.vue'
import BatchImport from './batchImport.vue'
import { pagination as getList } from "@/api/bdTest/manuFactor";
import { mapGetters } from 'vuex';

export default {
  name: "PutInStorage",
  components: {
    SingleImport,
    BatchImport,
  },
  mixins: [],
  dicts: ["testDeviceType", "bdmDeviceType"],
  data () {
    return {
      activeName: "SingleImport",
    };
  },
  computed: {
    ...mapGetters(['removeTags'])
  },
  activated() {
    const path = this.$route.fullPath;
    const removeIndex = this.removeTags.indexOf(path);
    if (removeIndex !== -1) {
      this.removeTags.splice(removeIndex, 1);
      this.$store.commit('SET_REMOVE_TAGS', this.removeTags);
    }
    this.getManufacturerList();
  },
  methods: {
    getManufacturerList () {
      getList({
        page: 0,
        size: 9999,
      }).then((res) => {
        let list = res.data.content || []
        const obj = {}
        if (list.length) {
          list = list.map((item) => {
            obj[item.code] = item.name
            return {
              label: item.name,
              value: item.code,
            };
          });
        }
        this.$set(this.dict, 'vendor', list);
        this.$set(this.dict.dict, 'vendor', obj);
      });
    },
  }
};
</script>

<style lang="less" scoped>
.add-page {
  height: 100%;
  overflow-y: auto;
  .title {
    text-align: left;
    font-size: 14px;
    color: #303133;
    padding: 10px 0;
    border-bottom: 2px solid #dcdfe6;
    font-weight: bold;
  }
  .content{
    margin: 15px auto;
  }
}
</style>
