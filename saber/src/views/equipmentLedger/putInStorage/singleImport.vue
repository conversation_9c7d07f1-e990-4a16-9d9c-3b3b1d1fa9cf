<template>
  <el-form
    ref="ruleForm"
    class="form-cur"
    :model="form"
    :rules="rules"
    label-width="150px"
  >
    <el-form-item label="终端类型" prop="category">
      <el-cascader
        placeholder="请选择终端类型"
        :options="dict.bdmDeviceType"
        :show-all-levels="false"
        v-model.trim="form.category"
      ></el-cascader>
    </el-form-item>
    <el-form-item label="赋码编号" prop="deviceNum">
      <DeviceNumSelect
        :source-form="form"
        :value="form.deviceNum"
        typeDictValue=""
        :dict="dict"
        @handleChange="handleChange"
      />
    </el-form-item>
    <el-form-item label="序列号" prop="uniqueId">
      <el-input
        placeholder="请输入序列号"
        v-model.trim="form.uniqueId"
        maxlength="50"
        disabled="true"
      />
    </el-form-item>
    <el-form-item label="终端型号" prop="model">
      <el-input
        placeholder="请输入终端型号"
        maxlength="50"
        v-model="form.model"
        disabled="true"
      ></el-input>
    </el-form-item>
    <el-form-item label="设备类别" prop="type">
      <xh-select
        v-model="form.type"
        clearable
        size="small"
        disabled="true"
        placeholder="请选择设备类别"
      >
        <el-option
          v-for="item in dict.testDeviceType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </xh-select>
    </el-form-item>
    <!-- <el-form-item label="产品序列号" prop="serial">
      <el-input
        placeholder="请输入产品序列号"
        disabled="true"
        maxlength="50"
        v-model="form.serial"
      ></el-input>
    </el-form-item> -->
    <el-form-item label="IMEI号" prop="imei">
      <el-input
        placeholder="请输入IMEI号"
        maxlength="50"
        disabled="true"
        v-model="form.imei"
      ></el-input>
    </el-form-item>
    <el-form-item label="北斗芯片序列号" prop="bdChipSn">
      <el-input
        v-model.trim="form.bdChipSn"
        disabled="true"
        placeholder="请输入北斗芯片序列号"
        maxlength="50"
      />
    </el-form-item>
    <el-form-item label="生产厂商" prop="vendor">
      <xh-select
        v-model="form.vendor"
        size="small"
        placeholder="请选择生产厂商"
        disabled="true"
      >
        <el-option
          v-for="item in dict.vendor"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </xh-select>
    </el-form-item>
    <el-form-item label="采购日期" prop="buytime">
      <el-date-picker
        type="date"
        placeholder="选择采购日期"
        v-model="form.buytime"
        format="yyyy-MM-dd"
        style="width: 100%"
      ></el-date-picker>
    </el-form-item>
    <el-form-item class="btn">
      <el-button size="small" type="primary" @click="submitForm">新增</el-button>
      <el-button size="small" @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { addBatch } from "@/api/equipmentLedger/equipmentManage.js";
import _ from "lodash";
import DeviceNumSelect from './deviceNumSelect.vue';
import { mapGetters } from 'vuex';

export default {
  name: "SingleImport",
  components: {
    DeviceNumSelect,
  },
  mixins: [],
  props: {
    dict: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        category: "",
        uniqueId: "",
        model: "",
        deviceNum: "",
        serial: "",
        type: "",
        bdChipSn: "",
        imei: "",
        buytime: "",
        vendor: "",
      },
      rules: {
        category: [
          { required: true, message: "请选择终端类型", trigger: "change" },
        ],
        uniqueId: [
          { required: true, message: "请输入序列号", trigger: "blur" },
        ],
        model: [{ required: true, message: "请输入终端型号", trigger: "blur" }],
        deviceNum: [
          { required: true, message: "请输入赋码编号", trigger: "change" },
        ],
        serial: [
          { required: true, message: "请输入产品序列号", trigger: "blur" },
        ],
        type: [{ required: true, message: "请输入设备类别", trigger: "blur" }],
        bdChipSn: [
          { required: true, message: "北斗芯片序列号", trigger: "blur" },
        ],
        imei: [{ required: true, message: "请输入IMEI号", trigger: "blur" }],
        // buytime: [
        //   { required: true, message: "请选择采购日期", trigger: "change" },
        // ],
        vendor: [{ required: true, message: "请选择厂家", trigger: "blur" }],
      },
    };
  },
  methods: {
    setData(item) {
      this.form.model = item.deviceModel || ''
      this.form.vendor = item.manufacturer || ''
      this.form.imei = item.imei || ''
      this.form.bdChipSn = item.chipSeq || ''
      this.form.uniqueId = item.deviceSeq || ''
      this.form.type = item.deviceCate || ''
      this.form.deviceNum = item.deviceNum || ''
    },
    ruleData(data) {
      data.forEach((item) => {
        if (!item.children || item.children.length === 0) {
          delete item.children;
        } else {
          item.children = this.ruleData(item.children);
        }
      });
      return data;
    },
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const { category: val, buytime, ...others } = this.form;
          const [deviceType, category] = val;
          addBatch([
            {
              category: Number(category),
              deviceType: Number(deviceType),
              buytime: buytime ? this.$moment(buytime).format("YYYY-MM-DD HH:mm:ss") : '',
              ...others,
            },
          ],0).then((res) => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.resetForm();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    handleChange(item) {
      this.setData(item)
    },
  },
  computed: {
    ...mapGetters(['removeTags'])
  },
  activated() {
    const path = this.$route.fullPath;
    const removeIndex = this.removeTags.indexOf(path);
    if (removeIndex !== -1) {
      this.removeTags.splice(removeIndex, 1);
      this.$store.commit('SET_REMOVE_TAGS', this.removeTags);
      if(this.form.category === 0 || this.form.category) {
        this.form.category = ''
        this.$refs.ruleForm.resetFields();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.form-cur {
  width: 800px;
  margin-top: 15px;
  .btn {
    margin-top: 25px;
    text-align: center;
  }
}
</style>
