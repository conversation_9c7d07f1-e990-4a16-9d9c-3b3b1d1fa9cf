<template>
  <div class="main">
    <div class="statistic">
      <el-card
        v-for="item in statisticList"
        :key="item.title"
        class="category-card"
      >
        <div class="card-content">
          <span class="category-title">{{ item.title }}</span>
          <span class="category-num">{{ item.number }}</span>
        </div>
      </el-card>
    </div>
    <basic-container>
      <div class="xh-container">
        <div class="head-container">
          <HeadCommon
            :dict="dict"
            :permission="permission"
            :head-config="headConfig"
            label-width="100px"
          />
        </div>
        <!--工具栏-->
        <div class="xh-crud-table-container">
          <crudOperation
            :permission="permission"
            :download="false"
          />
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            :data="crud.data"
            :cell-style="{ 'text-align': 'center' }"
            :max-height="tableMaxHeight"
            style="width: 100%;height: calc(100% - 47px)"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              v-permission="[
                'admin',
                'equipmentLedger:edit',
                'equipmentLedger:back',
              ]"
              label="操作"
              width="110"
              fixed="right"
              :resizable="false"
            >
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :hideEdit="true"
                  :hideDel="true"
                >
                  <template slot="right">
                    <el-button
                      v-if="scope.row.storageState !== 1"
                      v-permission="permission.edit"
                      class="table-button-edit"
                      size="small"
                      type="text"
                      @click="toEdit(scope.row)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      type="text"
                      class="row-btn table-button-view"
                      @click="toDetails(scope.row)"
                    >
                      详情
                    </el-button>
                    <el-button
                      v-if="scope.row.storageState === 1"
                      size="small"
                      type="text"
                      class="row-btn"
                      :disabled="scope.row.backStatus === 1"
                      @click="goBack(scope.row)"
                    >
                      <span v-if="scope.row.backStatus !== 1">回库</span>
                      <el-tooltip
                        v-else
                        content="先删除相应已录入的终端"
                        placement="top-start"
                      >
                        <span>回库</span>
                      </el-tooltip>
                    </el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('deviceNum')"
              label="赋码编号"
              prop="deviceNum"
              min-width="180"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('uniqueId')"
              label="序列号"
              prop="uniqueId"
              min-width="180"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('categoryName')"
              label="终端类型"
              prop="categoryName"
              min-width="220"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              v-if="columns.visible('entryTime')"
              label="入库时间"
              prop="entryTime"
              show-overflow-tooltip
              min-width="180"
              :resizable="false"
            >
              <template slot-scope="scope">
                <span class="table-date-td">
                  {{ scope.row.entryTime }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('deliveryTime')"
              label="出库时间"
              prop="deliveryTime"
              min-width="180"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                <span class="table-date-td">
                  {{ scope.row.deliveryTime }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('storageState')"
              label="管理状态"
              prop="storageState"
              min-width="100"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                {{ getEnumDictLabel("manageStatus", scope.row.storageState) }}
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('deptName')"
              label="使用单位"
              prop="deptName"
              min-width="160"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
        </div>
        <!--分页组件-->
        <pagination />
      </div>
    </basic-container>
    <!--表单渲染-->
    <eForm
      :dict="dict"
      :manufacturerList="manufacturerList"
      :btnShow="btnShow"
      :code-number="codeNumber"
      @cancelCU="cancel"
    />
  </div>
</template>

<script>
import CRUD, { presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import udOperation from "@/components/Crud/UD.operation";
import HeadCommon from "@/components/formHead/headCommon.vue";
import eForm from "./module/form";
import { pagination as getList } from "@/api/bdTest/manuFactor";
import api, { ledgerCount, recycle } from "@/api/equipmentLedger/equipmentManage.js";

// crud交由presenter持有
const crud = CRUD({
  title: "",
  crudMethod: { ...api },
  queryOnPresenterCreated: false,
});

export default {
  name: "EquipmentManage",
  components: {
    crudOperation,
    pagination,
    udOperation,
    HeadCommon,
    eForm,
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: ["bdmDeviceType", "manageStatus", 'testDeviceType'],
  data () {
    return {
      permission: {
        add: ["admin", "equipmentLedger:add"],
        edit: ["admin", "equipmentLedger:edit"],
        back: ["admin", "equipmentLedger:back"],
        del: ["admin", "equipmentLedger:del"],
      },
      headConfig: {
        initQuery: false,
        item: {
          0: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum'
          },
          1: {
            name: "序列号",
            type: "input",
            value: "uniqueId",
          },
          2: {
            name: "终端类型",
            type: "select",
            filterable: true,
            value: "category",
            dictOptions: "bdmDeviceType",
          },
          3: {
            name: "管理状态",
            type: "select",
            value: "storageState",
            dictOptions: "manageStatus",
          },
          4: {
            name: '使用单位',
            type: 'extra',
            value: 'userDeptId'
          },
        },
        button: {},
      },
      statisticList: [
        {
          title: "总数",
          prop: "totalCount",
          number: 0,
        },
        {
          title: "出库",
          prop: "outCount",
          number: 0,
        },
        {
          title: "入库",
          prop: "centerCount",
          number: 0,
        },
      ],
      btnShow: true, // 显示确认取消按钮
      terminalList: [],
      manufacturerList: [],
      manageStatus: [],
      codeNumber: ''
    };
  },
  activated () {
    this.getManufacturerList();
    this.crud.toQuery();
  },
  methods: {
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      this.getNums();
    },
    getNums () {
      ledgerCount().then((res) => {
        Object.keys(this.statisticList).forEach((index) => {
          const item = this.statisticList[index];
          item.number = res.data[item.prop];
        });
      });
    },
    getManufacturerList () {
      getList({
        page: 0,
        size: 9999,
      }).then((res) => {
        this.manufacturerList = res.data.content.map((item) => {
          return {
            label: item.name,
            value: item.code,
          };
        });
      });
    },
    goBack ({ id }) {
      this.$confirm(`确定回库?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        recycle({ id }).then(res => {
          this.$message.success('操作成功');
          crud.refresh();
        });
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return "";
      }
    },
    toEdit (param) {
      this.codeNumber = param.deviceNum;
      crud.toEdit(param);
    },
    toDetails (param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel () {
      this.btnShow = true;
      this.codeNumber = '';
    },
  },
};
</script>

<style lang="less" scoped>
.avue-view {
  height: 100%;
  // padding: 0 6px 4px;
}

.head-tabs {
  margin-bottom: 4px;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: #fcf0c1;
}

.main {
  display: flex;
  flex-direction: column;
  height: 100%;
  // padding: 0 6px 4px;
  .top-nums {
    display: flex;
    background-color: #fff;
    padding: 20px 0;
  }
}

.statistic {
  padding-bottom: 8px;
  display: flex;

  .category-card {
    flex: 1;
    margin-right: 8px;
    cursor: pointer;

    ::v-deep .el-card__body {
      padding: 12px 12px !important;
    }
  }

  .card-content {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    user-select: none;

    .category-title {
      font-size: 16px;
    }

    .category-num {
      margin-top: 2px;
      font-size: 26px;
      font-weight: bold;
      height: 40px;
    }
  }
}

.basic-container {
  height: calc(100% - 100px);
}

::v-deep .el-table__row {
  td {
    box-sizing: border-box;
    height: 42px;
    padding: 0;
  }
}

/*滚动条中间滑动部分*/
::v-deep ::-webkit-scrollbar-thumb {
  background-color: rgba(125, 125, 125, 0.5);
}

.rule-label {
  color: blue;
  cursor: pointer;
}

// 覆盖公共样式, 防止alarmType搜索框被隐藏
::v-deep .el-card {
  overflow: inherit;
}

.checkbox {
  zoom: 200%;
}

.num-attachReal {
  color: #4096d1;
  text-decoration: underline;
  cursor: pointer;
}
</style>
