<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="50%"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="btnShow ? rules : {}"
      size="small"
      label-width="150px"
      class="rewriting-form-disable"
    >
      <el-row type="flex" span="24">
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="终端类型" prop="category">
            <el-cascader
              placeholder="请选择终端类型"
              :options="dict.bdmDeviceType"
              :disabled="!btnShow"
              :show-all-levels="false"
              v-model="categoryCpt"
            ></el-cascader>
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="赋码编号" prop="deviceNum">
            <DeviceNumSelect
              :source-form="form"
              :value="form.deviceNum"
              typeDictValue=""
              :dict="dict"
              v-bind="$attrs"
              :disabled="!btnShow"
              @handleChange="handleChange"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="序列号" prop="uniqueId">
            <el-input
              v-model.trim="form.uniqueId"
              placeholder="序列号"
              maxlength="50"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="终端型号" prop="model">
            <el-input
              v-model.trim="form.model"
              placeholder="终端型号"
              maxlength="50"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="设备类别" prop="type">
            <xh-select
              v-model="form.type"
              clearable
              size="small"
              :disabled="true"
              placeholder="请选择设备类别"
            >
              <el-option
                v-for="item in dict.testDeviceType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <!-- <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="产品序列号" prop="bdChipSn">
            <el-input
              v-model.trim="form.bdChipSn"
              placeholder="请输入产品序列号"
              maxlength="50"
              :disabled="true"
            />
          </el-form-item>
        </div> -->
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="IMEI号" prop="imei">
            <el-input
              v-model.trim="form.imei"
              placeholder="IMEI号"
              maxlength="50"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="采购日期" prop="buytime">
            <el-date-picker
              v-model="form.buytime"
              size="small"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
              placeholder="请选择采购日期"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="北斗芯片序列号" prop="bdChipSn">
            <el-input
              v-model.trim="form.bdChipSn"
              :disabled="true"
              placeholder="请输入北斗芯片序列号"
              maxlength="50"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="生产厂商" prop="vendor">
            <xh-select
              v-model="form.vendor"
              clearable
              size="small"
              :disabled="true"
              placeholder="请选择生产厂商"
            >
              <el-option
                v-for="item in manufacturerList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="!btnShow"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="入库人" prop="inputerName">
            <el-input
              v-model.trim="form.inputerName"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div
          v-if="!btnShow"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="入库时间" prop="entryTime">
            <el-input
              v-model.trim="form.entryTime"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div
          v-if="!btnShow"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="出库人" prop="outputerName">
            <el-input
              v-model.trim="form.outputerName"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div
          v-if="!btnShow"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="出库时间" prop="deliveryTime">
            <el-input
              v-model.trim="form.deliveryTime"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div
          v-if="!btnShow"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="管理状态" prop="backStatusName">
            <el-input
              v-model.trim="form.backStatusName"
              :disabled="true"
              maxlength="50"
            />
          </el-form-item>
        </div>
        <div
          v-if="!btnShow"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item label="使用单位" prop="deptName">
            <el-input
              v-model.trim="form.deptName"
              :disabled="true"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import DeviceNumSelect from '../../putInStorage/deviceNumSelect.vue';

const defaultForm = {
  category: [],
  uniqueId: "",
  model: "",
  deviceNum: "",
  type: "",
  bdChipSn: "",
  imei: "",
  buytime: "",
  bdChipSn: "",
  vendor: "",
};
export default {
  mixins: [form(defaultForm)],
  components: {
    DeviceNumSelect,
  },
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      },
    },
    btnShow: {
      type: Boolean,
      default: true,
    },
    manufacturerList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      rules: {
        category: [
          { required: true, message: "请选择终端类型", trigger: "change" },
        ],
        uniqueId: [
          { required: true, message: "请输入序列号", trigger: "blur" },
        ],
        model: [{ required: true, message: "请输入终端型号", trigger: "blur" }],
        deviceNum: [
          { required: true, message: "请输入赋码编号", trigger: "change" },
        ],
        type: [{ required: true, message: "请输入设备类别", trigger: "blur" }],
        bdChipSn: [
          { required: true, message: "请输入产品序列号", trigger: "blur" },
        ],
        imei: [{ required: true, message: "请输入IMEI号", trigger: "blur" }],
        // buytime: [
        //   { required: true, message: "请选择采购日期", trigger: "change" },
        // ],
        vendor: [{ required: true, message: "请选择厂家", trigger: "blur" }],
      },
    };
  },
  computed: {
    categoryCpt: {
      get() {
        return [`${this.form.deviceType}`, `${this.form.category}`]
      },
      set(val) {
        const [deviceType, category] = val
        this.form.deviceType = Number(deviceType);
        this.form.category = Number(category);
      }
    }
  },
  methods: {
    handleChange(item) {
      this.setData(item)
    },
    setData(item) {
      this.form.model = item.deviceModel || ''
      this.form.vendor = item.manufacturer || ''
      this.form.imei = item.imei || ''
      this.form.bdChipSn = item.chipSeq || ''
      this.form.uniqueId = item.deviceSeq || ''
      this.form.type = item.deviceCate || ''
      this.form.deviceNum = item.deviceNum || ''
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.defaultValue = this.form.deviceNum
        }
      });
    },
    [CRUD.HOOK.beforeSubmit]() {},
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit("cancelCU");
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU]() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
        }
      });
    },
    /** 提交- 之后 */
    [CRUD.HOOK.afterSubmit]() {
      // this.form = defaultForm;
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU]() {
    }
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}
</style>
