<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            v-if="columns.visible('deviceNum')"
            prop="deviceNum"
            min-width="180"
            label="赋码编号"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('uniqueId')"
            prop="uniqueId"
            min-width="180"
            label="序列号"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            prop="deptName"
            min-width="160"
            label="所属机构"
          />
          <el-table-column
            v-if="columns.visible('deviceType')"
            prop="deviceType"
            min-width="120"
            label="终端类别"
          >
            <template slot-scope="scope">
              <div>
                {{ getEnumDictLabel('bdmDeviceType', scope.row.deviceType) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('targetName')"
            prop="targetName"
            min-width="120"
            label="监控对象"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.targetName || $utils.emptymap.targetName }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('action')"
            prop="action"
            min-width="100"
            label="终端状态"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.endTime ? '下线 ' : '上线' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('startTime')"
            prop="startTime"
            min-width="160"
            label="上线时间"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.startTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('endTime')"
            prop="endTime"
            label="下线时间"
            min-width="160"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.endTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('onlineTime')"
            prop="onlineTime"
            label="在线时长"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ durationTimeDifference(scope.row.onlineTime) }}</span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
  </basic-container>
</template>

<script>
import crudOnline from '@/api/statistics/online.js';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import HeadCommon from '@/components/formHead/headCommon.vue';
// crud交由presenter持有
const crud = CRUD({
  title: '在线记录查询',
  crudMethod: { ...crudOnline },
  optShow: {
    add: false,
    edit: false,
    del: false,
    download: false,
  },
  queryOnPresenterCreated: false
});

export default {
  name: 'DeviceOnline',
  components: {
    crudOperation,
    pagination,
    HeadCommon
  },
  mixins: [presenter(crud)],
  dicts: [
    'bdmDeviceType'
  ],
  data() {
    return {
      visibleForm: [
        'deviceType',
        'uniqueId',
        'deptName',
        'deviceNum',
        'targetName',
        'action',
        'startTime',
        'endTime',
        'onlineTime'
      ],
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum',
          },
          2: {
            name: '开始时间',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束时间',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '监控对象',
            type: 'input',
            value: 'targetName'
          },
          5: {
            name: '序列号',
            type: 'input',
            value: 'uniqueId'
          },
          6: {
            name: '终端类别',
            type: 'select',
            value: 'deviceType',
            dictsOptions: 'bdmDeviceType'
          }
        },
        button: {
        }
      }
    };
  },
  methods: {
    durationTimeDifference(value) {
      if(value === null) {
        return '';
      }
      let time = this.$utils.timeDifference(value * 1);
      return time;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.app-container ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1 !important
}
</style>
