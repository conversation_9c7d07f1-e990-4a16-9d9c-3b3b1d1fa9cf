<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation :download="false"/>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
        >
          <el-table-column
            label="操作"
            width="60"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="operation">
                <el-button
                  v-permission="permission.analysis"
                  size="small"
                  type="text"
                  @click="decode(scope.row)"
                >
                  解析
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('deviceType')"
            prop="deviceType"
            label="终端类别"
            width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel('bdmDeviceType', scope.row.deviceType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('uniqueId')"
            prop="uniqueId"
            label="序列号"
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deviceNum')"
            prop="deviceNum"
            label="赋码编号"
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('eventTime')"
            prop="eventTime"
            label="通信时间"
            min-width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ parseTime(scope.row.eventTime) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('content')"
            prop="content"
            label="通信内容"
            min-width="380"
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tooltip
                :enterable="true"
                :content="scope.row.content"
              >
                <div class="ellipsis">{{ scope.row.content }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :modal="false"
      class="dialog"
      title="详情"
      width="40vw"
    >
      <pre class="decode-content">{{ decodeContent }}</pre>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="dialogVisible = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import api, { parseprotocol } from '@/api/terminalLog.js';
import CRUD, { header, presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import HeadCommon from '@/components/formHead/headCommon.vue';

const paginationApi = ({endTime, startTime, ...data}) => {
  if(startTime) data.startTime = parseInt(startTime/1000);
  if(endTime) data.endTime = parseInt(endTime/1000);
  return api.pagination({...data, event: 1});
};
const crud = CRUD({
  title: '注册信息记录',
  optShow: {
    add: false,
    edit: false,
    download: false
  },
  crudMethod: { ...api, pagination: paginationApi },
  queryOnPresenterCreated: false
});

export default {
  name: 'RegistRecord',
  components: {
    crudOperation,
    pagination,
    HeadCommon
  },
  mixins: [
    presenter(crud),
    header()
  ],
  dicts: [
    'bdmDeviceType'
  ],
  data() {
    return {
      dialogVisible: false,
      decodeContent: '',
      permission: {
        analysis: ['admin','registRecord:analysis']
      },
      headConfig: {
        initQuery: false,
        item: {
          1: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum',
          },
          2: {
            name: '开始时间',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束时间',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '终端类别',
            type: 'select',
            value: 'deviceType',
            dictsOptions: 'bdmDeviceType'
          },
          5: {
            name: '序列号',
            type: 'input',
            value: 'uniqueId'
          }
        },
        button: {
        }
      }
    };
  },
  mounted () {
    this.$nextTick(() => {
      this.crud.loading = false;
    });
  },
  methods: {
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      if (!this.crud.query.deviceNum && !this.crud.query.uniqueId) {
        this.$message.warning('赋码编号和序列号不能同时为空, 请输入后查询');
        return false;
      }
    },
    decode(row) {
      parseprotocol({
        msg: row.content,
        iot_protocol: row.iotProtocol
      }).then(res => {
        if (res.code === 200) {
          this.decodeContent = res.data;
          this.dialogVisible = true;
        }
      });
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.decode-content {
  height: 400px;
  overflow-y: auto;
  font-size: 16px;
}

.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
