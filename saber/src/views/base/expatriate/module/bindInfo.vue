<template>
  <el-dialog
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    :modal-append-to-body="false"
    v-dialog-drag
    title="绑定终端"
    width="70%"
    @close="closeDialog"
  >
    <!--表格渲染-->
    <el-table
      ref="table"
      :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
      :data="data"
      :cell-style="{'text-align':'center'}"
      height="65vh"
    >
      <el-table-column
        type="index"
        label="#"
      />
      <el-table-column
        :label="getLabel('deviceType')"
        :show-overflow-tooltip="true"
        min-width="100"
        prop="deviceTypeName"
      >
      </el-table-column>
      <el-table-column
        :label="getLabel('category')"
        :show-overflow-tooltip="true"
        min-width="100"
        prop="categoryName"
      >
      </el-table-column>
      <el-table-column
        :label="getLabel('deviceNum')"
        :show-overflow-tooltip="true"
        prop="deviceNum"
        min-width="100"
      />
      <el-table-column
        :label="getLabel('uniqueId')"
        :show-overflow-tooltip="true"
        prop="uniqueId"
        min-width="100"
      />
      <el-table-column
        :label="getLabel('deptName')"
        :show-overflow-tooltip="true"
        min-width="100"
        prop="deptName"
      />

      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </el-table>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import {driverBindVehicleDetailList} from '@/api/base/expatriate.js';
const defaultForm = {
};
export default {
  name: 'BindInfo',
  components: {
  },
  mixins: [],
  props: {
    dict: {
      type: Object,
      required: true
    },// 外部控制显隐
    bindUser: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    },
    bindInfoDialogVisible: {
      type: Boolean,
      default: false
    },

  },
  data() {
    return {
      data:[],
      dialogVisible: true,

    };
  },
  watch: {
    bindInfoDialogVisible (newValue) {
      this.dialogVisible = newValue;
    },
  },
  created () {
    this.driverBindVehicleDetail();
  },
  methods: {
    driverBindVehicleDetail(){
      driverBindVehicleDetailList({id: this.bindUser.id}).then((res) => {
        console.log('-> res.data', res.data);
        this.data = res.data;
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Driver', value);
    },

    // 关闭dialog
    closeDialog () {
      this.$emit('closeBind');
    },

    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getMaster (master) {
      let result = '';
      switch (master) {
      case 0:
        result = '否';
        break;
      case 1:
        result = '是';
        break;

      default:
        break;
      }
      return result;
    }

  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

.subTitle /deep/ .el-divider__text {
  font-size: 14px;
  font-weight: 700;
  color: #606266;
}

/deep/ .el-form-item__label {
  font-weight: 400;
}

/deep/ .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

/deep/ .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

/deep/ .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

/deep/ .avatar {
  width: 128px;
  height: 128px;
  display: block;
}

/deep/ .el-upload-list__item-actions:hover {
  opacity: 1;
}

/deep/ .el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #ffffff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(255, 255, 255, 0.5);
  transition: opacity .3s;
}

/deep/ .el-upload-list__item-actions:hover span {
  display: inline-block;
}

/deep/ .upload-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -5px;
  margin-top: -15px;
  width: 30px;
  height: 30px;
}

/deep/ .el-icon-delete {
  font-size: 30px;
}

// /deep/ .el-dialog__body {
//   padding: 0;
// }
.section-title {
  height: 100%;
  padding: 0 20px;
  font-size: 16px;
  color: #409EFE;
}

::v-deep .el-collapse-item__header {
  border-bottom: 1px solid #e1e5e8 !important;

  &.is-active {
    border-bottom: none;
  }

}
</style>
