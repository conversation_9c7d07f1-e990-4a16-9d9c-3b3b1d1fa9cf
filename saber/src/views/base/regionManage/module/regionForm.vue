<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogFormVisible"
    :title="form.id ? '编辑' : '新增'"
    append-to-body
    width="30%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="110px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 电子围栏 -->
          <el-form-item
            :label="getLabel('fenceName')"
            prop="fenceId"
          >
            <el-cascader
              v-model="form.fenceId"
              :placeholder="getPlaceholder('fenceName')"
              :options="options"
              :props="props"
              :show-all-levels="false"
              filterable
              clearable
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 时间限制 -->
          <el-form-item
            :label="getLabel('enableTime')"
            prop="enableTime"
          >
            <el-radio-group
              v-model="form.enableTime"
              @change="handleEnableTime"
            >
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
            <el-checkbox
              v-if="form.enableTime"
              v-model="form.enableEveryday"
              class="form-checkbox"
              @change="handleEnableEveryday"
            >每天</el-checkbox>
          </el-form-item>
        </div>
        <div
          v-if="form.enableTime && !form.enableEveryday"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 开始时间 -->
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              :placeholder="getPlaceholder('startTime')"
              :clearable="false"
              value-format="timestamp"
              @blur="judge('startTime')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.enableTime && !form.enableEveryday"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 结束时间 -->
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              :placeholder="getPlaceholder('endTime')"
              :clearable="false"
              value-format="timestamp"
              @blur="judge('endTime')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.enableTime && form.enableEveryday"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 开始时间 -->
          <el-form-item
            :label="getLabel('everydayStart')"
            prop="everydayStart"
          >
            <el-time-picker
              v-model="form.everydayStart"
              :placeholder="getPlaceholder('everydayStart')"
              value-format="HH:mm:ss"
              :clearable="false"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.enableTime && form.enableEveryday"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 结束时间 -->
          <el-form-item
            :label="getLabel('everydayEnd')"
            prop="everydayEnd"
          >
            <el-time-picker
              v-model="form.everydayEnd"
              :placeholder="getPlaceholder('everydayEnd')"
              value-format="HH:mm:ss"
              :clearable="false"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 超速限制 -->
          <el-form-item
            :label="getLabel('enableSpeed')"
            prop="enableSpeed"
          >
            <el-radio-group
              v-model="form.enableSpeed"
              @change="handleEnableSpeed"
            >
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.enableSpeed"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 最高速度 -->
          <el-form-item
            :label="getLabel('highSpeed')"
            prop="highSpeed"
          >
            <el-input
              v-model.trim.number="form.highSpeed"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('highSpeed')"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.enableSpeed"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 夜间限速 -->
          <el-form-item
            :label="getLabel('nightSpeed')"
            prop="nightSpeed"
          >
            <el-input
              v-model.trim.number="form.nightSpeed"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('nightSpeed')"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.enableSpeed"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 持续时长 -->
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-input
              v-model.trim.number="form.duration"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('duration')"
            >
              <template slot="append">秒</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 进报驾员 -->
          <el-form-item
            :label="getLabel('enterDriver')"
            prop="enterDriver"
          >
            <el-radio-group v-model="form.enterDriver">
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 进报平台 -->
          <el-form-item
            :label="getLabel('enterPlatform')"
            prop="enterPlatform"
          >
            <el-radio-group v-model="form.enterPlatform">
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 出报驾员 -->
          <el-form-item
            :label="getLabel('outDriver')"
            prop="outDriver"
          >
            <el-radio-group v-model="form.outDriver">
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 出报平台 -->
          <el-form-item
            :label="getLabel('outPlatform')"
            prop="outPlatform"
          >
            <el-radio-group v-model="form.outPlatform">
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 进关通信 -->
          <el-form-item
            :label="getLabel('enterCloseTcp')"
            prop="enterCloseTcp"
          >
            <el-radio-group v-model="form.enterCloseTcp">
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 进采GNNS -->
          <el-form-item
            :label="getLabel('enterGatherGnss')"
            prop="enterGatherGnss"
          >
            <el-radio-group v-model="form.enterGatherGnss">
              <el-radio :label="false">
                否
              </el-radio>
              <el-radio :label="true">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="$emit('update:dialogFormVisible', false)"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    dict: {
      type: Object,
      required: true
    },
    dialogFormVisible: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Array,
      default: ()=>{return [];}
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    submitLoading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        id: null,
        fenceId: null,
        enableTime: false,
        enableSpeed: false,
        enterDriver: false,
        enterPlatform: false,
        outDriver: false,
        outPlatform: false,
        enterCloseTcp: false,
        enterGatherGnss: false,
        highSpeed: null,
        nightSpeed: null,
        duration: null,
        startTime: null,
        endTime: null,
        enableEveryday: false,
        everydayStart: null,
        everydayEnd: null
      },
      rules: {
        fenceId: { required: true, message: '请输入电子围栏名称', trigger: 'change' }, // 电子围栏名称
        highSpeed: { required: true, message: '请输入最高速度', trigger: 'blur' }, // 最高速度
        nightSpeed: { required: true, message: '请输入夜间限速', trigger: 'blur' }, // 夜间限速
        duration: { required: true, message: '请输入持续时长', trigger: 'blur' }, // 持续时长
        startTime: { required: true, message: '请选择开始时间', trigger: 'change' }, // 开始时间
        endTime: { required: true, message: '请选择结束时间', trigger: 'change' }, // 结束时间
      },
      props: {
        label: 'name',
        value: 'id',
        children: 'children',
        emitPath: false
      },
      options: []
    };
  },
  watch: {
    treeData: {
      handler(newVal) {
        this.options= JSON.parse(JSON.stringify(newVal));
        this.setTreeData(this.options);
      },
      deep: true
    },
    rowData: {
      handler(newVal) {
        this.form = { ...this.form, ...newVal };
        this.form.startTime = this.form.startTime ? this.form.startTime * 1000 : null;
        this.form.endTime = this.form.endTime ? this.form.endTime * 1000 : null;
      },
      deep: true
    }
  },
  methods: {
    judge (time) {
      if (this.form.startTime) {
        if (
          this.$moment(this.form.startTime).valueOf() >
          this.$moment(this.form.endTime).valueOf()
        ) {
          this.form[time] = null;
          this.$message({
            type: 'warning',
            message: '开始时间要小于结束时间'
          });
        }
      }
    },
    setTreeData(data) {
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.type === 1) {
          if (element.children && element.children.length) {
            element.id = 'd-' + element.id;
            this.setTreeData(element.children);
          } else {
            data.splice(index, 1);
            index--;
          }
        }
      }
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('handleSubmit', this.form);
        }
      });
    },
    // 勾选超速限制
    handleEnableSpeed() {
      if (!this.form.enableSpeed) {
        this.form.highSpeed = null;
        this.form.nightSpeed = null;
        this.form.duration = null;
      }
    },
    // 勾选每天
    handleEnableEveryday() {
      if (this.form.enableEveryday) {
        this.form.everydayStart = '00:00:00';
        this.form.everydayEnd = '23:59:59';
        this.form.startTime = null;
        this.form.endTime = null;
        this.$refs['form'].clearValidate(['startTime', 'endTime']);
      } else {
        this.form.everydayStart = null;
        this.form.everydayEnd = null;
        this.form.startTime = this.$moment().startOf('day').valueOf();
        this.form.endTime = this.$moment().endOf('day').valueOf();
      }
    },
    // 勾选时间限制
    handleEnableTime() {
      if (this.form.enableTime && !this.form.startTime) {
        this.form.startTime = this.$moment().startOf('day').valueOf();
        this.form.endTime = this.$moment().endOf('day').valueOf();
      } else if (!this.form.enableTime) {
        this.form.startTime = null;
        this.form.endTime = null;
        this.form.enableEveryday = false;
        this.form.everydayStart = null;
        this.form.everydayEnd = null;
      }
    },
    closed() {
      this.$emit('update:dialogFormVisible', false);
      this.form = {
        id: null,
        fenceId: null,
        enableTime: false,
        enableSpeed: false,
        enterDriver: false,
        enterPlatform: false,
        outDriver: false,
        outPlatform: false,
        enterCloseTcp: false,
        enterGatherGnss: false,
        highSpeed: null,
        nightSpeed: null,
        duration: null,
        startTime: null,
        endTime: null,
        enableEveryday: false,
        everydayStart: null,
        everydayEnd: null
      };
      this.$nextTick(()=>{
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('RegionManage', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('RegionManage', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .form-checkbox {
    margin-left: 30px;
  }
</style>
