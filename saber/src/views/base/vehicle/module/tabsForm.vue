<template>
  <el-dialog
    v-if="showDialog"
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="120px"
    >
      <el-tabs
        v-model="activeTab"
        type="border-card"
      >
        <el-tab-pane
          label="车辆信息"
          name="first"
          style="height: 50vh;overflow: auto;"
        >
          <el-row
            type="flex"
            span="24"
          >
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('licencePlate')"
                prop="licencePlate"
              >
                <el-input
                  v-model.trim="form.licencePlate"
                  @input="e => form.licencePlate = validInput(e)"
                  :placeholder="getPlaceholder('licencePlate')"
                  maxlength="8"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('licenceColor')"
                prop="licenceColor"
              >
                <xh-select
                  v-model="form.licenceColor"
                  :placeholder="getPlaceholder('licenceColor')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.licenceColor"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('deptIdStr')"
                prop="deptIdStr"
              >
                <Treeselect
                  ref="deptIdStrRef"
                  v-model="form.deptIdStr"
                  :options="depts"
                  :normalizer="normalizerDeptId"
                  :placeholder="getPlaceholder('deptIdStr')"
                  :append-to-body="true"
                  z-index="5000"
                  class="tree-select"
                  @input="validateTreeSelect('deptIdStr')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车架号 -->
              <el-form-item
                :label="getLabel('vin')"
                prop="vin"
              >
                <el-input
                  v-model.trim="form.vin"
                  @input="e => form.vin = validInput(e)"
                  :placeholder="getPlaceholder('vin')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 服务状态 -->
              <el-form-item
                :label="getLabel('serviceState')"
                prop="serviceState"
              >
                <xh-select
                  v-model="form.serviceState"
                  :placeholder="getPlaceholder('serviceState')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.serviceState"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车籍地 -->
              <el-form-item
                :label="getLabel('vehicleDomicileCode')"
                prop="vehicleDomicileCode"
              >
                <Treeselect
                  ref="vehicleDomicileCodeRef"
                  v-model="form.vehicleDomicileCode"
                  :options="districtTreeData"
                  :normalizer="normalizerVehicleDomicileAdcode"
                  :placeholder="getPlaceholder('vehicleDomicileCode')"
                  :append-to-body="true"
                  z-index="5000"
                  class="tree-select"
                  @input="validateTreeSelect('vehicleDomicileCode')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 行业类型 -->
              <el-form-item
                :label="getLabel('vehicleUseType')"
                prop="vehicleUseType"
              >
                <Treeselect
                  ref="vehicleUseTypeRef"
                  v-model="form.vehicleUseType"
                  :options="dict.vehicleUseType"
                  :normalizer="normalizer"
                  :placeholder="getPlaceholder('vehicleUseType')"
                  :append-to-body="true"
                  z-index="5000"
                  class="tree-select"
                  @input="validateTreeSelect('vehicleUseType')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆营运状态 -->
              <el-form-item
                :label="getLabel('operatingStatus')"
                prop="operatingStatus"
              >
                <xh-select
                  v-model="form.operatingStatus"
                  :placeholder="getPlaceholder('operatingStatus')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.operatingStatus"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆类型 -->
              <el-form-item
                :label="getLabel('vehicleModel')"
                prop="vehicleModel"
              >
                <Treeselect
                  ref="vehicleModelRef"
                  v-model="form.vehicleModel"
                  :options="dict.vehicleModel"
                  :normalizer="normalizer"
                  :placeholder="getPlaceholder('vehicleModel')"
                  :append-to-body="true"
                  z-index="5000"
                  class="tree-select"
                  @input="validateTreeSelect('vehicleModel')"
                />
              </el-form-item>
            </div>
            <!-- 车辆类型等级 -->
            <!-- <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('vehicleModelLevel')"
                prop="vehicleModelLevel"
              >
                <xh-select
                  v-model="form.vehicleModelLevel"
                  :placeholder="getPlaceholder('vehicleModelLevel')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.vehicleModelLevel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div> -->
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆归属 -->
              <el-form-item
                :label="getLabel('vehicleOwnerId')"
                prop="vehicleOwnerId"
              >
                <xh-select
                  v-model="form.vehicleOwnerId"
                  :placeholder="getPlaceholder('vehicleOwnerId')"
                  clearable
                >
                  <el-option
                    v-for="item in vehicleOwnerId"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 有效期起 -->
              <el-form-item
                :label="getLabel('certificateBeginDate')"
                prop="certificateBeginDate"
              >
                <DateStringPicker
                  v-model="form.certificateBeginDate"
                  :placeholder="getPlaceholder('certificateBeginDate')"
                  @input="judge('certificateBeginDate')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 有效期止 -->
              <el-form-item
                :label="getLabel('certificateExpireDate')"
                prop="certificateExpireDate"
              >
                <DateStringPicker
                  v-model="form.certificateExpireDate"
                  :placeholder="getPlaceholder('certificateExpireDate')"
                  @input="judge('certificateExpireDate')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 入网时间 -->
              <el-form-item
                :label="getLabel('netSignTime')"
                prop="netSignTime"
              >
                <DateStringPicker
                  v-model="form.netSignTime"
                  :placeholder="getPlaceholder('netSignTime')"
                  :time-limit="true"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 购车时间 -->
              <el-form-item
                :label="getLabel('buyTime')"
                prop="buyTime"
              >
                <DateStringPicker
                  v-model="form.buyTime"
                  :placeholder="getPlaceholder('buyTime')"
                  :time-limit="true"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 安装时间 -->
              <el-form-item
                :label="getLabel('installTime')"
                prop="installTime"
              >
                <DateStringPicker
                  v-model="form.installTime"
                  :placeholder="getPlaceholder('installTime')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 维修时间 -->
              <el-form-item
                :label="getLabel('repairTime')"
                prop="repairTime"
              >
                <DateStringPicker
                  v-model="form.repairTime"
                  :placeholder="getPlaceholder('repairTime')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 年检时间 -->
              <el-form-item
                :label="getLabel('yearCheckTime')"
                prop="yearCheckTime"
              >
                <DateStringPicker
                  v-model="form.yearCheckTime"
                  :placeholder="getPlaceholder('yearCheckTime')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 发动机号 -->
              <el-form-item
                :label="getLabel('engineNumber')"
                prop="engineNumber"
              >
                <el-input
                  v-model.trim="form.engineNumber"
                  @input="e => form.engineNumber = validInput(e)"
                  :placeholder="getPlaceholder('engineNumber')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 发动机型号 -->
              <el-form-item
                :label="getLabel('engineModel')"
                prop="engineModel"
              >
                <el-input
                  v-model.trim="form.engineModel"
                  @input="e => form.engineModel = validInput(e)"
                  :placeholder="getPlaceholder('engineModel')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 燃料类型 -->
              <el-form-item
                :label="getLabel('fuelType')"
                prop="fuelType"
              >
                <xh-select
                  v-model="form.fuelType"
                  :placeholder="getPlaceholder('fuelType')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.fuelType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 厂牌型号 -->
              <el-form-item
                :label="getLabel('brandModel')"
                prop="brandModel"
              >
                <el-input
                  v-model.trim="form.brandModel"
                  @input="e => form.brandModel = validInput(e)"
                  :placeholder="getPlaceholder('brandModel')"
                />
              </el-form-item>
            </div>
            <!-- 运输类型 -->
            <!-- <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('transportType')"
                prop="transportType"
              >
                <Treeselect
                  ref="transportTypeRef"
                  v-model="form.transportType"
                  :options="dict.transportType"
                  :normalizer="normalizer"
                  :placeholder="getPlaceholder('transportType')"
                  class="tree-select"
                  @input="validateTreeSelect('transportType')"
                />
              </el-form-item>
            </div> -->
            <!-- 车辆外廓尺寸 -->
            <!-- <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('vehicleCorridorSize')"
                prop="vehicleCorridorSize"
              >
                <el-input
                  v-model.number="form.vehicleCorridorSize"
                  :placeholder="getPlaceholder('vehicleCorridorSize')"
                />
              </el-form-item>
            </div> -->
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆车长 -->
              <el-form-item
                :label="getLabel('vehicleLength')"
                prop="vehicleLength"
              >
                <el-input
                  v-model.number.trim="form.vehicleLength"
                  type='number'
                  :placeholder="getPlaceholder('vehicleLength')"
                  @blur="form.vehicleLength = parseValue(form.vehicleLength)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆车宽 -->
              <el-form-item
                :label="getLabel('vehicleWidth')"
                prop="vehicleWidth"
              >
                <el-input
                  v-model.number.trim="form.vehicleWidth"
                  type='number'
                  :placeholder="getPlaceholder('vehicleWidth')"
                  @blur="form.vehicleWidth = parseValue(form.vehicleWidth)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆车高 -->
              <el-form-item
                :label="getLabel('vehicleHigh')"
                prop="vehicleHigh"
              >
                <el-input
                  v-model.number.trim="form.vehicleHigh"
                  type='number'
                  :placeholder="getPlaceholder('vehicleHigh')"
                  @blur="form.vehicleHigh = parseValue(form.vehicleHigh)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆车轴数 -->
              <el-form-item
                :label="getLabel('axlesAmount')"
                prop="axlesAmount"
              >
                <el-input
                  v-model.number.trim="form.axlesAmount"
                  :placeholder="getPlaceholder('axlesAmount')"
                  @blur="form.axlesAmount = parseValue(form.axlesAmount)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 接驳状态 -->
              <el-form-item
                :label="getLabel('connectStatus')"
                prop="connectStatus"
              >
                <xh-select
                  v-model="form.connectStatus"
                  :placeholder="getPlaceholder('connectStatus')"
                  clearable
                >
                  <el-option
                    v-for="item in connectStatus"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车辆接入方式 -->
              <el-form-item
                :label="getLabel('accessMode')"
                prop="accessMode"
              >
                <xh-select
                  v-model="form.accessMode"
                  :placeholder="getPlaceholder('accessMode')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.accessMode"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <!-- 是否接收主动安全告警 -->
            <!-- <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('receiveSecurity')"
                prop="receiveSecurity"
              >
                <el-radio-group v-model="form.receiveSecurity">
                  <el-radio :label="1">
                    是
                  </el-radio>
                  <el-radio :label="0">
                    否
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </div> -->
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 核载人数 -->
              <el-form-item
                :label="getLabel('mannedWeight')"
                prop="mannedWeight"
              >
                <el-input
                  v-model.number.trim="form.mannedWeight"
                  :placeholder="getPlaceholder('mannedWeight')"
                  @blur="form.mannedWeight = parseValue(form.mannedWeight)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车主姓名 -->
              <el-form-item
                :label="getLabel('ownerName')"
                prop="ownerName"
              >
                <el-input
                  v-model.trim="form.ownerName"
                  @input="e => form.ownerName = validInput(e)"
                  :placeholder="getPlaceholder('ownerName')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车主电话 -->
              <el-form-item
                :label="getLabel('ownerPhone')"
                prop="ownerPhone"
              >
                <el-input
                  v-model.trim="form.ownerPhone"
                  @input="e => form.ownerPhone = validInput(e)"
                  :placeholder="getPlaceholder('ownerPhone')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 道路运输证字 -->
              <el-form-item
                :label="getLabel('transCertificateWord')"
                prop="transCertificateWord"
              >
                <el-input
                  v-model.trim="form.transCertificateWord"
                  @input="e => form.transCertificateWord = validInput(e)"
                  :placeholder="getPlaceholder('transCertificateWord')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 道路运输证号 -->
              <el-form-item
                :label="getLabel('transCertificateCode')"
                prop="transCertificateCode"
              >
                <el-input
                  v-model.trim="form.transCertificateCode"
                  @input="e => form.transCertificateCode = validInput(e)"
                  :placeholder="getPlaceholder('transCertificateCode')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 证照介质类型 -->
              <el-form-item
                :label="getLabel('certificateType')"
                prop="certificateType"
              >
                <Treeselect
                  ref="certificateTypeRef"
                  v-model="form.certificateType"
                  :options="dict.certificateMediumType"
                  :normalizer="normalizer"
                  :placeholder="getPlaceholder('certificateType')"
                  :append-to-body="true"
                  z-index="5000"
                  class="tree-select"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 经营范围 -->
              <el-form-item
                :label="getLabel('businessScopeCode')"
                prop="businessScopeCode"
              >
                <Treeselect
                  ref="businessScopeCodeRef"
                  v-model="form.businessScopeCode"
                  :options="dict.transportType"
                  :normalizer="normalizer"
                  :placeholder="getPlaceholder('businessScopeCode')"
                  :append-to-body="true"
                  z-index="5000"
                  class="tree-select"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 发证机构 -->
              <el-form-item
                :label="getLabel('grantOrgan')"
                prop="grantOrgan"
              >
                <el-input
                  v-model.trim="form.grantOrgan"
                  @input="e => form.grantOrgan = validInput(e)"
                  :placeholder="getPlaceholder('grantOrgan')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 经营范围文字 -->
              <el-form-item
                :label="getLabel('businessScopeDesc')"
                prop="businessScopeDesc"
              >
                <el-input
                  v-model.trim="form.businessScopeDesc"
                  @input="e => form.businessScopeDesc = validInput(e)"
                  :placeholder="getPlaceholder('businessScopeDesc')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 车机连接方式 -->
              <el-form-item
                :label="getLabel('connectMode')"
                prop="connectMode"
              >
                <xh-select
                  v-model="form.connectMode"
                  :placeholder="getPlaceholder('connectMode')"
                  clearable
                  @change="handleConnectMode"
                >
                  <el-option
                    v-for="item in dict.connectMode"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div
              v-if="form.connectMode === '3'"
              class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8"
            >
              <!-- 809下级平台id -->
              <el-form-item
                :label="getLabel('centerId')"
                prop="centerId"
              >
                <el-input
                  v-model.trim="form.centerId"
                  @input="e => form.centerId = validInput(e)"
                  :placeholder="getPlaceholder('centerId')"
                />
              </el-form-item>
            </div>
            <div
              v-if="!form.id"
              class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8"
            >
              <!-- 告警规则配置 -->
              <el-form-item
                label="告警规则配置"
                prop="ruleIds"
              >
                <xh-select
                  v-model="ruleIds"
                  placeholder="请选择告警规则"
                  clearable
                  multiple
                >
                  <el-option
                    v-for="item in rulebindrealtime"
                    :key="item.rule_type_id_str"
                    :label="item.rule_name"
                    :value="item.rule_type_id_str"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
              <!-- 备注 -->
              <el-form-item
                :label="getLabel('remark')"
                prop="remark"
              >
                <el-input
                  v-model.trim="form.remark"
                  @input="e => form.remark = validInput(e)"
                  :autosize="{ minRows: 4, maxRows: 4}"
                  :placeholder="getPlaceholder('remark')"
                  type="textarea"
                  class="text-area"
                />
              </el-form-item>
            </div>
          </el-row>

        </el-tab-pane>
        <el-tab-pane
          label="终端信息"
          name="second"
          style="height: 50vh;overflow: auto;"
        >

          <el-row
            type="flex"
            span="24"
          >
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 唯一性编码 -->
              <el-form-item
                :label="getLabel('serial')"
                prop="terminal.serial"
              >
                <!-- <el-input
                  v-model.trim="form.terminal.serial"
                  @input="e => form.terminal.serial = validInput(e)"
                  :placeholder="getPlaceholder('serial')"
                /> -->
                <el-autocomplete
                  v-model="form.terminal.serial"
                  onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
                  :placeholder="getPlaceholder('serial')"
                  :fetch-suggestions="querySearch"
                  :highlight-first-item="true"
                  clearable
                  @select="handlePhone"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 终端电话 -->
              <el-form-item
                :label="getLabel('phone')"
                prop="terminal.phone"
              >
                <el-input
                  v-model.trim="form.terminal.phone"
                  onkeyup="value=value.replace(/[^\d]/g,'')"
                  :placeholder="getPlaceholder('phone')"
                  :disabled="fromTerminal"
                />
                <!-- <el-autocomplete
                  v-model="form.terminal.phone"
                  :placeholder="getPlaceholder('phone')"
                  :fetch-suggestions="querySearch"
                  :highlight-first-item="true"
                  clearable
                  @select="handlePhone"
                /> -->
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 序列号 -->
              <el-form-item
                :label="getLabel('terminalId')"
                prop="terminal.terminalId"
              >
                <el-input
                  v-model.trim="form.terminal.terminalId"
                  maxlength="11"
                  :placeholder="getPlaceholder('terminalId')"
                  @input="e => form.terminal.terminalId = validInput(e)"
                />
              </el-form-item>
            </div>
            <!-- 序列号 -->
            <!-- <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <el-form-item
                :label="getLabel('terminalNo')"
                prop="terminal.terminalNo"
              >
                <el-input
                  v-model.trim="form.terminal.terminalNo"
                  @input="e => form.terminal.terminalNo = validInput(e)"
                  :placeholder="getPlaceholder('terminalNo')"
                  :maxlength="20"
                  :disabled="fromTerminal"
                />
              </el-form-item>
            </div> -->
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- IMEI号 -->
              <el-form-item
                :label="getLabel('imei')"
                prop="terminal.imei"
              >
                <el-input
                  v-model.trim="form.terminal.imei"
                  :placeholder="getPlaceholder('imei')"
                  @input="e => form.terminal.imei = validInput(e)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 终端类型 -->
              <el-form-item
                :label="getLabel('terminalType')"
                prop="terminal.terminalType"
              >
                <xh-select
                  v-model="form.terminal.terminalType"
                  :placeholder="getPlaceholder('terminalType')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.terminalType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 终端型号 -->
              <el-form-item
                :label="getLabel('terminalModel')"
                prop="terminal.terminalModel"
              >
                <el-input
                  v-model.trim="form.terminal.terminalModel"
                  :placeholder="getPlaceholder('terminalModel')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 厂商编号 -->
              <el-form-item
                :label="getLabel('vendorId')"
                prop="terminal.vendorId"
              >
                <el-input
                  v-model.trim="form.terminal.vendorId"
                  :placeholder="getPlaceholder('vendorId')"
                  @input="e => form.terminal.vendorId = validInput(e)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 视频通道数 -->
              <el-form-item
                :label="getLabel('channelNum')"
                prop="terminal.channelNum"
              >
                <el-input
                  v-model.number.trim="form.terminal.channelNum"
                  :placeholder="getPlaceholder('channelNum')"
                  @blur="form.terminal.channelNum = parseValue(form.terminal.channelNum)"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 注册时间 -->
              <el-form-item
                :label="getLabel('registerTime')"
                prop="terminal.registerTime"
              >
                <DateStringPicker
                  v-model="form.terminal.registerTime"
                  :placeholder="getPlaceholder('registerTime')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 标准 -->
              <el-form-item
                :label="getLabel('localStandards')"
                prop="terminal.localStandards"
              >
                <xh-select
                  v-model="form.terminal.localStandards"
                  :placeholder="getPlaceholder('localStandards')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.localStandards"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 功能类型 -->
              <el-form-item
                :label="getLabel('terminalFunctionType')"
                prop="terminal.terminalFunctionType"
              >
                <xh-select
                  v-model="form.terminal.terminalFunctionType"
                  :placeholder="getPlaceholder('terminalFunctionType')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.terminalFunctionType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 主动安全设备 -->
              <el-form-item
                :label="getLabel('isAi')"
                prop="terminal.isAi"
              >
                <el-radio-group v-model="form.terminal.isAi">
                  <el-radio :label="1">
                    是
                  </el-radio>
                  <el-radio :label="0">
                    否
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-row>
        </el-tab-pane>
        <el-tab-pane
          label="sim卡信息"
          name="third"
          style="height: 50vh;overflow: auto;"
        >
          <el-row
            type="flex"
            span="24"
          >
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- sim卡号 -->
              <el-form-item
                :label="getLabel('simId')"
                prop="terminal.terminalSim.simId"
              >
                <el-input
                  v-model.trim="form.terminal.terminalSim.simId"
                  @input="e => form.terminal.terminalSim.simId = validInput(e)"
                  :placeholder="getPlaceholder('simId')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- iccid -->
              <el-form-item
                :label="getLabel('iccid')"
                prop="terminal.terminalSim.iccid"
              >
                <el-input
                  v-model.trim="form.terminal.terminalSim.iccid"
                  @input="e => form.terminal.terminalSim.iccid = validInput(e)"
                  :placeholder="getPlaceholder('iccid')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- imsi -->
              <el-form-item
                :label="getLabel('imsi')"
                prop="terminal.terminalSim.imsi"
              >
                <el-input
                  v-model.trim="form.terminal.terminalSim.imsi"
                  @input="e => form.terminal.terminalSim.imsi = validInput(e)"
                  :placeholder="getPlaceholder('imsi')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 卡类型 -->
              <el-form-item
                :label="getLabel('cardType')"
                prop="terminal.terminalSim.cardType"
              >
                <xh-select
                  v-model="form.terminal.terminalSim.cardType"
                  :placeholder="getPlaceholder('cardType')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.cardType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 服务期止 -->
              <el-form-item
                :label="getLabel('endDate')"
                prop="terminal.terminalSim.endDate"
              >
                <el-date-picker
                  v-model="form.terminal.terminalSim.endDate"
                  type="date"
                  :placeholder="getPlaceholder('endDate')"
                  @change="handleDateChange('endDate')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 发卡日期 -->
              <el-form-item
                :label="getLabel('releaseDate')"
                prop="terminal.terminalSim.releaseDate"
              >
                <el-date-picker
                  v-model="form.terminal.terminalSim.releaseDate"
                  type="date"
                  :placeholder="getPlaceholder('releaseDate')"
                  @change="handleDateChange('releaseDate')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 激活时间 -->
              <el-form-item
                :label="getLabel('activeDate')"
                prop="terminal.terminalSim.activeDate"
              >
                <el-date-picker
                  v-model="form.terminal.terminalSim.activeDate"
                  type="date"
                  :placeholder="getPlaceholder('activeDate')"
                  @change="handleDateChange('activeDate')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 客户名称 -->
              <el-form-item
                :label="getLabel('customer')"
                prop="terminal.terminalSim.customer"
              >
                <el-input
                  v-model.trim="form.terminal.terminalSim.customer"
                  @input="e => form.terminal.terminalSim.customer = validInput(e)"
                  :placeholder="getPlaceholder('customer')"
                />
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 运营商 -->
              <el-form-item
                :label="getLabel('operator')"
                prop="terminal.terminalSim.operator"
              >
                <xh-select
                  v-model="form.terminal.terminalSim.operator"
                  :placeholder="getPlaceholder('operator')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.operator"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 卡状态 -->
              <el-form-item
                :label="getLabel('cardState')"
                prop="terminal.terminalSim.cardState"
              >
                <xh-select
                  v-model="form.terminal.terminalSim.cardState"
                  :placeholder="getPlaceholder('cardState')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.cardState"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 流量套餐 -->
              <el-form-item
                :label="getLabel('package')"
                prop="terminal.terminalSim.package"
              >
                <xh-select
                  v-model="form.terminal.terminalSim.package"
                  :placeholder="getPlaceholder('package')"
                  clearable
                >
                  <el-option
                    v-for="item in dict.package"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-8">
              <!-- 流量套餐总量(MB) -->
              <el-form-item
                :label="getLabel('packageTotal')"
                prop="terminal.terminalSim.packageTotal"
              >
                <el-input
                  v-model.number.trim="form.terminal.terminalSim.packageTotal"
                  :placeholder="getPlaceholder('packageTotal')"
                  @blur="form.terminal.terminalSim.packageTotal = parseValue(form.terminal.terminalSim.packageTotal)"
                />
              </el-form-item>
            </div>
          </el-row>
        </el-tab-pane>
        <el-tab-pane
          label="外设信息"
          name="fourth"
          style="height: 50vh;overflow: auto;"
        >
          <el-row
            type="flex"
            span="24"
          >
            <div class="el-col el-col-8 el-col-offset-0 el-col-xs-8 el-col-sm-12 el-col-md-8">
              <!-- 摄像头数量 -->
              <el-form-item
                :label="getLabel('cameraNum')"
                prop="terminalDevice.cameraNum"
              >
                <xh-select
                  v-model="form.terminalDevice.cameraNum"
                  :placeholder="getPlaceholder('cameraNum')"
                  clearable
                  @clear="handleClear(1)"
                >
                  <el-option
                    v-for="item in cameraNum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @click.native="handleSelect(form.terminalDevice.cameraNum, 1)"
                  />
                </xh-select>
              </el-form-item>
              <!-- 正反传感器数量 -->
              <el-form-item
                :label="getLabel('pnsNum')"
                prop="terminalDevice.pnsNum"
              >
                <xh-select
                  v-model="form.terminalDevice.pnsNum"
                  :placeholder="getPlaceholder('pnsNum')"
                  clearable
                  @clear="handleClear(2)"
                >
                  <el-option
                    v-for="item in pnsNum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @click.native="handleSelect(form.terminalDevice.pnsNum, 2)"
                  />
                </xh-select>
              </el-form-item>
              <!-- 胎压传感器数量 -->
              <el-form-item
                :label="getLabel('tirePressureNum')"
                prop="terminalDevice.tirePressureNum"
              >
                <xh-select
                  v-model="form.terminalDevice.tirePressureNum"
                  :placeholder="getPlaceholder('tirePressureNum')"
                  clearable
                  @clear="handleClear(3)"
                >
                  <el-option
                    v-for="item in tirePressureNum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @click.native="handleSelect(form.terminalDevice.tirePressureNum, 3)"
                  />
                </xh-select>
              </el-form-item>
              <!-- 油耗传感器数量 -->
              <el-form-item
                :label="getLabel('oilNum')"
                prop="terminalDevice.oilNum"
              >
                <xh-select
                  v-model="form.terminalDevice.oilNum"
                  :placeholder="getPlaceholder('oilNum')"
                  clearable
                  @clear="handleClear(4)"
                >
                  <el-option
                    v-for="item in oilNum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @click.native="handleSelect(form.terminalDevice.oilNum, 4)"
                  />
                </xh-select>
              </el-form-item>
              <!-- 载重传感器数量 -->
              <el-form-item
                :label="getLabel('weightNum')"
                prop="terminalDevice.weightNum"
              >
                <xh-select
                  v-model="form.terminalDevice.weightNum"
                  :placeholder="getPlaceholder('weightNum')"
                  clearable
                  @clear="handleClear(5)"
                >
                  <el-option
                    v-for="item in weightNum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @click.native="handleSelect(form.terminalDevice.weightNum, 5)"
                  />
                </xh-select>
              </el-form-item>
              <!-- 震动传感器数量 -->
              <el-form-item
                :label="getLabel('quakeNum')"
                prop="terminalDevice.quakeNum"
              >
                <xh-select
                  v-model="form.terminalDevice.quakeNum"
                  :placeholder="getPlaceholder('quakeNum')"
                  clearable
                  @clear="handleClear(6)"
                >
                  <el-option
                    v-for="item in quakeNum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @click.native="handleSelect(form.terminalDevice.quakeNum, 6)"
                  />
                </xh-select>
              </el-form-item>
              <!-- 温度传感器数量 -->
              <el-form-item
                :label="getLabel('temperatureNum')"
                prop="terminalDevice.temperatureNum"
              >
                <xh-select
                  v-model="form.terminalDevice.temperatureNum"
                  :placeholder="getPlaceholder('temperatureNum')"
                  clearable
                  @clear="handleClear(7)"
                >
                  <el-option
                    v-for="item in temperatureNum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @click.native="handleSelect(form.terminalDevice.temperatureNum, 7)"
                  />
                </xh-select>
              </el-form-item>
            </div>
            <div class="el-col el-col-15 el-col-offset-0 el-col-xs-15 el-col-sm-15 el-col-md-15 device-content">
              <div class="table-header">
                <span>序列号</span>
                <span>通道编号</span>
                <span>位置名称</span>
                <span>阈值下限</span>
                <span>阈值上限</span>
                <span>厂商名称</span>
                <span>拍照通道标志</span>
                <span>设备类型</span>
                <span>设备名称</span>
                <span>设备型号</span>
                <span>采购日期</span>
                <span>质保期(月)</span>
                <span>首次安装时间</span>
                <span>设备状态</span>
              </div>
              <div
                v-for="(item,index) in deviceList"
                :key="index"
                class="table-item"
              >
                <!-- 序列号 -->
                <div class="table-item-input">
                  <!-- <el-input v-model="item.deviceSerial"/> -->
                  <el-autocomplete
                    v-model="item.deviceSerial"
                    :fetch-suggestions="querySerial"
                    :highlight-first-item="true"
                    clearable
                    @select="(val)=>handleSerial(val, item)"
                  />
                </div>
                <!-- 通道编号 -->
                <div class="table-item-input">
                  <xh-select
                    v-model="item.deviceId"
                  >
                    <el-option
                      v-for="element in 103"
                      :key="element"
                      :label="element"
                      :value="element"
                    />
                  </xh-select>
                </div>
                <!-- 位置名称 -->
                <div class="table-item-input">
                  <el-input v-model.trim="item.locationName"
                            @input="e => item.locationName = validInput(e)"
                  />
                </div>
                <!-- 阈值下限 -->
                <div class="table-item-input">
                  <el-input
                    v-model.number.trim="item.lowerThreshold"
                    @blur="item.lowerThreshold = parseValue(item.lowerThreshold)"
                  />
                </div>
                <!-- 阈值上限 -->
                <div class="table-item-input">
                  <el-input
                    v-model.number.trim="item.upperThreshold"
                    @blur="item.upperThreshold = parseValue(item.upperThreshold)"
                  />
                </div>
                <!-- 厂商名称 -->
                <div class="table-item-input">
                  <el-input v-model.trim="item.manufacturer"/>
                </div>
                <!-- 拍照通道标志 -->
                <div
                  class="table-item-input"
                  :style="item.deviceType === '1' ? 'text-align: center;' : 'border: none; text-align: center;'"
                >
                  <el-switch
                    v-if="item.deviceType === '1'"
                    v-model="item.shotPhoto"
                    style="height: 40px;"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                  <span v-else>-</span>
                </div>
                <!-- 设备类型 -->
                <div class="table-item-input">
                  <xh-select
                    v-model="item.deviceType"
                    disabled
                  >
                    <el-option
                      v-for="element in dict.deviceType"
                      :key="element.value"
                      :label="element.label"
                      :value="element.value"
                    />
                  </xh-select>
                </div>
                <!-- 设备名称 -->
                <div class="table-item-input">
                  <el-input
                    v-model.trim="item.deviceName"
                    @input="e => item.deviceName = validInput(e)"
                    disabled
                  />
                </div>
                <!-- 设备型号 -->
                <div class="table-item-input">
                  <xh-select
                    v-model="item.deviceModel"
                    disabled
                  >
                    <el-option
                      v-for="element in dict.deviceModel"
                      :key="element.value"
                      :label="element.label"
                      :value="element.value"
                    />
                  </xh-select>
                </div>
                <!-- 采购日期 -->
                <div class="table-item-input">
                  <DateStringPicker
                    v-model="item.buyDate"
                    disabled
                  />
                </div>
                <!-- 质保期 -->
                <div class="table-item-input">
                  <el-input
                    v-model.number.trim="item.warrantyPeriod"
                    disabled
                  />
                </div>
                <!-- 首次安装时间 -->
                <div class="table-item-input">
                  <DateStringPicker
                    v-model="item.firstInstallDate"
                    disabled
                  />
                </div>
                <!-- 设备状态 -->
                <div class="table-item-input">
                  <xh-select
                    v-model="item.deviceState"
                    :placeholder="getPlaceholder('deviceState')"
                    disabled
                  >
                    <el-option
                      v-for="element in dict.deviceState"
                      :key="element.value"
                      :label="element.label"
                      :value="element.value"
                    />
                  </xh-select>
                </div>
              </div>
            </div>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
// import crudDistrict from '@/api/base/district';
import DateStringPicker from '@/components/select/DateStringPicker/DateStringPicker';
import { validateFn, validateTid, validate7Id } from '@/validate/vehicle/vehicleRules';
import {validatePhoneThree, validateRules} from '@/utils/validate';
import { queryPhone, queryDeviceSerial, getRuleBindRealtime, pushRule } from '@/api/base/vehicle'
import { pagination as getTerminal } from '@/api/base/terminalManage';
import { pagination as getSerial } from '@/api/base/peripheralManage';

const defaultForm = {
  id: null,
  licencePlate: '',
  vehicleDomicileCode: null,
  netSignTime: null,
  engineNumber: '',
  repairTime: null,
  yearCheckTime: null,
  deptIdStr: null,
  vehicleModel: null,
  licenceColor: null,
  vehicleUseType: null,
  fuelType: null,
  // vehicleModelLevel: null,
  buyTime: null,
  mannedWeight: 0,
  brandModel: null,
  // transportType: null,
  vin: null,
  serviceState: null,
  operatingStatus: null,
  installTime: null,
  remark: '',
  connectMode: '1',
  centerId: null,
  vehicleOwnerId: null,
  engineModel: '',
  vehicleCorridorSize: null,
  vehicleLength: null,
  vehicleWidth: null,
  vehicleHigh: null,
  axlesAmount: null,
  ownerName: null,
  ownerPhone: null,
  terminal: {
    serial: null,
    terminalId: null,
    imei: null,
    // terminalNo: null,
    id: null,
    phone: '',
    channelNum: null,
    registerTime: null,
    remark: '',
    terminalModel: null,
    terminalType: null,
    vendorId: '',
    localStandards: null,
    terminalFunctionType: null,
    isAi: 0,
    terminalSim: {
      simId: null,
      iccid: null,
      imsi: null,
      customer: null,
      operator: null,
      cardState: null,
      releaseDate: null,
      activeDate: null,
      endDate: null,
      package: null,
      packageTotal: null,
      cardType: null
    }
  },
  // receiveSecurity: 0,
  terminalDevice: {
    cameraNum: null,
    cameraConfigs: [],
    pnsNum: null,
    pnsConfigs: [],
    tirePressureNum: null,
    tirePressureConfigs: [],
    oilNum: null,
    oilConfigs: [],
    weightNum: null,
    weightConfigs: [],
    quakeNum: null,
    quakeConfigs: [],
    temperatureNum: null,
    temperatureConfigs: [],
  },
  transCertificateWord: null,
  transCertificateCode: null,
  grantOrgan: null,
  certificateBeginDate: new Date().toISOString(),
  certificateExpireDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
  businessScopeCode: null,
  businessScopeDesc: null,
  certificateType: null
};
export default {
  components: { Treeselect, DateStringPicker },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
    fromTerminal: {
      type: Boolean,
      default: false
    },
    bindVehicle: {
      type: String,
      default: ''
    },
    districtTreeData: {
      type: Array,
      required: true
    },
    depts: {
      type: Array,
      required: true
    },
    vehicleOwnerId: {
      type: Array,
      required: true
    },
    rulebindrealtime: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      // treeSelect自定义键名
      normalizerVehicleDomicileAdcode (node) {
        return {
          id: node.adcode,
          label: node.name,
          children: node.children || undefined
        };
      },
      // treeSelect自定义键名
      normalizer (node) {
        return {
          id: node.value,
          label: node.label,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      // treeSelect自定义键名
      normalizerDeptId (node) {
        return {
          id: node.id,
          label: node.title,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      rules: {
        deptIdStr: { required: true, validator: validateFn('Vehicle', 'deptIdStr'), trigger: 'input' }, // 车组
        licenceColor: { required: true, validator: validateFn('Vehicle', 'licenceColor'), trigger: ['blur', 'change'] }, // 车牌颜色
        // licencePlate: { required: true, validator: validateRules('Vehicle', 'licencePlate', 'carLicence'), trigger: 'blur' }, // 车牌号码 暂时停用
        licencePlate: { required: true, validator: validateFn('Vehicle', 'licencePlate'), trigger: 'blur' }, // 车牌号码
        vehicleModel: { required: true, validator: validateRules('Vehicle', 'vehicleModel'), trigger: 'select' }, // 车辆类型
        vehicleUseType: { required: true, validator: validateRules('Vehicle', 'vehicleUseType'), trigger: 'select' }, // 行业类型
        // vin: { required: true, validator: validateRules('Vehicle', 'vin'), trigger: 'blur' }, // 车架号
        serviceState: { required: true, validator: validateRules('Vehicle', 'serviceState'), trigger: 'change' }, // 服务状态
        transportType: { required: true, validator: validateRules('Vehicle', 'transportType'), trigger: 'select' }, // 运输类型
        vehicleModelLevel: { required: true, validator: validateRules('Vehicle', 'vehicleModelLevel'), trigger: 'change' }, // 车辆类型等级
        operatingStatus: { required: true, validator: validateRules('Vehicle', 'operatingStatus'), trigger: 'change' }, // 车辆营运状态
        'terminal.terminalId': { required: true, validator: validateFn('Vehicle', 'terminalId'), trigger: 'blur' }, // 序列号
        // 'terminal.terminalNo': { required: true, validator: validateTid('Vehicle', 'terminalNo'), trigger: 'blur' }, // 序列号
        'terminal.vendorId': { required: true, validator: validateRules('Vehicle', 'vendorId', 'onlyNumber'), trigger: 'blur' }, // 厂商编号
        'terminal.terminalModel': { required: true, validator: validateFn('Vehicle', 'terminalModel'), trigger: 'change' }, // 终端型号
        'vehicleDomicileCode': { required: true, validator: validateFn('Vehicle', 'vehicleDomicileCode'), trigger: 'blur' }, // 车籍地
        // 'buyTime': { required: true, validator: validateFn('Vehicle', 'buyTime'), trigger: 'blur' }, // 购买时间
        'netSignTime': { required: true, validator: validateFn('Vehicle', 'netSignTime'), trigger: 'blur' }, // 入网时间
        'terminal.terminalType': { required: true, validator: validateFn('Vehicle', 'terminalType'), trigger: 'change' }, // 终端类型
        'terminal.registerTime': { required: true, validator: validateFn('Vehicle', 'registerTime'), trigger: 'change' }, // 注册时间
        'terminal.phone': { required: true, trigger: 'change' }, // 终端电话
        'vehicleOwnerId': { required: true, validator: validateFn('Vehicle', 'vehicleOwnerId'), trigger: ['blur', 'change'] }, // 车辆归属
        'terminal.terminalSim.simId': { required: true, validator: validateFn('Vehicle', 'simId'), trigger: 'blur' }, // sim卡号
        'terminal.terminalSim.iccid': { required: true, validator: validateFn('Vehicle', 'iccid'), trigger: 'blur' }, // iccid
        'terminal.terminalSim.cardType': { required: true, validator: validateFn('Vehicle', 'cardType'), trigger: 'change' }, // 卡类型
        'terminal.terminalSim.endDate': { required: true, validator: validateFn('Vehicle', 'endDate'), trigger: 'change' }, // 服务期止
        certificateBeginDate: { required: true, validator: validateRules('Vehicle', 'certificateBeginDate'), trigger: 'change' }, // 有效期起
        certificateExpireDate: { required: true, validator: validateRules('Vehicle', 'certificateExpireDate'), trigger: 'change' } // 有效期止
      },
      connectStatus: [
        {
          value: true,
          label: '接驳'
        },
        {
          value: false,
          label: '非接驳'
        }
      ],
      cameraNum: [
        { label: '0', value: 0},
        { label: '1', value: 1},
        { label: '2', value: 2},
        { label: '3', value: 3},
        { label: '4', value: 4},
        { label: '5', value: 5},
        { label: '6', value: 6},
        { label: '7', value: 7},
        { label: '8', value: 8}
      ],
      pnsNum: [
        { label: '0', value: 0},
        { label: '1', value: 1},
        { label: '2', value: 2},
        { label: '3', value: 3},
        { label: '4', value: 4},
        { label: '5', value: 5},
        { label: '6', value: 6},
        { label: '7', value: 7},
        { label: '8', value: 8}
      ],
      tirePressureNum: [
        { label: '0', value: 0},
        { label: '1', value: 1},
        { label: '2', value: 2},
        { label: '3', value: 3},
        { label: '4', value: 4},
        { label: '5', value: 5},
        { label: '6', value: 6},
        { label: '7', value: 7},
        { label: '8', value: 8}
      ],
      oilNum: [
        { label: '0', value: 0},
        { label: '1', value: 1},
        { label: '2', value: 2},
        { label: '3', value: 3},
        { label: '4', value: 4},
        { label: '5', value: 5},
        { label: '6', value: 6},
        { label: '7', value: 7},
        { label: '8', value: 8}
      ],
      weightNum: [
        { label: '0', value: 0},
        { label: '1', value: 1},
        { label: '2', value: 2},
        { label: '3', value: 3},
        { label: '4', value: 4},
        { label: '5', value: 5},
        { label: '6', value: 6},
        { label: '7', value: 7},
        { label: '8', value: 8}
      ],
      quakeNum: [
        { label: '0', value: 0},
        { label: '1', value: 1},
        { label: '2', value: 2},
        { label: '3', value: 3},
        { label: '4', value: 4},
        { label: '5', value: 5},
        { label: '6', value: 6},
        { label: '7', value: 7},
        { label: '8', value: 8}
      ],
      temperatureNum: [
        { label: '0', value: 0},
        { label: '1', value: 1},
        { label: '2', value: 2},
        { label: '3', value: 3},
        { label: '4', value: 4},
        { label: '5', value: 5},
        { label: '6', value: 6},
        { label: '7', value: 7},
        { label: '8', value: 8}
      ],
      showDialog: false,
      btnLoading: false, // 防止重复点击
      activeTab: 'first',
      deviceForm: {
        deviceId: null,
        deviceType: '',
        deviceModel: '',
        deviceName: '',
        deviceSerial: '',
        locationName: '',
        lowerThreshold: null,
        upperThreshold: null,
        manufacturer: null,
        shotPhoto: false,
        buyDate: null,
        warrantyPeriod: null,
        firstInstallDate: null,
        deviceState: null
      },
      deviceList: [],
      deviceTypeList: ['cameraConfigs', 'pnsConfigs', 'tirePressureConfigs', 'oilConfigs', 'weightConfigs', 'quakeConfigs', 'temperatureConfigs'],
      device: ['cameraNum', 'pnsNum', 'tirePressureNum', 'oilNum', 'weightNum', 'quakeNum', 'temperatureNum'],
      deviceNum: 0,
      ruleIds: [],
      submitLicencePlate: ''
    }
  },
  // created () {
  //   // 路由切换需要重新请求获取districtTreeData
  //   crudDistrict.tree().then(res => {
  //     this.districtTreeData = res.data;
  //   });
  // },
  methods: {
    // 相差8小时, 后台存储不会加上8小时, 直接往数据库存储, 因此前端自行加上8小时
    handleDateChange(time) {
      this.form.terminal.terminalSim[time] = new Date(new Date(this.form.terminal.terminalSim[time]).getTime() + 8 * 60 * 60 * 1000);
    },
    handleConnectMode () {
      this.form.centerId = null;
    },
    // 对唯一性编码数据进行处理
    async getOptions (_val) {
      let optionsReturn = [];
      const query = {
        serial: _val
      };
      const { code, data } = await queryPhone(query);
      if (code === 200 && data) {
        for (let index = 0; index < data.length && index <= 20; index++) {
          const element = data[index];
          optionsReturn.push({
            value: element
          });
        }
      }
      return optionsReturn;
    },
    // 远程搜索终端手机号/唯一性编码
    querySearch (_val, _callback) {
      this.getOptions(_val).then(res=>{
        _callback(res);
      });
    },
    // 选择终端手机号/唯一性编码 事件
    async handlePhone (item) {
      const query = {
        page: 0,
        size: 10,
        serial: item.value
      };
      const { code, data} = await getTerminal(query);
      if (code === 200 && data) {
        const obj = data.content.find((element)=>element.serial === item.value);
        Object.keys(this.form.terminal).forEach((key)=>{
          // 切换终端不改变sim卡信息
          if (key !== 'terminalSim') {
            this.$set(this.form.terminal, key, obj[key]);
          }
        });
      }
    },
    // 对外设序列号数据进行处理
    async getSerialOptions (_val) {
      let optionsReturn = [];
      const query = {
        deviceSerial: _val
      };
      const { code, data } = await queryDeviceSerial(query);
      if (code === 200 && data) {
        for (let index = 0; index < data.length && index <= 20; index++) {
          const element = data[index];
          optionsReturn.push({
            value: element
          });
        }
      }
      return optionsReturn;
    },
    // 远程搜索外设序列号
    querySerial (_val, _callback) {
      this.getSerialOptions(_val).then(res=>{
        _callback(res);
      });
    },
    // 选择外设序列号事件
    async handleSerial (item, from) {
      const query = {
        page: 0,
        size: 10,
        deviceSerial: item.value
      };
      const { code, data} = await getSerial(query);
      if (code === 200 && data) {
        const obj = data.content.find((element)=>element.deviceSerial === item.value);
        Object.keys(from).forEach((item)=>{
          this.$set(from, item, obj[item]);
        });
        this.device.forEach((item, index) => {
          const num = (index + 1).toString();
          const list = this.deviceList.filter((element) => element.deviceType === num);
          this.form.terminalDevice[item] = list.length || null;
        });
      }
    },
    // 外设选择
    handleSelect(val, num){
      if (val) {
        let result = this.deviceList.some((item) => item.deviceType === num.toString());
        // 存在旧数据就清除掉
        if (result) {
          this.handleClear(num);
        }
        for (let index = 0; index < val; index++) {
          let deviceForm = JSON.parse(JSON.stringify(this.deviceForm));
          deviceForm.deviceId = this.deviceList.length + 1;
          deviceForm.locationName = "通道" + deviceForm.deviceId;
          deviceForm.deviceType = num.toString();
          if (num !== 1) {
            delete deviceForm.shotPhoto;
          }
          this.deviceList.push(deviceForm);
        }
      }else{
        this.handleClear(num);
      }
    },
    // 外设删除
    handleClear (num) {
      const type = num.toString();
      this.deviceList = this.deviceList.filter((item) => item.deviceType !== type);
    },
    // 新建/编辑之前
    [CRUD.HOOK.beforeToCU] () {
      this.showDialog = true;
      this.activeTab = 'first';
      this.deviceList = [];
      // crudDistrict.tree().then(res => {
      //   this.districtTreeData = res.data;
      // });
      if (!this.form.vehicleDomicileCode) {
        this.form.vehicleDomicileCode = 650000; // 默认新疆维吾尔自治区
      }
      if (this.fromTerminal) {
        setTimeout(() => {
          this.form.terminal.phone = this.bindVehicle;
        }, 200);
      }
    },
    // 编辑之前
    [CRUD.HOOK.beforeToEdit] () {
      if (!this.form.terminal.terminalSim) {
        this.form.terminal.terminalSim = {};
      }
      if (this.form.vehicleUseType) {
        this.form.vehicleUseType = this.form.vehicleUseType + '';
      }
      this.$nextTick(()=>{
        for (let index = 0; index < this.deviceTypeList.length; index++) {
          const element = this.deviceTypeList[index];
          if (this.form.terminalDevice[element] && this.form.terminalDevice[element].length > 0) {
            this.deviceList = this.deviceList.concat(this.form.terminalDevice[element]);
          }
        }
      });
    },
    // // 通过验证之后
    // [CRUD.HOOK.afterValidateCU] () {
    //   if (!this.form.deptIdStr) {
    //     this.$notify({
    //       title: `${this.getLabel('deptIdStr')}不能为空`,
    //       type: 'warning'
    //     });
    //     return false;
    //   }
    //   return true;
    // },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Vehicle', value);
    },
    // 判断开始时间小于结束时间
    judge(time) {
      this.$nextTick(() => {
        if (this.$moment(this.form.certificateBeginDate).valueOf() > this.$moment(this.form.certificateExpireDate).valueOf()) {
          this.form[time] = null;
          this.$message({
            type: 'warning',
            message: '有效期止要大于有效期起'
          });
        }
      });
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU] () {
      this.$refs.form.validate((valid)=>{
        if (!valid) {
          this.validateTreeSelect('vehicleModel');
          this.validateTreeSelect('deptIdStr');
          this.validateTreeSelect('vehicleUseType');
          this.validateTreeSelect('vehicleDomicileCode');
          // this.validateTreeSelect('transportType');
          // this.validateTreeSelect('businessScopeCode');
          // this.validateTreeSelect('certificateType');
          this.$message.warning('请确认必填项全部填写');
        }
      });
    },
    // 提交之前
    [CRUD.HOOK.beforeSubmit] (hook, item) {
      if (this.btnLoading) {
        setTimeout(() => {
          this.btnLoading = false;
        }, 2000);
        return;
      } else {
        this.btnLoading = true;
      }
      if (this.fromTerminal) {
        this.crud.form.id = null;
      }
      let reg = /^0\d*$/;
      // if (reg.test(this.form.terminal.terminalNo)) {
      //   this.form.terminal.terminalNo = this.form.terminal.terminalNo.substring(1);
      // }
      this.deviceTypeList.forEach((element, index) => {
        // deviceType为字典值, 字典值-1对应的就是deviceTypeList里的字段名称
        const str = (index + 1).toString();
        this.form.terminalDevice[element] = this.deviceList.filter((item) => item.deviceType === str);
      });
      this.submitLicencePlate = this.form.licencePlate
    },
    // 提交之后
    [CRUD.HOOK.afterSubmit] (hook, item) {
      if(!this.form.id && this.ruleIds.length > 0) {
        pushRule({
          licence_plate: [this.submitLicencePlate],
          rule_id: this.ruleIds
        }).then(res => {
          const { code, msg } = res
          this.submitLicencePlate = null
          this.ruleIds = []
          if(code === 200) {
            this.$message.success('绑定告警规则成功')
          } else {
            this.$message.error(msg)
          }
        }).catch(()=> {
          this.submitLicencePlate = null
          this.ruleIds = []
        })
      }
      this.showDialog = false;
      this.btnLoading = false;
    },
    // 待绑定终端之后
    [CRUD.HOOK.afterEditError] (hook, item) {
      if (this.fromTerminal) {
        this.$emit('updateTerminalList');
      }
      this.showDialog = false;
      this.btnLoading = false;
    },
    // 监听关闭事件
    closed () {
      let refList = ['vehicleModelRef', 'deptIdStrRef', 'vehicleUseTypeRef', 'vehicleDomicileCodeRef', 'transportTypeRef', 'businessScopeCodeRef', 'certificateTypeRef'];
      for (let i = 0; i < refList.length; i++) {
        if (this.$refs[refList[i]]) {
          this.$refs[refList[i]].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      }
      this.submitLicencePlate = null;
      this.ruleIds = [];
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input__inner {
    text-align: left;
  }
.subTitle ::v-deep .el-divider__text{
  font-size: 14px;
  font-weight: 700;
  color: #606266;
}
::v-deep .el-form-item__label{
  font-weight: 400;
}
::v-deep .el-row{
  // justify-content: space-between;
}
.device-content{
  border: 1px solid rgb(204, 204, 204);
  background-color: rgb(255, 255, 255);
  overflow: auto;
  margin-left: 10px;
  .table-header{
    white-space: nowrap;
    span{
      display: inline-block;
      width: 150px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      border: 1px solid rgb(204, 204, 204);
    }
  }
  .table-item{
    white-space: nowrap;
    &-input{
      display: inline-block;
      width: 150px;
      border: 1px solid rgb(204, 204, 204);
    }
  }
}
::v-deep {
    .el-dialog__body {
        padding: 0;
    }
}
::v-deep .el-autocomplete{
  width: 100%;
}
</style>
