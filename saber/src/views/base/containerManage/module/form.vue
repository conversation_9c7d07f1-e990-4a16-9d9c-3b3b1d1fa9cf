<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="155px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="集装箱编号"
            prop="number"
          >
            <el-input
              v-model.trim="form.number"
              placeholder="要输入字母和数字"
              maxlength="11"
              style="width: 100%"
              :disabled="!crud.status.title.includes('新增')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="所属机构"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="箱型"
            prop="model"
          >
            <single-select
              :options="dict.boxType"
              v-model="form.model"
              placeholder="请选择箱型"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="尺寸(英尺)"
            prop="size"
          >
            <el-input-number
              v-model.number="form.size"
              controls-position="right"
              placeholder="请输入尺寸"
              style="width: 100%"
              :precision="2"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="总重(kg)"
            prop="maxGross"
          >
            <el-input-number
              v-model.number="form.maxGross"
              controls-position="right"
              placeholder="请输入总重"
              style="width: 100%"
              :precision="2"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="自重/皮重(kg)"
            prop="tare"
          >
            <el-input-number
              v-model.number="form.tare"
              controls-position="right"
              placeholder="请输入自重/皮重"
              style="width: 100%"
              :precision="2"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="载重/净重(kg)"
            prop="net"
          >
            <el-input-number
              v-model.number="form.net"
              controls-position="right"
              placeholder="请输入载重/净重"
              style="width: 100%"
              :precision="2"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="最大装货容积(m^3)"
            prop="cuCap"
          >
            <el-input-number
              v-model.number="form.cuCap"
              controls-position="right"
              placeholder="请输入最大装货容积"
              style="width: 100%"
              :precision="3"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="长度(mm)"
            prop="length"
          >
            <el-input-number
              v-model.number="form.length"
              controls-position="right"
              placeholder="请输入长度"
              style="width: 100%"
              :precision="0"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="高度(mm)"
            prop="height"
          >
            <el-input-number
              v-model.number="form.height"
              controls-position="right"
              placeholder="请输入高度"
              style="width: 100%"
              :precision="0"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
const defaultForm = {
  number: "",
  deptId: null,
  deptName: "",
  model: "",
  size: null,
  maxGross: null,
  tare: null,
  net: null,
  cuCap: null,
  length: null,
  height: null,
};
export default {
  components: {
    DeptFormSingleSelect,
    SingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true,
    },
    btnShow: {
      type: Boolean,
      default: true,
    },
    regTurn: {
      type: Object,
      required: true,
    },
    regTurnM: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      rules: {
        deptId: { required: true, trigger: "blur" }, // 车组
        number: {
          required: true,
          trigger: "blur",
        },
      },
      depts: [],
      loading: false,
    };
  },
  methods: {
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit("cancelCU");
    },
    // 新增/编辑前
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU] () {
      const list = Object.keys(this.regTurn);
      for (let i = 0; i < list.length; i++) {
        const key = list[i];
        if (this.form[key] && !this.regTurn[key].test(this.form[key])) {
          this.$message.error(this.regTurnM[key]);
          return false;
        }
      }
      return true;
    },
    // 监听关闭事件
    closed() {
      this.$emit("update:isDetail", false);
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}


/deep/ .el-form-item__label {
  font-weight: 400;
}



/deep/ .el-icon-delete {
  font-size: 30px;
}
</style>
