<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    :modal-append-to-body="false"
    title="绑定终端"
    width="70%"
    @close="closeDialog"
  >
    <!--表格渲染-->
    <el-table
      ref="table"
      :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
      :data="data"
      :cell-style="{'text-align':'center'}"
      :max-height="tableMaxHeight"
      style="width: 100%;height: calc(100% - 47px);"
    >
      <el-table-column
        type="index"
        label="#"
      />
      <el-table-column
        :label="getLabel('deviceType')"
        :show-overflow-tooltip="true"
        min-width="20"
        prop="deviceTypeName"
      />
      <el-table-column
        :label="getLabel('category')"
        :show-overflow-tooltip="true"
        min-width="20"
        prop="categoryName"
      />
      <el-table-column

        :label="getLabel('uniqueId')"
        :show-overflow-tooltip="true"
        prop="uniqueId"
        min-width="20"
      />
      <el-table-column
        :label="getLabel('model')"
        :show-overflow-tooltip="true"
        min-width="20"
        prop="model"
      />
      <el-table-column
        :label="getLabel('deptName')"
        :show-overflow-tooltip="true"
        min-width="40"
        prop="deptName"
      />

      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </el-table>
  </el-dialog>
</template>

<script>

import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import {driverBindVehicleDetail} from '@/api/base/driver.js';
const defaultForm = {
};
export default {
  name: 'BindInfo',
  components: {
  },
  mixins: [],
  props: {
    dict: {
      type: Object,
      required: true
    },// 外部控制显隐
    bindUser: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    },
    bindInfoDialogVisible: {
      type: Boolean,
      default: false
    },

  },
  data() {
    return {
      data:[],
      dialogVisible: true,

    };
  },
  watch: {
    bindInfoDialogVisible (newValue) {
      this.dialogVisible = newValue;
    },
  },
  created () {
    this.driverBindVehicleDetail();
  },
  methods: {
    driverBindVehicleDetail(){
      driverBindVehicleDetail(this.bindUser).then((res) => {
        console.log('-> res.data', res.data);
        this.data = res.data;
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Driver', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('Driver', value);
    },

    // 关闭dialog
    closeDialog () {
      this.$emit('closeBind');
    },

    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getMaster (master) {
      let result = '';
      switch (master) {
      case 0:
        result = '否';
        break;
      case 1:
        result = '是';
        break;

      default:
        break;
      }
      return result;
    }

  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

.subTitle /deep/ .el-divider__text {
  font-size: 14px;
  font-weight: 700;
  color: #606266;
}

/deep/ .el-form-item__label {
  font-weight: 400;
}

/deep/ .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

/deep/ .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

/deep/ .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

/deep/ .avatar {
  width: 128px;
  height: 128px;
  display: block;
}

/deep/ .el-upload-list__item-actions:hover {
  opacity: 1;
}

/deep/ .el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #ffffff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(255, 255, 255, 0.5);
  transition: opacity .3s;
}

/deep/ .el-upload-list__item-actions:hover span {
  display: inline-block;
}

/deep/ .upload-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -5px;
  margin-top: -15px;
  width: 30px;
  height: 30px;
}

/deep/ .el-icon-delete {
  font-size: 30px;
}

// /deep/ .el-dialog__body {
//   padding: 0;
// }
.section-title {
  height: 100%;
  padding: 0 20px;
  font-size: 16px;
  color: #409EFE;
}

::v-deep .el-collapse-item__header {
  border-bottom: 1px solid #e1e5e8 !important;

  &.is-active {
    border-bottom: none;
  }

}
</style>
