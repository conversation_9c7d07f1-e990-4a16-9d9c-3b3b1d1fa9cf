<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="90px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            label="操作"
            width="120"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('code')"
            label="厂商编号"
            :show-overflow-tooltip="true"
            min-width="80"
            prop="code"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('name')"
            label="厂商名称"
            :show-overflow-tooltip="true"
            min-width="120"
            prop="name"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('address')"
            label="厂商地址"
            :show-overflow-tooltip="true"
            min-width="80"
            prop="address"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('contactPerson')"
            label="联系人"
            :show-overflow-tooltip="true"
            min-width="100"
            prop="contactPerson"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('contactPhone')"
            label="联系电话"
            :show-overflow-tooltip="true"
            min-width="100"
            prop="contactPhone"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--表单渲染-->
      <pagination/>
      <eForm
        :dict="dict"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
      <BatchImport
        :visible="batchvisible"
        :rulebindrealtime="rulebindrealtime"
        mod="vehicle"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
    </div>
  </basic-container>
</template>

<script>
import crudManuFactor from '@/api/bdTest/manuFactor';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from './module/msgDialog.vue';
import HeadCommon from '@/components/formHead/headCommon.vue';
import pagination from '@/components/Crud/Pagination';

// crud交由presenter持有
const crud = CRUD({
  title: '厂商信息管理',
  crudMethod: { ...crudManuFactor }
});

export default {
  name: 'BdcManuFactor',
  components: {
    eForm,
    crudOperation,
    udOperation,
    BatchImport,
    msgDialog,
    HeadCommon,
    pagination
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [],
  data() {
    return {
      permission: {
        add: [
          'admin',
          'bdcmanufactor:add'
        ],
        edit: [
          'admin',
          'bdcmanufactor:edit'
        ],
        del: [
          'admin',
          'bdcmanufactor:del'
        ]
      },
      // 批量引入相关
      batchvisible: false,
      dataType: { // excel里文字对应的key
        '厂商名称': 'name'
      },
      // 时间类型，统一转换时间
      typeTime: [
        'buyTime',
        'netSignTime',
        'repairTime',
        'yearCheckTime',
        'installTime',
        'registerTime',
        'endDate',
        'releaseDate',
        'activeDate',
        'certificateBeginDate',
        'certificateExpireDate'
      ],
      // 必填项
      typeRequired: [
        'licencePlate'
      ],
      numKey: undefined, // 递归中继值
      tipsKey: [], // 提示点集合
      msgData: [], // 批量导入提示消息
      terminalList: [
        'serial',
        'terminalId',
        'phone',
        'imei',
        'terminalType',
        'terminalModel',
        'vendorId',
        'channelNum',
        'registerTime',
        'localStandards',
        'terminalFunctionType',
        'isAi'
      ], // 终端字段
      terminalSimList: [
        'simId',
        'iccid',
        'imsi',
        'customer',
        'operator',
        'cardState',
        'releaseDate',
        'activeDate',
        'endDate',
        'package',
        'packageTotal',
        'cardType'
      ], // sim卡字段
      terminalDeviceList: ['cameraNum'], // 外设字段
      strNumType: {
        str: [
          'vin',
          'engineNumber',
          'ownerPhone',
          'engineModel',
          'brandModel',
          'transCertificateCode',
          'terminalModel'
        ],
        num: [
          'channelNum',
          'vehicleUseType'
        ]
      },
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        certificateType: 'certificateMediumType',
        businessScopeCode: 'transportType'
      },
      deviceForm: {
        deviceId: null,
        uniqueId: '',
        deviceType: '',
        deviceModel: '',
        deviceName: '',
        deviceSerial: '',
        locationNo: null,
        locationName: '',
        lowerThreshold: null,
        upperThreshold: null,
        manufacturer: null,
        shotPhoto: true
      },
      headConfig: {
        item: {
          2: {
            name: '厂商编号',
            type: 'input',
            value: 'code'
          },
          1: {
            name: '厂商名称',
            type: 'input',
            value: 'name'
          },
        },
        button: {}
      },
      rulebindrealtime: [],
      isHomeVehicle: false,
      btnShow: true // 显示确认取消按钮
    };
  },
  created() {
  },
  activated() {
  },
  beforeDestroy() {
    this.crud.resetQuery(false);
  },
  methods: {
    /** 刷新 - 之后 */
    [CRUD.HOOK.afterRefresh]() {
    },
    /** 导出 - 之前 */
    [CRUD.HOOK.beforeExport]() {
      if (this.crud.selections.length) {
        const list = this.crud.selections.map((item) => {
          return item.id;
        });
        this.$set(crud.query, 'ids', list.join(','));
      }
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh]() {
      if (this.crud.query.ids) {
        this.crud.query.ids = null;
      }
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    toDetails(param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel() {
      this.btnShow = true;
      console.log(crud.form);
    }

  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.xh-container ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1;
}
</style>
