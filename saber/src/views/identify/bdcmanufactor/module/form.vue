<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow?crud.status.title:'查看'"
    append-to-body
    width="50%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="btnShow ? rules : {}"
      size="small"
      label-width="150px"
      class="rewriting-form-disable"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="厂商编号"
            prop="code"
          >
            <el-input
              v-model.trim="form.code"
              placeholder="请输入厂商编号(四位数字)"
              maxlength="4"
              minlength="4"
              :disabled="!btnShow || crud.status.edit"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="厂商名称"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              placeholder="请输入厂商名称"
              maxlength="50"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="厂商地址"
            prop="address"
          >
            <el-input
              v-model.trim="form.address"
              placeholder="请输入厂商地址"
              maxlength="200"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="联系人"
            prop="contactPerson"
          >
            <el-input
              v-model.trim="form.contactPerson"
              placeholder="请输入联系人"
              maxlength="20"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="联系电话"
            prop="contactPhone"
          >
            <el-input
              v-model.trim="form.contactPhone"
              placeholder="请输入联系电话"
              maxlength="15"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import { validatePhoneTwo } from '@/utils/validate';

const defaultForm = {
  code: '',
  name: '',
  contactPerson: '',
  address: '',
  contactPhone: '',
  id: ''
};
export default {
  components: {},
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    btnShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      depts: [],
      // treeSelect自定义键名
      normalizerDeptId(node) {
        return {
          id: node.id,
          label: node.title,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      rules: {
        code: {
          required: true,
          trigger: 'blur'
        },
        name: {
          required: true,
          trigger: 'blur'
        },
        contactPerson: {
          required: true,
          trigger: 'blur'
        },
        address: {
          required: true,
          trigger: 'blur'
        },
        contactPhone: [
          {
            required: true,
            trigger: 'blur'
          }
        ]
      }
    };
  },
  created() {
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit('cancelCU');
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU](hook) {
      console.log('-> hook', hook)
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message.warning('请确认必填项已全部正确填写');
        }
      });
    },
    /** 提交- 之后 */
    [CRUD.HOOK.afterSubmit]() {
      // this.form = defaultForm;
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU]() {
      const reg = /^((0\d{2,3}-\d{7,8})|(1\d{10}))$/;
      if (!/^\d{4}$/.test(this.form.code)) {
        console.log(32323);
        this.$message.error('厂商编号必须为四位数字');
        return false;
      }
      else if (!reg.test(this.form.contactPhone)) {
        this.$message.warning('请填写正确的手机号码');
        return false;
      }
    },

    // 监听关闭事件
    closed() {
      let refList = [
        'deptIdStrRef'
      ];
      for (let i = 0; i < refList.length; i++) {
        if (this.$refs[refList[i]]) {
          this.$refs[refList[i]].$children[0].$el.style.borderColor = '#bfbfbf';
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

</style>
