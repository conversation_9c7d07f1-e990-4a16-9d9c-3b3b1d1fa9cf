<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        >
          <!--导入暂未开发            -->
          <el-button
            v-if="false"
            slot="right"
            class="filter-item"
            icon="el-icon-upload2"
            size="small"
            @click="batchvisible = true"
          >
            导入
          </el-button>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
            :selectable="checkCanDel"
          />
          <el-table-column
            label="操作"
            width="120"
            align="center"
            fixed="right"
            style="z-index: 999;"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                :disabledDle="disabledDleAndEdit(scope.row)"
                :disabledEdit="disabledDleAndEdit(scope.row)"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <el-table-column
            type="expand"
            label="参与检测列表"
            width="120px"
            :resizable="false"
            style="z-index: 999;"
          >
            <template slot-scope="props">
              <el-card style="padding: 12px 6px;margin: 4px 124px 4px 52px;">
                <el-table
                  :data="getInCheckList(props.row.terminalCheckData)"
                  class="detail-table"
                >
                  <el-table-column
                    type="index"
                    width="70"
                    label="序号"
                  />
                  <el-table-column
                    prop="deviceSeq"
                    label="序列号"
                    min-width="160"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  />
                  <el-table-column
                    prop="deviceType"
                    label="设备类型"
                    min-width="140"
                    :resizable="false"
                  >
                    <template slot-scope="scope">
                      <span>
                        {{ getEnumDictLabel('testDeviceType', scope.row.deviceType) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="deviceModel"
                    label="设备型号"
                    min-width="140"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  />
                  <el-table-column
                    prop="protocol"
                    label="协议"
                    min-width="120"
                    :resizable="false"
                  />
                  <el-table-column
                    prop="manufacturer"
                    label="设备厂商"
                    min-width="140"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  >
                    <template slot-scope="scope">
                      <span>
                        {{ getManufacturerLabel(scope.row.manufacturer) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="imei"
                    label="IMEI"
                    min-width="120"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  />
                  <!--                    <el-table-column-->
                  <!--                      prop="deviceSeq"-->
                  <!--                      label="序列号"-->
                  <!--                      :show-overflow-tooltip="true"-->
                  <!--                    />-->
                  <el-table-column
                    prop="chipSeq"
                    label="北斗芯片序列号"
                    min-width="120"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  />
                  <!--                    <el-table-column-->
                  <!--                      prop="sim"-->
                  <!--                      label="SIM卡号"-->
                  <!--                      :show-overflow-tooltip="true"-->
                  <!--                    />-->
                  <el-empty
                    slot="empty"
                    :image="require('@/assets/images/nodata.png')"
                  />
                </el-table>
              </el-card>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('companyName')"
            prop="companyName"
            label="委托企业"
            min-width="140"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('batchNo')"
            prop="batchNo"
            label="批次"
            min-width="100"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.batchNo }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('sendDate')"
            prop="sendDate"
            label="送检日期"
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.sendDate }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('total')"
            prop="total"
            label="总数量"
            width="80"
            :resizable="false"
          >
            <template slot-scope="props">
              <span> {{ props.row.terminalCheckData && props.row.terminalCheckData.length }} </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkNum')"
            prop="checkNum"
            label="送检数量"
            width="80"
            :resizable="false"
          >
            <template slot-scope="props">
              {{ getInCheckList(props.row.terminalCheckData).length }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkResult')"
            prop="checkResult"
            min-width="160"
            label="检测状态"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getCheckRes(scope.row) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkType')"
            prop="checkType"
            min-width="100"
            label="检测方式"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel('bdmCheckType', scope.row.checkType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkAddress')"
            prop="checkAddress"
            min-width="120"
            label="检测地址"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('checkBase')"
            prop="checkBase"
            min-width="120"
            label="检测依据"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('checkMethod')"
            prop="checkMethod"
            min-width="120"
            label="检测方法"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('allTerminalList')"
            prop="allTerminalList"
            label="全部设备列表"
            width="120"
            :show-overflow-tooltip="true"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span
                class="active-label"
                @click="openDetailAllList(scope.row.terminalCheckData)"
              >
                查看列表
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
    <eForm
      :dict="dict"
      :btnShow="btnShow"
      :company-list="companyList"
      :manufacturer-list="manufacturerList"
      @editDialogClosed="editDialogClosed"
    />
    <BatchImport
      :visible="batchvisible"
      :rulebindrealtime="rulebindrealtime"
      mod="vehicle"
      @close="batchvisible = false"
      @getBatchData="getBatchData"
    />
    <msgDialog
      ref="msgDialog"
      :msg-data="msgData"
    />
    <el-dialog
      v-dialog-drag
      :close-on-click-modal="false"
      :visible="detailListVisible"
      title="全部设备列表"
      append-to-body
      width="75%"
      @close="detailListVisible = false"
    >
      <el-table
        :data="detailAllList"
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
        />
        <el-table-column
          prop="deviceSeq"
          label="序列号"
          width="180"
        />
        <el-table-column
          prop="deviceCate"
          label="设备类别"
          width="120"
        >
          <template slot-scope="scope">
            <span>
              {{ getEnumDictLabel('testDeviceType', scope.row.deviceCate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="deviceType"
          label="设备类型"
          width="120"
        >
          <template slot-scope="scope">
            <span>
              {{ getEnumDictLabel('testDeviceType', scope.row.deviceType) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="deviceModel"
          label="设备型号"
          width="140"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="protocol"
          label="协议"
          min-width="100"
        />
        <el-table-column
          prop="manufacturer"
          label="设备厂商"
          min-width="140"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>
              {{ getManufacturerLabel(scope.row.manufacturer) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="imei"
          label="IMEI"
          min-width="140"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="chipSeq"
          label="北斗芯片序列号"
          min-width="140"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="isInCheck"
          label="参与检测"
          width="80"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.isInCheck === 0 ? '否' : '是' }}
            </span>
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </el-table>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="detailListVisible = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import terminalManager from '@/api/bdTest/terminalManager.js';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import eForm from './module/form.vue';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from './module/msgDialog.vue';
import { addbatch } from '@/api/bdTest/company';
import udOperation from '@/components/Crud/UD.operation.vue';
import { pagination as getList } from '@/api/bdTest/manuFactor';
import HeadCommon from '@/components/formHead/headCommon.vue';
import { allCompanyName } from '@/api/bdTest/report';

// crud交由presenter持有
const crud = CRUD({
  title: '入网设备管理',
  crudMethod: { ...terminalManager }
});

export default {
  name: 'TerminalManager',
  components: {
    udOperation,
    msgDialog,
    BatchImport,
    eForm,
    crudOperation,
    pagination,
    HeadCommon
  },
  mixins: [presenter(crud)],
  dicts: [
    'testDeviceType',
    'checkProcess',
    'testDeviceType',
    'bdmCheckType',
    'bdmTerminalProtocol'
  ],
  data() {
    return {
      permission: {
        add: [
          'admin',
          'terminalManager:add'
        ],
        edit: [
          'admin',
          'terminalManager:edit'
        ],
        del: [
          'admin',
          'terminalManager:del'
        ]
      },
      batchvisible: false,
      firstOpen: true,
      dialogVisible: false,
      dialogDetailId: null,
      msgData: [],
      btnShow: true,
      detailAllList: [],
      detailListVisible: false,
      manufacturerList: [],
      headConfig: {
        item: {
          1: {
            name: '委托企业',
            type: 'select',
            value: 'companyId',
            options: []
          },
          2: {
            name: '设备类型',
            type: 'select',
            value: 'terminalType',
            dictsOptions: 'testDeviceType'
          },
          3: {
            name: '设备型号',
            type: 'input',
            value: 'terminalModel'
          },
          4: {
            name: '批次',
            type: 'date',
            value: 'batchNo',
            valueFormat: 'yyyyMMdd'
          },
          5: {
            name: '序列号',
            type: 'input',
            value: 'deviceSeq',
          },
        },
        button: {}
      },
      companyList: []
    };
  },
  computed: {
    getManufacturerLabel() {
      return (manufacturer) => {
        let labelName = '';
        for (let manufacturerListElement of this.manufacturerList) {
          if (manufacturerListElement.value === manufacturer) {
            labelName = manufacturerListElement.label;
          }
        }
        return labelName;
      };
    }
  },
  created() {
    this.getManufacturerList();
    this.getCompanyList();
  },
  methods: {
    getManufacturerList() {
      getList({
        page: 0,
        size: 9999
      }).then(res => {
        this.manufacturerList = res.data.content.map(item => ({
          label: item.name,
          value: item.code
        }));
      });
    },
    getCompanyList() {
      allCompanyName().then(res => {
        this.companyList = res.data.map(item => ({
          label: item.companyName,
          value: item.id
        }));
        this.headConfig.item['1'].options = this.companyList;
      });
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    // 提交请求
    addbatchPost(arr) {
      addbatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    // 必填项判断
    typeRequiredTurn(obj, k, reversalObj, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, reversalObj, v.required);
        }
        else if (!obj[v] && obj[v] !== 0) {
          if (!this.tipsKey[k]) {
            this.tipsKey[k] = [reversalObj[v]];
          }
          else {
            this.tipsKey[k].push(reversalObj[v]);
          }
        }
      });
    },
    typeOwnerTurn(obj) {
      let word = obj['vehicleOwnerId'];
      if (!word && word !== 0) {
        return;
      }
      this.treeTurn(this.vehicleOwnerId, obj['vehicleOwnerId'], 'value', 'label');
      obj['vehicleOwnerId'] = this.numKey;
      this.numKey = undefined;
    },
    typeCityTurn(obj) {
      let word = obj['vehicleDomicileCode'];
      if (!word && word !== 0) {
        return;
      }
      this.treeTurn(this.districtTreeData, obj['vehicleDomicileCode'], 'adcode', 'name');
      obj['vehicleDomicileCode'] = this.numKey;
      this.numKey = undefined;
    },
    typeDeptTurn(obj) {
      let word = obj['deptIdStr'];
      if (!word && word !== 0) {
        return;
      }
      this.treeTurn(this.depts, obj['deptIdStr'], 'id', 'title');
      obj['deptIdStr'] = this.numKey;
      this.numKey = undefined;
    },

    // 字典里的转key/id terminalModel除外
    typeTreeTurn(obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j]);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
          else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j]);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    // 递归找key/id
    treeTurn(tree, word, iKey = 'value', iValue = 'label', iChildren = 'children') {
      tree.forEach(item => {
        if (item[iValue] === word) {
          this.numKey = item[iKey];
        }
        else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, iKey, iValue, iChildren);
        }
      });
    },

    // 时间转化
    typeTimeTurn(obj) {
      for (let k in obj) {
        this.typeTime.forEach(v => {
          if (k === v && obj[k]) {
            obj[k] = this.timeTurnDate(obj[k]);
          }
        });
      }
    },
    // excel表里时间转化日期格式
    timeTurnDate(numb) {
      let yeraData = new Date(1900, 0, numb - 1);
      let year = yeraData.getFullYear();
      let month = yeraData.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      let day = yeraData.getDate();
      day = day < 10 ? '0' + day : day;
      let t = `${year}-${month}-${day}`;
      t = new Date(t);
      t.setHours(t.getHours() - 8);
      return t;
    },
    // excel表里时间转化时间戳
    timeTurn(numb) {
      let yeraData = new Date(1900, 0, numb - 1);
      let year = yeraData.getFullYear();
      let month = yeraData.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      let day = yeraData.getDate();
      day = day < 10 ? '0' + day : day;
      let t = `${year}-${month}-${day}`;
      t = new Date(t).getTime() / 1000;
      return t;
    },
    toDetails(param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    editDialogClosed() {
      this.btnShow = true;
    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData(returnValue) {
      let data = returnValue.data;
      let reversalObj = {};
      for (let k in this.dataType) {
        reversalObj[this.dataType[k]] = k;
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        if (obj['isAi']) {
          obj['isAi'] = obj['isAi'] === '主动安全设备' ? 1 : 0;
        }
        this.typeTimeTurn(obj);
        this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index, reversalObj);
        this.typeStringTurn(obj);
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let str = '';
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            let s = '';
            item.forEach(v => {
              s += `[${v}]`;
            });
            str += `【第${index + 1}条数据${s}】`;
          }
        });
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            let str = `共${item.length}条`;
            item.forEach(v => {
              str += `[${v}]`;
            });
            arr.push({
              sort: `第${index + 1}条`,
              details: str
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      }
      else {
        this.addbatchPost(arr);
        returnValue.close();
      }
    },
    getCheckRes({
      checkProcess,
      checkResult,
      testResult
    }) {
      if (checkProcess === '0') {
        return '检测未开始';
      }
      else if (checkProcess === '1' || checkProcess === '2') {
        return '检测中';
      }
      else if (checkProcess === '3') {
        if (checkResult === 1 && testResult === 1) {
          return '检测通过';
        }
        else {
          return '检测未通过';
        }
      }
    },
    openDetailAllList(list) {
      this.detailAllList = list;
      this.detailListVisible = true;
    },
    getInCheckList(list) {
      return list.filter(item => item.isInCheck === 1);
    },
    disabledDleAndEdit({
      checkProcess,
      checkResult,
      testResult
    }) {
      const isChecking = checkProcess === '1' || checkProcess === '2';
      const isPass = checkProcess === '3' && checkResult === 1 && testResult === 1;
      if (isPass || isChecking) {
        return true;
      }
    },
    checkCanDel(row) {
      console.log('->  !this.disabledDleAndEdit(row)',  !this.disabledDleAndEdit(row))
      return !this.disabledDleAndEdit(row);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.detail-table {
  width: 100%;

  ::v-deep .el-table__header-wrapper .el-table__cell {
    background-color: #f2f7ff !important;
  }
}
</style>
