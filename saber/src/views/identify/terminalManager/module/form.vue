<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow?crud.status.title:'查看'"
    append-to-body
    destroy-on-close
    width="70%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="btnShow ?rules: {}"
      size="small"
      label-width="150px"
      class="rewriting-form-disable"
    >
      <el-row
        type="flex"
        span="24"
      >
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            label="委托企业"
            prop="companyId"
          >
            <el-select
              v-model="form.companyId"
              clearable
              size="small"
              :disabled="!btnShow"
              filterable
              placeholder="请选择委托企业"
            >
              <el-option
                v-for="item in companyList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            label="批次"
            prop="batchNo"
          >
            <el-date-picker
              v-model="form.batchNo"
              size="small"
              :disabled="!btnShow"
              value-format="yyyyMMdd"
              placeholder="请选择批次"
            />
          </el-form-item>
        </el-col>
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            label="送检日期"
            prop="sendDate"
          >
            <el-date-picker
              v-model="form.sendDate"
              size="small"
              :disabled="!btnShow"
              value-format="yyyy-MM-dd"
              placeholder="请选择送检日期"
            />
          </el-form-item>
        </el-col>
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            prop="deviceType"
          >
            <template #label>
              <div class="device-type-item">
                <span>设备类型</span>
                <el-tooltip>
                  <div slot="content">
                    人员定位产品：安全帽，工卡，手表，手持机等
                    <br>
                    运载定位产品：车载定位终端，无人矿车定位终端，防碰撞定位终端等
                    <br>
                    资产定位管理产品：物资定位终端等
                    <br>
                    短报文通信设备：短报文手持机、数传终端等
                    <br>
                    授时设备：授时设备
                    <br>
                    探测与监测设备：监测接收机，RTK测量，基准站设备,雷达设备，无人机，无人船，精密控制等专业高精度设备
                    <br>
                    模组：定位模组，短报文模组等
                  </div>
                  <i class="el-icon-info"/>
                </el-tooltip>
              </div>
            </template>
            <xh-select
              v-model="form.deviceType"
              clearable
              size="small"
              :disabled="!btnShow"
              placeholder="请选择设备类型"
            >
              <el-option
                v-for="item in dict.dict.testDeviceType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </el-col>
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            label="设备型号"
            prop="deviceModel"
          >
            <el-input
              v-model.trim="form.deviceModel"
              placeholder="请输入设备型号"
              maxlength="20"
              :disabled="!btnShow"
            />
          </el-form-item>
        </el-col>
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            label="协议"
            prop="protocol"
          >
            <xh-select
              v-model="form.protocol"
              size="small"
              placeholder="请选择设备协议"
              class="xh-select-component-filter-item-value"
              :disabled="!btnShow"
              style="margin-left: 0;"
            >
              <el-option
                v-for="item in dict.dict.bdmTerminalProtocol"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </el-col>
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            label="检测方式"
            prop="checkType"
          >
            <xh-select
              v-model="form.checkType"
              clearable
              size="small"
              :disabled="!btnShow"
              placeholder="请选择检测方式"
            >
              <el-option
                v-for="item in dict.dict.bdmCheckType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </el-col>
        <el-col
          :xs="12"
          :sm="12"
          :md="12"
          :lg="8"
          :xl="6"
        >
          <el-form-item
            label="设备厂商"
            prop="manufacturer"
          >
            <xh-select
              v-model="form.manufacturer"
              size="small"
              :disabled="!btnShow"
              placeholder="请选择设备厂商"
            >
              <el-option
                v-for="item in manufacturerList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="检测地址"
            prop="checkAddress"
          >
            <el-input
              v-model.trim="form.checkAddress"
              placeholder="请输入检测地址"
              maxlength="100"
              :disabled="!btnShow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="检测依据"
            prop="checkBase"
          >
            <el-input
              v-model.trim="form.checkBase"
              placeholder="请输入检测依据"
              maxlength="1000"
              :disabled="!btnShow"
              type="textarea"
              :autosize="{
                minRows: 2,
                maxRows: 6
              }"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="检测方法"
            prop="checkMethod"
          >
            <el-input
              v-model.trim="form.checkMethod"
              placeholder="请输入检测方法"
              maxlength="1000"
              :disabled="!btnShow"
              type="textarea"
              :autosize="{
                minRows: 2,
                maxRows: 6
              }"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="全部设备列表"
            :required="btnShow"
          >
            <el-button
              v-if="btnShow"
              size="mini"
              type="primary"
              icon="el-icon-plus"
              style="margin-bottom: 8px;"
              @click="add"
            >新增
            </el-button>
            <el-table
              :data="form.terminalCheckData"
              class="detail-table"
            >
              <el-table-column
                type="index"
                width="50"
                label="序号"
              />
              <el-table-column
                prop="deviceSeq"
                label="序列号"
                align="left"
              >
                <template slot-scope="{row}">
                  <el-input
                    v-if="btnShow && row.id.includes('temp')"
                    v-model.trim="row.deviceSeq"
                    maxlength="30"
                  />
                  <span v-else>{{ row.deviceSeq }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="imei"
                label="IMEI"
              >
                <template slot-scope="{row}">
                  <el-input
                    v-if="btnShow"
                    v-model.trim="row.imei"
                    maxlength="30"
                  />
                  <span v-else>{{ row.imei }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="chipSeq"
                label="北斗芯片序列号"
              >
                <template slot-scope="{row}">
                  <el-input
                    v-if="btnShow"
                    v-model.trim="row.chipSeq"
                    maxlength="30"
                  />
                  <span v-else>{{ row.chipSeq }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="!btnShow && form.id && form.checkResult === 1"
                prop="deviceNum"
                label="设备赋码号"
              >
                <template slot-scope="{row}">
                  <span>{{ row.deviceNum || '未赋码' }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="isInCheck"
                label="参与检测"
                width="80"
              >
                <template slot-scope="{row}">
                  <el-checkbox
                    v-if="btnShow"
                    v-model="row.isInCheck"
                    :true-label="1"
                    :false-label="0"
                  />
                  <span v-else>{{ row.isInCheck === 0 ? '否' : '是' }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="btnShow"
                label="操作"
                width="80"
                fixed="right"
              >
                <template slot-scope="{row ,$index}">
                  <el-button
                    class="table-btn"
                    type="primary"
                    icon="el-icon-copy-document"
                    size="small"
                    @click="copy(row, $index)"
                  />
                  <el-button
                    :disabled="form.terminalCheckData.length === 1"
                    class="table-btn"
                    type="danger"
                    icon="el-icon-delete"
                    size="small"
                    @click="remove(row.id)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';

const defaultListItem = {
  deviceSeq: '',
  imei: '',
  chipSeq: '',
  deviceNum: '',
  isInCheck: 1,
  id: `temp-${new Date().getTime()}`
};
const defaultForm = {
  id: '',
  companyId: '',
  companyName: '',
  batchNo: '',
  sendDate: '',
  deviceType: '',
  checkType: '',
  checkAddress: '',
  checkBase: '',
  checkMethod: '',
  deviceModel: '',
  protocol: '',
  manufacturer: '',
  checkProcess: '',
  reportNo: '',
  reportTime: '',
  state: '',
  terminalCate: '',
  terminalModel: '',
  terminalType: '',
  testResult: '',
  testTime: '',
  updateTime: '',
  createTime: '',
  terminalCheckData: [defaultListItem]
};

export default {
  components: {},
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    btnShow: {
      type: Boolean,
      default: true
    },
    manufacturerList: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      rules: {
        companyId: {
          required: true,
          trigger: 'blur'
        },
        batchNo: {
          required: true,
          trigger: 'change'
        },
        sendDate: {
          required: true,
          trigger: 'change'
        },
        manufacturer: {
          required: true,
          trigger: 'change'
        },
        protocol: {
          required: true,
          trigger: 'change'
        },
        deviceType: {
          required: true,
          trigger: 'blur'
        },
        deviceModel: {
          required: true,
          trigger: 'blur'
        },
        checkType: {
          required: true,
          trigger: 'change'
        },
        checkAddress: {
          required: true,
          trigger: 'blur'
        },
        checkBase: {
          required: true,
          trigger: 'blur'
        },
        checkMethod: {
          required: true,
          trigger: 'blur'
        }
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU]() {
      if (this.form.terminalCheckData.length > 0) {
        const {
          deviceType,
          deviceModel,
          protocol,
          manufacturer
        } = this.form.terminalCheckData[0];
        this.form.deviceType = deviceType;
        this.form.deviceModel = deviceModel;
        this.form.protocol = protocol;
        this.form.manufacturer = manufacturer;
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    [CRUD.HOOK.afterAddError](hook, item) {
      console.log('-> hook, item', hook, item);
      const {
        form,
        response
      } = hook;
      form.terminalCheckData.forEach(item => {
        if (!item.id) {
          item.id = `temp-${new Date().getTime()}`;
        }
      });
      console.log('-> response', response);
      if (response) {
        window.open(response);
      }
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit('cancelCU');
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU]() {
      if (!this.checkTerminalList()) {
        return false;
      }
      if (!this.checkTerminalRepeat()) {
        return false;
      }
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message.warning('请确认必填项已全部填写');
        }
        else {
          this.form.terminalCheckData.forEach(item => {
            if (item?.id?.includes('temp')) {
              delete item.id;
            }
          });
        }
      });
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU]() {
    },
    /**
     * 检查送检设备列表内容完整性
     * @returns {boolean}
     */
    checkTerminalList() {
      let flag = true;
      for (let item of this.form.terminalCheckData) {
        if (!item.deviceSeq || !item.imei || !item.chipSeq) {
          this.$message.error('请确认送检设备列表内容完整填写');
          flag = false;
          return false;
        }
      }
      return flag;
    },
    /**
     * 检查送检设备全部表格项的值是否存在重复
     * @returns {boolean}
     */
    checkTerminalRepeat() {
      let flag = true;
      const imeiArr = [];
      const deviceSeqArr = [];
      const chipSeqArr = [];
      this.form.terminalCheckData.forEach(item => {
        deviceSeqArr.push(item.deviceSeq);
        imeiArr.push(item.imei);
        chipSeqArr.push(item.chipSeq);
      });
      const hasDeviceSeqRepeat = this.hasRepeat(deviceSeqArr);
      const haSIMeiNoRepeat = this.hasRepeat(imeiArr);
      const hasChipSeqNoRepeat = this.hasRepeat(chipSeqArr);
      if (hasDeviceSeqRepeat) {
        this.$message.error('送检设备【序列号】存在重复, 请检查');
        return flag = false;
      }
      if (haSIMeiNoRepeat) {
        this.$message.error('送检设备【IMEI】存在重复, 请检查');
        return flag = false;
      }
      if (hasChipSeqNoRepeat) {
        this.$message.error('送检设备【北斗芯片序列号】存在重复, 请检查');
        return flag = false;
      }
      return flag;
    },
    /**
     * 检查2个数组存在的相同项
     * @param arr1 对比数组1
     * @param arr2 对比数组2
     * @returns {*[]} 返回的结果数组
     */
    findCommonItems(arr1, arr2) {
      return arr1.filter(item => arr2.includes(item));
    },
    /**
     * 检查2个数组存在的不同项
     * @param arr1 对比数组1
     * @param arr2 对比数组2
     * @returns {*[]} 返回的结果数组
     */
    findDifferentItems(arr1, arr2) {
      return [
        ...arr1.filter(item => !arr2.includes(item)),
        ...arr2.filter(item => !arr1.includes(item))
      ];
    },
    /**
     * 校验数组是否有重复值
     * @param array
     * @returns {boolean}
     */
    hasRepeat(array) {
      return new Set(array).size !== array.length;
    },
    // 监听关闭事件
    closed() {
      this.$emit('editDialogClosed');
    },
    add() {
      const listItem = JSON.parse(JSON.stringify(defaultListItem));
      const item = {
        ...listItem,
        id: `temp-${new Date().getTime()}`
      };
      this.form.terminalCheckData.push(item);
    },
    remove(id) {
      const index = this.form.terminalCheckData.findIndex(item => item.id === id);
      this.form.terminalCheckData.splice(index, 1);
    },
    copy(row, index) {
      const listItem = JSON.parse(JSON.stringify(row));
      const item = {
        ...listItem,
        deviceNo: '',
        id: `temp-${new Date().getTime()}`
      };
      this.form.terminalCheckData.splice(index + 1, 0, item);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

.table-btn {
  width: 22px;
  height: 22px;
  padding: 0;
}

.tips {
  font-size: 14px;
  color: #409eff;
}

.device-type-item {
  display: inline-block;

  .el-icon-info {
    color: #409eff;
    font-size: 16px;
    position: relative;
    top: 2px;
  }
}
</style>
