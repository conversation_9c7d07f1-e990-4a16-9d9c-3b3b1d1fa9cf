<template>
  <div id="BDTerminalIdentify">
    <div class="page-title">北斗识别</div>
    <div
      class="fullscreen-btn"
      @click="full"
    >
      <i :class="[fullscreen ? 'icon-tuichuquanping' :'icon-quanping']"/>
    </div>
    <div id="map">
      <div
        id="container"
        v-loading="mapLoading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      />
    </div>
    <div class="info-container">
      <div class="info-table">
        <div class="top-info">
          <div class="info-item">
            <div class="satellite-title">
              <span>在线终端数量</span>
              <div class="info-value green-color">{{ online }}</div>
            </div>
          </div>
          <div class="info-item">
            <div class="satellite-title">
              <span>终端总量</span>
              <div class="info-value">{{ total }}</div>
            </div>
          </div>
        </div>
        <div class="table-info">
          <el-table
            ref="tableBD"
            :header-cell-style="{'text-align':'center'}"
            :data="tableDataBD"
            :cell-style="{'text-align':'center'}"
            style="height: 50%;overflow-x:hidden;"
            row-class-name="green-row"
            empty-text=" "
          >
            <!-- 车牌号码 -->
            <el-table-column
              label="赋码编号"
              prop="deviceNum"
              width="160"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              label="定位时间"
              prop="locTime"
              width="100"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                <span class="table-date-td">
                  {{ scope.row.locTime }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="终端类型"
              prop="terminalType"
              width="100"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              label="所属机构"
              prop="deptName"
              show-overflow-tooltip
              :resizable="false"
            />
<!--            <el-table-column-->
<!--              label="识别结果"-->
<!--              width="130px"-->
<!--              prop="checkResName"-->
<!--              show-overflow-tooltip-->
<!--            />-->
            <el-table-column
              label="结果描述"
              width="100"
              prop="checkResMessage"
              :resizable="false"
            >
              <template slot-scope="{ row }">
                <el-tooltip
                  v-if="row.checkResMessage"
                  :content="row.checkResMessage"
                  placement="bottom-end"
                >
                  <i class="el-icon-document"/>
                </el-tooltip>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            ref="tableNoBD"
            :header-cell-style="{'text-align':'center'}"
            :data="tableDataNoBD"
            :cell-style="{'text-align':'center'}"
            style="height: 50%;overflow-x:hidden;"
            row-class-name="red-row"
            empty-text=" "
          >
            <!-- 车牌号码 -->
            <el-table-column
              label="赋码编号"
              prop="deviceNum"
              width="160"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              label="定位时间"
              prop="locTime"
              width="100"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                <span class="table-date-td">
                  {{ scope.row.locTime }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="终端类型"
              width="100"
              prop="terminalType"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              label="所属机构"
              prop="deptName"
              width="100"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              label="识别结果"
              prop="checkResName"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              label="结果描述"
              width="100"
              prop="checkResMessage"
              :resizable="false"
            >
              <template slot-scope="{ row }">
                <el-tooltip
                  v-if="row.checkResMessage"
                  :content="row.checkResMessage"
                  placement="bottom-end"
                >
                  <i class="el-icon-document"/>
                </el-tooltip>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>


import AMapUtil from '@/components/map/AMapUtil';
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';
import { getSystemParam } from '@/api/user';
import { getAuthCodeForBD, terminalNumInfoNew } from '@/api/center/beidouVerification';

export default {
  name: 'BDTerminalIdentify',
  data() {
    return {
      tableDataBD: [],
      tableDataNoBD: [],
      AMap: null, // 高德地图对象
      map: null, // 本页面地图实例
      checkResObj: {
        0: '疑似非单北斗定位',
        1: '北斗定位'
      },
      online: 0, // 在线终端数量
      total: 0, // 全部终端数量
      mapLoading: false,
      fullscreen: false,
      websock: null,
      intervalTimer: null
    };
  },
  created() {
    this.getTerminalNum();
  },
  activated() {
    this.initWebSocket();
    this.intervalTimer = setInterval(() => {
      this.getTerminalNum();
    }, 10000);
    this.mapReload();
  },
  deactivated(){
    clearInterval(this.intervalTimer);
  },
  beforeDestroy(){
    clearInterval(this.intervalTimer);
    this.closeWebsocket();
  },
  mounted() {
    this.mapLoading = true;
    this.$nextTick(() => {
      this.initMap();
      window.addEventListener('fullscreenchange', this.fullscreenchange);
    });
  },
  methods: {
    mapReload () {
      let canvas = document.getElementsByClassName('amap-layer')[0];
      if (canvas) {
        console.log(canvas); // 打印绘制的canvas
        let canvasContent = canvas.getContext('webgl'); // 因为高德地图是通过webGl来绘制的，所以通过getContext(‘webgl’)才能获取到内容
        console.log(canvasContent);
        if (canvasContent?.drawingBufferHeight < 10 && canvasContent?.drawingBufferWidth < 10) {
          this.initMap();
        }
      }
    },
    fullscreenchange(){
      this.fullscreen = document.fullscreenElement;
    },
    getTerminalNum() {
      terminalNumInfoNew().then(res => {
        this.online = res.data.terminalOnlineCount || 0;
        this.total = res.data.allTerminalCount || 0;
      });
    },
    initMap() {
      AMapUtil.loadAMap(AMap => {
        console.log('-> 初始化地图成功');
        this.AMap = AMap;
        this.$nextTick(() => {
          this.map = new AMap.Map('container', {
            //设置地图容器id
            viewMode: '3D', //是否为3D地图模式
            zoom: 4.1, //初始化地图级别
            center: [
              103.552500,
              34.322700
            ], //初始化地图中心点位置
            zoomEnable: false, // 地图是否可缩放
            doubleClickZoom: false, // 地图是否可通过双击鼠标放大地图
            keyboardEnable: false, // 地图是否可通过键盘控制,默认为true
            dragEnable: false, // 地图是否可通过鼠标拖拽平移
            features: [
              'bg',
              'point',
              'road'
            ],
            mapStyle: 'amap://styles/darkblue'
          });
          this.mapLoading = false;
        });
      });
    },
    addRow(newRow) {
      this.addMapMarker(newRow);
      this.setTableData(newRow);
    },
    setTableData(newRow) {
      let tbody = null;
      if (newRow.checkRes === 1) {
        this.tableDataBD.unshift(newRow);
        tbody = this.$refs.tableBD.$el.querySelector('.el-table__body-wrapper tbody');
      }
      else {
        this.tableDataNoBD.unshift(newRow);
        tbody = this.$refs.tableNoBD.$el.querySelector('.el-table__body-wrapper tbody');
      }
      if (this.tableDataBD.length > 500) {
        this.tableDataBD = this.tableDataBD.slice(0, 400);
      }
      if (this.tableDataNoBD.length > 500) {
        this.tableDataNoBD = this.tableDataNoBD.slice(0, 400);
      }
      this.$nextTick(() => {
        const rows = tbody.querySelectorAll('tr');
        const firstRow = rows[0];
        firstRow.style.animation = 'fadeInLeft 0.8s ease-in-out';
        firstRow.addEventListener('animationend', () => {
          firstRow.style.animation = '';
        });
      });
    },
    addMapMarker(newRow) {
      if(!this.AMap) {
        return false;
      }
      const typeColor = newRow.checkRes === 1 ? 'marker-green' : '';
      const markerObj = new this.AMap.Marker({
        position: [
          newRow.longitude,
          newRow.latitude
        ],
        content: `<div class="marker-item ${typeColor}"></div>`
      });
      this.map.add([markerObj]);
      setTimeout(() => {
        this.map.remove(markerObj);
      }, 500);
    },
    initWebSocket() {
      this.closeWebsocket();
      getAuthCodeForBD().then(res => {
        const socketCode = res.data;
        getSystemParam('websocketBdCheck').then(res => {
          const wsLocation = res.data.data;
          const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
          const wsUrl = `${protocol}${wsLocation}/ws/bdCheck/pushAll/${socketCode}`;
          this.websock = new ReconnectingWebSocket(wsUrl);
          this.websock.onopen = () => {
            console.log('成功');
          };
          this.websock.onmessage = (e) => {
            const data = JSON.parse(e.data);
            const locationData = this.$utils.wgs84togcj02(data.longitude, data.latitude);
            console.log('-> 北斗识别全部终端接收数据', data);
            const newRow = {
              deviceNum: data.deviceNum, //赋码编号
              locTime: data.locTime.split(' ')[1], //定位时间
              terminalType: data.terminalType, //终端类型
              deptName: data.deptName, //所属机构
              checkRes: Number(data.checkRes), //1:北斗定位   0:疑似非北斗定位
              checkResName: this.checkResObj[data.checkRes], //1:北斗定位   0:疑似非北斗定位
              longitude: locationData[0], //经度
              latitude: locationData[1], //纬度
              checkResMessage: data.checkResMessage //结果描述
            };
            this.addRow(newRow);
          };
          this.websock.onerror = () => {
            console.log('北斗识别全部终端数据传输已断开, 正在尝试重新连接');
          };
          this.$once('hook:deactivated', () => {
            this.closeWebsocket();
          });
        });
      });
    },
    closeWebsocket() {
      this.websock?.close();
    },
    full() {
      let element = document.getElementById('BDTerminalIdentify');
      if (this.fullscreen) {
        // 关闭全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
        else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        }
        else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        }
        else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
      else {
        // 全屏
        if (element.requestFullscreen) {
          element.requestFullscreen();
        }
        else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        }
        else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        }
        else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.fullscreen = !this.fullscreen;

    }
  }
};
</script>

<style lang="less" scoped>
@topInfoHeight: 60px;
@satelliteTitleHeight: 40px;
#BDTerminalIdentify {
  background: url(../../../assets/images/beidouVerification/bg.jpg) no-repeat;
  background-size: 100% 100%;
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
}

#map {
  width: calc(100% - 42vw);
  height: 100%;
}

#container {
  height: 100%;
}

.info-container {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 42vw;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
}
.page-title {
  z-index: 9999;
  font-size: 32px;
  height: 40px;
  line-height: 40px;
  padding-left: 8px;
  flex-shrink: 0;
  position: absolute;
  left: 20px;
  top: 10px;
  color: #f0f8ff;
}
.info-table {
  height: 100%;
  color: #f0f8ff;
  padding: 6px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 10px 5px #00ffff;

  .satellite-title {
    margin-left: 2px;
    background: url(../../../assets/images/beidouVerification/title-bg.png) no-repeat;
    background-size: 100% 100%;
    padding: 6px 0 6px 28px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }

  .top-info {
    display: flex;
    height: @topInfoHeight;
    align-items: center;
    flex-shrink: 0;

    .satellite-title {
      width: 100%;
      height: @satelliteTitleHeight;
    }
  }
}


.table-info {
  height: calc(100% - @topInfoHeight);
}

.info-item {
  display: flex;
  flex: 1;
  margin-right: 20px;
}

.info-value {
  font-size: 28px;
  color: #ffff00;
  margin-left: 20px;
  position: relative;
  top: -3px;
}

.green-color {
  color: #00ff00;
}

::v-deep .el-table,
.el-table__expanded-cell {
  background-color: transparent;
}

::v-deep .el-table {
  .el-table__header-wrapper {
    tr {
      background-color: transparent !important;
      height: 30px;
    }
  }
}

::v-deep .el-table__body td, ::v-deep .el-table__header th,
.el-table .cell {
  background-color: transparent !important;
}

::v-deep .el-table::before {
  //去除底部白线
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0;
}

//th的样式
::v-deep .el-table__header th {
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}

::v-deep .el-table td.el-table__cell {
  border-bottom: none;
  height: 30px !important;
  font-size: 14px;
  color: #ffffff;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0 !important;
}

::v-deep .green-row {
  background-color: rgba(221, 255, 0, 0.35) !important;
}

::v-deep .red-row {
  background-color: rgba(255, 0, 0, 0.35);
}

::v-deep .marker-item {
  width: 12px;
  height: 12px;
  background-color: #fb4242;
  border-radius: 50%;
  animation: showHideMarker 0.5s;
}

::v-deep .marker-green {
  background-color: #00ff00;
}

@keyframes showHideMarker {
  0% {
    opacity: 0.5;
    transform: scale(0.9, 0.9);
  }
  80% {
    opacity: 1;
    transform: scale(1.2, 1.2);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.7, 0.7);
  }
}
@keyframes fadeInLeft {
  0% {
    opacity: 0.5;
    transform: translate3d(-100%, 0, 0)
  }
  to {
    opacity: 1;
    transform: none
  }
}
.fullscreen-btn {
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 22px;
  right: 20px;
  color: #fff;
  font-size: 20px;
  z-index: 999;
}
</style>
