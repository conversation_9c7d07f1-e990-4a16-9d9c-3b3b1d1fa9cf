<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="90px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            label="操作"
            width="120"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('companyName')"
            label="企业名称"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="companyName"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            label="所属机构"
            :show-overflow-tooltip="true"
            min-width="120"
            prop="deptName"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('address')"
            label="地址"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="address"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('contactPerson')"
            label="联系人"
            :show-overflow-tooltip="true"
            min-width="80"
            prop="contactPerson"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('contactPhone')"
            label="联系电话"
            :show-overflow-tooltip="true"
            min-width="100"
            prop="contactPhone"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :depts="depts"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
    </div>
  </basic-container>
</template>

<script>
import crudCompany from '@/api/bdTest/company';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import HeadCommon from '@/components/formHead/headCommon.vue';
import pagination from '@/components/Crud/Pagination';

// crud交由presenter持有
const crud = CRUD({
  title: '委托企业管理',
  crudMethod: { ...crudCompany }
});

export default {
  name: 'Company',
  components: {
    eForm,
    crudOperation,
    udOperation,
    HeadCommon,
    pagination
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [],
  data() {
    return {
      permission: {
        add: [
          'admin',
          'testCompany:add'
        ],
        edit: [
          'admin',
          'testCompany:edit'
        ],
        del: [
          'admin',
          'testCompany:del'
        ]
      },
      headConfig: {
        item: {
          1: {
            name: '企业名称',
            type: 'input',
            value: 'companyName',
            span: 6
          },
          3: {
            name: '所属机构',
            type: 'extra',
            value: 'deptId',
            span: 6
          }
        },
        button: {}
      },
      btnShow: true, // 显示确认取消按钮
      depts: []
    };
  },
  created() {
  },
  beforeDestroy() {
    this.crud.resetQuery(false);
  },
  methods: {
    /** 刷新 - 之后 */
    [CRUD.HOOK.afterRefresh]() {
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh]() {
      if (this.crud.query.ids) {
        this.crud.query.ids = null;
      }
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    toDetails(param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel() {
      this.btnShow = true;
      console.log(crud.form);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.xh-container ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1;
}
</style>
