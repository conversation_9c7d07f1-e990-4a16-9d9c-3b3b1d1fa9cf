<template>
  <div
    class="satellite"
    :class="[isSelect ? 'select-style' : '']"
    @click="selectSatellite"
  >
    <!--GEO 红色-->
    <div
      v-if="type === 'GEO'"
      class="satellite_icon satellite_a"
    />
    <!--MEO 黄色-->
    <div
      v-if="type === 'MEO'"
      class="satellite_icon satellite_b"
    />
    <!--IGSO 绿色-->
    <div
      v-if="type === 'IGSO'"
      class="satellite_icon satellite_c"
    />
    <div
      v-if="flag === 0"
      class="satellite_name text_visible"
    >
      {{ id }}
    </div>
    <div
      v-if="flag === 1"
      class="satellite_name text_position"
    >
      {{ id }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'SatelliteIcon',
  components: {},
  props: {
    id: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: ''
    },
    flag: {
      type: Number,
      default: 0
    },
    selectPickId: {
      type: String,
      default: null
    }
  },
  data() {
    return {};
  },
  computed: {
    isSelect() {
      return this.selectPickId ? this.selectPickId.split('/')[1] === this.id : false;
    }
  },
  mounted() {
  },
  methods: {
    selectSatellite() {
      if (this.isSelect) {
        this.$emit('selectSatellite', null);
      }
      else {
        this.$emit('selectSatellite', this.id);
      }
    }
  }
};
</script>

<style scoped>
.satellite {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
  justify-content: center;
  align-items: center;
  margin-bottom: 4px;
  margin-top: 4px;
  padding: 4px;
  box-sizing: border-box;
}

.satellite_icon {
  width: 35px;
  height: 35px;
}

.satellite_a {
  background: url(../../../../assets/images/beidouVerification/s-icon-geo.png) no-repeat;
  background-size: 100% 100%;
}

.satellite_b {
  background: url(../../../../assets/images/beidouVerification/s-icon-meo.png) no-repeat;
  background-size: 100% 100%;
}

.satellite_c {
  background: url(../../../../assets/images/beidouVerification/s-icon-igso.png) no-repeat;
  background-size: 100% 100%;
}

.satellite_name {
  font-size: 14px;
}

.text_visible {
  color: #ffffff;
  text-shadow: -.5px -.5px 0 #fad900,
  .5px -.5px 0 #fad900,
  -.5px .5px 0 #fad900,
  .5px .5px 0 #fad900;;
}

.text_position {
  color: #ffffff;
  text-shadow: -.5px -.5px 0 #05daa1,
  .5px -.5px 0 #05daa1,
  -.5px .5px 0 #05daa1,
  .5px .5px 0 #05daa1;
}

.select-style {
  border: 2px solid #22ee22;
  padding: 2px;
}
</style>
