<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="crud.status.title"
    append-to-body
    width="60%"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="130px"
    >
      <el-row
        type="flex"
        span="24"
      >

        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 所属机构 -->
          <el-form-item
            :label="getLabel('conpany')"
            prop="conpany"
          >
            <el-input
              v-model.trim="form.conpany"
              onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
              :placeholder="getPlaceholder('conpany')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- imsi -->
          <el-form-item
            :label="getLabel('bdCard')"
            prop="imbdCardsi"
          >
            <el-input
              v-model.trim="form.bdCard"
              :placeholder="getPlaceholder('bdCard')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 实名登记用户 -->
          <el-form-item
            :label="getLabel('user')"
            prop="user"
          >
            <el-input
              v-model.trim="form.user"
              :placeholder="getPlaceholder('user')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 短报文序列号 -->
          <el-form-item
            :label="getLabel('messageTerminalId')"
            prop="messageTerminalId"
          >
            <el-input
              v-model.trim="form.messageTerminalId"
              :placeholder="getPlaceholder('messageTerminalId')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 发卡日期 -->
          <el-form-item
            :label="getLabel('startDate')"
            prop="startDate"
          >
            <el-date-picker
              v-model="form.startDate"
              type="date"
              :placeholder="getPlaceholder('startDate')"
              @change="handleDateChange('startDate')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 到期时间 -->
          <el-form-item
            :label="getLabel('endDate')"
            prop="endDate"
          >
            <el-date-picker
              v-model="form.endDate"
              type="date"
              :placeholder="getPlaceholder('endDate')"
              @change="handleDateChange('endDate')"
            />
          </el-form-item>
        </div>

      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import DateStringPicker from '@/components/select/DateStringPicker/DateStringPicker';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  simId: null,
  iccid: null,
  imsi: null,
  customer: null,
  operator: null,
  cardState: null,
  releaseDate: null,
  activeDate: null,
  endDate: null,
  package: null,
  packageTotal: null,
  cardType: null
};
export default {
  components: { DateStringPicker },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      rules: {
        simId: { required: true, message: '请输入sim卡号', trigger: 'blur' }, // sim卡号
        iccid: { required: true, message: '请输入iccid', trigger: 'blur' }, // iccid
        cardType: { required: true, message: '请选择卡类型', trigger: 'change' }, // 卡类型
        endDate: { required: true, message: '请选择服务期止', trigger: 'change' }, // 服务期止
      }
    };
  },
  methods: {
    // 相差8小时, 后台存储不会加上8小时, 直接往数据库存储, 因此前端自行加上8小时
    handleDateChange(time) {
      this.form[time] = new Date(new Date(this.form[time]).getTime() + 8 * 60 * 60 * 1000);
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$nextTick(()=>{
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('SimCardManage', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('SimCardManage', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
