<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <!-- 车牌 -->
    <div class="xh-page-searchItem-content">
      <el-input
        v-model="query.licencePlate"
        :placeholder="getPlaceholder('licencePlate')"
        clearable
        size="small"
        @keyup.enter.native="crud.toQuery"
      />
    </div>
    <div class="xh-page-searchItem-content">
      <xh-select
        v-model="query.licenceColor"
        :placeholder="getPlaceholder('licenceColor')"
        clearable
        size="small"
      >
        <el-option
          v-for="item in dict.dict.vehicleColor"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </xh-select>
    </div>
    <!-- 车组 -->
    <div class="xh-page-searchItem-content-auto">
      <DeptSingleSelect
        ref="deptId"
        v-model="query.deptId"
      />
    </div>
    <!-- 车辆类型 -->
    <div class="xh-page-searchItem-content">
      <Treeselect
        v-model="query.vehicleModel"
        :options="vehicleModel"
        :normalizer="normalizerVehicleModel"
        :placeholder="getPlaceholder('vehicleModel')"
        class="tree-select"
      />
    </div>
    <rrOperation
      :crud="crud"
      @handleClear="handleClear"
    >
      <el-tooltip
        slot="center"
        content="更多筛选项"
        placement="top"
      >
        <el-button
          type="primary"
          icon="el-icon-arrow-down"
          size="mini"
          style="margin-left: 0"
          @click="moreSearch=!moreSearch"
        />
      </el-tooltip>
    </rrOperation>

    <div
      v-show="moreSearch"
      class="moreSearch"
    >
      <!-- 序列号 -->
      <div class="xh-page-searchItem-content">
        <el-input
          v-model="query.terminalId"
          :placeholder="getPlaceholder('terminalId')"
          clearable
          size="small"
          onkeyup="value=value.replace(/[^\d]/g,'')"
          @keyup.enter.native="crud.toQuery"
        />
      </div>
      <!-- SIM卡号 -->
      <div class="xh-page-searchItem-content">
        <el-input
          v-model="query.simId"
          :placeholder="getPlaceholder('simId')"
          clearable
          size="small"
          onkeyup="value=value.replace(/[^\d]/g,'')"
          @keyup.enter.native="crud.toQuery"
        />
      </div>
      <!-- 车辆归属 -->
      <div class="xh-page-searchItem-content">
        <xh-select
          v-model="query.vehicleOwnerId"
          :placeholder="getPlaceholder('vehicleOwnerId')"
          clearable
          size="small"
        >
          <el-option
            v-for="item in vehicleOwnerId"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </div>
    </div>
  </div>
</template>

<script>
import { header } from '@/components/Crud/crud';
import rrOperation from '@/components/Crud/RR.operation';
import getPlaceholder from '@/utils/getPlaceholder';
import DeptSingleSelect from '@/components/select/DeptSingleSelect/DeptSingleSelectNoDefault';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
  components: {rrOperation, DeptSingleSelect, Treeselect},
  mixins: [header()],
  props: {
    dict: {
      type: Object,
      required: true
    },
    permission: {
      type: Object,
      required: true
    },
    vehicleOwnerId: {
      type: Array,
      required: true
    },
    vehicleModel: {
      type: Array,
      required: true
    },
    districtTreeData: {
      type: Array,
      required: true
    },
    depts: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      normalizerVehicleModel (node) {
        console.log(node);
        return {
          id: node.dictCode,
          label: node.dictName,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      moreSearch: false
    };
  },

  methods: {

    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Vehicle', value);
    },
    handleClear () {
      this.$refs.deptId.selectedDeptId = [];
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .vue-treeselect__control{
  height: 32px;
}
.xh-page-searchItem-content{
  height: 13px;
}
.moreSearch{
  height: 40px;
}
</style>
