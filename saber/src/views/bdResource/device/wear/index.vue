<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="batchPer.imp"
              class="filter-item"
              icon="el-icon-upload2"
              size="small"
              @click="batchvisible = true"
            >
              导入
            </el-button>

            <el-button
              v-permission="batchPer.exp"
              :loading="crud.downloadLoading"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="handleExport"
            >
              导 出
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','wear:edit','wear:del']"
            width="158"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-permission="permission.change"
                    type="text"
                    size="small"
                    :disabled="scope.row.specificity !== '2' || scope.row.iotProtocol !== 1 || scope.row.targetType === 0"
                    @click="toChange(scope.row)"
                  >
                    变更
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 终端类型 -->
          <el-table-column
            v-if="columns.visible('categoryName')"
            :label="getLabel('category')"
            prop="categoryName"
            min-width="220"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 厂商名称 -->
          <el-table-column
            v-if="columns.visible('vendorName')"
            :label="getLabel('vendor')"
            prop="vendorName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />

          <!-- 终端型号 -->
          <el-table-column
            v-if="columns.visible('model')"
            :label="getLabel('model')"
            prop="model"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />

          <!-- 终端名称 -->
          <el-table-column
            v-if="columns.visible('deviceName')"
            :label="getLabel('deviceName')"
            prop="deviceName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel("bdmDeviceModel", scope.row.model) }}
              </span>
            </template>
          </el-table-column>

          <!-- 序列号 -->
          <el-table-column
            v-if="columns.visible('uniqueId')"
            :label="getLabel('uniqueId')"
            prop="uniqueId"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 设备 -->
          <el-table-column
            v-if="columns.visible('specificityName')"
            :label="getLabel('specificity')"
            prop="specificityName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 定位模式 -->
          <el-table-column
            v-if="columns.visible('gnssModeName')"
            :label="getLabel('gnssMode')"
            prop="gnssModeName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deviceNum')"
            :label="getLabel('deviceNum')"
            prop="deviceNum"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('terminalId')"
            :label="getLabel('terminalId')"
            prop="terminalId"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- IMEI -->
          <el-table-column
            v-if="columns.visible('imei')"
            :label="getLabel('imei')"
            prop="imei"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />

          <!-- 物联网卡 -->
          <el-table-column
            v-if="columns.visible('numbers')"
            :label="getLabel('numbers')"
            prop="numbers"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />

          <!-- 北斗芯片序列号 -->
          <el-table-column
            v-if="columns.visible('bdChipSn')"
            :label="getLabel('bdChipSn')"
            prop="bdChipSn"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 所属机构 -->
          <el-table-column
            v-if="columns.visible('deptName')"
            label="使用单位"
            prop="deptName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 视频设备编号 -->
          <el-table-column
            v-if="columns.visible('channelIds')"
            label="视频设备编号"
            prop="channelIds"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 视频通道数 -->
          <el-table-column
            v-if="columns.visible('channelNum')"
            label="视频通道数"
            prop="channelNum"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 资产类型 -->
          <el-table-column
            v-if="columns.visible('assetType')"
            :label="getLabel('assetType')"
            prop="assetType"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel("assetType", scope.row.assetType) }}
              </span>
            </template>
          </el-table-column>
          <!-- 归属单位类型 -->
          <el-table-column
            v-if="columns.visible('ownDeptType')"
            :label="getLabel('ownDeptType')"
            prop="ownDeptType"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel("ownDeptType", scope.row.ownDeptType) }}
              </span>
            </template>
          </el-table-column>
          <!-- 归属单位名称 -->
          <el-table-column
            v-if="columns.visible('ownDeptName')"
            :label="getLabel('ownDeptName')"
            prop="ownDeptName"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 卡套餐 -->
          <!-- <el-table-column
            v-if="columns.visible('dataPlan')"
            :label="getLabel('dataPlan')"
            prop="dataPlan"
            min-width="100"
            show-overflow-tooltip
          >
          <template slot-scope="scope">
              <div class="xh-table-cell-same-line">
                {{ getEnumDictLabel("bdmIotcardDataPlan", scope.row.dataPlan) }}
              </div>
            </template>
        </el-table-column>  -->

          <!-- 卡类型 -->
          <!-- <el-table-column
                  v-if="columns.visible('cardCategory')"
                  :label="getLabel('cardCategory')"
                  prop="cardCategory"
                  min-width="100"
                  show-overflow-tooltip
                >
                <template slot-scope="scope">
                    <div class="xh-table-cell-same-line">
                      {{ getEnumDictLabel("bdmIotcardCategory", scope.row.cardCategory) }}
                    </div>
                  </template>
              </el-table-column>  -->
          <!-- 到期时间 -->
          <!-- <el-table-column
            v-if="columns.visible('expire')"
            :label="getLabel('expire')"
            prop="expire"
            min-width="100"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.expire }}
              </span>
            </template>
          </el-table-column> -->

          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
      <!--表单渲染-->
      <eForm
        ref="from"
        :dict="dict"
        :regTurn="regTurn"
        :regTurnM="regTurnM"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
      <BatchImport
        :visible="batchvisible"
        mod="wear"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
      <AlterationForm
        ref="alterationForm"
        :dict="dict"
        :dialog-visible.sync="dialogAlterationVisible"
        :alteration-data="alterationData"
        @refresh="crud.refresh"
      />
    </div>
  </basic-container>
</template>

<script>
import crudHandel from '@/api/bdResource/wear';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from '@/components/importErr';
import { getDeptPerInit } from '@/api/base/dept';
import { deviceType } from '@/api/base/driver.js';
import { pagination as getList } from '@/api/bdTest/manuFactor';
import AlterationForm from '@/components/alterationForm/index.vue';
// crud交由presenter持有
const crud = CRUD({
  title: getLabel('deviceHandel', 'uniName'), // 车辆类型
  crudMethod: { ...crudHandel }
});

export default {
  name: 'DeviceHandel',
  components: {
    eForm,
    crudOperation,
    udOperation,
    pagination,
    HeadCommon,
    BatchImport,
    msgDialog,
    AlterationForm
  },
  mixins: [
    presenter(crud),
    header()
  ],
  // 数据字典
  dicts: [
    'terminalType',
    'terminalFunctionType',
    'localStandards',
    'licenceColor',
    'bdmDeviceType',
    'bdmType',
    'bdmStatus',
    'bdmIotcardDataPlan',
    'bdmWearCategory',
    'bdmTerminalApp',
    'bdmAppSense',
    'bdmGnssMode',
    'targetModel',
    'assetType',
    'ownDeptType',
    'bdmDeviceModel'
  ],
  data() {
    this.installdateReg = {
      installdate: /^(\d+)-(\d+)-(\d+)/
    };
    this.regTurn = {
      imei: /^\d+$/,
      bdChipSn: /^[a-zA-z0-9]+$/,
      uniqueId: /^[a-zA-z0-9]+$/,
      numbers: /^[a-zA-z0-9]+$/,
      model: /^[^\u4E00-\u9FA5]+$/
    };
    this.regTurnM = {
      bdChipSn: '北斗芯片序列号只能输入数字跟字母',
      model: '终端型号不允许输入中文',
      imei: 'IMEI只允许输入数字',
      uniqueId: '序列号只能输入数字跟字母',
      numbers: '物联网卡只能输入数字跟字母'
    };
    return {
      batchPer: {
        add: [
          'admin',
          'wear:add'
        ],
        imp: [
          'admin',
          'wear:imp'
        ],
        exp: [
          'admin',
          'wear:exp'
        ],
        del: [
          'admin',
          'wear:del'
        ]
      },
      permission: {
        add: [
          'admin',
          'wear:add'
        ],
        edit: [
          'admin',
          'wear:edit'
        ],
        del: [
          'admin',
          'wear:del'
        ],
        change: [
          'admin',
          'wear:change'
        ]
      },
      headConfig: {
        item: {
          1: {
            name: '终端类型',
            type: 'select',
            value: 'category',
            dictOptions: 'bdmWearCategory'
          },
          2: {
            name: '序列号',
            type: 'input',
            value: 'uniqueId'
          },
          3: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum'
          },
          4: {
            name: 'IMEI',
            type: 'input',
            value: 'imei'
          },
          5: {
            name: '物联网卡号',
            type: 'input',
            value: 'number'
          },
          6: {
            name: '设备',
            type: 'select',
            value: 'specificity',
            dictOptions: 'bdmType'
          },
          7: {
            name: '使用单位',
            type: 'extra',
            value: 'deptId'
          }
        },
        button: {}
      },
      // 批量引入相关
      batchvisible: false,
      msgData: [], // 批量导入提示消息
      numKey: undefined, // 递归中继值
      dataType: { // excel里文字对应的key
        '序列号': 'uniqueId',
        '终端型号': 'model',
        '所属机构': 'deptId',
        '厂商名称': 'vendor',
        '终端类型': 'category',
        // '所属基础设施': 'infrastructure',
        // '物联网卡号': 'internetCardNumber',
        'IMEI': 'imei',
        '北斗芯片序列号': 'bdChipSn',
        '安装日期': 'installdate',
        '设备': 'specificity',
        '应用场景': 'scenario',
        '应用方向': 'domain',
        '赋码编号': 'deviceNum',
        '物联网卡': 'numbers',
        '定位模式': 'gnssMode',
        '终端编号': 'terminalId',
        '视频通道数': 'channelNum',
        '视频设备编号': 'channelIds',
        '资产类型': 'assetType',
        '归属单位类型': 'ownDeptType',
        '归属单位名称': 'ownDeptName'
      },
      // 必填项
      typeRequired: [
        // 'uniqueId',
        'model',
        'deptId',
        // 'vendor',
        // 'category',
        // 'imei',
        // 'bdChipSn',
        // 'installdate',
        // 'numbers',
        'specificity'
        // 'scenario',
        // 'domain'
      ],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        category: 'bdmWearCategory',
        domain: 'bdmTerminalApp',
        specificity: 'bdmType',
        gnssMode: 'bdmGnssMode',
        model: 'bdmDeviceModel'
      },
      cascade: ['scenario'],
      cascadeDictName: {
        scenario: 'bdmAppSense'
      },//级联对应字典
      tipsKey: [], // 提示点集合
      btnShow: true, // 显示确认取消按钮
      dialogAlterationVisible: false,
      alterationData: {}
    };
  },
  watch: {
    'dict.bdmAppSense'(arr) {
      if (!this.lastBdmAppSense && arr?.length) {
        const list = [];
        const fun = (data) => {
          data.forEach(i => {
            if (i.children?.length) {
              fun(i.children);
            }
            else {
              list.push(i.value);
            }
          });
        };
        fun(arr);
        this.lastBdmAppSense = list;
      }
    },
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          this.dict['deptId'] = data;
        });
      }
    }
  },
  created() {
    this.getManufacturerList();
    deviceType().then(res => {
      res.data['0'].children.forEach(item => {
        if (item.value === '2') {
          this.$set(this.dict.dict, 'bdmWearCategory', item.children);
          this.$set(this.dict, 'bdmWearCategory', item.children);
        }
      });
    });
  },
  methods: {
    toChange (row) {
      this.alterationData = {
        deviceType: row.deviceType,
        deviceId: row.id
      };
      this.dialogAlterationVisible = true;
    },
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    getManufacturerList() {
      getList({
        page: 0,
        size: 9999
      }).then(res => {
        let obj = {};
        const list = res.data.content.map(item => {
          const data = {
            label: item.name,
            value: item.code
          };
          obj[item.code] = data;
          return data;
        });
        this.$set(this.dict, 'vendor', list);
        this.$set(this.dict.dict, 'vendor', obj);
      });
    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData(returnValue) {
      const rData = returnValue.data;
      rData.forEach((cur, index) => {
        let scenario = [];
        Object.keys(cur).forEach(key => {
          if (key.includes('一级')) {
            scenario[0] = cur[key];
            delete cur[key];
          }
          else if (key.includes('二级')) {
            scenario[1] = cur[key];
            delete cur[key];
          }
          else if (key.includes('三级')) {
            scenario[2] = cur[key];
            delete cur[key];
          }
          else if (key.includes('四级')) {
            scenario[3] = cur[key];
            delete cur[key];
          }
        });
        if (scenario.length > 0) {
          cur['应用场景'] = scenario.join('-');
        }
      });
      const data = rData;
      if (!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.typeTreeTurn(obj);
        this.getCascadeKey(obj);
        this.typeRequiredTurn(obj, index);
        this.extraValidate(data, obj, index);
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = [];
            item.forEach(v => {
              errList.push(v);
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      }
      else {
        this.addbatchPost(arr);
        returnValue.close();
      }
    },
    extraValidate(data, obj, index) {
      const map = { ...this.installdateReg, ...this.regTurn };
      const reL = Object.keys(map);
      reL.forEach(key => {
        const reg = map[key];
        if (obj[key] && !reg.test(obj[key])) {
          this.handleErrFun(index, key);
        }
      });
      if (obj.specificity === '2') {
        if (!obj.deviceNum) this.handleErrFun(index, 'deviceNum');
      }
      else if ([
        '1',
        '3'
      ].includes(obj.specificity)) {
        const keys = [
          'uniqueId',
          'category',
          'terminalId'
        ];
        keys.forEach(key => {
          if (!obj[key]) this.handleErrFun(index, key);
        });
      }
      if (obj.scenario?.length && !this.lastBdmAppSense.includes(obj.scenario)) {
        this.handleErrFun(index, 'scenario');
      }
      const rList = [
        'specificity',
        'gnssMode',
        'domain',
        'vendor',
        'category',
        'model'
      ];
      rList.forEach(v => {
        const pL = this.reversalObj[v];
        if (data[pL] && (data[pL] === obj[v] || !obj[v])) {
          this.handleErrFun(index, v);
        }
      });
      const LenObj = {
        deviceNum: 16,
        model: 50,
        imei: 17,
        bdChipSn: 50,
        uniqueId: 50,
        numbers: 50
      };
      Object.keys(LenObj).forEach(key => {
        if (`${obj[key]}`.length > LenObj[key]) {
          this.handleErrFun(index, key);
        }
      });
    },
    handleErrFun(index, v) {
      const pL = this.reversalObj[v];
      if (pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        }
        else if (!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    // 必填项判断
    typeRequiredTurn(obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        }
        else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v);
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn(obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
          else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn(str) {
      let obj = {
        value: 'value',
        label: 'label'
      };
      switch (str) {
      case 'deptId':
        obj.value = 'id';
        obj.label = 'title';
        break;
      default:
        break;
      }
      return obj;
    },
    // 递归找key/id
    treeTurn(tree, word, str, iValue = this.getTreeTurn(str).value, iLabel = this.getTreeTurn(str).label, iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        }
        else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue, iLabel, iChildren);
        }
      });
    },
    // 提交请求
    addbatchPost(arr) {
      crudHandel.addbatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('bdResourceDevice', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('bdResourceDevice', value);
    },
    handleExport() {
      crud.toQuery();
      const ids = crud.selections?.length ? crud.selections.map(item => item.id) : null;
      crud.doExport(ids);
    },
    toDetails(param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel() {
      this.btnShow = true;
    },
    //获取级联项key
    getCascadeKey(obj) {
      this.cascade.forEach(item => {
        let arr = obj[item]?.split?.('-');
        if (arr?.length > 0) this.cascadeRecursion(obj, this.dict[this.cascadeDictName[item]], arr, 0, item);

      });

    },
    //递归
    cascadeRecursion(obj, dict, arr, index, type) {
      let value = arr[index];
      if (value || value === 0) {
        const cur = dict.find(item => `${item.label}`.replace(/\s/, '') === `${value}`.replace(/\s/, ''));
        if (cur) {
          if (cur.children?.length) {
            this.cascadeRecursion(obj, cur.children, arr, index + 1, type);
          }
          else {
            obj[type] = cur.value;
          }
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.xh-container /deep/ .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1;
}
</style>
