<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '电子围栏规则-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="60%"
    top="5vh"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="125px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              maxlength="15"
              show-word-limit
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleType')"
            prop="ruleType"
          >
            <xh-select
              v-model="form.ruleType"
              :placeholder="getPlaceholder('ruleType')"
              clearable
              @input="handleRemark"
            >
              <el-option
                v-for="item in ruleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <DeptFormSingleSelect
              v-if="crud.status.add > 0"
              v-model="form.deptIds"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
            />
            <el-input
              v-else
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="速度阈值"
            prop="limitSpeed"
          >
            <el-input-number
              v-model="form.limitSpeed"
              placeholder="请输入速度阈值(Km/h)"
              maxlength="3"
              :controls="false"
              :min="0"
              :max="160"
              style="width: 100%;"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.ruleType !== '110'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24"
        >
          <el-form-item
            :label=" (form.ruleType === '108' || form.ruleType === '109') ? getLabel('durationType') : getLabel('duration')"
            prop="duration"
          >
            <div class="form-item">
              <xh-select
                v-if="form.ruleType === '108' || form.ruleType === '109'"
                v-model="form.durationType"
                :placeholder="getPlaceholder('durationType')"
              >
                <el-option
                  v-for="item in durationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </xh-select>
              <el-time-picker
                v-model="form.duration"
                :placeholder="getPlaceholder('duration')"
                value-format="HH:mm:ss"
                @input="handleRemark"
              />
            </div>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('regionId')"
            prop="regionId"
          >
            <xh-select
              v-if="!form.isEdit"
              ref="regionSelect"
              v-model="form.regionName"
              :placeholder="getPlaceholder('regionId')"
              clearable
              @clear="handleClear"
              @click.native="clickRegionHandle"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleNodeCheck"
                />
              </el-option>
            </xh-select>
            <el-input
              v-else
              v-model="form.regionName"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isPhoto')"
          >
            <el-radio-group
              v-model="form.isPhoto"
            >
              <el-radio :label="1">
                拍照
              </el-radio>
              <el-radio :label="0">
                不拍照
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoNumber')"
          >
            <el-input
              v-model.number="form.photoNumber"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('photoNumber')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoInterval')"
            prop="photoInterval"
          >
            <el-input
              v-model.number="form.photoInterval"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('photoInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('camera')"
            prop="camera"
          >
            <xh-select
              v-model="form.camera"
              :placeholder="getPlaceholder('camera')"
              clearable
            >
              <el-option
                v-for="item in channelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
              @change="$refs.form.clearValidate('tips')"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
            :rules="form.isAlarm !== 0 ? validate.validateTips : ''"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import { ruledetail, regionFencebyDept } from '@/api/rule';

const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  ruleType: '106',
  limitSpeed: 0,
  durationType: 0,
  duration: '00:10:00',
  regionId: null,
  regionName: '',
  isPhoto: 0,
  startTime: '00:00:00',
  endTime: '23:59:59',
  photoNumber: null,
  photoInterval: null,
  camera: null,
  isAlarm: 1,
  tipsText: '',
  tipsTimes: 1,
  tipsInterval: 5,
  remark: '',
  tips: [2, 3],
  deptIds: '',
};
export default {
  components: { DeptFormSingleSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    }
  },
  data () {
    const validateTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.tipsText === '') {
        callback(new Error('请选择语音提示并输入语音提示内容'));
      } else {
        callback();
      }
    };
    return {
      rules: {
        ruleName: { required: true, message: '请输入规则名称', trigger: 'blur' },
        naruleTypeme: { required: true, message: '请选择规则类型', trigger: 'change' },
        limitSpeed: { required: true, message: '请输入速度阈值', trigger: 'blur' },
        duration: { required: true, message: '请选择持续时长', trigger: 'change' },
        durationType: { required: true, message: '请选择区域逗留时长', trigger: 'change' },
        photoInterval: { required: true, message: '请输入拍照间隔', trigger: 'blur' },
        camera: { required: true, message: '请选择摄像头', trigger: 'change' },
        tipsTimes: { required: true, message: '请输入语音提示总次数', trigger: 'blur' },
        tipsInterval: { required: true, message: '请输入语音提示间隔', trigger: 'blur' },
        deptIds: { required: true, message: '请选择所属机构', trigger: 'change' },
      },
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: node => node.type === 1 && !node.children
      },
      durationOptions: [
        {label: '关闭', value: 0},
        {label: '大于', value: 1},
        {label: '小于', value: 2}
      ],
      ruleOptions: [],
      channelOptions: [],
      validate: {
        validateTips: {required: true, validator: validateTips, trigger: 'blur'}
      },
      interRegionList: []
    };
  },
  watch: {
    dict: {
      handler(){
        const ruleData = this.dict.ruleManage.find((item)=>item.value === 'rule_area');
        this.ruleOptions = ruleData.children;
        const channelData = this.dict.ruleManage.find((item)=>item.value === 'rule_camera');
        this.channelOptions = channelData?.children;
      },
      deep: true,
      immediate: true
    },
    'form.deptIds': {
      handler(val) {
        if (val) {
          this.getRegionList(val);
        }
      }
    }
  },
  methods: {
    getRegionList(val) {
      regionFencebyDept({
        deptId: val,
        types:[1, 2, 3]
      }).then(res => {
        this.interRegionList = res.data;
        this.$nextTick(() => {
          if (this.form.regionId) {
            let list = this.form.regionId.split(',');
            this.$nextTick(()=>{
              this.$refs.tree.setCheckedKeys(list);
            });
          }
        });
      });
    },
    clickRegionHandle() {
      if(!this.form.deptIds && !this.form.deptId){
        this.$message.warning('请先选择所属机构');
        this.$refs.regionSelect.blur();
      }
    },
    handleClear(){
      this.form.regionId = '';
      this.$refs.tree.setCheckedKeys([]);
    },
    handleNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.regionName = list.toString();
      this.form.regionId = ids.toString();
    },
    handleRemark(){
      let data = this.ruleOptions.find((item)=>item.value === this.form.ruleType);
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}, 规则类型：${data?.label}, 速度阈值：${this.form.limitSpeed || 0}(Km/h), 开始时间：${this.form.startTime}，结束时间：${this.form.endTime}`);
    },
    close(){
      this.$refs.tree?.setCheckedKeys([]);
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
      this.$nextTick(() => {
        this.form.deptId = undefined;
      });
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      let data = this.ruleOptions.find((item)=>item.value === this.form.ruleType);
      this.form.remark = `名称：${this.form.ruleName}, 规则类型：${data?.label}, 速度阈值：${this.form.limitSpeed || 0}(Km/h), 开始时间：${this.form.startTime}, 结束时间：${this.form.endTime}`;
      this.$nextTick(() => {
        this.rules.deptIds = this.crud.status.add > 0 ? { required: true, message: '请选择所属机构', trigger: 'change' } : null;
        this.$refs['form'].clearValidate('deptIds');
      });
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      if (!this.form.isEdit) {
        if (this.form.deptId) {
          this.getRegionList(this.form.deptId);
        }
        ruledetail({ruleType: this.form.ruleTypeId, ruleId: this.form.id}).then(res => {
          Object.assign( this.form, res.data);
          if (this.form.regionId) {
            let list = this.form.regionId.split(',');
            this.$nextTick(()=>{
              this.$refs.tree?.setCheckedKeys(list);
            });
          }
        }).catch(err => {
          console.log('获取详情失败', err);
        });
      }

    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      this.form.ruleTypeId = this.ruleTypeId;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('ElectronicFenceRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('ElectronicFenceRule', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .form-item{
    display: flex;
    ::v-deep .el-select{
      width: 30% !important;
    }
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: left;
      // margin-left: 15px;
      left: 15px;
      transform: translate(0, -50%);
      z-index: 3001;
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
