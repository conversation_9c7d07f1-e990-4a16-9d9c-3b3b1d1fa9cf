<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :visible="dialogVisible"
    title="分配对象详情"
    append-to-body
    width="30%"
  >
    <!-- <el-row>
      <el-form
        ref="form"
        :label-width="labelWidth"
      >
        <div class="xh-header-content">
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12 xh-header-content-item">
            <el-form-item label="车牌号码">
              <el-input
                v-model="query.licencePlate"
                clearable
                size="small"
                placeholder="请输入车牌号码"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-md-12 no-print xh-crud-search">
            <el-form-item>
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchClick"
              >查 询
              </el-button>
              <el-button
                class="filter-item clear-item"
                size="small"
                icon="el-icon-refresh-left"
                @click="clearClick"
              >重 置
              </el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-row> -->
    <!--表格渲染-->
    <el-table
      ref="table"
      :data="tableData"
      :cell-style="{'text-align':'center'}"
      height="65vh"
    >
      <el-table-column
        type="index"
        label="#"
      />
      <el-table-column
        label="监控对象"
        prop="targetName"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
        >
        <template slot-scope="scope">
            {{ scope.row.targetName || $utils.emptymap.targetName }}
        </template>
      </el-table-column>
      <el-empty slot="empty" :image="require('@/assets/images/nodata.png')" />
</el-table>
  </el-dialog>
</template>
<script>
export default {
  props:{
    dialogVisible:{
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      labelWidth: '80px',
      query: {
        licencePlate: '',
        ruleTypeId: undefined,
        ruleId: undefined
      },
      tableData: [],
      loading: false
    };
  },
  methods:{
    searchClick(){

    },
    beforeClose(){
      this.$emit('update:dialogVisible', false);
    },
    getData(data){
      this.tableData = data;
    },
    clearClick(){

    }
  }
};
</script>
<style lang="less" scoped>
.xh-crud-search{
    ::v-deep .el-form-item__content{
      margin-left: 0 !important;
    }
}
</style>
