<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '禁行规则' : '禁行规则-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="120px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('limitMode')"
            prop="limitMode"
          >
            <el-radio-group
              v-model="form.limitMode"
            >
              <el-radio :label="0">
                不限
              </el-radio>
              <el-radio :label="1">
                电子围栏内
              </el-radio>
              <el-radio :label="2">
                电子围栏外
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('accCondition')"
            prop="accCondition"
          >
            <el-radio-group
              v-model="form.accCondition"
              @input="handleRemark"
            >
              <el-radio :label="0">
                不限制
              </el-radio>
              <el-radio :label="1">
                限制开
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('limitSpeed')"
            prop="limitSpeed"
          >
            <el-input
              v-model.number="form.limitSpeed"
              :placeholder="getPlaceholder('limitSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <el-input
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div
          v-if="form.limitMode"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('regionId')"
            prop="regionId"
          >
            <el-input
              v-model="form.regionName"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isPhoto')"
          >
            <el-radio-group
              v-model="form.isPhoto"
            >
              <el-radio :label="1">
                拍照
              </el-radio>
              <el-radio :label="0">
                不拍照
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoNumber')"
          >
            <el-input
              v-model.number="form.photoNumber"
              :placeholder="getPlaceholder('photoNumber')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoInterval')"
            prop="photoInterval"
          >
            <el-input
              v-model.number="form.photoInterval"
              :placeholder="getPlaceholder('photoInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('camera')"
            prop="camera"
          >
            <el-input
              v-model.number="form.camera"
              :placeholder="getPlaceholder('camera')"
            >
              <template slot="append">号摄像头</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            label="告警等级"
            prop="levelConfig"
          >
            <div class="main">
              <div class="item_label main_size">
                告警持续时间
              </div>
              <div class="item_label main_parameter">
                告警等级
              </div>
              <div class="item_label">
                  <span
                    class="add_item"
                  >添加</span>
              </div>
            </div>
            <div
              v-for="(item,index) in form.levelConfig"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <div class="main_item_select">
                  <el-input-number
                    v-model="form.levelConfig[index].alarmDuration"
                    placeholder="请输入告警持续时间"
                    :controls="false"
                    style="width: 90%;"
                  ></el-input-number>
                </div>
              </div>
              <div class="main_item">
                <el-select
                  v-model="form.levelConfig[index].alarmLevel"
                  clearable
                  placeholder="请选择告警等级"
                  style="width: 90%;"
                >
                  <el-option
                    v-for="item in dict.dict.alarmLevel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="main_item">
                <div
                  class="reduce_item"
                >
                  <i
                    class="el-icon-remove-outline reduce_icon"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dict: {
      type: Object,
      required: true
    },
  },
  data () {
    return {
      form: {
        id: null,
        ruleName: '',
        limitSpeed: 5,
        limitMode: 0,
        accCondition: 1,
        duration: '00:01:00',
        regionId: null,
        regionName: '',
        isPhoto: 0,
        startTime: '22:00:00',
        endTime: '06:00:00',
        photoNumber: null,
        photoInterval: null,
        camera: null,
        isAlarm: 1,
        tipsText: '',
        tipsTimes: 1,
        tipsInterval: 5,
        remark: '',
        tips: [2, 3],
        formItem: {
          alarmDuration: '',
          alarm_level: ''
        },
        deptIds: []
      }
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '禁行规则') {
          this.form = Object.assign(this.form, newVal);
          this.form.remark = `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，ACC：${this.form.accCondition ? '限制开' : '不限制'}，速度阈值：${this.form.limitSpeed}km/h，持续时长：${this.form.duration}`;
        }
      },
      deep: true
    }
  },
  methods: {
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，ACC：${this.form.accCondition ? '限制开' : '不限制'}，速度阈值：${this.form.limitSpeed}km/h，持续时长：${this.form.duration}`);
    },
    close(){
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('ProhibitRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('ProhibitRule', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
  .main {
    display: flex;
    width: 85%;
    text-align: center;
  }

  .main_item {
    flex: 1;
    height: 45px;
    line-height: 45px;
    border: 1px solid #c1c9da;
  }

  .main_size {
    flex: 1 !important;
  }

  .item_label {
    flex: 1;
    height: 40px;
    line-height: 40px;
    background-color: #e1e5ee;
    border: 1px solid #c1c9da;
  }

  .main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
    width: 100%;
  }

  .add_item {
    border: 1px solid #aebac5;
    padding: 3px 5px;
    background-color: #ffffff;
    margin: 5px;
    cursor: pointer;
  }

  .main_item_input {
    ::v-deep .el-input__inner {
      background-color: #ecf5ff;
      border-color: #d9ecff;
      display: inline-block;
      height: 32px;
      line-height: 30px;
      color: #409eff;
      border-width: 1px;
      border-style: solid;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      white-space: nowrap;
    }
  }
</style>
