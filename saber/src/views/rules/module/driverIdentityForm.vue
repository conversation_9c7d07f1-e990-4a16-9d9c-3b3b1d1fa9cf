<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '驾驶员身份识别' : '驾驶员身份识别-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="140px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('shootingChannel')"
            prop="shootingChannel"
          >
            <el-input
              v-model.number="form.shootingChannel"
              :placeholder="getPlaceholder('shootingChannel')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('identifyPassValue')"
            prop="identifyPassValue"
          >
            <el-input
              v-model.number="form.identifyPassValue"
              :placeholder="getPlaceholder('identifyPassValue')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('retryNumber')"
            prop="retryNumber"
          >
            <el-input
              v-model.number="form.retryNumber"
              :placeholder="getPlaceholder('retryNumber')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('produceType')"
            prop="produceType"
          >
            <xh-select
              v-model="form.produceType"
              :placeholder="getPlaceholder('produceType')"
              @input="handleRemark"
            >
              <el-option
                v-for="item in produceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType === 1"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('accConditions')"
            prop="accConditions"
          >
            <el-radio-group
              v-model="form.accConditions"
            >
              <el-radio :label="1">
                忽略ACC
              </el-radio>
              <el-radio :label="2">
                ACC开
              </el-radio>
              <el-radio :label="3">
                ACC关
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType === 3"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('accTriggerInterval')"
            prop="accTriggerInterval"
          >
            <el-input
              v-model.number="form.accTriggerInterval"
              :placeholder="getPlaceholder('accTriggerInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType !== 3"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('speedThreshold')"
            prop="speedThreshold"
          >
            <el-input
              v-model.number="form.speedThreshold"
              :placeholder="getPlaceholder('speedThreshold')"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType !== 3"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isPhoto')"
          >
            <el-radio-group
              v-model="form.isPhoto"
            >
              <el-radio :label="1">
                拍照
              </el-radio>
              <el-radio :label="0">
                不拍照
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoNumber')"
          >
            <el-input
              v-model.number="form.photoNumber"
              :placeholder="getPlaceholder('photoNumber')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoInterval')"
            prop="photoInterval"
          >
            <el-input
              v-model.number="form.photoInterval"
              :placeholder="getPlaceholder('photoInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('camera')"
            prop="camera"
          >
            <el-input
              v-model.number="form.camera"
              :placeholder="getPlaceholder('camera')"
            >
              <template slot="append">号摄像头</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('rightTips')"
            prop="rightTips"
          >
            <el-checkbox-group v-model="form.rightTips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.rightTipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('rightTipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      produceOptions: [
        {label: 'IC卡登记', value: 1},
        {label: '驾驶员绑定匹配（ACC从开到关）', value: 2},
        {label: 'acc开时间间隔触发', value: 3},
        {label: '速度触发', value: 4},
      ],
      form: {
        id: null,
        ruleTypeId: null,
        ruleName: '',
        shootingChannel: null,
        identifyPassValue: 70,
        retryNumber: 1,
        produceType: 1,
        speedThreshold: null,
        accConditions: 1,
        accTriggerInterval: null,
        duration: '',
        isPhoto: 0,
        startTime: '00:00:00',
        endTime: '23:59:59',
        photoNumber: null,
        photoInterval: null,
        camera: null,
        isAlarm: 1,
        tipsText: '',
        tipsTimes: 1,
        tipsInterval: 5,
        rightTipsText: '',
        remark: '',
        tips: [2, 3],
        rightTips: [2, 3]
      }
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '驾驶员身份识别') {
          this.form = Object.assign(this.form, newVal);
          let data = this.produceOptions.find((item)=> item.value === this.form.produceType);
          this.form.remark = `名称：${this.form.ruleName}，产生类型：${data.label}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}`;
        }
      },
      deep: true
    }
  },
  methods: {
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    handleRemark(){
      let data = this.produceOptions.find((item)=> item.value === this.form.produceType);
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，产生类型：${data.label}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}`);
    },
    close(){
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('DriverIdentity', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('DriverIdentity', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
