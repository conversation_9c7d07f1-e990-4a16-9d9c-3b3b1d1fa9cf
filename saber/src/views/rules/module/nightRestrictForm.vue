<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '夜间限速规则' : '夜间限速规则-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="110px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('minRoadSpeed')"
            prop="minRoadSpeed"
          >
            <el-input
              v-model.number="form.minRoadSpeed"
              :placeholder="getPlaceholder('minRoadSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('minPositionSpeed')"
            prop="minPositionSpeed"
          >
            <el-input
              v-model.number="form.minPositionSpeed"
              :placeholder="getPlaceholder('minPositionSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
            prop="isAlarm"
          >
            <el-radio-group v-model="form.isAlarm">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('percentage')"
            prop="percentage"
          >
            <el-slider
              v-model="form.percentage"
              :format-tooltip="formatTooltip"
              @input="handleRemark"
            />
            <!-- <el-input
              v-model="form.limitSpeed"
              :placeholder="getPlaceholder('limitSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input> -->
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <el-input
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form:{
        id: null,
        ruleName: '',
        startTime: '22:00:00',
        endTime: '06:00:00',
        // limitSpeed: 80,
        isAlarm: 1,
        percentage: 80,
        deptIds: [],
        remark: '',
        duration: '00:10:00',
        minRoadSpeed: 60,
        minPositionSpeed: 60,
        tips: [2, 3],
        tipsText: '',
        tipsTimes: 1,
        tipsInterval: 5,
      }
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '夜间限速规则') {
          this.form = Object.assign(this.form, newVal);
          this.form.remark = `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，持续时间：${this.form.duration}，路网最低速度：${this.form.minRoadSpeed}km/h，定位最低速度：${this.form.minPositionSpeed}km/h，限速百分比：${this.form.percentage}%`;
        }
      },
      deep: true
    }
  },
  methods: {
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    // 格式化滑块的显示值
    formatTooltip (val) {
      return val + '%';
    },
    close(){
      this.$emit('update:dialogVisible', false);
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，持续时间：${this.form.duration}，路网最低速度：${this.form.minRoadSpeed}km/h，定位最低速度：${this.form.minPositionSpeed}km/h，限速百分比：${this.form.percentage}%`);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('NightRestrictRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('NightRestrictRule', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
