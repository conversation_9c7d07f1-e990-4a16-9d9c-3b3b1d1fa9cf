<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :visible="dialogVisible"
    title="关联对象"
    append-to-body
    width="40%"
  >
    <el-row>
      <el-form
        ref="form"
        :label-width="labelWidth"
      >
        <div class="xh-header-content">
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12 xh-header-content-item">
            <el-form-item label="监控对象">
              <el-input
                v-model="query.targetName"
                clearable
                size="small"
                placeholder="请输入监控对象"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-md-12 no-print xh-crud-search">
            <el-form-item>
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchClick"
              >查 询
              </el-button>
              <el-button
                class="filter-item clear-item"
                size="small"
                icon="el-icon-refresh-left"
                @click="clearClick"
              >重 置
              </el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-row>
    <!--表格渲染-->
    <u-table
      ref="table"
      v-loading="loading"
      use-virtual
      :data="tableData"
      :row-height="54"
      :cell-style="{'text-align':'center'}"
      :border="false"
      height="600px"
    >
      <u-table-column
        type="index"
        label="#"
        width="80"
      />
      <u-table-column
        label="所属机构"
        prop="deptName"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      />
      <u-table-column
        label="对象类别"
        prop="targetType"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      >
        <template slot-scope="scope">
          <div>
            {{ getEnumDictLabel('targetType', scope.row.targetType) | nullValueStr }}
          </div>
        </template>
      </u-table-column>
      <u-table-column
        label="监控对象"
        prop="targetName"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
        >
        <template slot-scope="scope">
            {{ scope.row.targetName || $utils.emptymap.targetName }}
        </template>
      </u-table-column>
      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </u-table>
  </el-dialog>
</template>
<script>
import { getRuleVehicle } from '@/api/rule';
export default {
  props:{
    dialogVisible:{
      type: Boolean,
      default: false
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data(){
    return{
      labelWidth: '80px',
      query: {
        targetName: '',
        ruleType: undefined,
        ruleId: undefined
      },
      tableData: [],
      loading: false
    };
  },
  methods:{
    searchClick(){
      this.getData();
    },
    beforeClose(){
      this.$emit('update:dialogVisible', false);
      this.query = {
        targetName: '',
        ruleType: undefined,
        ruleId: undefined
      };
      this.tableData = [];
    },
    getData(data){
      if (data) {
        this.query.ruleType = data.ruleType;
        this.query.ruleId = data.ruleId;
      }
      this.loading = true;
      getRuleVehicle(JSON.parse(JSON.stringify(this.query))).then((res)=>{
        this.loading = false;
        if (res.data.content) {
          this.tableData = res.data.content;
        } else {
          this.tableData = [];
        }
      }).catch((err)=>{
        this.loading = false;
      });
    },
    clearClick(){
      this.query.targetName = null;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    }
  }
};
</script>
<style lang="less" scoped>
.xh-crud-search{
    ::v-deep .el-form-item__content{
      margin-left: 0 !important;
    }
}
</style>
