<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '分段限速规则-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="60%"
    top="5vh"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="140px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              maxlength="15"
              show-word-limit
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('minSpeed')"
            prop="minSpeed"
          >
            <el-input
              v-model.number="form.minSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('minSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('positionMinSpeed')"
            prop="positionMinSpeed"
          >
            <el-input
              v-model.number="form.positionMinSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('positionMinSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startDate')"
            prop="startDate"
          >
            <el-date-picker
              v-model="form.startDate"
              :placeholder="getPlaceholder('startDate')"
              value-format="yyyy-MM-dd"
              type="date"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endDate')"
            prop="endDate"
          >
            <el-date-picker
              v-model="form.endDate"
              type="date"
              :placeholder="getPlaceholder('endDate')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <DeptFormMultiSelect
              v-if="crud.status.add > 0"
              v-model="form.deptIds"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              checkStrictly
            />
            <el-input
              v-else
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <!-- <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="超速比"
            prop="overSpeedRatio"
          >
            <el-input-number
              v-model="form.overSpeedRatio"
              placeholder="请输入超速比"
              :controls="false"
              :min="0"
              :max="100"
              style="width: 100%;"
              @input="handleRemark"
            >
            </el-input-number>
          </el-form-item>
        </div> -->
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            label="告警等级"
            prop="levelConfig"
          >
            <div class="main">
              <div class="item_label main_size">
                超速比
              </div>
              <div class="item_label main_parameter">
                超速时间(分钟)
              </div>
              <div class="item_label main_parameter">
                告警等级
              </div>
              <div class="item_label">
                <span
                  class="add_item"
                  @click="addHandle"
                >添加</span>
              </div>
            </div>
            <div
              v-for="(item,index) in form.levelConfig"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <div class="main_item_select">
                  <el-input-number
                    v-model="form.levelConfig[index].percentage"
                    placeholder="请输入超速比"
                    :controls="false"
                    style="width: 90%;"
                  />
                </div>
              </div>
              <div class="main_item main_parameter">
                <el-input-number
                  v-model="form.levelConfig[index].overSpeedTime"
                  clearable
                  placeholder="请输入超速时间"
                  :controls="false"
                  style="width: 90%;"
                />
              </div>
              <div class="main_item">
                <el-select
                  v-model="form.levelConfig[index].alarmLevel"
                  clearable
                  placeholder="请选择告警等级"
                  style="width: 90%;"
                >
                  <el-option
                    v-for="item in dict.dict.alarmLevel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="main_item">
                <div
                  class="reduce_item"
                >
                  <i
                    class="el-icon-remove-outline reduce_icon"
                    @click="reduceHandle(index)"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24 tips-container">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
            :rules="form.isAlarm !== 0 ? validate.validateTips : ''"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急
              </el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示
              </el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读
              </el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示
              </el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
          <el-select
            v-if="!form.isEdit"
            v-model="tipsTemplate"
            class="tips-select"
            placeholder="选择语音提示内容模板"
            size="small"
            @change="handleTipsTemplate"
          >
            <el-option
              v-for="item in tipsTemplateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import DeptFormMultiSelect from '@/components/select/DeptFormMultiSelect/DeptFormMultiSelect.vue';
import { ruledetail } from '@/api/rule';

const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  isAlarm: 1,
  minSpeed: 60,
  positionMinSpeed: 60,
  startDate: '',
  endDate: '',
  duration: '00:02:00',
  startTime: '00:00:00',
  endTime: '23:59:59',
  deptIds: [],
  // overSpeedRatio: 0,
  // limitRate: 80,
  remark: '',
  tips: [
    2,
    3
  ],
  tipsText: '',
  tipsTimes: 1,
  tipsInterval: 5,
  levelConfig: []
};
export default {
  components: { DeptFormMultiSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    }
  },
  data() {
    const validateTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.tipsText === '') {
        callback(new Error('请选择语音提示并输入语音提示内容'));
      }
      else {
        callback();
      }
    };
    return {
      rules: {
        ruleName: {
          required: true,
          message: '请输入规则名称',
          trigger: 'blur'
        },
        minSpeed: {
          required: true,
          message: '请输入分段限速最低速度',
          trigger: 'blur'
        },
        positionMinSpeed: {
          required: true,
          message: '请输入定位最低速度',
          trigger: 'blur'
        },
        duration: {
          required: true,
          message: '请选择持续时间',
          trigger: 'change'
        },
        startDate: {
          required: true,
          message: '请选择有效开始日期',
          trigger: 'change'
        },
        endDate: {
          required: true,
          message: '请选择有效结束日期',
          trigger: 'change'
        },
        startTime: {
          required: true,
          message: '请选择开始时间',
          trigger: 'change'
        },
        endTime: {
          required: true,
          message: '请选择结束时间',
          trigger: 'change'
        },
        // overSpeedRatio: {
        //   required: true,
        //   message: '请输入超速比',
        //   trigger: 'blur'
        // },
        tipsTimes: {
          required: true,
          message: '请输入语音提示总次数',
          trigger: 'blur'
        },
        tipsInterval: {
          required: true,
          message: '请输入语音提示间隔',
          trigger: 'blur'
        },
        deptIds: { required: true, message: '请选择所属机构', trigger: 'change' },
      },
      validate: {
        validateTips: {
          required: true,
          validator: validateTips,
          trigger: 'blur'
        }
      },
      formItem: {
        percentage: '',
        overSpeedTime: '',
        alarm_level: ''
      },
      tipsTemplateOptions: [
        { label: '您已超速，限速{}，当前速度{}', value: 1 },
        { label: '自定义', value: 2}
      ],
      tipsTemplate: 2
    };
  },
  methods: {
    // 选择语音提示内容模板
    handleTipsTemplate() {
      if (this.tipsTemplate === 1) {
        this.form.tipsText = '您已超速，限速{}，当前速度{}';
        this.$refs.form.validateField('tips');
      } else {
        this.form.tipsText = '';
      }
    },
    // 格式化滑块的显示值
    formatTooltip(val) {
      return val + '%';
    },
    handleRemark() {
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，持续时间：${this.form.duration}，分段限速最低速度：${this.form.minSpeed}km/h，定位最低速度：${this.form.positionMinSpeed}km/h`);
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      if (!this.form.isEdit) {
        ruledetail({ruleType: this.form.ruleTypeId, ruleId: this.form.id}).then(res => {
          Object.assign( this.form, res.data);
        }).catch(err => {
          console.log('获取详情失败', err);
        });
      }
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd]() {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.form.remark = `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，持续时间：${this.form.duration}，分段限速最低速度：${this.form.minSpeed}km/h，定位最低速度：${this.form.positionMinSpeed}km/h`;
      this.$nextTick(() => {
        this.rules.deptIds = this.crud.status.add > 0 ? { required: true, message: '请选择所属机构', trigger: 'change' } : null;
        this.$refs['form'].clearValidate('deptIds');
      });
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit]() {
      this.form.ruleTypeId = this.ruleTypeId;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('SubsectionRestrictRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('SubsectionRestrictRule', value);
    },
    addHandle() {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      this.form.levelConfig.push(obj);
    },
    reduceHandle (index) {
      this.form.levelConfig.splice(index, 1);
    },
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.form-dialog {
  z-index: 3001 !important;
  pointer-events: none;

  ::v-deep .el-dialog {
    pointer-events: auto;
    width: 49% !important;
    float: left;
    // margin-left: 15px;
    left: 15px;
    transform: translate(0, -50%);
    z-index: 3001;

    .el-dialog__headerbtn {
      display: none;
    }
  }
}

.main {
  display: flex;
  width: 85%;
  text-align: center;
}

.main_item {
  flex: 1;
  height: 45px;
  line-height: 45px;
  border: 1px solid #c1c9da;
}

.main_size {
  flex: 1 !important;
}

.item_label {
  flex: 1;
  height: 40px;
  line-height: 40px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}

.main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
  width: 100%;
}

.add_item {
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #ffffff;
  margin: 5px;
  cursor: pointer;
}

.main_item_input {
  ::v-deep .el-input__inner {
    background-color: #ecf5ff;
    border-color: #d9ecff;
    display: inline-block;
    height: 32px;
    line-height: 30px;
    color: #409eff;
    border-width: 1px;
    border-style: solid;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
  }
}
.tips-container {
  position: relative;
}
.tips-select {
  position: absolute;
  top: 0;
  right: 0;
  width: 200px !important;
}
</style>
