<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '超速规则-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="60%"
    top="5vh"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="150px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('regionId')"
            prop="regionId"
          >
            <xh-select
              v-model="form.regionName"
              :placeholder="getPlaceholder('regionId')"
              clearable
              @clear="handleClear"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleNodeCheck"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.regionName"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('limitingMode')"
            prop="limitingMode"
          >
            <el-radio-group
              v-model="form.limitingMode"
            >
              <el-radio :label="1">
                区域内超速告警
              </el-radio>
              <el-radio :label="2">
                区域外超速告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('interRegionId')"
            prop="interRegionId"
          >
            <xh-select
              v-model="form.interRegionName"
              :placeholder="getPlaceholder('interRegionId')"
              clearable
              @clear="handleInterClear"
            >
              <el-option>
                <el-tree
                  ref="interTree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleInterNodeCheck"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isWarn')"
            prop="isWarn"
          >
            <el-radio-group
              v-model="form.isWarn"
              @input="handleRemark"
              @change="handleIsWarn"
            >
              <el-radio :label="1">
                是
              </el-radio>
              <el-radio :label="0">
                否
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('limitSpeed')"
            prop="limitSpeed"
          >
            <el-input
              v-model.number="form.limitSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('limitSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isWarn"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('warnSpeed')"
            prop="warnSpeed"
          >
            <el-input
              v-model.number="form.warnSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('warnSpeed')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isWarn"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('warnAlarmDuration')"
            prop="warnAlarmDuration"
          >
            <el-time-picker
              v-model="form.warnAlarmDuration"
              :placeholder="getPlaceholder('warnAlarmDuration')"
              value-format="HH:mm:ss"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isWarn"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24"
          style="text-align: center;"
        >
          <el-radio-group
            v-model="tab"
          >
            <el-radio-button :label="true">必要设置</el-radio-button>
            <el-radio-button :label="false">预警相关设置</el-radio-button>
          </el-radio-group>
        </div>
        <div v-if="tab">
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('isPhoto')"
            >
              <el-radio-group
                v-model="form.isPhoto"
              >
                <el-radio :label="1">
                  拍照
                </el-radio>
                <el-radio :label="0">
                  不拍照
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.isPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('photoNumber')"
            >
              <el-input
                v-model.number="form.photoNumber"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('photoNumber')"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.isPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('photoInterval')"
              prop="photoInterval"
            >
              <el-input
                v-model.number="form.photoInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('photoInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.isPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('camera')"
              prop="camera"
            >
              <xh-select
                v-model="form.camera"
                :placeholder="getPlaceholder('camera')"
                clearable
              >
                <el-option
                  v-for="item in channelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </xh-select>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('isAlarm')"
            >
              <el-radio-group
                v-model="form.isAlarm"
                @change="$refs.form.clearValidate('tips')"
              >
                <el-radio :label="0">
                  不告警
                </el-radio>
                <el-radio :label="1">
                  仅告警一次
                </el-radio>
                <el-radio :label="2">
                  间隔告警
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.isAlarm === 2"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('intervalDuration')"
              prop="intervalDuration"
            >
              <el-time-picker
                v-model="form.intervalDuration"
                :placeholder="getPlaceholder('intervalDuration')"
                value-format="HH:mm:ss"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
            <el-form-item
              :label="getLabel('tips')"
              prop="tips"
              :rules="form.isAlarm !== 0 ? validate.validateTips : ''"
            >
              <el-checkbox-group v-model="form.tips">
                <el-checkbox
                  :label="1"
                >紧急</el-checkbox>
                <el-checkbox
                  :label="2"
                >显示器显示</el-checkbox>
                <el-checkbox
                  :label="3"
                >TTS播读</el-checkbox>
                <el-checkbox
                  :label="4"
                >广告屏显示</el-checkbox>
              </el-checkbox-group>
              <el-input
                v-model="form.tipsText"
                :autosize="{ minRows: 2, maxRows: 2 }"
                :placeholder="getPlaceholder('tipsText')"
                type="textarea"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.tipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('tipsTimes')"
              prop="tipsTimes"
            >
              <el-input
                v-model.number="form.tipsTimes"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('tipsTimes')"
              >
                <template slot="append">次</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.tipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('tipsInterval')"
              prop="tipsInterval"
            >
              <el-input
                v-model.number="form.tipsInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('tipsInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div v-else>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('warnIsPhoto')"
            >
              <el-radio-group
                v-model="form.warnIsPhoto"
              >
                <el-radio :label="1">
                  拍照
                </el-radio>
                <el-radio :label="0">
                  不拍照
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnPhotoNumber')"
            >
              <el-input
                v-model.number="form.warnPhotoNumber"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnPhotoNumber')"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnPhotoInterval')"
              prop="warnPhotoInterval"
            >
              <el-input
                v-model.number="form.warnPhotoInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnPhotoInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnCamera')"
              prop="warnCamera"
            >
              <xh-select
                v-model="form.warnCamera"
                :placeholder="getPlaceholder('warnCamera')"
                clearable
              >
                <el-option
                  v-for="item in channelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </xh-select>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('warnIsAlarm')"
            >
              <el-radio-group
                v-model="form.warnIsAlarm"
                @change="$refs.form.clearValidate('warnTips')"
              >
                <el-radio :label="0">
                  不告警
                </el-radio>
                <el-radio :label="1">
                  仅告警一次
                </el-radio>
                <el-radio :label="2">
                  间隔告警
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsAlarm === 2"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnAlarmIntervalDuration')"
              prop="warnAlarmIntervalDuration"
            >
              <el-time-picker
                v-model="form.warnAlarmIntervalDuration"
                :placeholder="getPlaceholder('warnAlarmIntervalDuration')"
                value-format="HH:mm:ss"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
            <el-form-item
              :label="getLabel('warnTips')"
              prop="warnTips"
              :rules="form.warnIsAlarm !== 0 ? validate.validateWarnTips : ''"
            >
              <el-checkbox-group v-model="form.warnTips">
                <el-checkbox
                  :label="1"
                >紧急</el-checkbox>
                <el-checkbox
                  :label="2"
                >显示器显示</el-checkbox>
                <el-checkbox
                  :label="3"
                >TTS播读</el-checkbox>
                <el-checkbox
                  :label="4"
                >广告屏显示</el-checkbox>
              </el-checkbox-group>
              <el-input
                v-model="form.warnTipsText"
                :autosize="{ minRows: 2, maxRows: 2 }"
                :placeholder="getPlaceholder('warnTipsText')"
                type="textarea"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.warnTipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnTipsTimes')"
              prop="warnTipsTimes"
            >
              <el-input
                v-model.number="form.warnTipsTimes"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnTipsTimes')"
              >
                <template slot="append">次</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.warnTipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnTipsInterval')"
              prop="warnTipsInterval"
            >
              <el-input
                v-model.number="form.warnTipsInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnTipsInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  limitSpeed: 80,
  duration: '00:10:00',
  warnAlarmDuration: '00:01:00',
  regionId: null,
  regionName: null,
  limitingMode: null,
  interRegionId: null,
  interRegionName: '',
  isWarn: 0,
  startTime: '00:00:00',
  endTime: '23:59:59',
  warnSpeed: null,
  isPhoto: 0,
  photoNumber: null,
  photoInterval: null,
  camera: null,
  isAlarm: 1,
  intervalDuration: '00:03:00',
  tipsText: '',
  tipsTimes: 1,
  tipsInterval: 5,
  warnIsPhoto: 0,
  warnIsAlarm: 1,
  warnPhotoInterval: null,
  warnPhotoNumber: null,
  warnCamera: null,
  warnTipsTimes: 1,
  warnTipsInterval:  5,
  warnTipsText: '',
  remark: '',
  warnAlarmIntervalDuration: '00:03:00',
  tips: [2, 3],
  warnTips: [2, 3]
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    },
    interRegionList: {
      type: Array,
      default: ()=>{return [];}
    }
  },
  data () {
    const validateTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.tipsText === '') {
        callback(new Error('请选择语音提示并输入语音提示内容'));
      } else {
        callback();
      }
    };
    const validateWarnTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.warnTipsText === '') {
        callback(new Error('请选择预警语音提示并输入预警语音提示内容'));
      } else {
        callback();
      }
    };
    return {
      rules: {
        ruleName: { required: true, message: '请输入规则名称', trigger: 'blur' },
        limitSpeed: { required: true, message: '请输入速度阈值', trigger: 'blur' },
        warnAlarmDuration: { required: true, message: '请选择预警持续时长', trigger: 'change' },
        warnSpeed: { required: true, message: '请输入预警值', trigger: 'blur' },
        photoInterval: { required: true, message: '请输入拍照间隔', trigger: 'blur' },
        camera: { required: true, message: '请选择摄像头', trigger: 'change' },
        intervalDuration: { required: true, message: '请输入持续间隔时间', trigger: 'blur' },
        warnPhotoInterval: { required: true, message: '请输入预警拍照间隔', trigger: 'blur' },
        warnCamera: { required: true, message: '请选择预警摄像头', trigger: 'change' },
        warnAlarmIntervalDuration: { required: true, message: '请输入预警持续间隔时间', trigger: 'blur' },
        tipsTimes: { required: true, message: '请输入语音提示总次数', trigger: 'blur' },
        tipsInterval: { required: true, message: '请输入语音提示间隔', trigger: 'blur' },
        warnTipsInterval: { required: true, message: '请输入预警语音提示间隔', trigger: 'blur' },
        warnTipsTimes: { required: true, message: '请输入预警语音提示总次数', trigger: 'blur' }
      },
      tab: true,
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: node => node.type === 1 && !node.children
      },
      channelOptions: [],
      validate: {
        validateTips: {required: true, validator: validateTips, trigger: 'blur'},
        validateWarnTips: {required: true, validator: validateWarnTips, trigger: 'blur'}
      }
    };
  },
  watch: {
    dict: {
      handler(){
        const channelData = this.dict.ruleManage.find((item)=>item.value === 'rule_camera');
        this.channelOptions = channelData?.children;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleIsWarn(val){
      if (!val) {
        this.tab = true;
      }
    },
    handleInterClear(){
      this.form.interRegionId = '';
      this.$refs.interTree.setCheckedKeys([]);
    },
    handleClear(){
      this.form.regionId = '';
      this.$refs.tree.setCheckedKeys([]);
    },
    handleNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.regionName = list.toString();
      this.form.regionId = ids.toString();
      if (this.form.regionName) {
        this.form.limitingMode = 1;
      }else{
        this.form.limitingMode = null;
      }
    },
    handleInterNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.interRegionName = list.toString();
      this.form.interRegionId = ids.toString();
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，速度阈值：${this.form.limitSpeed}km/h，持续时间：${this.form.duration}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，${this.form.isWarn ? '预警' : '不预警'}`);
    },
    close(){
      this.tab = true;
      this.$refs.tree.setCheckedKeys([]);
      this.$refs.interTree.setCheckedKeys([]);
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.form.remark = `名称：${this.form.ruleName}，速度阈值：${this.form.limitSpeed}km/h，持续时间：${this.form.duration}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，${this.form.isWarn ? '预警' : '不预警'}`;
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      if (this.form.interRegionId) {
        let list = this.form.interRegionId.split(',');
        this.$nextTick(()=>{
          this.$refs.interTree.setCheckedKeys(list);
        });
      }
      if (this.form.regionId) {
        let list = this.form.regionId.split(',');
        this.$nextTick(()=>{
          this.$refs.tree.setCheckedKeys(list);
        });
      }
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      this.form.ruleTypeId = this.ruleTypeId;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('OverspeedRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('OverspeedRule', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: left;
      // margin-left: 15px;
      left: 15px;
      transform: translate(0, -50%);
      z-index: 3001;
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
