<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '离线通知规则-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="60%"
    top="5vh"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="120px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('regionId')"
            prop="regionId"
          >
            <xh-select
              v-model="form.regionName"
              :placeholder="getPlaceholder('regionId')"
              clearable
              @clear="handleClear"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleNodeCheck"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.regionName"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('limitMode')"
            prop="limitMode"
          >
            <el-radio-group
              v-model="form.limitMode"
            >
              <el-radio :label="1">
                电子围栏内
              </el-radio>
              <el-radio :label="2">
                电子围栏外
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('acc')"
            prop="acc"
          >
            <el-radio-group
              v-model="form.acc"
            >
              <el-radio :label="1">
                不限制
              </el-radio>
              <el-radio :label="2">
                限制开
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isSpeed')"
            prop="isSpeed"
          >
            <el-radio-group
              v-model="form.isSpeed"
              @input="handleRemark"
            >
              <el-radio :label="0">
                否
              </el-radio>
              <el-radio :label="1">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isSpeed"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('limitSpeed')"
            prop="limitSpeed"
          >
            <el-input
              v-model.number="form.limitSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('limitSpeed')"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleType')"
            prop="ruleType"
          >
            <el-radio-group
              v-model="form.ruleType"
              @input="handleRemark"
            >
              <el-radio :label="1">
                通知
              </el-radio>
              <el-radio :label="2">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div v-if="form.ruleType === 1">
          <div
            class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24 flex-item"
          >
            <el-form-item
              :label="getLabel('offlineTime')"
              prop="offlineTime"
            >
              <el-input
                v-model.number="day"
                oninput="value=value.replace(/[^\d]/g,'')"
              >
                <template slot="append">天</template>
              </el-input>
              <el-input
                v-model.number="hour"
                oninput="value=value.replace(/[^\d]/g,'')"
              >
                <template slot="append">时</template>
              </el-input>
              <el-input
                v-model.number="minute"
                oninput="value=value.replace(/[^\d]/g,'')"
              >
                <template slot="append">分</template>
              </el-input>
              <el-input
                v-model.number="second"
                oninput="value=value.replace(/[^\d]/g,'')"
              >
                <template slot="append">秒</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('notifyOnce')"
              prop="notifyOnce"
            >
              <el-radio-group
                v-model="form.notifyOnce"
              >
                <el-radio :label="0">
                  否
                </el-radio>
                <el-radio :label="1">
                  是
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('notifyObj')"
              prop="notifyObj"
            >
              <el-checkbox-group
                v-model="notifyObj"
                @change="handleChange"
              >
                <el-checkbox
                  :label="1"
                >自定义</el-checkbox>
                <el-checkbox
                  :label="2"
                >车队联系人</el-checkbox>
                <el-checkbox
                  :label="3"
                >车主</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div
            v-if="isCustom"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('phone')"
              prop="phone"
            >
              <el-input
                v-model.number="form.phone"
                :placeholder="getPlaceholder('phone')"
              />
            </el-form-item>
          </div>
          <div
            v-if="isCustom"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('email')"
              prop="email"
            >
              <el-input
                v-model.number="form.email"
                :placeholder="getPlaceholder('email')"
              />
            </el-form-item>
          </div>
        </div>
        <div
          v-if="form.ruleType === 2"
          style="width: 100%"
        >
          <div
            class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24"
          >
            <el-form-item
              :label="getLabel('alarmLevel')"
              prop="alarmLevel"
            >
              <AlarmLevel
                v-model="form.alarmLevel"
                @clearRule="clearRule"
              />
            </el-form-item>
          </div>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import AlarmLevel from './alarmLevel.vue';

const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  limitSpeed: 1,
  acc: 2,
  regionId: null,
  regionName: '',
  limitMode: null,
  isSpeed: 0,
  ruleType: 1,
  alarmLevel: null,
  notifyOnce: 0,
  offlineTime: null,
  notifyObj: '2',
  startTime: '00:00:00',
  endTime: '23:59:59',
  phone: null,
  email: null,
  remark: ''
};
export default {
  components: { AlarmLevel },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    },
    interRegionList: {
      type: Array,
      default: ()=>{return [];}
    }
  },
  data () {
    return {
      rules: {
        ruleName: { required: true, message: '请输入规则名称', trigger: 'blur' },
        limitSpeed: { required: true, message: '请输入速度阈值', trigger: 'blur' },
        alarmLevel: { required: true, message: '请输入告警级别', trigger: 'blur' }
      },
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: node => node.type === 1 && !node.children
      },
      isCustom: false,
      day: 0,
      hour: 0,
      minute: 10,
      second: 0,
      notifyObj: [2]
    };
  },
  mounted(){
  },
  methods: {
    handleClear(){
      this.form.regionId = '';
      this.$refs.tree.setCheckedKeys([]);
    },
    clearRule(val){
      this.form.alarmLevel = val;
      this.$refs.form.clearValidate('alarmLevel');
    },
    handleChange(){
      this.isCustom = this.notifyObj.includes(1);
      this.form.notifyObj = this.notifyObj.toString();
    },
    handleNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.regionName = list.toString();
      this.form.regionId = ids.toString();
      if (this.form.regionName) {
        this.form.limitMode = 1;
      }else{
        this.form.limitMode = null;
      }
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，是否判断速度：${this.form.isSpeed ? '是' : '否'}，规则类型：${this.form.ruleType === 1 ? '通知' : '告警'}`);
    },
    close(){
      this.notifyObj = [2];
      this.$refs.tree.setCheckedKeys([]);
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.form.remark = `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，是否判断速度：${this.form.isSpeed ? '是' : '否'}，规则类型：${this.form.ruleType === 1 ? '通知' : '告警'}`;
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      if (this.form.notifyObj) {
        this.notifyObj = this.form.notifyObj.split(',');
      }else{
        this.notifyObj = [];
      }
      if (this.form.offlineTime) {
        let list = this.form.offlineTime.split(':');
        this.day = list[0];
        this.hour = list[1];
        this.minute = list[2];
        this.second = list[3];
      }
      if (this.form.regionId) {
        let list = this.form.regionId.split(',');
        this.$nextTick(()=>{
          this.$refs.tree.setCheckedKeys(list);
        });
      }
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      this.form.ruleTypeId = this.ruleTypeId;
      this.form.offlineTime = this.day + ':' + this.hour + ':' + this.minute + ':' + this.second;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('OfflineNotification', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('OfflineNotification', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .flex-item{
    ::v-deep .el-form-item__content{
      display: flex;
    }
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: left;
      // margin-left: 15px;
      left: 15px;
      transform: translate(0, -50%);
      z-index: 3001;
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
