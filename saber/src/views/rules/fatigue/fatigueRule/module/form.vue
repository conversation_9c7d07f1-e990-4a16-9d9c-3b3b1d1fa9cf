<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '疲劳规则-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="60%"
    top="5vh"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="150px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              maxlength="15"
              show-word-limit
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('limitSpeed')"
            prop="limitSpeed"
          >
            <el-input
              v-model.number="form.limitSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('limitSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <DeptFormSingleSelect
              v-if="crud.status.add > 0"
              v-model="form.deptIds"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
            />
            <el-input
              v-else
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-input
              v-model.number="form.duration"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('duration')"
              @input="handleRemark"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('restDuration')"
            prop="restDuration"
          >
            <el-input
              v-model.number="form.restDuration"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('restDuration')"
              @input="handleRemark"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('cumulativeDrivingTime')"
            prop="cumulativeDrivingTime"
          >
            <el-input
              v-model.number="form.cumulativeDrivingTime"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('cumulativeDrivingTime')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('interRegionId')"
            prop="interRegionId"
          >
            <xh-select
              v-if="!form.isEdit"
              ref="regionSelect"
              v-model="form.interRegionName"
              :placeholder="getPlaceholder('interRegionId')"
              clearable
              @clear="handleClear"
              @click.native="clickRegionHandle"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleNodeCheck"
                />
              </el-option>
            </xh-select>
            <el-input
              v-else
              v-model="form.interRegionName"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isWarn')"
            prop="isWarn"
          >
            <el-radio-group
              v-model="form.isWarn"
              @input="handleRemark"
              @change="handleIsWarn"
            >
              <el-radio :label="1">
                是
              </el-radio>
              <el-radio :label="0">
                否
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isWarn"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('warnTime')"
            prop="warnTime"
          >
            <el-input
              v-model.number="form.warnTime"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('warnTime')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isWarn"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24"
          style="text-align: center;"
        >
          <el-radio-group
            v-model="tab"
          >
            <el-radio-button :label="true">必要设置</el-radio-button>
            <el-radio-button :label="false">预警相关设置</el-radio-button>
          </el-radio-group>
        </div>
        <div v-if="tab">
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('isPhoto')"
            >
              <el-radio-group
                v-model="form.isPhoto"
              >
                <el-radio :label="1">
                  拍照
                </el-radio>
                <el-radio :label="0">
                  不拍照
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.isPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('photoNumber')"
            >
              <el-input
                v-model.number="form.photoNumber"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('photoNumber')"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.isPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('photoInterval')"
              prop="photoInterval"
            >
              <el-input
                v-model.number="form.photoInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('photoInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.isPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('camera')"
              prop="camera"
            >
              <xh-select
                v-model="form.camera"
                :placeholder="getPlaceholder('camera')"
                clearable
              >
                <el-option
                  v-for="item in channelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </xh-select>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('isAlarm')"
            >
              <el-radio-group
                v-model="form.isAlarm"
                @change="$refs.form.clearValidate('tips')"
              >
                <el-radio :label="0">
                  不告警
                </el-radio>
                <el-radio :label="1">
                  仅告警一次
                </el-radio>
                <el-radio :label="2">
                  间隔告警
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.isAlarm === 2"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('intervalDuration')"
              prop="intervalDuration"
            >
              <el-time-picker
                v-model="form.intervalDuration"
                :placeholder="getPlaceholder('intervalDuration')"
                value-format="HH:mm:ss"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
            <el-form-item
              label="告警等级"
              prop="levelConfig"
            >
              <div class="main">
                <div class="item_label main_size">
                  告警持续时间
                </div>
                <div class="item_label main_parameter">
                  告警等级
                </div>
                <div class="item_label">
                  <span
                    class="add_item"
                    @click="addHandle"
                  >添加</span>
                </div>
              </div>
              <div
                v-for="(item,index) in form.levelConfig"
                :key="index"
                class="main"
              >
                <div class="main_item main_size">
                  <div class="main_item_select">
                    <el-input-number
                      v-model="form.levelConfig[index].alarmDuration"
                      placeholder="请输入告警持续时间"
                      :controls="false"
                      style="width: 90%;"
                    />
                  </div>
                </div>
                <div class="main_item">
                  <el-select
                    v-model="form.levelConfig[index].alarmLevel"
                    clearable
                    placeholder="请选择告警等级"
                    style="width: 90% !important;"
                  >
                    <el-option
                      v-for="item in dict.dict.alarmLevel"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="main_item">
                  <div
                    class="reduce_item"
                  >
                    <i
                      class="el-icon-remove-outline reduce_icon"
                      @click="reduceHandle(index)"
                    />
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
            <el-form-item
              :label="getLabel('tips')"
              prop="tips"
              :rules="form.isAlarm !== 0 ? validate.validateTips : ''"
            >
              <el-checkbox-group v-model="form.tips">
                <el-checkbox
                  :label="1"
                >紧急</el-checkbox>
                <el-checkbox
                  :label="2"
                >显示器显示</el-checkbox>
                <el-checkbox
                  :label="3"
                >TTS播读</el-checkbox>
                <el-checkbox
                  :label="4"
                >广告屏显示</el-checkbox>
              </el-checkbox-group>
              <el-input
                v-model="form.tipsText"
                :autosize="{ minRows: 2, maxRows: 2 }"
                :placeholder="getPlaceholder('tipsText')"
                type="textarea"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.tipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('tipsTimes')"
              prop="tipsTimes"
            >
              <el-input
                v-model.number="form.tipsTimes"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('tipsTimes')"
              >
                <template slot="append">次</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.tipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('tipsInterval')"
              prop="tipsInterval"
            >
              <el-input
                v-model.number="form.tipsInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('tipsInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div v-else>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('warnIsPhoto')"
            >
              <el-radio-group
                v-model="form.warnIsPhoto"
              >
                <el-radio :label="1">
                  拍照
                </el-radio>
                <el-radio :label="0">
                  不拍照
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnPhotoNumber')"
            >
              <el-input
                v-model.number="form.warnPhotoNumber"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnPhotoNumber')"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnPhotoInterval')"
              prop="warnPhotoInterval"
            >
              <el-input
                v-model.number="form.warnPhotoInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnPhotoInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsPhoto"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnCamera')"
              prop="warnCamera"
            >
              <xh-select
                v-model="form.warnCamera"
                :placeholder="getPlaceholder('warnCamera')"
                clearable
              >
                <el-option
                  v-for="item in channelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </xh-select>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('warnIsAlarm')"
            >
              <el-radio-group
                v-model="form.warnIsAlarm"
                @change="$refs.form.clearValidate('warnTips')"
              >
                <el-radio :label="0">
                  不告警
                </el-radio>
                <el-radio :label="1">
                  仅告警一次
                </el-radio>
                <el-radio :label="2">
                  间隔告警
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            v-if="form.warnIsAlarm === 2"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnAlarmIntervalDuration')"
              prop="warnAlarmIntervalDuration"
            >
              <el-time-picker
                v-model="form.warnAlarmIntervalDuration"
                :placeholder="getPlaceholder('warnAlarmIntervalDuration')"
                value-format="HH:mm:ss"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
            <el-form-item
              :label="getLabel('warnTips')"
              prop="warnTips"
              :rules="form.warnIsAlarm !== 0 ? validate.validateWarnTips : ''"
            >
              <el-checkbox-group v-model="form.warnTips">
                <el-checkbox
                  :label="1"
                >紧急</el-checkbox>
                <el-checkbox
                  :label="2"
                >显示器显示</el-checkbox>
                <el-checkbox
                  :label="3"
                >TTS播读</el-checkbox>
                <el-checkbox
                  :label="4"
                >广告屏显示</el-checkbox>
              </el-checkbox-group>
              <el-input
                v-model="form.warnTipsText"
                :autosize="{ minRows: 2, maxRows: 2 }"
                :placeholder="getPlaceholder('warnTipsText')"
                type="textarea"
              />
            </el-form-item>
          </div>
          <div
            v-if="form.warnTipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnTipsTimes')"
              prop="warnTipsTimes"
            >
              <el-input
                v-model.number="form.warnTipsTimes"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnTipsTimes')"
              >
                <template slot="append">次</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            v-if="form.warnTipsText"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('warnTipsInterval')"
              prop="warnTipsInterval"
            >
              <el-input
                v-model.number="form.warnTipsInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('warnTipsInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import { ruledetail, regionFencebyDept } from '@/api/rule';
const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  limitSpeed: 10,
  duration: 120,
  interRegionId: null,
  interRegionName: '',
  restDuration: 20,
  cumulativeDrivingTime: 480,
  isWarn: 0,
  warnTime: null,
  deptIds: '',
  bindDriverCard: 0,
  isPhoto: 0,
  photoNumber: null,
  photoInterval: null,
  camera: null,
  isAlarm: 1,
  intervalDuration: '00:03:00',
  tipsText: '',
  tipsTimes: 1,
  tipsInterval: 5,
  warnIsPhoto: 0,
  warnIsAlarm: 1,
  warnPhotoInterval: null,
  warnPhotoNumber: null,
  warnCamera: null,
  warnTipsTimes: 1,
  warnTipsInterval:  5,
  warnTipsText: '',
  remark: '',
  warnAlarmIntervalDuration: '00:03:00',
  tips: [2, 3],
  warnTips: [2, 3],
  levelConfig: [],
  startTime: '00:00:00',
  endTime: '23:59:59'
};
export default {
  components: { DeptFormSingleSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    }
  },
  data () {
    const validateTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.tipsText === '') {
        callback(new Error('请选择语音提示并输入语音提示内容'));
      } else {
        callback();
      }
    };
    const validateWarnTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.warnTipsText === '') {
        callback(new Error('请选择预警语音提示并输入预警语音提示内容'));
      } else {
        callback();
      }
    };
    return {
      rules: {
        ruleName: { required: true, message: '请输入规则名称', trigger: 'blur' },
        limitSpeed: { required: true, message: '请输入速度阈值', trigger: 'blur' },
        duration: { required: true, message: '请输入持续时长', trigger: 'blur' },
        restDuration: { required: true, message: '请输入休息时长', trigger: 'blur' },
        warnTime: { required: true, message: '请输入预警值', trigger: 'blur' },
        deptIds: { required: true, message: '请选择所属机构', trigger: 'change' },
        photoInterval: { required: true, message: '请输入拍照间隔', trigger: 'blur' },
        camera: { required: true, message: '请选择摄像头', trigger: 'change' },
        intervalDuration: { required: true, message: '请选择持续间隔时间', trigger: 'change' },
        warnPhotoInterval: { required: true, message: '请输入预警拍照间隔', trigger: 'blur' },
        warnCamera: { required: true, message: '请选择预警摄像头', trigger: 'change' },
        warnAlarmIntervalDuration: { required: true, message: '请选择预警持续间隔时间', trigger: 'change' },
        tipsTimes: { required: true, message: '请输入语音提示总次数', trigger: 'blur' },
        tipsInterval: { required: true, message: '请输入语音提示间隔', trigger: 'blur' },
        warnTipsInterval: { required: true, message: '请输入预警语音提示间隔', trigger: 'blur' },
        warnTipsTimes: { required: true, message: '请输入预警语音提示总次数', trigger: 'blur' }
      },
      tab: true,
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: node => node.type === 1 && !node.children
      },
      channelOptions: [],
      validate: {
        validateTips: {required: true, validator: validateTips, trigger: 'blur'},
        validateWarnTips: {required: true, validator: validateWarnTips, trigger: 'blur'}
      },
      formItem: {
        alarmDuration: '',
        alarm_level: ''
      },
      interRegionList: []
    };
  },
  watch: {
    'dict.ruleManage': {
      handler(){
        const channelData = this.dict.ruleManage.find((item)=>item.value === 'rule_camera');
        this.channelOptions = channelData?.children;
      },
      deep: true,
      immediate: true
    },
    'form.deptIds': {
      handler(val) {
        if (val) {
          this.getRegionList(val);
        }
      }
    }
  },
  methods: {
    getRegionList(val) {
      regionFencebyDept({deptId: val}).then(res => {
        this.interRegionList = res.data;
        this.$nextTick(() => {
          if (this.form.interRegionId) {
            let list = this.form.interRegionId.split(',');
            this.$nextTick(()=>{
              this.$refs.tree.setCheckedKeys(list);
            });
          }
        });
      });
    },
    clickRegionHandle() {
      if(!this.form.deptIds && !this.form.deptId){
        this.$message.warning('请先选择所属机构');
        this.$refs.regionSelect.blur();
      }
    },
    handleIsWarn(val){
      if (!val) {
        this.tab = true;
      }
    },
    handleClear(){
      this.form.interRegionId = '';
      this.$refs.tree.setCheckedKeys([]);
    },
    handleNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.interRegionName = list.toString();
      this.form.interRegionId = ids.toString();
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，速度阈值：${this.form.limitSpeed}km/h，持续时长：${this.form.duration}分钟，休息时长：${this.form.restDuration}分钟，${this.form.isWarn ? '预警' : '不预警'}`);
    },
    close(){
      this.tab = true;
      this.$refs.tree?.setCheckedKeys([]);
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
      this.$nextTick(() => {
        this.form.deptId = undefined;
      });
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.form.remark = `名称：${this.form.ruleName}，速度阈值：${this.form.limitSpeed}km/h，持续时长：${this.form.duration}分钟，休息时长：${this.form.restDuration}分钟，${this.form.isWarn ? '预警' : '不预警'}`;
      this.$nextTick(() => {
        this.rules.deptIds = this.crud.status.add > 0 ? { required: true, message: '请选择所属机构', trigger: 'change' } : null;
        this.$refs['form'].clearValidate('deptIds');
      });
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      if (!this.form.isEdit) {
        if (this.form.deptId) {
          this.getRegionList(this.form.deptId);
        }
        ruledetail({ruleType: this.form.ruleTypeId, ruleId: this.form.id}).then(res => {
          Object.assign( this.form, res.data);
          if (this.form.interRegionId) {
            let list = this.form.interRegionId.split(',');
            this.$nextTick(()=>{
              this.$refs.tree.setCheckedKeys(list);
            });
          }
        }).catch(err => {
          console.log('获取详情失败', err);
        });
      }
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      this.form.ruleTypeId = this.ruleTypeId;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('FatigueRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('FatigueRule', value);
    },
    addHandle() {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      this.form.levelConfig.push(obj);
    },
    reduceHandle (index) {
      this.form.levelConfig.splice(index, 1);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: left;
      // margin-left: 15px;
      left: 15px;
      transform: translate(0, -50%);
      z-index: 3001;
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
  .main {
    display: flex;
    width: 85%;
    text-align: center;
  }

  .main_item {
    flex: 1;
    height: 45px;
    line-height: 45px;
    border: 1px solid #c1c9da;
  }

  .main_size {
    flex: 1 !important;
  }

  .item_label {
    flex: 1;
    height: 40px;
    line-height: 40px;
    background-color: #e1e5ee;
    border: 1px solid #c1c9da;
  }

  .main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
    width: 100%;
  }

  .add_item {
    border: 1px solid #aebac5;
    padding: 3px 5px;
    background-color: #ffffff;
    margin: 5px;
    cursor: pointer;
  }

  .main_item_input {
    ::v-deep .el-input__inner {
      background-color: #ecf5ff;
      border-color: #d9ecff;
      display: inline-block;
      height: 32px;
      line-height: 30px;
      color: #409eff;
      border-width: 1px;
      border-style: solid;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      white-space: nowrap;
    }
  }
</style>
