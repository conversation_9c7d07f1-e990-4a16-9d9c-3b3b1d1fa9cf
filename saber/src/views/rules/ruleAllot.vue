<template>
  <el-dialog
    id="dialog"
    ref="dialog"
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :visible="dialogVisible"
    append-to-body
    title="规则分配"
    width="90%"
  >
    <div class="dialog-container">
      <div class="container-left">
        <div class="left-header-title">
          <TableTitleSlot title="规则列表" />
        </div>
        <el-form
          size="small"
          label-width="80px"
        >
          <el-row>
            <el-col :span="10">
              <el-form-item
                label="规则名称"
              >
                <el-input
                  v-model="ruleForm.ruleName"
                  placeholder="请输入规则名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item
                label="所属机构"
              >
                <DeptFormSingleSelect
                  ref="deptIdRef"
                  v-model="ruleForm.deptId"
                  :is-show="true"
                  placeholder="请选择所属机构"
                  size="small"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="3"
              class="form-left-search"
            >
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="leftSearchClick"
              >查 询
              </el-button>
            </el-col>
          </el-row>
        </el-form>
        <div class="left-table-container">
          <el-table
            v-loading="leftLoading"
            :data="ruleList"
            :header-cell-style="{background:'#ebf5ff',color:'#606266'}"
            highlight-current-row
            height="55vh"
            @row-click="leftRowClick"
          >
            <el-table-column
              prop="ruleName"
              label="规则名称"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              prop="deptName"
              label="所属机构"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
          <div class="left-pagination-container">
            <el-pagination
              background
              layout="total, prev, pager, next, sizes"
              :current-page="ruleForm.current"
              :page-size="ruleForm.size"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :total="leftTotal"
              @size-change="leftSizeChange"
              @current-change="leftCurrentChange"
            />
          </div>
        </div>
      </div>

      <div class="container-right">
        <div class="right-header-title">
          <TableTitleSlot title="终端列表" />
        </div>
        <el-form
          size="small"
          label-width="80px"
        >
          <el-row>
            <el-col :span="7">
              <el-form-item
                label="序列号"
              >
                <el-input
                  v-model="terminalForm.uniqueId"
                  placeholder="请输入序列号"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item
                label="监控对象"
              >
                <el-input
                  v-model="terminalForm.targetName"
                  placeholder="请输入监控对象"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item
                label="分配状态"
              >
                <xh-select
                  v-model="terminalForm.bindType"
                  placeholder="请选择分配状态"
                >
                  <el-option
                    v-for="item in bindTypeOptions"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </xh-select>
              </el-form-item>
            </el-col>
            <el-col
              :span="3"
              class="form-right-search"
            >
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="rightSearchClick"
              >查 询
              </el-button>
            </el-col>
          </el-row>
        </el-form>
        <div class="right-table-container">
          <el-table
            ref="multipleTable"
            v-loading="rightLoading"
            :data="terminalList"
            row-key="idStr"
            :header-cell-style="{background:'#ebf5ff',color:'#606266'}"
            height="55vh"
            @select="rightTableSelect"
            @select-all="rightTableSelectAll"
          >
            <el-table-column
              type="selection"
              width="50px"
              reserve-selection
            />
            <el-table-column
              prop="name"
              label="监控对象"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              prop="uniqueId"
              label="序列号"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
          <div class="right-pagination-container">
            <el-pagination
              background
              layout="total, prev, pager, next, sizes"
              :current-page="terminalForm.current"
              :page-size="terminalForm.size"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :total="rightTotal"
              @size-change="rightSizeChange"
              @current-change="rightCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="beforeClose"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        :loading="btnLoading"
        @click="handleSubmitClick"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { ruleListWith, ruleBindTags, ruleBindVehicle } from '@/api/rule';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';

export default {
  components: {
    TableTitleSlot,
    DeptFormSingleSelect
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    nodeData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      ruleForm: {
        ruleName: '',
        deptId: null,
        current: 1,
        size: 10,
        ruleType: null
      },
      ruleList: [],
      leftLoading: false,
      leftTotal: 0,
      leftRowData: null,
      terminalForm: {
        uniqueId: null,
        targetName: null,
        bindType: 0,
        current: 1,
        size: 10,
        ruleType: null,
        ruleId: null,
        deptId: null
      },
      terminalList: [],
      rightLoading: false,
      rightTotal: 0,
      bindTypeOptions: [
        { label: '所有', value: 0 },
        { label: '已分配', value: 1 },
        { label: '未分配', value: 2 }
      ],
      params: {
        delTags: [],
        addTags: [],
        ruleType: null,
        ruleId: null
      },
      btnLoading: false
    };
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.ruleForm.ruleType = this.nodeData.id;
        this.terminalForm.ruleType = this.nodeData.id;
        this.params.ruleType = this.nodeData.id;
        this.leftSearchClick();
      }
    }
  },
  methods: {
    leftSizeChange (val) {
      this.ruleForm.size = val;
      this.leftSearchClick();
    },
    leftCurrentChange (val) {
      this.ruleForm.current = val;
      this.leftSearchClick();
    },
    leftSearchClick () {
      this.leftLoading = true;
      ruleListWith(JSON.parse(JSON.stringify(this.ruleForm))).then(res => {
        this.ruleList = res.data.content;
        this.leftTotal = res.data.total;
      }).catch(err => {
        this.ruleList = [];
        this.leftTotal = 0;
      }).finally(() => {
        this.leftLoading = false;
      });
    },
    leftRowClick (data) {
      this.leftRowData = data;
      this.terminalForm.ruleId = data.id;
      this.terminalForm.deptId = data.deptIdStr;
      this.params.ruleId = data.id;
      this.$refs.multipleTable.clearSelection();
      this.rightSearchClick();
    },
    rightSizeChange (val) {
      this.terminalForm.size = val;
      this.rightSearchClick();
    },
    rightCurrentChange (val) {
      this.terminalForm.current = val;
      this.rightSearchClick();
    },
    rightSearchClick () {
      if (!this.leftRowData) {
        this.$message.warning('请先选择左侧规则');
        return;
      }
      this.rightLoading = true;
      ruleBindTags(JSON.parse(JSON.stringify(this.terminalForm))).then(res => {
        this.terminalList = res.data.content;
        this.terminalList.forEach(item => {
          if (item.binding) {
            this.$refs.multipleTable.toggleRowSelection(item);
          }
        });
        this.rightTotal = res.data.total;
      }).finally(() => {
        this.rightLoading = false;
      });
    },
    rightTableSelect (rows, row) {
      let selected = rows.length && rows.indexOf(row) !== -1;
      if (selected) {
        if (row.binding) {
          this.params.delTags = this.params.delTags.filter(item => item !== row.idStr);
        } else {
          this.params.addTags.push(row.idStr);
        }
      } else {
        if (row.binding) {
          this.params.delTags.push(row.idStr);
        } else {
          this.params.addTags = this.params.addTags.filter(item => item !== row.idStr);
        }
      }
    },
    rightTableSelectAll (selection) {
      if (selection.length) {
        selection.forEach(item => {
          if (item.binding) {
            this.params.delTags = this.params.delTags.filter(element => element !== item.idStr);
          } else {
            this.params.addTags.push(item.idStr);
          }
        });
      } else {
        this.terminalList.forEach(item => {
          if (item.binding) {
            this.params.delTags.push(item.idStr);
          } else {
            this.params.addTags = this.params.addTags.filter(element => element !== item.idStr);
          }
        });
      }
    },
    handleSubmitClick () {
      if (!this.leftRowData) {
        this.$message.warning('请先选择左侧规则');
        return;
      }
      if (!this.params.addTags.length && !this.params.delTags.length) {
        this.$message.warning('当前未对该规则下的监控对象进行勾选状态编辑, 请对右侧监控对象进行勾选或取消勾选操作');
        return;
      }
      this.params.addTags = [...new Set(this.params.addTags)];
      this.params.delTags = [...new Set(this.params.delTags)];
      this.btnLoading = true;
      ruleBindVehicle(JSON.parse(JSON.stringify(this.params))).then(res => {
        this.$message.success('编辑成功');
        this.$refs.multipleTable.clearSelection();
        this.params.addTags = [];
        this.params.delTags = [];
        this.$nextTick(() => {
          this.rightSearchClick();
        });
      }).finally(() => {
        this.btnLoading = false;
      });
    },
    beforeClose () {
      this.$refs.multipleTable.clearSelection();
      this.$refs.deptIdRef.clear();
      this.leftRowData = null;
      this.ruleList = [];
      this.leftTotal = 0;
      this.terminalList = [];
      this.rightTotal = 0;
      this.params = {
        delTags: [],
        addTags: [],
        ruleType: null,
        ruleId: null
      };
      this.ruleForm = {
        ruleName: '',
        deptId: null,
        current: 1,
        size: 10,
        ruleType: null
      };
      this.terminalForm = {
        uniqueId: null,
        targetName: null,
        bindType: 0,
        current: 1,
        size: 10,
        ruleType: null,
        ruleId: null,
        deptId: null
      };
      this.$emit('update:dialogVisible', false);
    }
  }
};
</script>
<style lang="less" scoped>
.dialog-container {
  display: flex;
  justify-content: space-between;
  .container-left, .container-right {
    width: calc(50% - 10px);
    border: 1px solid #e4e7ed;
    border-radius: 4px 4px 0 0;
  }
  .container-left {
    /deep/ .el-table__row.current-row > td {
      background-color: rgba(var(--gn-color-rgb), .4) !important;
    }
  }
}
</style>
