<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看数据权限配置' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 权限名称 -->
          <el-form-item
            :label="getLabel('scopeName')"
            prop="scopeName"
          >
            <el-input
              v-model.trim="form.scopeName"
              :disabled="isDetail"
              :placeholder="getPlaceholder('scopeName')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 权限编号 -->
          <el-form-item
            :label="getLabel('resourceCode')"
            prop="resourceCode"
          >
            <el-input
              v-model.trim="form.resourceCode"
              :disabled="isDetail"
              :placeholder="getPlaceholder('resourceCode')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 权限字段 -->
          <el-form-item
            :label="getLabel('scopeColumn')"
            prop="scopeColumn"
          >
            <el-input
              v-model.trim="form.scopeColumn"
              :disabled="isDetail"
              :placeholder="getPlaceholder('scopeColumn')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 规则类型 -->
          <el-form-item
            :label="getLabel('scopeType')"
            prop="scopeType"
          >
            <xh-select
              v-model="form.scopeType"
              :placeholder="getPlaceholder('scopeType')"
              clearable
              :disabled="isDetail"
              @change="handleScopeTypeChange"
            >
              <el-option
                v-for="item in scopeTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 可见字段 -->
          <el-form-item
            :label="getLabel('scopeField')"
            prop="scopeField"
          >
            <el-input
              v-model.trim="form.scopeField"
              :disabled="isDetail"
              :placeholder="getPlaceholder('scopeField')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 权限类名 -->
          <el-form-item
            :label="getLabel('scopeClass')"
            prop="scopeClass"
          >
            <el-input
              v-model.trim="form.scopeClass"
              :disabled="isDetail"
              :placeholder="getPlaceholder('scopeClass')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.scopeType === '5'"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24"
        >
          <!-- 规则值 -->
          <el-form-item
            :label="getLabel('scopeValue')"
            prop="scopeValue"
          >
            <el-input
              v-model.trim="form.scopeValue"
              type="textarea"
              :rows="5"
              :disabled="isDetail"
              :placeholder="getPlaceholder('scopeValue')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 备注 -->
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model.trim="form.remark"
              :disabled="isDetail"
              :placeholder="getPlaceholder('remark')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  scopeName: null,
  resourceCode: null,
  scopeColumn: '-',
  scopeType: null,
  scopeField: '*',
  scopeClass: null,
  scopeValue: null,
  remark: null,
  menuId: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    isDetail: {
      type: Boolean,
      default: false
    },
    scopeTypeOptions: {
      type: Array,
      default: () => {
        return [];
      }
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    rowCode: {
      type: String,
      default: ''
    },
    menuId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      rules: {
        scopeName: { required: true, message: '请输入权限名称', trigger: 'blur' }, // 权限名称
        resourceCode: { required: true, message: '请输入权限编号', trigger: 'blur' }, // 权限编号
        scopeColumn: { required: true, message: '请输入权限字段', trigger: 'blur' }, // 权限字段
        scopeType: { required: true, message: '请选择规则类型', trigger: 'change' }, // 规则类型
        scopeField: { required: true, message: '请输入可见字段', trigger: 'blur' }, // 可见字段
        scopeClass: { required: true, message: '请输入权限类名', trigger: 'blur' } // 权限类名
      }
    };
  },
  methods: {
    handleScopeTypeChange () {
      this.form.scopeValue = null;
      const data = this.scopeTypeOptions.find(item => item.value === this.form.scopeType);
      if (data) {
        this.form.scopeName = `${this.dialogTitle} [${data.label}]`;
      }
      switch (this.form.scopeType) {
      case '1':
        this.form.scopeColumn = '-';
        break;
      case '2':
        this.form.scopeColumn = 'create_user';
        break;
      case '5':
        this.form.scopeColumn = '';
        break;
      default:
        this.form.scopeColumn = 'create_dept';
        break;
      }
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
      this.form.menuId = this.menuId;
      this.form.scopeType = this.form.scopeType ? String(this.form.scopeType) : null;
    },
    /** 新建 - 之前 */
    [CRUD.HOOK.beforeToAdd] () {
      this.form.scopeName = this.dialogTitle + ' [暂无]';
      this.form.resourceCode = this.rowCode;
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Datascope', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Datascope', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
