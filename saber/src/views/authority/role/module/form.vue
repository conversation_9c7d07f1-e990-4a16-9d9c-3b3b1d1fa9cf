<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看角色' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 角色名称 -->
          <el-form-item
            :label="getLabel('roleName')"
            prop="roleName"
          >
            <el-input
              v-model.trim="form.roleName"
              :disabled="isDetail"
              :placeholder="getPlaceholder('roleName')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 角色别名 -->
          <el-form-item
            :label="getLabel('roleAlias')"
            prop="roleAlias"
          >
            <el-input
              v-model.trim="form.roleAlias"
              oninput="value=value.replace(/[^\w]/g,'')"
              :disabled="isDetail"
              :placeholder="getPlaceholder('roleAlias')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 上级角色 -->
          <el-form-item
            :label="getLabel('parentId')"
            prop="parentName"
          >
            <xh-select
              ref="select"
              v-model="form.parentName"
              :disabled="isDetail"
              clearable
              :placeholder="getPlaceholder('parentId')"
              @clear="handleParentClick"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  :data="roleData"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 角色排序 -->
          <el-form-item
            :label="getLabel('sort')"
            prop="sort"
          >
            <el-input-number
              v-model.trim="form.sort"
              controls-position="right"
              :min="0"
              :disabled="isDetail"
              :placeholder="getPlaceholder('sort')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { getRoleTreeById } from '@/api/system/role';

const defaultForm = {
  id: null,
  roleName: null,
  roleAlias: null,
  parentId: null,
  parentName: null,
  sort: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      rules: {
        roleName: { required: true, message: '请输入角色名称', trigger: 'blur' }, // 角色名称
        roleAlias: { required: true, message: '请输入角色别名', trigger: 'blur' }, // 角色别名
        sort: { required: true, message: '请输入角色排序', trigger: 'blur' }, // 角色排序
      },
      roleData: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      }
    };
  },
  methods: {
    handleParentClick () {
      this.form.parentId = null;
    },
    handleNodeClick (node) {
      this.$set(this.form, 'parentName', node.title);
      this.$set(this.form, 'parentId', node.id);
      this.$refs.select.blur();
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
      getRoleTreeById(this.form.id).then(res => {
        this.roleData = res.data;
        if (this.form.parentId && this.form.parentId !== '0') {
          this.$nextTick(()=>{
            this.$refs.tree.setCurrentKey(this.form.parentId);
          });
        } else if (this.form.parentId === '0') {
          this.form.parentId = null;
          this.form.parentName = null;
        }
      });
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Role', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Role', value);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number {
    width: 100%;
    .el-input__inner {
        text-align: left;
    }
}
</style>
