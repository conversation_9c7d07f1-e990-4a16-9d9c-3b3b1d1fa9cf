<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    :title="`[${dialogTitle}] 接口权限配置`"
    append-to-body
    width="80%"
    @close="close"
  >
    <div class="xh-container">
      <div class="head-container">
        <HeadCommon
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','apiscopeSetting:edit','apiscopeSetting:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 权限名称 -->
          <el-table-column
            v-if="columns.visible('scopeName')"
            prop="scopeName"
            :label="getLabel('scopeName')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 权限编号 -->
          <el-table-column
            v-if="columns.visible('resourceCode')"
            prop="resourceCode"
            :label="getLabel('resourceCode')"
            min-width="50"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 权限路径 -->
          <el-table-column
            v-if="columns.visible('scopePath')"
            prop="scopePath"
            :label="getLabel('scopePath')"
            min-width="50"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 接口类型 -->
          <el-table-column
            v-if="columns.visible('scopeTypeName')"
            prop="scopeTypeName"
            :label="getLabel('scopeTypeName')"
            min-width="50"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tag>{{ scope.row.scopeTypeName }}</el-tag>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
    </div>
    <!--分页组件-->
    <pagination />
    <!--表单渲染-->
    <eForm
      :is-detail.sync="isDetail"
      :scope-type-options="scopeTypeOptions"
      :menu-id="menuId"
    />
  </el-dialog>
</template>

<script>
import crudApiscopeSetting from '@/api/system/apiscopeSetting';
import eForm from './form';
import HeadCommon from '@/components/formHead/headCommon.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import { getDictionary } from '@/api/system/dictNew';
// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Apiscope', 'settingName'),
  crudMethod: { ...crudApiscopeSetting },
  queryOnPresenterCreated: false
});

export default {
  name: 'ApiscopeSetting',
  components: {
    HeadCommon, pagination, crudOperation, udOperation, eForm
  },
  mixins: [presenter(crud)],
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    menuId: {
      type: String,
      default: ''
    },
    dialogTitle: {
      type: String,
      default: ''
    }
  },
  data(){
    return{
      permission: {
        add: ['admin', 'apiscopeSetting:add'],
        edit: ['admin', 'apiscopeSetting:edit'],
        del: ['admin', 'apiscopeSetting:del']
      },
      headConfig: {
        item: {
          1: {
            name: '权限名称',
            type: 'input',
            value: 'scopeName',
          },
          2: {
            name: '权限编号',
            type: 'input',
            value: 'resourceCode',
          }
        },
        button: {
        }
      },
      dialogFormVisible: false,
      scopeTypeOptions: [],
      isDetail: false,
      isFirstOpen: true
    };
  },
  watch: {
    dialogVisible: {
      handler (newValue) {
        if (newValue) {
          this.handleQuery();
          if (this.isFirstOpen) {
            this.isFirstOpen = false;
            this.getInterDictList();
            this.initTable();
          }
        }
      }
    }
  },
  methods: {
    initTable () {
      this.$nextTick(() => {
        let columns = {};
        // 兼容u-table获取表格列
        const tableColumns = this.$refs.table.columns || this.$refs.table.getTableColumn();
        tableColumns.forEach(e => {
          if (!e.property || e.type !== 'default') {
            return;
          }
          columns[e.property] = {
            label: e.label,
            visible: true
          };
        });
        this.columns = this.obColumns(columns);
        this.crud.updateProp('tableColumns', columns);
        const element = this.$refs['table'].$el;
        this.tableMaxHeight = element.offsetHeight;
      });
    },
    obColumns(columns) {
      return {
        visible(col) {
          return !columns || !columns[col] ? true : columns[col].visible;
        }
      };
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      // 避免重置后将menuId清空, 因此请求前赋值
      this.crud.query.menuId = this.menuId;
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    // 查询
    handleQuery () {
      this.crud.toQuery();
    },
    getInterDictList () {
      // 接口类型字典
      getDictionary({code: 'api_scope_type'}).then(res => {
        this.scopeTypeOptions = res.data?.map(item => ({
          label: item.dictValue,
          value: item.dictKey
        }));
      });
    },
    close () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Apiscope', value);
    }
  }
};
</script>

<style lang="less" scoped>
.xh-container{
    height: calc(80vh - 60px - 42px);
}
</style>
