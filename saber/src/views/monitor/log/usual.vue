<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation :download="false"/>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          @header-dragend="headerDragend"
        >
          <el-table-column
            v-if="columns.visible('userAccount')"
            prop="userAccount"
            label="账号"
            min-width="160"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('menu')"
            prop="menu"
            label="菜单"
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('operation')"
            prop="operation"
            label="操作类型"
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel('userOperateType', scope.row.operation) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('description')"
            prop="description"
            label="操作内容"
            min-width="300"
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tooltip
                :enterable="true"
              >
                <div
                  slot="content"
                  class="tooltip-content"
                >{{ scope.row.description }}</div>
                <div class="ellipsis">{{ scope.row.description }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('serverIp')"
            prop="serverIp"
            label="账号IP"
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('time')"
            prop="time"
            label="操作时间"
            min-width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.time }}
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
  </basic-container>
</template>

<script>
import api from '@/api/log/usual.js';
import CRUD, { header, presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import HeadCommon from '@/components/formHead/headCommon.vue';

const paginationApi = ({endTime, startTime, ...data}) => {
  if(startTime) data.startTime = parseInt(startTime/1000);
  if(endTime) data.endTime = parseInt(endTime/1000);
  return api.pagination({...data});
};

const crud = CRUD({
  title: '操作日志',
  optShow: {
    add: false,
    edit: false,
    download: false
  },
  crudMethod: { ...api, pagination: paginationApi },
  queryOnPresenterCreated: false
});

export default {
  name: 'Usual',
  components: {
    crudOperation,
    pagination,
    HeadCommon
  },
  mixins: [
    presenter(crud),
    header()
  ],
  dicts: [
    'userOperateType'
  ],
  data() {
    return {
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: '账号',
            type: 'input',
            value: 'userAccount',
          },
          2: {
            name: '开始时间',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束时间',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '日志类型',
            type: 'cascader',
            value: 'menuOpt',
            options: []
          },
          5: {
            name: '序列号',
            type: 'input',
            value: 'uniqueId'
          },
        },
        button: {
        }
      }
    };
  },
  created () {
    this.getMenuList();
  },
  methods: {
    getMenuList() {
      api.getMenuList().then(res => {
        const arr = res.data.data || [];
        function handle(list) {
          if(list.length) {
            list.map(item => {
              item.label = item.menuName;
              item.value = `${item.menuId}-${item.menuName}`;
              if(item.children) {
                if(item.children.length) {
                  handle(item.children);
                } else {
                  delete item.children;
                }
              }
            });
          }
        }
        handle(arr, true);
        // 给initQuery赋值false, 避免重复调用查询接口
        this.headConfig.initQuery = false;
        this.headConfig.item['4'].options = arr;
      });
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    headerDragend(newWidth, oldWidth, column, event){
      console.log('-> newWidth, oldWidth, column, event', newWidth, oldWidth, column, event);
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.tooltip-content {
  max-height: 260px;
  padding: 1px; // 防止某些分辨率下会直接出现滚动条
  overflow-y: auto;
}
</style>
