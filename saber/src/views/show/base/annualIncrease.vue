<template>
  <div ref="annualIncreaseRef"></div>
</template>

<script>
export default {
  name: "annualIncrease",
  data() {
    return {
      options: {
        backgroundColor: "rgba(5,9,18, 0.8)",
        label: {
          color: "rgba(255, 255, 255, 1)",
        },
        legend: {
          data: ["年设备应用量", "增长趋势"],
          bottom: 0,
          itemWidth: 8,
          itemHeight: 8,
          borderRadius: 8,
          textStyle: {
            color: "#fff", // 设置图例文字颜色为红色
          },
        },
        grid: {
          top: "15%",
          left: "10%",
          right: "10%",
          bottom: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        xAxis: {
          type: "category",
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 1)",
          },
          data: [],
        },
        yAxis: [
          {
            type: "value",
            name: "",
            axisLabel: {
              color: "rgba(255, 255, 255, 1)",
            },
            axisLine: {
              show: true, // 是否显示y轴轴线
              lineStyle: {
                type: "solid", // 设置y轴轴线的类型
              },
            },
            splitLine: {
              show: false,
            },
          },
          {
            type: "value",
            name: "",
            min: 0,
            show: false,
            interval: 20,
            axisLabel: {
              formatter: "{value} %",
              color: "rgba(255, 255, 255, 1)",
            },
            axisLine: {
              show: true, // 是否显示y轴轴线
              lineStyle: {
                type: "solid", // 设置y轴轴线的类型
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            data: [],
            name: "年设备应用量",
            type: "bar",
            barWidth: "20px",
            position: "left",
            tooltip: {
              valueFormatter: function (value) {
                return value + "";
              },
            },
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#5480d9" },
                { offset: 0.5, color: "#62acd9" },
                { offset: 1, color: "#62c7d9" },
              ]),
            },
            emphasis: {
              itemStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#2378f7" },
                  { offset: 0.7, color: "#2378f7" },
                  { offset: 1, color: "#83bff6" },
                ]),
              },
            },
          },
          {
            data: [],
            name: "增长趋势",
            position: "right",
            type: "line",
            color: "#eacd58",
            symbol: "none",
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + " %";
              },
            },
          },
        ],
      },
    };
  },
  props: {
    mapData: {
      type: Array,
      default: () => {},
    },
  },
  watch: {
    mapData() {
      this.setData();
    },
  },
  mounted() {
    this.init();
    window.addEventListener("resize", this.resize);
  },
  beforeDestroy() {
    if(this.chartElement) {
      this.chartElement.dispose()
      this.chartElement = null
    }
    window.removeEventListener("resize", this.resize);
  },
  methods: {
    setData() {
      const list = Object.keys(this.mapData);
      if (list.length) {
        this.options.xAxis.data = list;
        const data1 = [];
        const data2 = [];
        list.forEach(key => {
          data1.push(this.mapData[key].count);
          data2.push(this.mapData[key].rate);
        });
        this.options.series[0].data = data1;
        this.options.series[1].data = data2;
        this.init();
      }
    },
    resize() {
      this.$nextTick(() => {
        this.chartElement.resize();
      });
    },
    init() {
      this.chartElement = this.$echarts.init(this.$refs.annualIncreaseRef);
      this.chartElement.setOption(this.options);
    },
  },
};
</script>

<style lang="less" scoped></style>
