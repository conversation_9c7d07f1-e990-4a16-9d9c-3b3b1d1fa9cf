<template>
  <div ref="highPrecisionRef"></div>
</template>
  
<script>
export default {
  name: "highPrecision",
  data() {
    return {
      options: {
        backgroundColor: "rgba(5,9,18, 0.8)",
        // tooltip: {
        //   trigger: "item",
        //   formatter: "{a} <br/>{b} : {c} ({d}%)",
        // },
        grid: {
          top: "15%",
          left: "10%",
          right: "10%",
          bottom: "15%",
          containLabel: true,
        },
        label: {
          // alignTo: "edge",
          formatter: "{name|{b}}\n{time|{c}}",
          // minMargin: 5,
          // edgeDistance: 20,
          // lineHeight: 15,
          color: '#fff',
          rich: {
            time: {
              // fontSize: 10,
              // color: "#999",
            },
          },
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["20%", "70%"],
            center: ["45%", "55%"],
            roseType: "area",
            itemStyle: {
              borderRadius: 5,
            },
            colors: [
              "#5570c7",
              "#91cd77",
              "#fac857",
              "#ee6666",
              "#73c0de",
              "#3ca272",
              "#fd8553",
              "#9a60b4",
              "#f17ccb",
            ],
            data: [
              { value: 0, name: "人员定位" },
              { value: 0, name: "测绘勘探" },
              { value: 0, name: "智能巡检" },
              { value: 0, name: "资产管理" },
              { value: 0, name: "精密控制" },
              { value: 0, name: "安全监测" },
              { value: 0, name: "运载定位" },
              { value: 0, name: "应急通讯" },
              { value: 0, name: "时频同步" },
            ],
          },
        ],
      }
    };
  },
  mounted() {
    this.init();
    window.addEventListener("resize", this.resize);
  },
  beforeDestroy() {
    if(this.chartElement) {
      this.chartElement.dispose()
      this.chartElement = null
    }
    window.removeEventListener("resize", this.resize);
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    mapData: {
      type: Object,
      default: () => [],
    },
  },
  watch: {
    list() {
      this.setData();
    },
    mapData() {
      this.setData();
    },
  },
  methods: {
    setData() {
      if (this.list.length && this.mapData.length) {
        const data = [];
        this.mapData.forEach((item) => {
          const cur = { value: 0, name: item.label };
          const obj = this.list.find((each) => each.category == item.value);
          if (obj) {
            cur.value = obj.count;
          }
          data.push(cur)
        });
        this.options.series[0].data = data;
        this.init()
      }
    },
    resize() {
      this.$nextTick(() => {
        this.chartElement.resize();
      })
    },
    init() {
      // const colors = [
      //   {label: '人员定位', color: '#5570c7',},
      //   {label: '测绘勘探', color: '#91cd77',},
      //   {label: '智能巡检', color: '#fac857',},
      //   {label: '资产管理', color: '#ee6666',},
      //   {label: '精密控制', color: '#73c0de',},
      //   {label: '安全监测', color: '#3ca272',},
      //   {label: '运载定位', color: '#fd8553',},
      //   {label: '应急通讯', color: '#9a60b4',},
      //   {label: '时频同步', color: '#f17ccb',},
      // ]
      this.chartElement = this.$echarts.init(this.$refs.highPrecisionRef);
      this.chartElement.setOption(this.options);
    },
  },
};
</script>
  
<style lang="less" scoped>
</style>
  