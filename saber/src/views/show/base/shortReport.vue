<template>
  <div ref="shortReportRef"></div>
</template>

<script>
export default {
  name: "shortReport",
  data() {
    return {
      options: {
        // title: {
        //   text: "Referer of a Website",
        //   subtext: "Fake Data",
        //   left: "center",
        // },
        tooltip: {
          trigger: "item",
        },
        backgroundColor: "rgba(5,9,18, 0.8)",
        // legend: {
        //   orient: "vertical",
        //   left: "left",
        // },
        label: {
          // alignTo: "edge",
          formatter: "{name|{b}}\n{time|{c}}",
          // minMargin: 5,
          // edgeDistance: 20,
          // lineHeight: 15,
          color: '#fff',
          rich: {
            time: {
              // fontSize: 10,
              // color: "#999",
            },
          },
        },
        grid: {
          top: "15%",
          left: "10%",
          right: "10%",
          bottom: "15%",
          containLabel: true,
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["40%", "50%"],
            legendHoverLink: false,
            hoverAnimation: true,
            data: [
              { value: 0, name: "人员定位" },
              { value: 0, name: "测绘勘探" },
              { value: 0, name: "智能巡检" },
              { value: 0, name: "资产管理" },
              { value: 0, name: "精密控制" },
              { value: 0, name: "安全监测" },
              { value: 0, name: "运载定位" },
              { value: 0, name: "应急通讯" },
              { value: 0, name: "射频同步" },
            ],
            itemStyle: {
              normal: {
                color: function (params) {
                  // 预定义颜色数组
                  var colors = [
                    "#57aaba",
                    "#4fad4f",
                    "#1480be",
                    "#b0a64d",
                    "#b259b3",
                    "#b355b3",
                    "#b93737",
                    "#42474d",
                    "#446caa",
                  ];
                  // 返回每个饼图项的颜色，可以按照索引或者其他逻辑来分配颜色
                  return colors[params.dataIndex];
                },
              },
            },
            labelLine: {
              normal: {
                show: true,
                lineStyle: {
                  // color: "#fff", // 指示线颜色
                },
              },
            },
          },
        ],
      },
    };
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    mapData: {
      type: Object,
      default: () => [],
    },
  },
  watch: {
    list() {
      this.setData();
    },
    mapData() {
      this.setData();
    },
  },
  mounted() {
    this.init();
    window.addEventListener("resize", this.resize);
  },
  beforeDestroy() {
    if(this.chartElement) {
      this.chartElement.dispose()
      this.chartElement = null
    }
    window.removeEventListener("resize", this.resize);
  },
  methods: {
    setData() {
      if (this.list.length && this.mapData.length) {
        const data = [];
        this.mapData.forEach((item) => {
          const cur = { value: 0, name: item.label };
          const obj = this.list.find((each) => each.domain == item.value);
          if (obj) {
            cur.value = obj.count;
          }
          data.push(cur)
        });
        this.options.series[0].data = data;
        this.init()
      }
    },
    resize() {
      this.$nextTick(() => {
        this.chartElement.resize();
      });
    },
    init() {
      this.chartElement = this.$echarts.init(this.$refs.shortReportRef);
      this.chartElement.setOption(this.options);
    },
  },
};
</script>

<style lang="less" scoped></style>
