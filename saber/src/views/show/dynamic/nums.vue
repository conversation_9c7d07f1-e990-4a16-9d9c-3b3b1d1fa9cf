<template>
    <div class="nums">
      <div class="each-item" v-for="(item,index) in list" :key="index">
        <div class="text">{{item.text.includes('__') ? getText(item.text) : item.text}}</div>
        <div class="num">{{getNum(item.num)}}</div>
        <div class="note" v-if="item.note">({{item.note}})</div>
      </div>
    </div>
  </template>
    
  <script>
  export default {
    name: "rankingLine",
    props: {
      list: {
        type: Array,
        default: () => []
      },
      map: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
      };
    },
    created() {
    },
    methods: {
      getText(text) {
        if(text.includes('__')) {
          const arr = text.split('__')
          return this.map[arr[0]] + arr[1]
        }
        return text
      },
      getNum(num) {
        if(`${num}`.split('.')[0].length > 8) {
          return (num / 10000).toFixed(1) + '万'
        } else {
          return num
        }
      }
    },
  };
  </script>
    
  <style lang="less" scoped>
  .nums {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    background-color: rgba(5,9,18, 0.8);
    .each-item{
      display: flex;
      justify-content: center;
      align-items: center;
      .text{
          color: #fff;
          margin-right: 10px;
          line-height: 26px;
      }
      .num{
          color: #ffc32d;
          font-size: 30px;
          line-height: 26px;
          font-weight: bold;
      }
      .note{
          color: #fff;
          line-height: 26px;
      }
    }
  }
  </style>
    