<template>
  <div class="big-screen-base">
    <div class="top-center-title">北斗动态数据</div>
    <div class="content">
      <div class="left">
        <div class="echart-box">
          <div class="short-report-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">数据采集/共享量(点位数据)</div>
              <div class="img-right"></div>
            </div>
            <Nums class="content-echart" :list="numsList" />
            <div class="item-bottom"></div>
          </div>
        </div>
        <div class="echart-box">
          <div class="annual-increase-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">终端上线趋势分析</div>
              <div class="img-right"></div>
            </div>
            <LineBox class="content-echart" :mapData="trendMap" />
            <div class="item-bottom"></div>
          </div>
        </div>
        <div class="echart-box">
          <div class="position-mode-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">告警分析</div>
              <div class="img-right"></div>
            </div>
            <Bar class="content-echart" :mapData="alarmData" />
            <div class="item-bottom"></div>
          </div>
        </div>
      </div>
      <div class="center">
        <div class="ab-top">
          <div class="nums">
            <div
              :class="['nums-item']"
              v-for="item in topNumList"
              :key="item.key"
            >
              <div class="value">
                {{ handleData(item) }}
              </div>
              <div class="label">{{ item.label }}</div>
            </div>
          </div>
        </div>
        <div
          class="map-content"
          id="containerDynamic"
          v-loading="mapLoading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        />
        <div class="bot-note">
          <div class="notes">
            <div
              class="note-item"
              v-for="(item, index) in iconList"
              :key="index"
            >
              <img :src="item.url" /><span>{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="echart-box">
          <div class="high-precision-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">里程分析(总里程：km)</div>
              <div class="img-right"></div>
            </div>
            <Nums class="content-echart" :list="mileageList" :map="dict.car" />
            <div class="item-bottom"></div>
          </div>
        </div>
        <div class="echart-box line">
          <div class="rank-line-item echart-line">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">北斗终端在线/上线排名</div>
              <div class="img-right"></div>
            </div>
            <div class="tabs">
              <div
                :class="['tab', curTab === item.key ? 'active' : '']"
                v-for="item in tabList"
                :key="item.key"
                @click="curTab = item.key"
              >
                {{ item.name }}
              </div>
            </div>
            <rankingLine class="content-line" :list="tabData[curTab]" />
            <div class="item-bottom"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Nums from "./nums.vue";
import LineBox from "./lineBox.vue";
import Bar from "./bar.vue";
import RankingLine from "../base/rankingLine.vue";
import AMapUtil from "@/components/map/AMapUtil";
import {
  onLineTrend,
  alarmStatics,
  onlineTerminal,
  locationTrend,
  targetOdometer
} from "@/api/show/dynamic.js";
import { getDictDetail } from "@/api/security/alarmHistory";

let __map = null;
let __massMarks = null;
const iconList = [
  { name: "定位终端", url: require(`@/assets/images/map/icon01.png`) },
  { name: "穿戴终端", url: require(`@/assets/images/map/icon02.png`) },
  { name: "短报文终端", url: require(`@/assets/images/map/icon05.png`) },
  { name: "监测终端", url: require(`@/assets/images/map/icon04.png`) },
  { name: "授时终端", url: require(`@/assets/images/map/icon03.png`) },
];
export default {
  name: "bigScreenBase",
  components: {
    Nums,
    LineBox,
    Bar,
    RankingLine,
  },
  data() {
    this.topNumList = [
      {
        label: "总数量",
        key: "terminalTotal",
        num: true,
      },
      {
        label: "上线量",
        key: "terminalLinkCount",
        num: true,
      },
      {
        label: "在线量",
        key: "terminalOnlineCount",
        num: true,
      },
      {
        label: "告警量",
        key: "alarmCount",
        num: true,
      },
      {
        label: "上线率",
        key: "terminalLinkRate",
        unit: "%",
      },
      {
        label: "在线率",
        key: "terminalOnlineRate",
        unit: "%",
      },
    ];
    this.iconList = iconList
    return {
      AMap: null, // 高德地图对象
      map: null, // 本页面地图实例
      mapLoading: false,
      numsList: [
        {
          num: 0,
          key: 'total',
          text: "累计数据采集量",
          // note: "点位数据",
        },
        {
          num: 0,
          text: "月度累计采集量",
          key: 'monthTotal'
          // note: "点位数据",
        },
        // {
        //   num: 0,
        //   text: "共享数据量",
        //   // note: "点位数据",
        // },
      ],
      mileageList: [
        {
          num: 0,
          text: "作业车",
        },
        {
          num: 0,
          text: "运输车",
        },
        {
          num: 0,
          text: "铁路货车",
        },
      ],
      tabList: [
        {
          name: "在线量排名",
          key: "online",
        },
        {
          name: "上线量排名",
          key: "goline",
        },
      ],
      curTab: "online",
      totalMap: {},
      trendMap: {
        ups: {},
        ons: {},
      },
      alarmData: [],
      tabData: {
        goline: [],
        online: []
      },
      dict: {car: {}}
    };
  },
  created() {
    this.deptsMap = {}
    this.getDictData()
  },
  mounted() {
    this.mapLoading = true;
    this.getInitData();
    this.$nextTick(() => {
      this.initMap();
      window.addEventListener("fullscreenchange", this.fullscreenchange);
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
    if(__massMarks) {
      __massMarks = null
    }
    if(__map) {
      //解绑地图的点击事件
      // __map.off("click", clickHandler);
      //销毁地图，并清空地图容器
      __map.destroy();
      //地图对象赋值为null
      __map = null
      //清除地图容器的 DOM 元素
      document.getElementById("containerDynamic").remove(); //"container" 为指定 DOM 元素的id
    }
  },
  activated() {
    this.initInterval();
    this.mapReload();
  },
  methods: {
    mapReload () {
      let canvas = document.getElementsByClassName('amap-layer')[0];
      if (canvas) {
        console.log(canvas); // 打印绘制的canvas
        let canvasContent = canvas.getContext('webgl'); // 因为高德地图是通过webGl来绘制的，所以通过getContext(‘webgl’)才能获取到内容
        console.log(canvasContent);
        if (canvasContent?.drawingBufferHeight < 10 && canvasContent?.drawingBufferWidth < 10) {
          this.initMap();
        }
      }
    },
    initInterval() {
      this.timer = setInterval(() => {
        this.getInitData();
      }, 1000 * 30);
      this.$once('hook:deactivated', () => {
        clearInterval(this.timer);
        this.timer = null;
      });
    },
    handleData(item) {
      const value = this.totalMap[item.key];
      if (value) {
        if (item.unit) {
          return value.toFixed(2) + item.unit;
        }
        return value.toLocaleString("en-US");
      }
      return 0;
    },
    getDictData() { 
      getDictDetail("bdm_car_category").then((res) => {
        const list = res.data || []
        this.dict.car = list.reduce((obj, item) => {
          obj[item.value] = item.label
          return obj
        }, {})
      });
    },
    getInitData() {
      onLineTrend().then((res) => {
        const { link, online } = res.data.data
        const ups = link.reduce((obj, item) => {
          obj[item.hourOfDay] = item.onlineCount
          return obj;
        }, {});
        const ons = online.reduce((obj, item) => {
          obj[item.hourLabel] = item.onlineCount
          return obj;
        }, {});
        this.trendMap = {
          ups,
          ons
        }
      });
      alarmStatics().then((res) => {
        const list = res.data.data || [];
        this.alarmData = list.reduce((obj, item) => {
          obj[item.monthDay] = item.alarmCount;
          return obj;
        }, {});
      });
      onlineTerminal().then((res) => {
        this.totalMap = res.data.data || {};
        const { onlineGroupCount, linkGroupCount } = this.totalMap
        if(onlineGroupCount) {
          const gKeys = Object.keys(onlineGroupCount)
          this.tabData.goline = gKeys.map( key => ({deptName: key, count: onlineGroupCount[key]}))
        }
        if(linkGroupCount) {
          const oKeys = Object.keys(linkGroupCount)
          this.tabData.online = oKeys.map( key => ({deptName: key, count: linkGroupCount[key]}))
        }
        this.createMarker();
      });
      locationTrend().then((res) => {
        if(res.data.data) {
          const { harvests, shares} = res.data.data
          if(harvests?.length) {
            Object.keys(harvests[0]).map( key => {
              const item = this.numsList.find( item => item.key === key)
              item.num = harvests[0][key]
            })
          }
        }
      });
      targetOdometer().then((res) => {
        const values = [2, 3, 5]
        if(res.data.data?.length) {
          const list = res.data.data
          this.mileageList = values.reduce( (arr, val) => {
            const item = list.find( item => +item.targetCategory === val)
            if(item) {
              arr.push({num: `${item.totalOdometer}`.split('.')[1] ? item.totalOdometer.toFixed(1) : item.totalOdometer, text: `${item.targetCategory}__`})
            } else {
              arr.push({num: 0, text: `${val}__`})
            }
            return arr
          }, [])
        }
      });
    },
    createMarker() {
      if (this.totalMap.onlineTerminalInfoList?.length && __massMarks) {
        const tList = this.totalMap.onlineTerminalInfoList;
        const allList = tList.reduce((arr, item) => {
          arr.push({
            lnglat: this.$utils.wgs84togcj02(item.longitude, item.latitude),
            style: Number(item.deviceType) - 1,
          });
          return arr;
        }, []);
        allList[0] && __map.setCenter(allList[0].lnglat)
        __massMarks.setData(allList);
      }
    },
    initMap() {
      AMapUtil.loadAMap((AMap) => {
        console.log("-> 初始化地图成功");
        __map = new AMap.Map("containerDynamic", {
          //设置地图容器id
          viewMode: "3D", //是否为3D地图模式
          zoom: 15, //初始化地图级别
          zooms: [4, 18],
          // pitch: 40,
          center: [116.397428, 39.90923], //初始化地图中心点位置
          zoomEnable: true, // 地图是否可缩放
          doubleClickZoom: true, // 地图是否可通过双击鼠标放大地图
          keyboardEnable: true, // 地图是否可通过键盘控制,默认为true
          dragEnable: true, // 地图是否可通过鼠标拖拽平移
          features: ["bg", "point", "road"],
          mapStyle: "amap://styles/darkblue",
          style: "normal",
        });
        this.mapLoading = false;
        const style = iconList.map(({ url }, index) => ({
          url, //图标地址
          anchor: new AMap.Pixel(6, 6), //图标显示位置偏移量，基准点为图标左上角
          size: new AMap.Size(20, 20), //图标的尺寸
          zIndex: index + 2, //每种样式图标的叠加顺序，数字越大越靠前
        }));
        __massMarks = new AMap.MassMarks([], {
          zIndex: 99,
          zooms: [4, 18],
          style, //设置样式对象
        });
        __massMarks.setMap(__map);
        this.createMarker();
        // const scale = new AMap.Scale({
        //   visible: true,
        // });
        // __map.addControl(scale);
      });
    },
    fullscreenchange() {},
  },
};
</script>

<style lang="less" scoped>
.big-screen-base {
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+ */
  user-select: none; /* Standard syntax */
  flex: 1;
  height: 100%;
  background-color: #050912;
  color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  .top-center-title {
    // color: #fff;
    height: 100px;
    line-height: 60px;
    font-weight: 500;
    font-size: 26px;
    text-align: center;
    background: url(../images/top.png) center top/contain no-repeat;
  }
  .content {
    flex: 1;
    text-align: center;
    .echart-box{
      height: 33.33%;
      padding-bottom: 20px;
      &.line{
        height: 66.66%;
      }
      .echart-item,
      .echart-line {
        background-color: rgba(5,9,18, 0.8);
        .item-title {
          padding: 8px 10px;
          background-color: #051e34;
          display: flex;
          .img-left {
            flex: 1;
            background: url(../images/frame01_1.png) left top/contain no-repeat;
          }
          .text {
            text-align: left;
            flex: 6;
          }
          .img-right {
            flex: 1;
            background: url(../images/frame01_2.png) right top/contain no-repeat;
          }
        }
        .item-bottom {
          height: 20px;
          background: url(../images/frame02.png) center top/contain no-repeat;
        }
      }
    }
    .left {
      z-index: 999;
      position: absolute;
      left: 0;
      top: 80px;
      width: 21vw;
      height: calc(100% - 100px);
      overflow-y: auto;
      // background-color: #050912;
      padding: 0px 10px;
      .echart-item {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .content-echart {
          flex: 1;
        }
      }
    }
    .center {
      height: 100%;
      position: relative;
      .ab-top {
        position: absolute;
        margin: 0 auto;
        z-index: 99;
        width: 100%;
        .nums {
          display: flex;
          justify-content: center;
          padding: 10px;
          .nums-item {
            padding: 8px 16px;
            cursor: pointer;
            background-color: #09131d;
            border: 1px solid transparent;
            margin-right: 8px;
            // &.active {
            //   background-color: #2b6f94;
            //   border: 1px solid #3083af;
            // }
            .value {
              font-size: 30px;
              font-weight: bold;
              color: #f5bd34;
            }
            .label {
              font-size: 16px;
              color: #fff;
            }
          }
        }
      }
      .map-content {
        width: 100%;
        height: 100%;
        background-color: #050912;
      }
      .bot-note{
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        .notes {
          display: flex;
          justify-content: center;
          height: 40px;
          .note-item {
            display: flex;
            align-items: center;
            padding: 5px 10px;
          }
        }
      }
    }
    .right {
      z-index: 999;
      position: absolute;
      right: 0;
      top: 80px;
      width: 21vw;
      height: calc(100% - 100px);
      overflow-y: auto;
      // background-color: #050912;
      padding: 0px 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .echart-item,
      .echart-line {
        height: 100%;
        .tabs {
          display: flex;
          justify-content: center;
          padding: 20px 10px 10px 10px;
          background-color: rgba(5,9,18, 0.8);
          .tab {
            padding: 8px 16px;
            cursor: pointer;
            &.active {
              border-color: #1c80a2;
              background-color: #1f5486;
              color: #9be0ff;
            }
          }
        }
        display: flex;
        flex-direction: column;
        .content-echart {
          flex: 1;
        }
      }
    }
  }
}
</style>
