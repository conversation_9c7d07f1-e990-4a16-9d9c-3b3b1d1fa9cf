<template>
  <div>
    <basic-container>
      <avue-form :option="option"
                 v-model="form"
                 @tab-click="handleTabClick"
                 @submit="handleSubmit"></avue-form>
    </basic-container>
  </div>
</template>

<script>
import option from '@/option/user/info'
import { getUserInfo, updateInfo, updatePassword } from '@/api/system/user'
import md5 from 'js-md5'
import func from '@/util/func'


export default {
  data() {
    return {
      index: 0,
      option: option,
      form: {}
    }
  },
  created() {
    this.handleWitch()
  },
  methods: {
    handleSubmit(form, done) {
      if (form.newPassword && form.newPassword.trim().length > 0) {
        this.index = 1
      } else {
        this.index = 0
      }
      // 输入新密码时判断为修改密码请求
      if(form.newPassword) {
        if(!form.oldPassword) {
          this.$message.error('修改密码时请输入旧密码')
          done()
          return false
        }
        if(form.newPassword !== form.newPassword1) {
          this.$message.error('两次密码不一致')
          done()
          return false
        }
      }
      // 修改密码时先调用修改密码接口再调用修改信息接口
      if(this.index === 1) {
        updatePassword(md5(form.oldPassword), md5(form.newPassword), md5(form.newPassword1)).then(res => {
          if (res.data.success) {
            // this.$message({
            //   type: 'success',
            //   message: '修改密码成功!'
            // })
            updateInfo(form).then(res => {
              if (res.data.success) {
                this.$message({
                  type: 'success',
                  message: '修改信息成功!'
                })
              }
              else {
                this.$message({
                  type: 'error',
                  message: res.data.msg
                })
              }
              done()
            }, error => {
              window.console.log(error)
              done()
            })
            if (!form.newPassword1) {
              form.newPassword1 = ''
            }
          }
          else {
            this.$message({
              type: 'error',
              message: res.data.msg
            })
          }
          done()
        }, error => {
          window.console.log(error)
          done()
        })
      } else {
        // 单独修改信息只调用修改信息接口
        updateInfo(form).then(res => {
          if (res.data.success) {
            this.$message({
              type: 'success',
              message: '修改信息成功!'
            })
          }
          else {
            this.$message({
              type: 'error',
              message: res.data.msg
            })
          }
          done()
        }, error => {
          window.console.log(error)
          done()
        })
        if (!form.newPassword1) {
          form.newPassword1 = ''
        }
      }
    },
    handleWitch() {
      if (this.index === 0) {
        getUserInfo().then(res => {
          const user = res.data.data
          this.form = {
            id: user.id,
            avatar: user.avatar,
            name: user.name,
            realName: user.realName,
            phone: user.phone,
            email: user.email
          }
        })
      }
    },
    handleTabClick(tabs) {
      this.index = func.toInt(tabs.index)
      this.handleWitch()
    }
  }
}
</script>

<style>
</style>
