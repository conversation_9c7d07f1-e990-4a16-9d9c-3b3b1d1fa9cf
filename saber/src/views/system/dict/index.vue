<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :head-config="headConfig"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
          @row-click="handleSetting"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','dict:edit','dict:del', 'dict:setting']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click.native.stop="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-permission="permission.setting"
                    type="text"
                    size="small"
                    @click.native.stop="handleSetting(scope.row)"
                  >
                    字典配置
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 字典编号 -->
          <el-table-column
            v-if="columns.visible('code')"
            :label="getLabel('code')"
            prop="code"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tag>{{ scope.row.code }}</el-tag>
            </template>
          </el-table-column>
          <!-- 字典名称 -->
          <el-table-column
            v-if="columns.visible('dictValue')"
            :label="getLabel('dictValue')"
            prop="dictValue"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 字典排序 -->
          <el-table-column
            v-if="columns.visible('sort')"
            :label="getLabel('sort')"
            prop="sort"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 封存 -->
          <el-table-column
            v-if="columns.visible('isSealed')"
            :label="getLabel('isSealed')"
            prop="isSealed"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tag>{{ scope.row.isSealed === 0 ? '否' : '是' }}</el-tag>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :is-detail.sync="isDetail"
      />
      <DictSetting
        ref="dictSetting"
        :dialog-visible.sync="dialogSettingVisible"
        :parent-id="parentId"
        :dict-code="dictCode"
        :dialog-title="dialogTitle"
      />
    </div>
  </basic-container>
</template>

<script>
import crudDict from '@/api/system/dictNew';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import DictSetting from './module/dictSetting';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Dict', 'uniName'), // 系统字典
  crudMethod: { ...crudDict }
});

export default {
  name: 'Dict',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon, DictSetting },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'dict:add'],
        edit: ['admin', 'dict:edit'],
        del: ['admin', 'dict:del'],
        view: ['admin', 'dict:view'],
        setting: ['admin', 'dict:setting']
      },
      headConfig: {
        item: {
          1: {
            name: '字典编号',
            type: 'input',
            value: 'code',
          },
          2: {
            name: '字典名称',
            type: 'input',
            value: 'dictValue',
          }
        },
        button: {
        }
      },
      isDetail: false,
      dialogSettingVisible: false,
      parentId: null,
      dialogTitle: '',
      dictCode: null
    };
  },
  methods: {
    handleSetting (row) {
      this.parentId = row.id;
      this.dialogTitle = row.dictValue;
      this.dictCode = row.code;
      this.dialogSettingVisible = true;
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Dict', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Dict', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
