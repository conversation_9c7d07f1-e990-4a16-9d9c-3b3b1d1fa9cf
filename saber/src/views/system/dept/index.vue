<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="90px"
        >
          <template slot="search-left">
            <el-button
              class="filter-item"
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchClick"
            >查 询
            </el-button>
            <slot name="center" />
            <el-button
              class="filter-item"
              size="small"
              icon="el-icon-refresh-right"
              @click="clearClick"
            >重 置
            </el-button>
          </template>
        </HeadCommon>
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
          :hasToggleCol="false"
        >
          <template slot="right">
            <el-button
              v-permission="permission.add"
              class="filter-item"
              size="small"
              type="primary"
              icon="el-icon-plus"
              @click="addItem"
            >
              新 增
            </el-button>
            <el-button
              :loading="crud.delAllLoading"
              v-permission="permission.del"
              class="filter-item"
              icon="el-icon-delete"
              size="small"
              @click="toDelete"
            >
              删 除
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <u-table
          ref="table"
          fixed-columns-roll
          header-drag-style
          :treeConfig="{
            children: 'children',
            expandAll: false,
            lazy: true,
            load: treeLoad,
            hasChildren: 'hasChildren'}"
          use-virtual
          row-height="54"
          row-id="id"
          :border="false"
          :height="tableHeight"
          @selection-change="handleSelect"
        >
          <u-table-column
            type="selection"
            width="50"
          />
          <u-table-column
            v-if="columns.visible('id')"
            label="机构编码"
            :tree-node="true"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="id"
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('deptName')"
            label="机构名称"
            :tree-node="true"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="deptName"
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('shortName')"
            label="机构简称"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="shortName"
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('deptCategory')"
            label="机构类型"
            :show-overflow-tooltip="true"
            min-width="100"
            prop="deptCategory"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ getEnumDictLabel('orgCategory', scope.row.deptCategory) }}
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('sort')"
            label="排序"
            :show-overflow-tooltip="true"
            width="120"
            prop="sort"
            :resizable="false"
          />
          <u-table-column
            label="操作"
            width="180"
            align="center"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                :hideEdit="true"
                :hideDel="true"
              >
                <template slot="right">
                  <el-button
                    v-permission="permission.edit"
                    class="table-button-edit"
                    size="small"
                    type="text"
                    @click="toEdit(scope.row, true)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-permission="permission.del"
                    :loading="crud.delAllLoading"
                    :disabled="scope.row.parentId === '0'"
                    class="table-button-del"
                    type="text"
                    size="small"
                    @click="toDelete([scope.row])"
                  >
                    删除
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toEdit(scope.row)"
                  >
                    新增子项
                  </el-button>
                </template>
              </udOperation>
            </template>
          </u-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </u-table>
      </div>
      <formOther
        :dict="dict"
        :regTurn="regTurn"
        :regTurnM="regTurnM"
        v-model="showModal"
        :curInfo="curInfo"
        :isEdit="isEdit"
        :btnShow.sync="btnShow"
        @refreshAdd="refreshAdd"
      />
    </div>
  </basic-container>
</template>

<script>
import crudCompany, { getLazyList, del } from '@/api/system/deptNew';
import formOther from './module/formOther';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import HeadCommon from '@/components/formHead/headCommon.vue';
import { getDictionary } from '@/api/system/dictNew'
import { email, phoneNum } from "@/utils/validate";
// crud交由presenter持有
const crud = CRUD({
  title: '',
  crudMethod: { ...crudCompany },
  optShow: {}
});

export default {
  name: 'DeptIndex',
  components: {
    crudOperation,
    udOperation,
    HeadCommon,
    formOther
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [],
  data () {
    this.regTurn = {
      id: /^[0-9]+$/,
      businessCertificate: /^[a-zA-Z0-9]+$/,
      concatMail: email,
      concatPhone: phoneNum,
    };
    this.regTurnM = {
      id: '机构编码只包含数字',
      businessCertificate: '经营许可证只能输入字母和数字',
      concatMail: '邮箱不正确',
      concatPhone: '联系电话不正确',
    };
    return {
      permission: {
        add: [
          'admin',
          'testCompany:add'
        ],
        edit: [
          'admin',
          'testCompany:edit'
        ],
        del: [
          'admin',
          'testCompany:del'
        ]
      },
      headConfig: {
        initQuery: false,
        item: {
          1: {
            name: '机构编码',
            type: 'input',
            value: 'id',
            span: 6
          },
          2: {
            name: '机构名称',
            type: 'input',
            value: 'deptName',
            span: 6
          },
          3: {
            name: '机构简称',
            type: 'input',
            value: 'shortName',
            span: 6
          },
        },
        button: {
          query: false,
          clear: false
        }
      },
      btnShow: true, // 显示确认取消按钮
      isEdit: false, //是否是点击了编辑按钮
      curInfo: {},
      tableKey: 'tableKey',
      showModal: false,
      selections: [],
      tableHeight: 500
    };
  },
  created () {
    this.treeLoadMap = {}
    getDictionary({ code: 'org_category' }).then(res => {
      let list = res.data || []
      const obj = {}
      if (list.length) {
        list = list.map(item => {
          obj[item.dictKey] = item.dictValue
          return { value: item.dictKey, label: item.dictValue }
        })
      }
      this.$set(this.dict, 'orgCategory', list);
      this.$set(this.dict.dict, 'orgCategory', obj);
    })
    this.getInitData()
  },
  mounted() {
    this.getTableHeight();
  },
  beforeDestroy () {
    this.crud.resetQuery(false);
  },
  methods: {
    getTableHeight(){
      this.$nextTick(() => {
        const containerHeight = document.querySelector('.xh-crud-table-container').offsetHeight;
        this.tableHeight = containerHeight - 60;
      });
    },
    getInitData () {
      this.treeLoadMap = {}
      getLazyList(Object.keys(crud.query).length ? '' : 0, crud.query).then(res => {
        this.$refs.table.reloadData(res)
      });
    },
    clearClick () {
      this.crud.resetQuery();
      this.getInitData()
    },
    searchClick () {
      this.getInitData()
    },
    handleSelect(rows) {
      this.selections = rows
    },
    refreshAdd (data, type) {
      switch (type) {
        case '新增':
          this.handleAdd(data)
          break;
        default:
        this.getInitData(0)
          break;
      }
    },
    handleAdd(data) {
      if(!data.parentId || data.parentId=== '0') {
        // 最外层
        this.getInitData(0)
      } else if(this.treeLoadMap[data.parentId]){
        // 以展开就加载数据
        const { row, resolve } = this.treeLoadMap[data.parentId]
        const list = row.children || []
        list.push(data)
        resolve(list)
      } else {
        this.getInitData(0)
      }
    },
    addItem () {
      this.curInfo = {}
      this.showModal = true;
      this.isEdit = false;
    },
    toDetails (param) {
      this.showModal = true
      this.btnShow = false;
      this.isEdit = false;
      this.curInfo = param
    },
    toEdit (param, all) {
      this.showModal = true;
      this.isEdit = true;
      this.curInfo = all ? param : {id: param.id}
    },
    toDelete (list) {
      const datas = Array.isArray(list) ? list : this.selections;
      if (!datas.length) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm(`确定将选择数据删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.crud.delAllLoading = true;
        del(datas.map( item => item.id)).then( res => {
          this.crud.delAllLoading = false;
          this.$message.success(res.data.msg);
          this.getInitData()
        }).catch(() => {
          this.crud.delAllLoading = false;
        });
      })
    },
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value]
      }
      else {
        return '';
      }
    },
    cancel () {
      this.btnShow = true;
    },
    treeLoad (row, resolve) {
      this.treeLoadMap[row.id] = { row, resolve }
      const parentId = row.id;
      getLazyList(parentId).then(res => {
        resolve(res);
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}

.xh-container
  /deep/
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: #fcf0c1;
}
/deep/.plTableBox .el-table__body-wrapper {
  overflow-x: hidden !important;
}
</style>
