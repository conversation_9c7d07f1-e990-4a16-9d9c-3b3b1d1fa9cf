<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="value"
    :title="dialogTittle"
    append-to-body
    width="50%"
    :before-close="handleCancel"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="150px"
      class="rewriting-form-disable"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="机构编码"
            prop="id"
          >
            <el-input
              v-model.trim="form.id"
              placeholder="请输入机构编码"
              maxlength="10"
              :disabled="!btnShow || isEdit"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="机构名称"
            prop="deptName"
          >
            <el-input
              v-model.trim="form.deptName"
              placeholder="请输入机构名称"
              maxlength="30"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="机构简称"
            prop="shortName"
          >
            <el-input
              v-model.trim="form.shortName"
              placeholder="请输入机构全称"
              maxlength="30"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="上级机构"
            prop="parentId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-if="Object.keys(curInfo).length !== 1 && btnShow && !isParent"
              v-model="form.parentId"
              :no-req="!btnShow || Object.keys(curInfo).length === 1"
              :detail-name="detailName"
              :dept-list="deptList"
              :disabled="!btnShow || Object.keys(curInfo).length === 1"
              :is-show="value"
              placeholder="请选择上级机构"
              size="small"
            />
            <el-input
              v-else
              :disabled="true"
              :value="detailName"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="机构类型"
            prop="deptCategory"
          >
            <el-select
              v-model="form.deptCategory"
              placeholder="请选择机构类型"
              size="small"
              :disabled="!btnShow"
            >
              <el-option
                v-for="item in dict.orgCategory"
                :key="item.value"
                :label="item.label"
                :value="+item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="经营许可证"
            prop="businessCertificate"
          >
            <el-input
              v-model.trim="form.businessCertificate"
              placeholder="请输入经营许可证"
              maxlength="20"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="注册地"
            prop="region"
          >
            <el-input
              v-model.trim="form.region"
              placeholder="请输入注册地"
              maxlength="30"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="行政区划"
            prop="areaCode"
          >
            <el-input
              v-model.trim="form.areaCode"
              placeholder="请输入行政区划"
              maxlength="50"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="联系电话"
            prop="concatPhone"
          >
            <el-input
              v-model.trim="form.concatPhone"
              placeholder="请输入联系电话"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="联系邮箱"
            prop="concatMail"
          >
            <el-input
              v-model.trim="form.concatMail"
              placeholder="请输入联系邮箱"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="排序"
            prop="sort"
          >
            <el-input-number
              v-model="form.sort"
              style="width: 100%"
              :precision="0"
              controls-position="right"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="备注"
            prop="remark"
          >
            <el-input
              v-model.trim="form.remark"
              placeholder="请输入备注"
              maxlength="255"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
      v-if="btnShow"
    >
      <el-button
        size="small"
        @click="handleCancel"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import { getDept } from '@/api/system/dept';
import crudCompany, { add, edit } from '@/api/system/deptNew';

export default {
  components: {
    DeptFormSingleSelect
  },
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    regTurn: {
      type: Object,
      default: () => {
        return {};
      },
    },
    regTurnM: {
      type: Object,
      default: () => {
        return {};
      },
    },
    btnShow: {
      type: Boolean,
      default: true
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    curInfo: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Boolean,
      default: false
    },
  },
  data () {
    return {
      rules: {
        deptName: {
          required: true,
          trigger: 'blur'
        },
        shortName: {
          required: true,
          trigger: 'blur'
        },
        deptCategory: {
          required: true,
          trigger: 'blur'
        },
        businessCertificate: {
          required: true,
          trigger: 'blur'
        },
        sort: {
          required: true,
          trigger: 'blur'
        },
        parentId: {
          required: true,
          trigger: 'change'
        }
      },
      form: {
        id: '',
        deptName: '',
        shortName: '',
        parentId: '',
        parentName: '',
        deptCategory: '',
        businessCertificate: '',
        region: '',
        areaCode: '',
        concatPhone: '',
        concatMail: '',
        sort: '',
        remark: '',
      },
      detailName: '',
      deptList: [],
      isParent: false
    };
  },
  computed: {
    dialogTittle() {
      const isCurrentRow = Object.keys(this.curInfo).length > 1
      if(isCurrentRow && this.btnShow) {
        return '编辑'
      } else if(!this.btnShow) {
        return '详情'
      } else {
        return '新增'
      }
    }
  },
  watch: {
    value() {
      if(this.value) {
        if (this.curInfo.id) {
        getDept(this.curInfo.id).then(res => {
          const isCurrentRow = Object.keys(this.curInfo).length > 1
          const data= res.data.data;
          if(isCurrentRow) {
            this.form = data;
          } else {
            this.form.parentId = data.id
            this.form.parentName = data.deptName
          }
          if(!(['编辑', '详情'].includes(this.dialogTittle) && (!this.form.parentId || this.form.parentId === '0'))) {
            // 编辑顶层不需要
            this.detailName = this.form.parentName;
            this.$refs['deptIdRef'].setTreeValue(this.detailName);
          } else { // 父级节点点击编辑详情时
            this.isParent = true;
          }
        })
        }
      } else {
        this.detailName = ''
      }
    }
  },
  methods: {
    handleCancel () {
      this.$refs.form.resetFields();
      this.isParent = false;
      this.$emit('input', false);
      this.$emit('update:btnShow', true);
    },
    handleSubmit () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const list = Object.keys(this.regTurn);
          for (let i = 0; i < list.length; i++) {
            const key = list[i];
            if (this.form[key] && !this.regTurn[key].test(this.form[key])) {
              this.$message.error(this.regTurnM[key]);
              return
            }
          }
          const isCurrentRow = Object.keys(this.curInfo).length > 1
          const fun = this.curInfo.id && isCurrentRow ? edit : add
          const params = this.form
          if(isCurrentRow) {
            params.id = this.curInfo.id
          }
          fun(params).then( res => {
            if(!params.id) {
              params.id = res.data.id
            }
            this.$emit('refreshAdd', JSON.parse(JSON.stringify(params)), this.dialogTittle)
            this.handleCancel()
          })
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}
</style>
