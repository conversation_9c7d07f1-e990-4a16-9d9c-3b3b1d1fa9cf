<template>
  <div class="terminalConfiguration">
    <div class="car-list media-terminal-tree-container">
      <VehicleMultiSelectAllTree
        ref="VehicleMultiSelectAllTree"
        :is-show="true"
        @checkedVehiclesChange="checkedVehiclesChange"
        @treeNodeClick="treeNodeClick"
      />
    </div>
    <el-tabs
      v-model="activeName"
      type="card"
      class="car-content"
      @tab-click="handleClick"
    >
      <el-tab-pane
        label="参数配置"
        name="first"
      >
        <BaseConfig
          :phones="phones"
          :car-pick="carPick"
          :dict="dict"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="MQTT协议终端下发指令"
        name="thirteenth"
      >
        <MqttConfig
          :phones="phones"
          :dict="dict"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="高级驾驶辅助系统参数"
        name="second"
      >
        <AssistSeniorConfig
          ref="assistSeniorConfig"
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="驾驶员状态监测系统参数"
        name="third"
      >
        <StateMonitorConfig
          ref="stateMonitorConfig"
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="音视频参数设置"
        name="fourth"
      >
        <AudioAndVideoConfig
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="激烈驾驶参数"
        name="fifth"
      >
        <IntenseDriveConfig
          ref="intenseDriveConfig"
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="行驶路况检测功能参数"
        name="sixth"
      >
        <RoadConditionConfig
          ref="roadConditionConfig"
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="音视频通道列表设置"
        name="seventh"
      >
        <ChannelListConfig
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="单独通道视频参数设置"
        name="eighth"
      >
        <SingleChannelConfig
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="特殊告警录像参数设置"
        name="ninth"
      >
        <SpecialAlarmConfig
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="音视频属性查询"
        name="tenth"
      >
        <AudioVideoPropertyConfig :car-pick="carPick" />
      </el-tab-pane>
      <el-tab-pane
        label="终端控制"
        name="eleventh"
      >
        <VehicleControl
          :phones="phones"
          :car-pick="carPick"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
      <el-tab-pane
        label="特殊告警阈值参数设置"
        name="twelfth"
      >
        <SpecialThresholdConfig
          :phones="phones"
          :change-vehicle="changeVehicle"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import VehicleMultiSelectAllTree from '@/components/select/VehicleMultiSelectTree/VehicleMultiSelectAllTree';
import BaseConfig from './baseConfig/index.vue';
import AssistSeniorConfig from './assistSeniorConfig/index.vue';
import StateMonitorConfig from './stateMonitorConfig/index.vue';
import AudioAndVideoConfig from './audioAndVideoConfig/index.vue';
import IntenseDriveConfig from './intenseDriveConfig/index.vue';
import RoadConditionConfig from './roadConditionConfig/index.vue';
import ChannelListConfig from './channelListConfig/index.vue';
import SingleChannelConfig from './singleChannelConfig/index.vue';
import SpecialAlarmConfig from './specialAlarmConfig/index.vue';
import AudioVideoPropertyConfig from './audioVideoPropertyConfig/index.vue';
import VehicleControl from './vehicleControl/index.vue';
import SpecialThresholdConfig from './specialThresholdConfig/index.vue';
import MqttConfig from './mqttConfig/index.vue';
export default {
  name: 'TerminalConfiguration',
  components: {
    VehicleMultiSelectAllTree,
    BaseConfig,
    AssistSeniorConfig,
    StateMonitorConfig,
    AudioAndVideoConfig,
    IntenseDriveConfig,
    RoadConditionConfig,
    ChannelListConfig,
    SingleChannelConfig,
    SpecialAlarmConfig,
    AudioVideoPropertyConfig,
    VehicleControl,
    SpecialThresholdConfig,
    MqttConfig
  },
  dicts: [
    'targetModel'
  ],
  data () {
    return {
      phones: [],
      activeName: 'first',
      carPick: [],
      searchList: [
        { label: 'second', value: 'assistSeniorConfig' },
        { label: 'third', value: 'stateMonitorConfig' },
        { label: 'fifth', value: 'intenseDriveConfig' },
        { label: 'sixth', value: 'roadConditionConfig' },
      ],
      changeVehicle: null
    };
  },
  methods: {
    // 节点被点击时
    treeNodeClick(data) {
      if (data.type === undefined) {
        this.changeVehicle = {
          targetName: data.name,
          deviceId: data.id,
          deviceType: data.deviceType
        };
        this.$nextTick(() => {
          const result = this.searchList.find(item => item.label === this.activeName);
          if (result) {
            this.$refs[result.value].searchHandle(true);
          }
        });
      } else {
        this.changeVehicle = null;
      }
    },
    // 切换tab页
    handleClick (tab, event) {
      console.log(tab, event);
      const result = this.searchList.find(item => item.label === this.activeName);
      if (result && this.changeVehicle) {
        this.$refs[result.value].searchHandle(true);
      }
    },
    // 选择车辆
    checkedVehiclesChange (data) {
      const result = data.map(item => ({
        targetName: item.name,
        deviceId: item.id,
        deviceType: item.deviceType
      }));
      this.phones = result;
      this.carPick = result;
    }
  }

};
</script>
<style lang="less" scoped>
.terminalConfiguration{
    display: flex;
    flex-direction: row;
    height: 100%;
    // padding: 0 5px;
  }
.car-list{
    width: 450px;
    background-color: @xhBackgroundColor2;
    margin-right: 8px;
    border-radius: @xhSpacingBase;
    box-shadow: @xh-button-shadow;
  }
.car-content{
    flex: 1;
    border-radius: @xhSpacingBase;
    background: #ffffff;
    overflow-y: auto;
  }
</style>
