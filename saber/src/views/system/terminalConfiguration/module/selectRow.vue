<template>
  <div class="layout">
    <div class="content">
      <div class="option">
        {{ getLabel(label) }}
      </div>
      <div class="value">
        <el-select
          v-model="value"
          placeholder="请选择内容"
          :multiple="multiple"
          :collapse-tags="collapseHandle()"
          @change="selectChange"
        >
          <el-option
            v-for="(item,index) in options"
            :key="index"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </div>
      <div
        v-show="!rowSet.delCh"
        class="tick"
      >
        <el-checkbox
          v-model="checked"
          class="tick-box"
          @change="checkboxChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import getLabel from '@/utils/getLabel';
export default {
  props: {
    label: {
      type: [Number, String],
      default: ''
    },
    initValue: {
      type: [Number, String, Object, Array],
      default: ''
    },
    options: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    checkedFlag: {
      type: [Number, String, Object, Array],
      default: () => {
        return {};
      }
    },
    multiple: {
      type: [Boolean],
      default: false
    },
    rowSet: {
      type: [Object],
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      value: undefined,
      checked: true
    };
  },
  watch: {
    initValue: { // 深度监听，可监听到对象、数组的变化
      handler (newV, oldV) {
        this.getInitValue(newV);
      },
      deep: true,
      immediate: true
    },
    checkedFlag: { // 深度监听，可监听到对象、数组的变化
      handler (newV, oldV) {
        this.checked = newV.value;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化
    getInitValue (v) {
      this.value = v;
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('terminalConfiguration', value);
    },
    // 选项改变
    selectChange (v) {
      this.$emit('formChange', {
        key: this.label,
        value: v
      });
    },
    // 勾选改变
    checkboxChange (v) {
      this.$emit('formCheckedChange', {
        index: this.checkedFlag.index,
        key: this.checkedFlag.key,
        value: v
      });
    },
    // 多选时判断是否收缩文字
    collapseHandle () {
      if (this.multiple && this.value && this.value.length > 2) {
        return true;
      } else {
        return false;
      }
    }
  }
};
</script>

<style src="./rowForm.css" scoped>

</style>
