// type默认input number类型，输入类型i，单选r，选择m/s
export const typeDms = [
  {
    field: 'speedThreshold',
    range: [0, 60],
    tip: '单位km/h 取值范围0~60，默认30',
    unit: 'km/h'
  }, {
    field: 'tipVolume',
    range: [0, 8],
    tip: '0~8，默认6'
  }, {
    field: 'photoStrategy',
    type: 'r',
    options: [{k: 0, v: '不开启'}, {k: 1, v: '定时拍照'}, {k: 2, v: '定距拍照'}, {k: 3, v: '保留'}]
  }, {
    field: 'photoTimeInterval',
    range: [0, 3600],
    tip: '单位秒，取值范围0~3600，默认60',
    unit: '秒'
  }, {
    field: 'photoDistanceInterval',
    range: [0, 60000],
    tip: '单位米，取值范围0~60000，默认200',
    unit: '米'
  }, {
    field: 'photoNums',
    range: [1, 10],
    tip: '取值范围1-10，默认3张',
    unit: '张'
  }, {
    field: 'photoInterval',
    range: [1, 100],
    tip: '单位100ms,取值范围1~5，默认2',
    unit: '100ms'
  }, {
    field: 'driverReg',
    type: 'r', // input
    options: [{k: 0, v: '不开启'}, {k: 1, v: '定时触发'}, {k: 2, v: '定距触发'}, {k: 3, v: '插卡开始行驶触发'}, {k: 4, v: '保留'}]
  }, {
    field: 'photoResolution',
    type: 'r', // input
    options: [{k: 1, v: '352×288'}, {k: 2, v: '704×288'}, {k: 3, v: '704×576'}, {k: 4, v: '640×480'}, {k: 5, v: '1280×720'}, {k: 6, v: '1920×1080'}]
  }, {
    field: 'videoResolution',
    type: 'r', // input
    options: [{k: 1, v: 'CIF'}, {k: 2, v: 'HD1'}, {k: 3, v: 'D1'}, {k: 4, v: 'WD1'}, {k: 5, v: 'VGA'}, {k: 6, v: '720P'}, {k: 7, v: '1080P'}]
  }, {
    field: 'alarmEnable',
    type: 'm', // input
    options: [{k: '0', v: '疲劳驾驶一级告警'}, {k: '1', v: '疲劳驾驶二级告警'}, {k: '2', v: '接打手持电话一级告警'}, {k: '3', v: '接打手持电话二级告警'},
      {k: '4', v: '抽烟一级告警'}, {k: '5', v: '抽烟二级告警'}, {k: '6', v: '长时间不目视前方一级告警'}, {k: '7', v: '长时间不目视前方二级告警'},
      {k: '8', v: '未检测到驾驶员一级告警'}, {k: '9', v: '未检测到驾驶员二级告警'}
    ]
  }, {
    field: 'eventEnable',
    type: 'm', // input
    options: [{k: '0', v: '驾驶员变更事件'}, {k: '1', v: '主动拍照事件'}],
    margin: true
  }, {
    field: 'tirePhotoNums',
    range: [0, 10],
    tip: '取值范围0-10，缺省值3,0表示不抓拍',
    unit: '张'
  }, {
    field: 'tirePhotoInterval',
    range: [0, 10],
    tip: '单位100ms，取值范围1~5，默认2',
    unit: '100ms'
  }, {
    field: 'tireVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5,0表示不录像',
    unit: '秒'
  }, {
    field: 'tireSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发告警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h',
    margin: true
  }, {
    field: 'abnormalPhotoNums',
    range: [1, 10],
    tip: '取值范围1-10，默认值3,0表示不抓拍',
    unit: '张'
  }, {
    field: 'abnormalInterval',
    range: [1, 5],
    tip: '单位100ms，取值范围1~5，默认2',
    unit: '100ms'
  }, {
    field: 'abnormalVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5,0表示不录像',
    unit: '秒'
  }, {
    field: 'abnormalSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发报,警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h',
    margin: true
  }, {
    field: 'phoneInterval',
    range: [0, 3600],
    tip: '单位秒，取值范围0~3600。默认值为120。表示在此时间间隔内仅触发一次接打电话告警',
    unit: '秒'
  }, {
    field: 'phoneDriverNums',
    range: [1, 10],
    tip: '取值范围1-10，默认值3,0表示不抓拍',
    unit: '张'
  }, {
    field: 'phoneDriverInterval',
    range: [1, 5],
    tip: '单位100ms，取值范围1~5，默认值2',
    unit: '100ms'
  }, {
    field: 'phoneVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5，0表示不录像',
    unit: '秒'
  }, {
    field: 'phoneSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发告警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h',
    margin: true
  }, {
    field: 'attentionPhotoNums',
    range: [1, 10],
    tip: '取值范围1-10，默认值3,0表示不抓拍',
    unit: '张'
  }, {
    field: 'attentionInterval',
    range: [1, 5],
    tip: '单位100ms，取值范围1~5，默认2',
    unit: '100ms'
  }, {
    field: 'attentionVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值50表示不录像',
    unit: '秒'
  }, {
    field: 'attentionSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发告警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h',
    margin: true
  }, {
    field: 'smokeInterval',
    range: [0, 3600],
    tip: '单位秒，取值范围0~3600。默认值为180',
    unit: '秒'
  }, {
    field: 'smokePhotoNums',
    range: [1, 10],
    tip: '取值范围1-10，默认值3,0表示不抓拍',
    unit: '张'
  }, {
    field: 'smokeDriverInterval',
    range: [1, 5],
    tip: '单位100ms，取值范围1~5，默认2',
    unit: '100ms'
  }, {
    field: 'smokeVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5,0表示不录像',
    unit: '秒'
  }, {
    field: 'smokeSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发告警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h'
  }
];

/* 2 */

export const typeAdas = [
  {
    field: 'speedThreshold',
    range: [0, 60],
    tip: '单位km/h 取值范围0~60，默认30',
    unit: 'km/h'
  }, {
    field: 'tipVolume',
    range: [0, 8],
    tip: '0~8，默认6'
  }, {
    field: 'photoStrategy',
    type: 'r',
    options: [{k: 0, v: '不开启'}, {k: 1, v: '定时拍照'}, {k: 2, v: '定距拍照'}, {k: 3, v: '保留'}]
  }, {
    field: 'photoTimeInterval',
    range: [0, 3600],
    tip: '单位秒，取值范围0~3600，默认60',
    unit: '秒'
  }, {
    field: 'photoDistanceInterval',
    range: [0, 60000],
    tip: '单位米，取值范围0~60000，默认200',
    unit: '秒'
  }, {
    field: 'photoNums',
    range: [1, 10],
    tip: '取值范围1-10，默认3张',
    unit: '张'
  }, {
    field: 'photoInterval',
    range: [1, 100],
    tip: '单位100ms,取值范围1~5，默认2',
    unit: '100ms'
  }, {
    field: 'photoResolution',
    type: 'r', // input
    options: [{k: 1, v: '352×288'}, {k: 2, v: '704×288'}, {k: 3, v: '704×576'}, {k: 4, v: '640×480'}, {k: 5, v: '1280×720'}, {k: 6, v: '1920×1080'}]
  }, {
    field: 'videoResolution',
    type: 'r', // input
    options: [{k: 1, v: 'CIF'}, {k: 2, v: 'HD1'}, {k: 3, v: 'D1'}, {k: 4, v: 'WD1'}, {k: 5, v: 'VGA'}, {k: 6, v: '720P'}, {k: 7, v: '1080P'}]
  }, {
    field: 'alarmEnable',
    type: 'm', // input
    options: [{k: '0', v: '障碍检测一级告警'}, {k: '1', v: '障碍检测二级告警'}, {k: '2', v: '频繁变道一级告警'}, {k: '3', v: '频繁变道二级告警'},
      {k: '4', v: '车道偏离一级告警'}, {k: '5', v: '车道偏离二级告警'}, {k: '6', v: '前向碰撞一级告警'}, {k: '7', v: '前向碰撞二级告警'},
      {k: '8', v: '行人碰撞一级告警'}, {k: '9', v: '行人碰撞二级告警'}, {k: '10', v: '车距过近一级告警'}, {k: '11', v: '车距过近二级告警'}, {k: '16', v: '道路标识超限告警'}
    ]
  }, {
    field: 'eventEnable',
    type: 'm', // input
    options: [{k: '0', v: '道路标识识别'}, {k: '1', v: '主动拍照'}],
    margin: true
  }, {
    field: 'obstacleDisThreshold',
    range: [10, 50],
    tip: '单位 100ms，取值范围 10-50，默认值 30',
    unit: '100ms'
  }, {
    field: 'obstacleSpeedThreshold',
    range: [0, 220],
    tip: '单位 km/h，取值范围 0~220，默认值 50。表示触发告警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h'
  }, {
    field: 'obstacleVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围 0-60，默认值 5， 0 表示不录像',
    unit: '秒'
  }, {
    field: 'obstaclePhotoNums',
    range: [0, 10],
    tip: '取值范围 0-10，默认值 3， 0 表示不抓拍',
    unit: '张'
  }, {
    field: 'obstaclePhotoInterval',
    range: [1, 10],
    tip: '单位 100ms，取值范围 1~10，默认值 2',
    unit: '100ms',
    margin: true
  }, {
    field: 'oftenWayDuration',
    range: [30, 120],
    tip: '单位秒，取值范围 30~120，默认值 60',
    unit: '秒'
  }, {
    field: 'oftenWayTimes',
    range: [3, 10],
    tip: '变道次数 3~10，默认 5',
    unit: '次'
  }, {
    field: 'oftenWaySpeedThreshold',
    range: [0, 220],
    tip: '单位 km/h，取值范围 0~220，默认值 50，表示触发告警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h'
  }, {
    field: 'oftenWayVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5，0表示不录像单位秒，取值范围0-60，默认值5，0表示不录像',
    unit: '秒'
  }, {
    field: 'oftenWayPhotoNums',
    range: [0, 10],
    tip: '取值范围0-10，默认值3，0表示不抓拍',
    unit: '张'
  }, {
    field: 'oftenWayPhotoInterval',
    range: [1, 10],
    tip: '单位100ms  取值范围1~10，默认2',
    unit: '100ms',
    margin: true
  }, {
    field: 'wayDeviateSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发告警时车速高于阈值为二级告警，否则为一级告警0表示不录像',
    unit: 'km/h'
  }, {
    field: 'wayDeviateVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5',
    unit: '秒'
  }, {
    field: 'wayDeviatePhotoNums',
    range: [0, 10],
    tip: '取值范围0-10，默认值3，0表示不抓拍',
    unit: '张'
  }, {
    field: 'wayDeviatePhotoInterval',
    range: [1, 10],
    tip: '单位100ms，取值范围1~10，默认值2',
    unit: '100ms',
    margin: true
  }, {
    field: 'frontDurationThreshold',
    range: [10, 50],
    tip: '单位100ms，取值范围10~50，目前使用国标规定值27，预留修改接口。0xFF表示不修改参数',
    unit: '100ms'
  }, {
    field: 'frontSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发告警时车速高于阈值为二级告警，否则为一级告警0xFF 表示不修改参数',
    unit: 'km/h'
  }, {
    field: 'frontVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5，0表示不录像',
    unit: '秒'
  }, {
    field: 'frontPhotoNums',
    range: [0, 10],
    tip: '取值范围0-10，默认值3，0表示不抓拍',
    unit: '张'
  }, {
    field: 'frontPhotoInterval',
    range: [1, 10],
    tip: '单位100ms，取值范围1~10，默认值2',
    unit: '100ms',
    margin: true
  }, {
    field: 'personDurationThreshold',
    range: [10, 50],
    tip: '单位100ms，取值范围10-50，默认值30',
    unit: '100ms'
  }, {
    field: 'personSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。低于该值时进行告警，高于该值时功能关闭',
    unit: 'km/h'
  }, {
    field: 'personVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5，0表示不录像',
    unit: '秒'
  }, {
    field: 'personPhotoNums',
    range: [0, 10],
    tip: '取值范围0-10，默认值3，0表示不抓拍',
    unit: '张'
  }, {
    field: 'personPhotoInterval',
    range: [1, 10],
    tip: '单位100ms，取值范围1~10，默认值2',
    unit: '100ms',
    margin: true
  }, {
    field: 'carDisThreshold',
    range: [10, 50],
    tip: '单位100ms，取值范围10-50，默认值10',
    unit: '100ms'
  }, {
    field: 'carSpeedThreshold',
    range: [0, 220],
    tip: '单位km/h，取值范围0~220，默认值50。表示触发告警时车速高于阈值为二级告警，否则为一级告警',
    unit: 'km/h'
  }, {
    field: 'carVideoDuration',
    range: [0, 60],
    tip: '单位秒，取值范围0-60，默认值5，0表示不录像',
    unit: '秒'
  }, {
    field: 'carPhotoNums',
    range: [0, 10],
    tip: '取值范围0-10，默认值3，0表示不抓拍',
    unit: '张'
  }, {
    field: 'carPhotoInterval',
    range: [1, 10],
    tip: '单位100ms，取值范围1~10，默认值2',
    unit: '100ms',
    margin: true
  }, {
    field: 'wayFlagPhotoNums',
    range: [0, 10],
    tip: '取值范围0-10，默认值30表示不抓拍',
    unit: '张'
  }, {
    field: 'wayFlagPhotoInterval',
    range: [1, 10],
    tip: '单位100ms，取值范围1~10，默认值2',
    unit: '100ms',
    margin: true
  }, {
    field: 'obligate',
    type: 'i'
  }, {
    field: 'obligateBytes_4',
    type: 'i'
  }
];

export const dmsDefault = {
  abnormalInterval: null,
  abnormalPhotoNums: null,
  abnormalSpeedThreshold: null,
  abnormalVideoDuration: null,
  alarmEnable: null,
  alarmEnableStr: null,
  attentionInterval: null,
  attentionPhotoNums: null,
  attentionSpeedThreshold: null,
  attentionVideoDuration: null,
  driverReg: null,
  eventEnable: null,
  eventEnableStr: null,
  obligateBytes_2: null,
  obligateBytes_3: null,
  phoneDriverInterval: null,
  phoneDriverNums: null,
  phoneInterval: null,
  phoneSpeedThreshold: null,
  phoneVideoDuration: null,
  photoDistanceInterval: null,
  photoInterval: null,
  photoNums: null,
  photoResolution: null,
  photoStrategy: null,
  photoTimeInterval: null,
  smokeDriverInterval: null,
  smokeInterval: null,
  smokePhotoNums: null,
  smokeSpeedThreshold: null,
  smokeVideoDuration: null,
  speedThreshold: null,
  tipVolume: null,
  tirePhotoInterval: null,
  tirePhotoNums: null,
  tireSpeedThreshold: null,
  tireVideoDuration: null,
  videoResolution: null
};

export const adasDefault = {
  alarmEnable: null,
  alarmEnableStr: null,
  carDisThreshold: null,
  carPhotoInterval: null,
  carPhotoNums: null,
  carSpeedThreshold: null,
  carVideoDuration: null,
  eventEnable: null,
  eventEnableStr: null,
  frontDurationThreshold: null,
  frontPhotoInterval: null,
  frontPhotoNums: null,
  frontSpeedThreshold: null,
  frontVideoDuration: null,
  obligate: null,
  obligateBytes_4: null,
  obstacleDisThreshold: null,
  obstaclePhotoInterval: null,
  obstaclePhotoNums: null,
  obstacleSpeedThreshold: null,
  obstacleVideoDuration: null,
  oftenWayDuration: null,
  oftenWayPhotoInterval: null,
  oftenWayPhotoNums: null,
  oftenWaySpeedThreshold: null,
  oftenWayTimes: null,
  oftenWayVideoDuration: null,
  personDurationThreshold: null,
  personPhotoInterval: null,
  personPhotoNums: null,
  personSpeedThreshold: null,
  personVideoDuration: null,
  photoDistanceInterval: null,
  photoInterval: null,
  photoNums: null,
  photoResolution: null,
  photoStrategy: null,
  photoTimeInterval: null,
  speedThreshold: null,
  tipVolume: null,
  videoResolution: null,
  wayDeviatePhotoInterval: null,
  wayDeviatePhotoNums: null,
  wayDeviateSpeedThreshold: null,
  wayDeviateVideoDuration: null,
  wayFlagPhotoInterval: null,
  wayFlagPhotoNums: null
};

export const typeAudioVideo = [
  {
    field: 'rtsBitType',
    type: 'r',
    options: [{k: 0, v: 'CBR(固定码率)'}, {k: 1, v: 'VBR(可变码率)'}, {k: 2, v: 'ABR(平均码率)'}]
  }, {
    field: 'rtsResolution',
    type: 'r',
    options: [{k: 0, v: 'QCIF'}, {k: 1, v: 'CIF'}, {k: 2, v: 'WCIF'}, {k: 3, v: 'D1'}, {k: 4, v: 'WD1'}, {k: 5, v: '720P'}, {k: 6, v: '1080P'}]
  }, {
    field: 'rtsFrame',
    range: [1, 1000],
    tip: '范围（1~1000）帧',
    unit: '帧'
  }, {
    field: 'rtsFps',
    range: [0, 8],
    tip: '范围(1~120)帧/s',
    unit: '帧/秒'
  }, {
    field: 'rtsBps',
    tip: '单位为千位每秒(kbps)',
    unit: 'kbps',
    margin: true
  }, {
    field: 'msBitType',
    type: 'r',
    options: [{k: 0, v: 'CBR(固定码率)'}, {k: 1, v: 'VBR(可变码率)'}, {k: 2, v: 'ABR(平均码率)'}]
  }, {
    field: 'msResolution',
    type: 'r',
    options: [{k: 0, v: 'QCIF'}, {k: 1, v: 'CIF'}, {k: 2, v: 'WCIF'}, {k: 3, v: 'D1'}, {k: 4, v: 'WD1'}, {k: 5, v: '720P'}, {k: 6, v: '1080P'}]
  }, {
    field: 'msFrame',
    range: [1, 1000],
    tip: '范围（1~1000）帧',
    unit: '帧'
  }, {
    field: 'msFps',
    range: [0, 8],
    tip: '范围(1~120)帧/s',
    unit: '帧/秒'
  }, {
    field: 'msBps',
    tip: '单位为千位每秒(kbps)',
    unit: 'kbps',
    margin: true
  }, {
    field: 'osdCoverStr',
    type: 'm',
    options: [{k: '0', v: '日期和时间'}, {k: '1', v: '车牌号码'}, {k: '2', v: '逻辑通道号'}, {k: '3', v: '经纬度'}, {k: '4', v: '行驶记录速度'}, {k: '5', v: '卫星定位速度'}, {k: '6', v: '连续驾驶时间'}],
    margin: true
  }, {
    field: 'audioOn',
    type: 'r',
    options: [{k: 0, v: '不启用'}, {k: 1, v: '启用'}]
  }
];
export const audioVideoDefault = {
  audioOn: null,
  msBitType: null,
  msBps: null,
  msFps: null,
  msFrame: null,
  msResolution: null,
  osdCover: null,
  osdCoverStr: null,
  rtsBitType: null,
  rtsBps: null,
  rtsFps: null,
  rtsFrame: null,
  rtsResolution: null
};

export const typeIntenseDrive = [
  {
    field: 'violentEnable',
    type: 'm', // input
    options: [{k: '0', v: '急加速告警'}, {k: '1', v: '急减速告警'}, {k: '2', v: '急转弯告警'}, {k: '3', v: '怠速告警'},
      {k: '4', v: '异常熄火告警'}, {k: '5', v: '空挡滑行告警'}, {k: '6', v: '发动机超转告警'}
    ],
    margin: true
  }, {
    field: 'acceleratedTime',
    range: [1, 10],
    tip: '取值范围1-10',
    unit: '秒'
  }, {
    field: 'accelerated',
    range: [1, 100],
    tip: '取值范围1~100',
    unit: '1/100g'
  }, {
    field: 'moderateTime',
    range: [1, 10],
    tip: '取值范围1~10',
    unit: '秒'
  }, {
    field: 'moderate',
    range: [1, 100],
    tip: '取值范围1~100',
    unit: '1/100g',
    margin: true
  }, {
    field: 'turnTime',
    range: [1, 10],
    tip: '取值范围1~10',
    unit: '秒'
  }, {
    field: 'turn',
    range: [1, 100],
    tip: '取值范围1~100',
    unit: '1/100g',
    margin: true
  }, {
    field: 'idlingTime',
    range: [1, 600],
    tip: '取值范围1~600',
    unit: '秒'
  }, {
    field: 'idlingSpeed',
    range: [1, 30],
    tip: '取值范围1~30',
    unit: 'km/h'
  }, {
    field: 'idlingRotation',
    range: [1, 2000],
    tip: '取值范围1~2000',
    unit: 'RPM',
    margin: true
  }, {
    field: 'misfireTime',
    range: [1, 30],
    tip: '取值范围1~30',
    unit: '秒'
  }, {
    field: 'misfireSpeed',
    range: [10, 200],
    tip: '取值范围10~200',
    unit: 'km/h'
  }, {
    field: 'misfireRotation',
    range: [1, 1000],
    tip: '取值范围1~1000',
    unit: 'RPM',
    margin: true
  }, {
    field: 'neutralSlide',
    range: [1, 30],
    tip: '取值范围1~30',
    unit: '秒'
  }, {
    field: 'neutralSpeed',
    range: [10, 200],
    tip: '取值范围10~200',
    unit: 'km/h'
  }, {
    field: 'neutralRotation',
    range: [1, 2000],
    tip: '取值范围1~2000',
    unit: 'RPM',
    margin: true
  }, {
    field: 'excessTime',
    range: [1, 60],
    tip: '取值范围1~60',
    unit: '秒'
  }, {
    field: 'excessSpeed',
    range: [10, 200],
    tip: '取值范围10~200',
    unit: 'km/h'
  }, {
    field: 'excessRotation',
    range: [1000, 6000],
    tip: '取值范围1000~6000',
    unit: 'RPM'
  }

];
export const intenseDriveDefault = {
  violentEnable: null,
  acceleratedTime: null,
  accelerated: null,
  moderateTime: null,
  moderate: null,
  turnTime: null,
  turn: null,
  idlingTime: null,
  idlingSpeed: null,
  idlingRotation: null,
  misfireTime: null,
  misfireSpeed: null,
  misfireRotation: null,
  neutralSlide: null,
  neutralSpeed: null,
  neutralRotation: null,
  excessTime: null,
  excessSpeed: null,
  excessRotation: null
};

export const typeRoadCondition = [
  {
    field: 'volume',
    range: [0, 8],
    tip: '取值范围0~8，8 最大，0 静音，默认值 6',
    unit: ''
  }, {
    field: 'roadPhotoResolution',
    type: 'r',
    options: [{k: 1, v: '352×288'}, {k: 2, v: '704×288'}, {k: 3, v: '704×576'}, {k: 4, v: '640×480'}, {k: 5, v: '1280×720'}, {k: 6, v: '1920×1080'}]
  }, {
    field: 'roadVideoResolution',
    type: 'r',
    options: [{k: 1, v: 'CIF'}, {k: 2, v: 'HD1'}, {k: 3, v: 'D1'}, {k: 4, v: 'WD1'}, {k: 5, v: 'VGA'}, {k: 6, v: '720P'}, {k: 7, v: '1080P'}]
  }, {
    field: 'checkEnable',
    type: 'm', // input
    options: [{k: '0', v: '超车道占用检测'}, {k: '1', v: '道路破损检测'}
    ],
    margin: true
  }, {
    field: 'overTakeNum',
    range: [0, 10],
    tip: '取值范围 0~10，默认值 1',
    unit: '张'
  }, {
    field: 'overTakeInterval',
    range: [1, 10],
    tip: '取值范围 1~10，默认值 2',
    unit: '100ms'
  }, {
    field: 'overTakeRecord',
    range: [0, 60],
    tip: '取值范围 0~60，默认值 0，表示 不录像',
    unit: '秒',
    margin: true
  }, {
    field: 'roadDamageNum',
    range: [0, 10],
    tip: '取值范围 0~10',
    unit: '张'
  }, {
    field: 'roadDamageInterval',
    range: [1, 10],
    tip: '取值范围 1~10，默认值 2',
    unit: '100ms'
  }, {
    field: 'roadDamageRecord',
    range: [0, 60],
    tip: '取值范围 0~60，默认值 0，表示 不录像',
    unit: '秒'
  }

];
export const roadConditionDefault = {
  volume: null,
  roadPhotoResolution: null,
  roadVideoResolution: null,
  checkEnable: null,
  overTakeNum: null,
  overTakeInterval: null,
  overTakeRecord: null,
  roadDamageNum: null,
  roadDamageInterval: null,
  roadDamageRecord: null
};

export const typeSpecialAlarm = [
  {
    field: 'threshHold',
    range: [1, 99],
    tip: '特殊告警录像占用主存储器存储阈值百分比，取值1~99，默认值为20',
    unit: '张'
  }, {
    field: 'duration',
    tip: '特殊告警录像的最长持续时间，单位为分钟（min），默认值为5',
    unit: '分钟'
  }, {
    field: 'start',
    tip: '特殊录像发生前进行标记的录像时间，单位为分钟（min），默认值为1',
    unit: '分钟'
  }

];
export const specialAlarmDefault = {
  threshHold: null,
  duration: null,
  start: null
};
