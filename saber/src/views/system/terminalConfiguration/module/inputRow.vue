<template>
  <div class="layout">
    <div class="content">
      <div class="option">
        <!-- {{ getLabel(set.field) }} -->
        <el-tooltip
          :disabled="!set.tip"
          class="item"
          effect="light"
          :content="set.tip"
          placement="right-end"
          :open-delay="500"
        >
          <span>{{ getLabel(set.field) }}</span>
        </el-tooltip>
      </div>
      <div class="value">
        <el-input
          v-if="set.type==='i'"
          v-model="input"
          placeholder="请输入内容"
          @input="inputChange"
        >
          <i
            v-if="set.unit"
            slot="suffix"
            class="inputSlot"
          >{{ set.unit }}</i>
        </el-input>
        <el-input
          v-else
          v-model.number="input"
          placeholder="请输入内容"
          type="number"
          :min="set.range && set.range[0]"
          :max="set.range && set.range[1]"
          @input="inputChange"
        >
          <i
            v-if="set.unit"
            slot="suffix"
            class="inputSlot"
          >{{ set.unit }}</i>
        </el-input>
      </div>
      <div
        v-show="!rowSet.delCh"
        class="tick"
      >
        <el-checkbox
          v-model="checked"
          class="tick-box"
          @change="checkboxChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import getLabel from '@/utils/getLabel';
export default {
  props: {
    set: {
      type: [Number, String, Object, Array],
      default: () => {
        return {};
      }
    },
    initValue: {
      type: [Number, String, Object, Array],
      default: ''
    },
    checkedFlag: {
      type: [Number, String, Object, Array],
      default: () => {
        return {};
      }
    },
    rowSet: {
      type: [Object],
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      input: '',
      checked: true
    };
  },
  watch: {
    initValue: { // 深度监听，可监听到对象、数组的变化
      handler (newV, oldV) {
        this.getInitValue(newV);
      },
      deep: true,
      immediate: true
    },
    checkedFlag: { // 深度监听，可监听到对象、数组的变化
      handler (newV, oldV) {
        this.checked = newV.value;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 获取值
    getInitValue (v) {
      this.input = v;
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('terminalConfiguration', value);
    },
    // 值改变
    inputChange (v) {
      this.$emit('formChange', {
        key: this.set.field,
        value: v
      });
    },
    // 勾选改变
    checkboxChange (v) {
      this.$emit('formCheckedChange', {
        index: this.checkedFlag.index,
        key: this.checkedFlag.key,
        value: v
      });
    }
  }
};
</script>

<style src="./rowForm.css" scoped>
</style>
<style  scoped>
.inputSlot{
  font-size: 12px;
  font-style: unset;
  line-height: 30px;
  color: #2c3e50;
}
.value ::v-deep.el-input--suffix .el-input__inner {
    padding-right: 50px;
}
</style>
