<template>
  <div>
    <div class="stateMonitorLayout">
      <div class="work_item_room">
        <div>
          <div
            class="header3_title"
          >
            终端控制
          </div>
          <div
            class="work_item_box"
          >
            <span class="work_item_title">车辆油电路控制：</span>
            <el-radio-group
              v-model="cmd"
              class="work_item_box"
            >
              <el-radio
                v-for="radioItemData in gateLockOptions"
                :key="radioItemData.value"
                :label="radioItemData.value"
              >
                {{ radioItemData.label }}
              </el-radio>
            </el-radio-group>
          </div>
          <div
            class="work_item_box"
          >
            <span class="work_item_title">车辆油路：</span>
            <span>{{ petrolState }}</span>
          </div>
          <div
            class="work_item_box"
          >
            <span class="work_item_title">车辆电路：</span>
            <span>{{ circuitState }}</span>
          </div>
          <div class="work_item_button">
            <el-button
              size="mini"
              type="primary"
              @click="queryTerminal"
            >
              查询
            </el-button>
            <el-button
              size="mini"
              type="primary"
              @click="setTerminal"
            >
              设置
            </el-button>
          </div>
        </div>
        <div>
          <div
            class="header3_title"
          >
            人工告警确认
          </div>
          <div
            class="work_item_box"
          >
            <span class="work_item_title">告警确认：</span>
            <el-radio-group
              v-model="alarmType"
              class="work_item_box alarm-container"
            >
              <el-radio
                v-for="radioItemData in alarmOptions"
                :key="radioItemData.value"
                :label="radioItemData.value"
              >
                {{ radioItemData.label }}
              </el-radio>
            </el-radio-group>
          </div>
          <div class="work_item_button">
            <el-button
              size="mini"
              type="primary"
              @click="handleAlarm"
            >
              设置
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { terminalcontrol, confirmAlarm } from '@/api/system/terminalConfig.js';
import { sendLocationMsg } from '@/api/center/instruction';
export default {
  name: 'VehicleControl',
  components: {

  },
  props: {
    phones: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    carPick: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      cmd: null,
      gateLockOptions: [
        { label: '断油电', value: 100 },
        { label: '恢复油电', value: 101 }
      ],
      petrolState: null,
      circuitState: null,
      alarmOptions: [
        { label: '紧急告警', value: 1 },
        { label: '危险预警', value: 8 },
        { label: '进出区域告警', value: 1048576 },
        { label: '进出路线告警', value: 2097152 },
        { label: '路段行驶时间不足/过长告警', value: 4194304 },
        { label: '车辆非法点火告警', value: 134217728 },
        { label: '车辆非法位移告警', value: 268435456 }
      ],
      alarmType: null
    };
  },
  methods: {
    // 选车错误信息
    selectMsg () {
      let flag = this.phones.length === 1 ? 0 : this.phones.length > 1 ? 2 : 1;
      if (flag) {
        this.$message({
          type: 'error',
          message: flag > 1 ? '仅支持单辆车' : '请先选择车辆'
        });
      }
      return !flag;
    },
    // 人工告警确认
    handleAlarm() {
      if (!this.selectMsg()) {
        return;
      }
      if (!this.alarmType) {
        this.$message({
          type: 'warning',
          message: '请勾选设置项'
        });
        return;
      }
      let objOut = {
        // 'vehicleId': this.carPick[0].id,
        'type': this.alarmType,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      };
      confirmAlarm(objOut).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 查询
    queryTerminal() {
      // if (!this.selectMsg()) {
      //   return;
      // }
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let params = {
        // vehicleId: this.changeVehicle.id,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      };
      sendLocationMsg(params).then(res => {
        if (res.code === 200 && res.data) {
          const { circuitState, petrolState } = res.data;
          this.cmd = circuitState ? 100 : 101;
          this.circuitState = circuitState ? '断开' : '正常';
          this.petrolState = petrolState ? '断开' : '正常';
        }
      });
    },
    // 设置
    setTerminal() {
      if (!this.cmd) {
        this.$message({
          type: 'warning',
          message: '请勾选设置项'
        });
        return;
      }
      let objOut = {
        'devsInfo': this.phones,
        'cmd': this.cmd
      };
      terminalcontrol(objOut).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.queryTerminal();// 重新获取数据
        }
      }).catch(err => {
        console.log(err);
      });
    }
  }
};
</script>

<style scoped>
.stateMonitorLayout{
  max-height:calc(100vh - 220px) ;
  overflow: scroll;
  display: flex;
  padding-left: 10px;
}
  .header3_title{
    font-size: 14px;
    font-weight: bold;
    margin-left: 15px;
  }
  .work_item_room{
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
  }
  .work_item_box{
    margin: 2px;
    width: 70vh;
    display: flex;
    align-items: center;
    margin-right: 8px;
    margin-left: 30px;
  }
  .work_item_title{
    width: 30%;
    font-size: 14px;
    color: #909399;
  }
  .work_item_button{
    margin: 5px;
    float: right;
  }
  .alarm-container {
    display: flex;
    flex-wrap: wrap;
  }
</style>
