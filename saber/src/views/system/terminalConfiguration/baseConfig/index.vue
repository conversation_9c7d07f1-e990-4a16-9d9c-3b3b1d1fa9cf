<template>
  <div>
    <div class="car-content">
      <el-card class="work_card">
        <!-- <div
          slot="header"
          class="header_room"
        >
          <div class="header_logo" />
          <span
            class="header_title"
          >
            参数设置
          </span>
        </div> -->
        <div class="work_house_title">
          <div
            class="header2_title"
          >
            基本参数设置
          </div>
          <BtnMore
            :is-down="IsShow.basicParameters"
            @click="IsShow.basicParameters = !IsShow.basicParameters"
          />
        </div>
        <div
          v-show="IsShow.basicParameters"
          class="work_item_room"
        >
          <div
            v-for="(item, i) in stringInputBasic"
            :key="'stringInputBasic' + i"
          >
            <div
              class="header3_title"
            >
              {{ item.title }}
            </div>
            <div
              v-for="stringItem in item.body"
              :key="stringItem.key"
              class="work_item_box"
            >
              <span class="work_item_title">{{ stringItem.name }}：</span>
              <el-input
                v-if="stringItem.type === 'integer'"
                v-model="DetailForm[stringItem.key]"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                class="work_item_value"
                size="mini"
              />
              <div
                v-else-if="stringItem.type === 'other'"
                class="other-container"
              >
                <div
                  class="mask-operate"
                  @click="targetFlagHandle(stringItem.key)"
                />
                <el-input
                  v-model="DetailForm[stringItem.key]"
                  readonly
                  class="work_item_value"
                  size="mini"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    style="cursor: pointer;"
                    type="info"
                  />
                </el-input>
              </div>
              <el-input
                v-else
                v-model="DetailForm[stringItem.key]"
                class="work_item_value"
                size="mini"
              />
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(stringInputBasic, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(stringInputBasic, i)"
              >
                设置
              </el-button>
            </div>
          </div>
          <div
            v-for="(radioItem, i) in radioDataBasic"
            :key="radioItem.key"
          >
            <div
              class="header3_title"
            >
              {{ radioItem.name }}
            </div>
            <el-radio-group
              v-model="DetailForm[radioItem.key]"
              class="work_item_box"
            >
              <el-radio
                v-for="radioItemData in radioItem.data"
                :key="radioItemData.value"
                :label="radioItemData.value"
              >
                {{ radioItemData.name }}
              </el-radio>
            </el-radio-group>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(radioDataBasic, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(radioDataBasic, i)"
              >
                设置
              </el-button>
            </div>
          </div>
          <div
            v-for="(timeItem, i) in timeDataBasic"
            :key="timeItem.key"
          >
            <div
              class="header3_title"
            >
              {{ timeItem.name }}
            </div>
            <div class="work_item_box">
              <el-time-picker
                v-model="timeItem.value.startTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="起始时间"
                size="mini"
                @change="changeTime($event,timeItem.key, 'startTime')"
              />
              -
              <el-time-picker
                v-model="timeItem.value.endTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="结束时间"
                size="mini"
                @change="changeTime($event,timeItem.key, 'endTime')"
              />
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(timeDataBasic, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(timeDataBasic, i)"
              >
                设置
              </el-button>
            </div>
          </div>
          <div
            v-for="(item, i) in mixDataBasic"
            :key="'mixDataBasic' + i"
          >
            <div v-if="item.style==='input'">
              <div
                class="header3_title"
              >
                {{ item.title }}
              </div>
              <div
                v-for="stringItem in item.body"
                :key="stringItem.key"
                class="work_item_box"
              >
                <span class="work_item_title">{{ stringItem.name }}：</span>
                <el-input
                  v-if="stringItem.type === 'integer'"
                  v-model="DetailForm[stringItem.key]"
                  onkeyup="value=value.replace(/[^\d]/g,'')"
                  class="work_item_value"
                  size="mini"
                />
                <el-input
                  v-else
                  v-model="DetailForm[stringItem.key]"
                  class="work_item_value"
                  size="mini"
                />
              </div>
              <div class="work_item_button">
                <el-button
                  v-show="showQueryButton"
                  size="mini"
                  type="primary"
                  @click="queryTerminal(mixDataBasic, i)"
                >
                  查询
                </el-button>
                <el-button
                  size="mini"
                  type="primary"
                  @click="setTerminal(mixDataBasic, i)"
                >
                  设置
                </el-button>
              </div>
            </div>
            <div v-else-if="item.style === 'checkbox'">
              <div
                class="header3_title"
              >
                {{ item.name }}
              </div>
              <!-- <span class="work_item_title">{{ item.title }}：</span> -->
              <div class="work_item_box">
                <el-checkbox
                  v-for="checkDataItem in item.checkBody"
                  :key="checkDataItem.key"
                  v-model="DetailForm[checkDataItem.key]"
                  :true-label="1"
                  :false-label="0"
                >
                  {{ checkDataItem.name }}
                </el-checkbox>
              </div>
              <div class="work_item_button">
                <el-button
                  v-show="showQueryButton"
                  size="mini"
                  type="primary"
                  @click="queryTerminal(mixDataBasic, i)"
                >
                  查询
                </el-button>
                <el-button
                  size="mini"
                  type="primary"
                  @click="setTerminal(mixDataBasic, i)"
                >
                  设置
                </el-button>
              </div>
            </div>
            <div
              v-else-if="item.style === 'radio'"
            >
              <div
                class="header3_title"
              >
                {{ item.name }}
              </div>
              <el-radio-group
                v-model="DetailForm[item.key]"
                class="work_item_box"
              >
                <el-radio
                  v-for="radioItemData in item.data"
                  :key="radioItemData.value"
                  :label="radioItemData.value"
                >
                  {{ radioItemData.name }}
                </el-radio>
              </el-radio-group>
              <div class="work_item_button">
                <el-button
                  v-show="showQueryButton"
                  size="mini"
                  type="primary"
                  @click="queryTerminal(mixDataBasic, i)"
                >
                  查询
                </el-button>
                <el-button
                  size="mini"
                  type="primary"
                  @click="setTerminal(mixDataBasic, i)"
                >
                  设置
                </el-button>
              </div>
            </div>
            <div
              v-else
            >
              <div
                class="header3_title"
              >
                {{ item.title }}
              </div>
              <div
                v-for="contentItem in item.content"
                :key="contentItem.key"
              >
                <div
                  v-if="contentItem.style ==='input'"
                  class="work_item_box"
                >
                  <span class="work_item_title">{{ contentItem.name }}：</span>
                  <el-input
                    v-if="contentItem.type === 'integer'"
                    v-model="DetailForm[contentItem.key]"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    class="work_item_value"
                    size="mini"
                  />
                  <el-input
                    v-else
                    v-model="DetailForm[contentItem.key]"
                    class="work_item_value"
                    size="mini"
                  />
                </div>
                <div
                  v-else-if="contentItem.style ==='radio'"
                  class="radio_content_item"
                >
                  <div
                    v-if="contentItem.name"
                    class="radio_content_name"
                  >
                    {{ contentItem.name }}
                  </div>
                  <el-radio-group
                    v-model="DetailForm[contentItem.key]"
                  >
                    <el-radio
                      v-for="subRadioItem in contentItem.params"
                      :key="subRadioItem.value"
                      :label="subRadioItem.value"
                    >
                      {{ subRadioItem.name }}
                    </el-radio>
                  </el-radio-group>
                </div>
                <div
                  v-else-if="contentItem.style ==='timePicker'"
                  class="picker_content_item"
                >
                  <div class="radio_content_name">
                    {{ contentItem.name }}
                  </div>
                  <div>
                    <el-time-picker
                      v-model="DetailForm[contentItem.key]"
                      value-format="HH:mm"
                      format="HH:mm"
                      placeholder="时间"
                      size="mini"
                    />
                  </div>
                </div>
              </div>
              <div class="work_item_button">
                <el-button
                  v-show="showQueryButton && !item.unQuery"
                  size="mini"
                  type="primary"
                  @click="preQueryTerminal(mixDataBasic, i)"
                >
                  查询
                </el-button>
                <el-button
                  v-show="!item.unSet"
                  size="mini"
                  type="primary"
                  @click="preSetTerminal(mixDataBasic, i)"
                >
                  设置
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="work_house_line"/>
        <div class="work_house_title">
          <div
            class="header2_title"
          >
            告警参数设置
          </div>
          <BtnMore
            :is-down="IsShow.alarmParameters"
            @click="IsShow.alarmParameters = !IsShow.alarmParameters"
          />
        </div>
        <div
          v-show="IsShow.alarmParameters"
          class="work_item_room"
        >
          <div
            v-for="(item, i) in stringInputAlarm"
            :key="'stringInputAlarm' + i"
          >
            <div
              class="header3_title"
            >
              {{ item.title }}
            </div>
            <div
              v-for="stringItem in item.body"
              :key="stringItem.key"
              class="work_item_box"
            >
              <span class="work_item_title">{{ stringItem.name }}：</span>
              <el-input
                v-model="DetailForm[stringItem.key]"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                class="work_item_value"
                size="mini"
              />
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(stringInputAlarm, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(stringInputAlarm, i)"
              >
                设置
              </el-button>
            </div>
          </div>
        </div>
        <div class="work_house_line"/>
        <div class="work_house_title">
          <div
            class="header2_title"
          >
            终端电话设置
          </div>
          <BtnMore
            :is-down="IsShow.phoneParameters"
            @click="IsShow.phoneParameters = !IsShow.phoneParameters"
          />
        </div>
        <div
          v-show="IsShow.phoneParameters"
          class="work_item_room"
        >
          <div
            v-for="(radioItem, i) in radioDataPhone"
            :key="radioItem.key"
          >
            <div
              class="header3_title"
            >
              {{ radioItem.name }}
            </div>
            <el-radio-group
              v-model="DetailForm[radioItem.key]"
              class="work_item_box"
            >
              <el-radio
                v-for="radioItemData in radioItem.data"
                :key="radioItemData.value"
                :label="radioItemData.value"
              >
                {{ radioItemData.name }}
              </el-radio>
            </el-radio-group>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(radioDataPhone, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(radioDataPhone, i)"
              >
                设置
              </el-button>
            </div>
          </div>
          <div
            v-for="(item, i) in stringInputPhone"
            :key="'{{item.title}}' + i"
          >
            <div
              class="header3_title"
            >
              {{ item.title }}
            </div>
            <div
              v-for="stringItem in item.body"
              :key="stringItem.key"
              class="work_item_box"
            >
              <span class="work_item_title">{{ stringItem.name }}：</span>
              <el-input
                v-if="stringItem.type === 'integer'"
                v-model="DetailForm[stringItem.key]"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                class="work_item_value"
                size="mini"
              />
              <el-input
                v-else
                v-model="DetailForm[stringItem.key]"
                class="work_item_value"
                size="mini"
              />
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(stringInputPhone, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(stringInputPhone, i)"
              >
                设置
              </el-button>
            </div>
          </div>
        </div>
        <div class="work_house_line"/>
        <div class="work_house_title">
          <div
            class="header2_title"
          >
            拍照参数设置
          </div>
          <BtnMore
            :is-down="IsShow.photoParameters"
            @click="IsShow.photoParameters = !IsShow.photoParameters"
          />
        </div>
        <div
          v-show="IsShow.photoParameters"
          class="work_item_room"
        >
          <div
            v-for="(item, i) in stringInputPhoto"
            :key="'stringInputPhoto' + i"
          >
            <div
              class="header3_title"
            >
              {{ item.title }}
            </div>
            <div
              v-for="stringItem in item.body"
              :key="stringItem.key"
              class="work_item_box"
            >
              <span
                class="work_item_title"
              >
                {{ stringItem.name }}：
              </span>
              <el-input
                v-model="DetailForm[stringItem.key]"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                class="work_item_value"
                size="mini"
              />
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(stringInputPhoto, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(stringInputPhoto, i)"
              >
                设置
              </el-button>
            </div>
          </div>
          <div
            v-for="(items, i) in checkDataPhoto"
            :key="'checkDataPhoto' + i"
          >
            <div
              class="header3_title"
            >
              {{ items.title }}
            </div>
            <div
              v-for="(item, index) in items.body"
              :key="'checkDataPhoto.checkDataItem' + index"
              class="work_item_box"
            >
              <span class="work_item_title">{{ item.title }}：</span>
              <el-checkbox
                v-for="checkDataItem in item.checkBody"
                :key="checkDataItem.key"
                v-model="DetailForm[checkDataItem.key]"
                :true-label="1"
                :false-label="0"
              >
                {{ checkDataItem.name }}
              </el-checkbox>
            </div>
            <div
              v-for="(item, index) in items.body"
              :key="'checkDataPhoto.stringInputItem' + index"
            >
              <div
                v-for="vals in item.inputBody"
                :key="vals.key"
                class="work_item_box"
              >
                <span class="work_item_title">{{ vals.name }}：</span>
                <el-input
                  v-model="DetailForm[vals.key]"
                  onkeyup="value=value.replace(/[^\d]/g,'')"
                  class="work_item_value"
                  size="mini"
                />
              </div>
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryTerminal(checkDataPhoto, i)"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setTerminal(checkDataPhoto, i)"
              >
                设置
              </el-button>
            </div>
          </div>
        </div>
        <!-- <div class="work_house_line" />
        <div class="work_house_title">
          <div
            class="header2_title"
          >
            终端远程升级
          </div>
          <BtnMore
            :is-down="IsShow.updateParameters"
            @click="IsShow.updateParameters = !IsShow.updateParameters"
          />
        </div>
        <div
          v-show="IsShow.updateParameters"
          class="work_item_room"
        >
          <div
            v-for="(item, i) in stringInputUpdate"
            :key="'stringInputUpdate' + i"
          >
            <div
              v-for="stringItem in item.body"
              :key="stringItem.key"
              class="work_item_box"
            >
              <span class="work_item_title">{{ stringItem.name }}：</span>
              <el-input
                v-model="DetailForm[stringItem.key]"
                class="work_item_value"
                size="mini"
              />
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="queryUpdateDetail()"
              >
                查询版本
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="updateFTPTerminal(stringInputUpdate)"
              >
                升级
              </el-button>
            </div>
          </div>
        </div> -->
        <div class="work_house_line"/>
        <div class="work_house_title">
          <div
            class="header2_title"
          >
            查询终端属性
          </div>
          <BtnMore
            :is-down="IsShow.machineParameters"
            @click="IsShow.machineParameters = !IsShow.machineParameters"
          />
        </div>
        <div
          v-show="IsShow.machineParameters"
          class="work_item_room"
        >
          <div
            v-for="(item, i) in optionSetConfig.inputmachine"
            :key="'inputmachine' + i"
          >
            <div
              class="header3_title"
            >
              {{ item.title }}
            </div>
            <div
              v-for="stringItem in item.body"
              :key="stringItem.key"
              class="work_item_box0"
            >
              <div
                class="work_item_title0"
              >
                {{ stringItem.name }}：
              </div>
              <!-- <el-input
                v-model="optionSetform['inputmachine'+stringItem.key]"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                class="work_item_value"
                size="mini"
              /> -->
              <div>
                <div v-if="typeof(optionSetform['inputmachine'+stringItem.key])=='string'">
                  {{ optionSetform['inputmachine' + stringItem.key] }}
                </div>

                <div v-else>
                  <div
                    v-for="(item,index) in optionSetform['inputmachine'+stringItem.key]"
                    :key="index"
                  >
                    {{ item }}
                  </div>
                </div>
              </div>
            </div>
            <div class="work_item_button">
              <el-button
                v-show="showQueryButton"
                size="mini"
                type="primary"
                @click="searchTerminal"
              >
                查询
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <UpdateDetail
      ref="updateDetail"
    />
    <BusinessList
      :is-show-table.sync="isShowTable"
      :target-model="dict.targetModel"
      @rowClick="rowClick"
    />
  </div>
</template>

<script>
import getLabel from '@/utils/getLabel';
import ConfigSystemSet from '@/config/configSystemSet.json';
import BtnMore from '@/components/BtnMore.vue';
import {
  queryTerminalParam,
  setTerminalParam,
  terminalControl,
  queryTerminalUpdate,
  restartTerminal,
  queryIcdriverinfo,
  queryterminalattr
} from '@/api/system/terminalConfig.js';
import UpdateDetail from './update/updateDetail.vue';
import BusinessList from '@/components/businessList/businessList.vue';

export default {
  components: {
    BtnMore,
    UpdateDetail,
    BusinessList
  },
  props: {
    phones: {
      type: Array,
      default: () => {
        return [];
      }
    },
    carPick: {
      type: Array,
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      timeValue: {
        startTime: '',
        endTime: ''
      },
      ModelOptions: [],
      InputContent: {},
      DetailForm: {},
      input: '',
      // 基本参数设置
      stringInputBasic: [],
      numberInputBasic: [],
      radioDataBasic: [],
      timeDataBasic: [],
      mixDataBasic: [],
      // 告警参数设置
      stringInputAlarm: [],
      // 终端电话设置
      stringInputPhone: [],
      radioDataPhone: [],
      // 拍照参数设置
      stringInputPhoto: [],
      checkDataPhoto: [],
      // 终端远程升级
      stringInputUpdate: [],
      /** 内容显示隐藏变量 */
      IsShow: {
        basicParameters: true,
        alarmParameters: true,
        phoneParameters: true,
        photoParameters: true,
        updateParameters: true,
        machineParameters: true
      },
      showQueryButton: true,
      LicenseDate: [],
      vehicleIds: [],
      optionSetConfig: {}, // 设置的集合new
      optionSetform: {}, // 设置的集合new
      isShowTable: false,
      otherValue: ''
    };
  },
  watch: {
    changeVehicle: function (val) {
      val ? this.showQueryButton = true : this.showQueryButton = false;
    }
  },
  mounted() {
    this.InputContent = ConfigSystemSet;
    let _new = '设置参数';
    let modelCon = this.InputContent.InputContent[_new];
    // 基本参数设置
    this.stringInputBasic = modelCon.Basic.stringInput;
    this.numberInputBasic = modelCon.Basic.numberInput;
    this.radioDataBasic = modelCon.Basic.radioData;
    this.timeDataBasic = modelCon.Basic.timeData;
    this.mixDataBasic = modelCon.Basic.mixData;
    // 告警参数设置
    this.stringInputAlarm = modelCon.Alarm.stringInput;
    // 终端电话设置
    this.stringInputPhone = modelCon.Phone.stringInput;
    this.radioDataPhone = modelCon.Phone.radioData;
    // 拍照参数设置
    this.stringInputPhoto = modelCon.Photo.stringInput;
    this.checkDataPhoto = modelCon.Photo.checkData;
    // 终端远程升级
    this.stringInputUpdate = modelCon.Update.stringInput;
    // 终端查询设置
    let op = ConfigSystemSet.inputSet;
    for (let key in op) {
      for (let k in op[key]) {
        this.optionSetConfig[k + key] = op[key][k];
      }
    }
    for (let key in this.optionSetConfig) {
      this.optionSetConfig[key].forEach((item, index) => {
        item.body.forEach(v => {
          this.optionSetform[key + v.key] = '';
        });
      });
    }
    this.optionSetform = Object.assign({}, this.optionSetform);
    this.init();
  },
  methods: {
    rowClick (row) {
      this.DetailForm[this.otherValue] = row.targetFlag;
    },
    targetFlagHandle (value) {
      this.isShowTable = true;
      this.otherValue = value;
    },
    // 查询终端参数 new 目前仅iccid
    searchTerminal() {
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let p = [{
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      }];
      queryterminalattr(p).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          // 处理获得的数据
          if (res.data && res.data.length === 1) {
            this.optionSetform['inputmachineiccid'] = res.data[0].iccid;
            let arr01 = res.data[0]['ter_type'].split('').reverse();
            let arr02 = [
              {
                0: '不适用客运车辆',
                1: '适用客运车辆'
              },
              {
                0: '不适用危险品车辆',
                1: '适用危险品车辆'
              },
              {
                0: '不适用普通货运车辆',
                1: '适用普通货运车辆'
              },
              {
                0: '不适用出租车辆',
                1: '适用出租车辆'
              },
              null,
              null,
              {
                0: '不支持硬盘录像',
                1: '支持硬盘录像'
              },
              {
                0: '一体机',
                1: '分体机'
              }
            ];
            this.optionSetform['inputmachineter_type'] = arr02.map((item, index) => {
              return item ? item[arr01[index]] : '';
            });
            this.optionSetform['inputmachinevendor_id'] = res.data[0]['vendor_id'];
            this.optionSetform['inputmachinemodel'] = res.data[0]['model'];
            this.optionSetform['inputmachineter_id'] = res.data[0]['ter_id'];
            this.optionSetform['inputmachinephone'] = res.data[0]['phone'];
            this.optionSetform['inputmachineimei'] = res.data[0]['imei'];
            this.optionSetform['inputmachinehw_version'] = res.data[0]['hw_version'];
            this.optionSetform['inputmachinefw_version'] = res.data[0]['fw_version'];
            let arr03 = res.data[0]['gnss_attr'].split('').reverse();
            let arr04 = [
              {
                0: '不支持 GPS 定位',
                1: '支持 GPS 定位'
              },
              {
                0: '不支持北斗定位',
                1: '支持北斗定位'
              },
              {
                0: '不支持 GLONASS 定位',
                1: '支持 GLONASS 定位'
              },
              {
                0: '不支持 Galileo 定位',
                1: '支持 Galileo 定位'
              }
            ];
            this.optionSetform['inputmachinegnss_attr'] = arr04.map((item, index) => {
              return item[arr03[index]];
            });
            let arr05 = res.data[0]['comm_attr'].split('').reverse();
            let arr06 = [
              {
                0: '不支持GPRS通信',
                1: '支持GPRS通信'
              },
              {
                0: '不支持CDMA通信',
                1: '支持CDMA通信'
              },
              {
                0: '不支持TD-SCDMA通信',
                1: '支持TD-SCDMA通信'
              },
              {
                0: '不支持WCDMA通信',
                1: '支持WCDMA通信'
              },
              {
                0: '不支持CDMA2000通信',
                1: '支持CDMA2000通信'
              },
              {
                0: '不支持TD-LTE通信',
                1: '支持TD-LTE通信'
              },
              {
                0: '不支持其他通信方式',
                1: '支持其他通信方式'
              }
            ];
            this.optionSetform['inputmachinecomm_attr'] = arr06.map((item, index) => {
              return item[arr05[index]];
            });
          }
        }
      }).catch(err => {
        console.log(err);
      });
    },
    /**
     * 设置终端参数
     */
    setTerminal(_data, _index) {
      let parme = {};
      for (let i = 0; i < _data.length; i++) {
        if (_data[i].body) {
          if (_index === i) {
            _data[i].body.forEach(element => {
              if (element.key) {
                // 处理stringInput数据
                parme[element.key] = element.type === 'integer' ? parseInt(this.DetailForm[element.key]) : this.DetailForm[element.key];
              }
              else {
                element.checkBody.forEach(item => {
                  // 处理checkData数据
                  console.log(this.DetailForm[item.key]);
                  parme[item.key] = !!this.DetailForm[item.key];
                });
                element.inputBody.forEach(item => {
                  parme[item.key] = parseInt(this.DetailForm[item.key]);
                });
              }
            });
          }
        }
        else if (_data[i].key) {
          if (_index === i && _data[i].data) {
            // 处理radioData数据
            parme[_data[i].key] = _data[i].type === 'integer' ? parseInt(this.DetailForm[_data[i].key]) : this.DetailForm[_data[i].key];
          }
          else if (_index === i && _data[i].value) {
            // 处理timeData数据
            parme[`${_data[i].key}StartTimeHour`] = this.DetailForm[`${_data[i].key}StartTimeHour`] ? this.DetailForm[`${_data[i].key}StartTimeHour`] : null;
            parme[`${_data[i].key}StartTimeMin`] = this.DetailForm[`${_data[i].key}StartTimeMin`] ? this.DetailForm[`${_data[i].key}StartTimeMin`] : null;
            parme[`${_data[i].key}EndTimeHour`] = this.DetailForm[`${_data[i].key}EndTimeHour`] ? this.DetailForm[`${_data[i].key}EndTimeHour`] : null;
            parme[`${_data[i].key}EndTimeMin`] = this.DetailForm[`${_data[i].key}EndTimeMin`] ? this.DetailForm[`${_data[i].key}EndTimeMin`] : null;
          }
          else if (_index === i && _data[i].checkBody) {
            parme[_data[i].key] = this.getParmeFormat(_data[i]);
          }
        }
        else if (_data[i].content && _index === i) {
          _data[i].content.forEach(element => {
            if (this.DetailForm[element.key] || this.DetailForm[element.key] === 0) {
              if (element.style === 'input') {
                parme[element.key] = element.type === 'integer' ? parseInt(this.DetailForm[element.key]) : this.DetailForm[element.key];
              }
              else if (element.style === 'radio') {
                parme[element.key] = element.type === 'integer' ? parseInt(this.DetailForm[element.key]) : this.DetailForm[element.key];
              }
              else if (element.style === 'timePicker') {

              }
              else if (element.style === 'checkData') {

              }
              if (element.fuction) {
                this.otherFuction(parme, element);
              }
            }
          });
        }
      }
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      // parme.phone = this.phones[0].phone;
      // parme.vehicleId = this.vehicleIds[0];
      parme.device_id = BigInt(this.phones[0].deviceId);
      parme.device_type = this.phones[0].deviceType;
      setTerminalParam(parme).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          // 处理获得的数据
          console.log(res);
        }
      }).catch(err => {
        console.log(err);
      });
    },
    getParmeFormat(data) {
      if (data.key === 'gnss_mode') {
        let arr = data.checkBody.map(v => {
          return this.DetailForm[v.key] === 1 ? '1' : '0';
        });
        arr.reverse();
        return arr.join('');
      }
    },
    getParmeFormatBack(key, datas) {
      if (key === 'gnss_mode') {
        let result = datas[key].split('');
        let arr = [
          'gnss_mode_Galileo',
          'gnss_mode_GLONASS',
          'gnss_mode_bd',
          'gnss_mode_GPS'
        ];
        arr.forEach((v, index) => {
          if (result[index] === '1') {
            this.$set(this.DetailForm, arr[index], 1);
          }
          else {
            this.$set(this.DetailForm, arr[index], 0);
          }
        });
      }
    },
    /**
     * 查询终端参数
     */
    queryTerminal(_data, _index) {
      console.log(_data);
      let paramIds = [];
      for (let i = 0; i < _data.length; i++) {
        if (_data[i].body) {
          if (_index === i) {
            _data[i].body.forEach(element => {
              // 十六进制转十进制
              paramIds.push(parseInt(parseInt(element.param_id), 10));
            });
          }
        }
        else if (_data[i].param_id) {
          if (_index === i) {
            paramIds.push(parseInt(parseInt(_data[i].param_id), 10));
          }
        }
      }
      console.log(paramIds);
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parme = {
        device_id: BigInt(this.changeVehicle.deviceId),
        device_type: this.changeVehicle.deviceType,
        param_ids: paramIds
      };
      queryTerminalParam(parme).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          // 处理获得的数据
          this.handleResultData(res.data, _data, _index);
        }
      }).catch(err => {
        console.log(err);
      });
    },
    handleResultData(results, datas, index) {
      for (let i = 0; i < datas.length; i++) {
        if (datas[i].body) {
          if (index === i) {
            datas[i].body.forEach(element => {
              if (element.key) {
                this.$set(this.DetailForm, element.key, '');
                // 处理stringInput数据
                console.log(this.DetailForm[element.key], results.Params[element.key]);
                this.DetailForm[element.key] = results.Params[element.key];
              }
              else {
                element.checkBody.forEach(item => {
                  this.$set(this.DetailForm, item.key, 0);
                  // 处理checkData数据
                  this.DetailForm[item.key] = results.Params[item.key] ? 1 : 0;
                });
                element.inputBody.forEach(item => {
                  this.$set(this.DetailForm, item.key, 0);
                  this.DetailForm[item.key] = results.Params[item.key];
                });
              }
            });
          }
        }
        else if (datas[i].key) {
          if (index === i && datas[i].data) {
            // 处理radioData数据
            this.$set(this.DetailForm, datas[i].key, 0);
            this.DetailForm[datas[i].key] = results.Params[datas[i].key];
          }
          else if (index === i && datas[i].value) {
            // 处理timeData数据
            this.handleQueryData(datas[i].key, results.Params);
          }
          else if (index === i && datas[i].checkBody) {
            this.getParmeFormatBack(datas[i].key, results.Params);
          }
        }
        else if (datas[i].content && index === i) {
          this.getContentBack(datas[i].content, results.Params);
        }
      }
    },
    /**
     * 获取标签
     * @param {String} className 数据的类型
     * @param {String} value 字段名称
     * @return {String}
     */
    getLabel(className, value) {
      return getLabel(className, value);
    },
    // 初始化内容
    init() {
      this.DetailForm = {};
    },
    setSelectedCars(val) {
      // this.phones = val.map(item => {
      //   return item.phone;
      // });
      this.vehicleIds = val.map(item => {
        return item.id;
      });
    },
    // 时间类型字段
    changeTime(value, key, type) {
      if (type === 'startTime') {
        this.DetailForm[`${key}StartTimeHour`] = value ? value.split(':')[0] : '';
        this.DetailForm[`${key}StartTimeMin`] = value ? value.split(':')[1] : '';
      }
      else {
        this.DetailForm[`${key}EndTimeHour`] = value ? value.split(':')[0] : '';
        this.DetailForm[`${key}EndTimeMin`] = value ? value.split(':')[1] : '';
      }
      if (this.DetailForm[`${key}StartTimeHour`] > this.DetailForm[`${key}EndTimeHour`] || ((this.DetailForm[`${key}StartTimeHour`] === this.DetailForm[`${key}EndTimeHour`]) &&
        (this.DetailForm[`${key}StartTimeMin`] >= this.DetailForm[`${key}EndTimeMin`]))) {
        this.$message({
          message: '禁行时段结束时间不能晚于开始时间！',
          type: 'warning'
        });
        this.timeDataBasic[0].value.endTime = '';
      }
    },
    /**
     * FTP远程升级
     */
    updateFTPTerminal(stringInputUpdate) {
      console.log(stringInputUpdate);
      let parme = {};
      for (let i = 0; i < stringInputUpdate.length; i++) {
        if (stringInputUpdate[i]) {
          stringInputUpdate[i].body.forEach(element => {
            parme[element.key] = this.DetailForm[element.key];
          });
        }
      }
      console.log(parme);
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      parme.phones = this.phones;
      terminalControl(parme).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }
      }).catch(err => {
        console.log(err);
      });
    },
    /**
     * 查询FTP终端远程升级详情
     */
    queryUpdateDetail() {
      let parme = {};
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      // parme.phone = this.changeVehicle.phone;
      parme.device_id = BigInt(this.changeVehicle.deviceId);
      parme.device_type = this.changeVehicle.deviceType;
      queryTerminalUpdate(parme).then(res => {
        if (res.code === 200) {
          this.$refs.updateDetail.showDialog = true;
          this.$refs.updateDetail.setUpdateDetail(res.data.fwVersion);
        }
      }).catch(err => {
        console.log(err);
      });
    },
    handleQueryData(key, datas) {
      this.$set(this.DetailForm, `${key}StartTimeHour`, '');
      this.$set(this.DetailForm, `${key}StartTimeMin`, '');
      this.$set(this.DetailForm, `${key}EndTimeHour`, '');
      this.$set(this.DetailForm, `${key}EndTimeMin`, '');
      if (datas) {
        this.timeDataBasic.forEach(item => {
          if (key === item.key) {
            item.value.startTime = `${datas[`${key}StartTimeHour`]}:${datas[`${key}StartTimeMin`]}`;
            item.value.endTime = `${datas[`${key}EndTimeHour`]}:${datas[`${key}EndTimeMin`]}`;
          }
        });
      }
    },
    otherFuction(parme, element) {
      if (element['key'] === 'start_time' || element['key'] === 'end_time') {
        let reg = new RegExp(':', 'g');

        parme[element.key] = this.DetailForm[element.key].replace(reg, '');
      }
    },
    preSetTerminal(_data, i) { // 处理特殊设置
      if (_data[i].post && _data[i].post === 'terminalcontrol') {
        if (this.phones.length === 0) {
          this.$message({
            type: 'error',
            message: '请先选择终端!'
          });
          return;
        }
        else if (!this.DetailForm.cmd) {
          this.$message({
            type: 'error',
            message: '请选择远程重启项'
          });
          return;
        }
        restartTerminal({
          phones: this.phones,
          cmd: this.DetailForm.cmd
        }).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            // 处理获得的数据
            console.log(res);
          }
        }).catch(err => {
          console.log(err);
        });
      }
      else {
        this.setTerminal(_data, i);
      }
    },
    preQueryTerminal(_data, _index) { // 处理特殊查询
      if (_data[_index].post && _data[_index].post === 'icdriverinfo') {
        if (!this.changeVehicle) {
          this.$message({
            type: 'error',
            message: '请先选择终端!'
          });
          return;
        }
        queryIcdriverinfo({
          device_id: BigInt(this.changeVehicle.deviceId),
          device_type: this.changeVehicle.deviceType,
        }).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            // 处理获得的数据
            console.log(res);
          }
        }).catch(err => {
          console.log(err);
        });
      }
      else {
        this.queryTerminal(_data, _index);
      }
    },
    getContentBack(content, params) {
      content.forEach(element => {
        if (element.style === 'input') {
          this.$set(this.DetailForm, element.key, params[element.key]);
        }
        else if (element.style === 'radio') {
          this.$set(this.DetailForm, element.key, params[element.key]);
        }

        if (element.fuction) {
          this.getContentSpecial(element, params);
        }
      });
    },
    getContentSpecial(data, params) {
      if (data.key === 'start_time' || data.key === 'end_time') {
        this.$set(this.DetailForm, data.key, params[data.key].slice(0, 2) + ':' + params[data.key].slice(2));
      }
    }
  }
};
</script>

<style lang="less" scoped>
// .terminalConfiguration{
//   display: flex;
//   flex-direction: row;
//   height: 100%;
//   margin: 10px;
// }
// .car-list{
//   width: 450px;
//   background-color: @xhBackgroundColor2;
//   margin-right: 10px;
//   border-radius: @xhSpacingBase;
//   box-shadow: @xh-button-shadow;
// }
// .car-content{
//   width: 100%;
//   border-radius: @xhSpacingBase;
//   background: #ffffff;
//   overflow-y: auto;
// }
/deep/ .el-card__header {
  padding: 5px;
}

.work_card {
  background-color: #ffffff;
  border-top: none;
}

.header_room {
  display: flex;
  align-items: center;
}

.header_logo {
  width: 4px;
  height: 18px;
  background: #118de3;
}

.header_title {
  margin-left: 10px;
  font-size: 18px;
}

.header2_title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.header3_title {
  font-size: 14px;
  font-weight: bold;
  margin-left: 15px;
}

.work_house_line {
  margin: 10px 0;
  width: 100%;
  height: 1px;
  background: #ebeef5;
}

.work_house_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.work_item_room {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
}

.work_item_box {
  margin: 2px;
  width: 70vh;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-right: 8px;
  margin-left: 30px;
}

.work_item_box0 {
  margin: 2px;
  width: 70vh;
  display: flex;
  // align-items: center;
  margin-right: 8px;
  margin-left: 30px;
}

.work_item_title {
  width: 30%;
  font-size: 14px;
  color: #909399;
}

.work_item_title0 {
  // width: 30%;
  font-size: 14px;
  color: #909399;
}

.work_item_value {
  color: #303133;
  font-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.work_item_button {
  margin: 5px;
  float: right;
}

::v-deep .el-radio__label {
  width: 100% !important;
  text-overflow: ellipsis;
  white-space: normal;
  // line-height: 18px;
  // word-wrap: break-word !important;    无效
}

.radio_content_item {
  display: flex;
  margin: 2px;
  width: 70vh;
  display: flex;
  align-items: center;
  margin-right: 8px;
  margin-left: 30px;
}

.radio_content_name {
  font-size: 14px;
  color: #909399;
  padding-right: 10px;
}

.picker_content_item {
  display: flex;
  margin: 2px;
  width: 70vh;
  display: flex;
  align-items: center;
  margin-right: 8px;
  margin-left: 30px;
}
.other-container {
  position: relative;
  flex: 1;
}
.mask-operate {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 1;
}
</style>
