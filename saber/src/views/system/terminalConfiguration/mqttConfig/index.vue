<template>
  <div>
    <el-card class="config-card">
      <div class="config-card-header">
        <div
          class="config-card-header-title"
        >
          文本消息下发
        </div>
        <BtnMore
          :is-down="textFormShow"
          @click="textFormShow = !textFormShow"
        />
      </div>
      <div
        v-show="textFormShow"
        class="config-card-body"
      >
        <el-form
          ref="form"
          :model="textForm"
          size="mini"
          class="config-card-form"
          label-width="80px"
        >
          <el-form-item
            label="标题"
            prop="title"
          >
            <el-input
              v-model.trim="textForm.title"
              placeholder="请输入标题"
            />
          </el-form-item>
          <el-form-item
            label="优先级"
            prop="pri"
          >
            <single-select
              v-model="textForm.pri"
              :options="priOptions"
              placeholder="请选择优先级"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="内容"
            prop="msg"
          >
            <el-input
              v-model.trim="textForm.msg"
              type="textarea"
              :rows="4"
              maxlength="255"
              show-word-limit
              placeholder="请输入标题"
            />
          </el-form-item>
        </el-form>
        <div class="config-card-btn">
          <el-button
            type="primary"
            size="small"
            @click="textSubmitHandle"
          >
            确认
          </el-button>
        </div>
      </div>
      <div class="config-line"/>
      <div class="config-card-header">
        <div
          class="config-card-header-title"
        >
          定位与通信频率查询
        </div>
        <BtnMore
          :is-down="frequencyFormShow"
          @click="frequencyFormShow = !frequencyFormShow"
        />
      </div>
      <div
        v-show="frequencyFormShow"
        class="config-card-body"
      >
        <el-form
          ref="form"
          :model="frequencyForm"
          size="mini"
          class="config-card-form"
          label-width="80px"
        >
          <el-form-item
            label="通信频率"
            prop="communication"
          >
            <el-input
              v-model.trim="frequencyForm.communication"
              placeholder="请输入通信频率"
            >
              <template slot="append">秒/次</template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="定位频率"
            prop="location"
          >
            <el-input
              v-model.trim="frequencyForm.location"
              placeholder="请输入定位频率"
            >
              <template slot="append">秒/次</template>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="config-card-btn">
          <el-button
            type="primary"
            size="small"
            @click="frequencySearchHandle"
          >
            查询
          </el-button>
        </div>
      </div>
      <div class="config-card-header">
        <div
          class="config-card-header-title"
        >
          模式频率参数设置
        </div>
        <BtnMore
          :is-down="patternShow"
          @click="patternShow = !patternShow"
        />
      </div>
      <div
        v-show="patternShow"
        class="config-card-body"
      >
        <el-form
          ref="form"
          :model="patternForm"
          size="mini"
          class="config-card-form"
          label-width="80px"
        >
          <el-form-item
            label="类型"
            prop="typ"
          >
            <single-select
              v-model="patternForm.typ"
              :options="typOptions"
              placeholder="请选择类型"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="模式"
            prop="mo"
          >
            <single-select
              v-model="patternForm.mo"
              :options="moOptions"
              placeholder="请选择模式"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="状态"
            prop="sts"
          >
            <single-select
              v-model="patternForm.sts"
              :options="stsOptions"
              placeholder="请选择状态"
              clearable
            />
          </el-form-item>
          <el-form-item
            v-if="patternForm.typ === 8"
            label="采样频率"
            prop="frq"
          >
            <div style="display: flex;">
              <single-select
                v-model="patternForm.frq"
                :options="frqOptions"
                placeholder="请选择采样频率"
                clearable
              />
              <el-input
                v-if="patternForm.frq === 1"
                v-model.trim="frequency"
                placeholder="请输入上报频率"
              >
                <template slot="append">秒/次</template>
              </el-input>
            </div>
          </el-form-item>
          <!-- <el-form-item
            label="时段一"
            prop="time1"
          >
            <el-time-picker
              v-model="patternForm.time1"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时段一"
            />
          </el-form-item>
          <el-form-item
            label="时段二"
            prop="time2"
          >
            <el-time-picker
              v-model="patternForm.time2"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时段二"
            />
          </el-form-item> -->
          <el-form-item
            v-if="patternForm.typ === 14"
            label="上报频率"
            prop="frq"
          >
            <el-input
              v-model.trim="patternForm.frq"
              placeholder="请输入上报频率"
            >
              <template slot="append">秒/次</template>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="config-card-btn">
          <el-button
            type="primary"
            size="small"
            @click="patternSubmitHandle"
          >
            设置
          </el-button>
        </div>
      </div>
      <div class="config-card-header">
        <div
          class="config-card-header-title"
        >
          主服务器地址设置
        </div>
        <BtnMore
          :is-down="primaryShow"
          @click="primaryShow = !primaryShow"
        />
      </div>
      <div
        v-show="primaryShow"
        class="config-card-body"
      >
        <el-form
          ref="form"
          :model="primaryForm"
          size="mini"
          class="config-card-form"
          label-width="120px"
        >
          <el-form-item
            label="IP/域名"
            prop="ip"
          >
            <el-input
              v-model.trim="primaryForm.ip"
              placeholder="请输入IP/域名"
            />
          </el-form-item>
          <el-form-item
            label="入网检测端口"
            prop="port"
          >
            <el-input
              v-model.trim="primaryForm.port"
              placeholder="请输入入网检测端口"
            />
          </el-form-item>
          <el-form-item
            label="数据上报端口"
            prop="dataPort"
          >
            <el-input
              v-model.trim="primaryForm.dataPort"
              placeholder="请输入数据上报端口"
            />
          </el-form-item>
          <el-form-item
            label="MQTT账号"
            prop="user"
          >
            <el-input
              v-model.trim="primaryForm.user"
              placeholder="请输入MQTT账号"
            />
          </el-form-item>
          <el-form-item
            label="MQTT密码"
            prop="password"
          >
            <el-input
              v-model.trim="primaryForm.password"
              type="password"
              placeholder="请输入MQTT密码"
            />
          </el-form-item>
        </el-form>
        <div class="config-card-btn">
          <el-button
            type="primary"
            size="small"
            @click="primarySubmitHandle"
          >
            设置
          </el-button>
        </div>
      </div>
      <div class="config-card-header">
        <div
          class="config-card-header-title"
        >
          副服务器地址设置
        </div>
        <BtnMore
          :is-down="assistantShow"
          @click="assistantShow = !assistantShow"
        />
      </div>
      <div
        v-show="assistantShow"
        class="config-card-body"
      >
        <el-form
          ref="form"
          :model="assistantForm"
          size="mini"
          class="config-card-form"
          label-width="120px"
        >
          <el-form-item
            label="IP/域名"
            prop="ip"
          >
            <el-input
              v-model.trim="assistantForm.ip"
              placeholder="请输入IP/域名"
            />
          </el-form-item>
          <el-form-item
            label="数据上报端口"
            prop="dataPort"
          >
            <el-input
              v-model.trim="assistantForm.dataPort"
              placeholder="请输入数据上报端口"
            />
          </el-form-item>
          <el-form-item
            label="MQTT账号"
            prop="user"
          >
            <el-input
              v-model.trim="assistantForm.user"
              placeholder="请输入MQTT账号"
            />
          </el-form-item>
          <el-form-item
            label="MQTT密码"
            prop="password"
          >
            <el-input
              v-model.trim="assistantForm.password"
              type="password"
              placeholder="请输入MQTT密码"
            />
          </el-form-item>
        </el-form>
        <div class="config-card-btn">
          <el-button
            type="primary"
            size="small"
            @click="assistantSubmitHandle"
          >
            设置
          </el-button>
        </div>
      </div>
      <div class="config-card-header">
        <div
          class="config-card-header-title"
        >
          远程调试开关
        </div>
        <BtnMore
          :is-down="longShow"
          @click="longShow = !longShow"
        />
      </div>
      <div
        v-show="longShow"
        class="config-card-body"
      >
        <el-form
          ref="form"
          :model="longForm"
          size="mini"
          class="config-card-form"
          label-width="80px"
        >
          <el-form-item
            label="远程调试"
            prop="switchStatus"
          >
            <el-switch
              v-model="longForm.switchStatus"
              active-text="开"
              inactive-text="关"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>
        </el-form>
        <div class="config-card-btn">
          <el-button
            type="primary"
            size="small"
            @click="longSubmitHandle"
          >
            设置
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import BtnMore from '@/components/BtnMore.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
import { frequencyConfig, dataSend, mqttControl } from '@/api/system/terminalConfig.js';
export default {
  components: {
    BtnMore,
    SingleSelect
  },
  props: {
    phones: {
      type: Array,
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      textForm: {
        title: '',
        pri: 3,
        msg: ''
      },
      textFormShow: true,
      priOptions: [
        { label: '高', value: 3 },
        { label: '中', value: 2 },
        { label: '低', value: 1 }
      ],
      frequencyForm: {
        communication: null,
        location: null
      },
      frequencyFormShow: true,
      frequency: null,
      patternForm: {
        typ: null,
        mo: null,
        sts: null,
        frq: null,
        // time1: null,
        // time2: null,
      },
      patternShow: true,
      typOptions: [
        { label: '日常GPS位置', value: 8 },
        { label: '上报频率', value: 14 }
      ],
      moOptions: [
        { label: '演示模式', value: 1 },
        { label: '下班模式', value: 2 },
        { label: '工作模式', value: 3 },
        { label: '普通模式', value: 4 },
        { label: '守护模式', value: 5 }
      ],
      stsOptions: [
        { label: '室内移动', value: 1 },
        { label: '室外移动', value: 2 },
        { label: '静止', value: 3 },
        { label: '脱腕', value: 4 }
      ],
      frqOptions: [
        { label: '不上报', value: 0 },
        { label: '实时腕表提醒', value: -1 },
        { label: '实时触发上报', value: -2 },
        { label: '实时腕表提醒并实时触发上报', value: -3 },
        { label: '常开', value: -4 },
        { label: '自定义', value: 1 }
      ],
      primaryForm: {
        ip: null,
        port: null,
        dataPort: null,
        user: null,
        password: null
      },
      primaryShow: true,
      assistantForm: {
        ip: null,
        dataPort: null,
        user: null,
        password: null
      },
      assistantShow: true,
      longForm: {
        switchStatus: 0
      },
      longShow: true
    };
  },
  methods: {
    // 远程调试开关
    longSubmitHandle () {
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      mqttControl({
        deviceId: BigInt(this.phones[0].deviceId),
        control: `set Print_YanZhen,${this.longForm.switchStatus}`
      }).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      });
    },
    // 副服务器地址设置
    assistantSubmitHandle () {
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      mqttControl({
        deviceId: BigInt(this.phones[0].deviceId),
        control: `set viceIP,${this.assistantForm.ip},${this.assistantForm.dataPort},${this.assistantForm.user},${this.assistantForm.password}`
      }).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      });
    },
    // 主服务器地址设置
    primarySubmitHandle () {
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      mqttControl({
        deviceId: BigInt(this.phones[0].deviceId),
        control: `set mainIP,${this.primaryForm.ip},${this.primaryForm.port},${this.primaryForm.dataPort},${this.primaryForm.user},${this.primaryForm.password}`
      }).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      });
    },
    // 模式频率参数设置
    patternSubmitHandle () {
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let params = {
        deviceId: BigInt(this.phones[0].deviceId),
        fr: 'crf',
        ...JSON.parse(JSON.stringify(this.patternForm))
      };
      if (params.typ === 8) {
        params.frq = params.frq === 1 ? this.frequency : params.frq;
      }
      params.frq = Number(params.frq);
      frequencyConfig(params).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      });
    },
    // 定位与通信频率查询
    frequencySearchHandle () {
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      frequencyConfig({
        deviceId: BigInt(this.changeVehicle.deviceId),
        fr: 'crf',
        typ: 20
      }).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '查询成功!'
          });
          if (res.data?.reply) {
            const list = res.data.reply.split(",");
            this.frequencyForm.communication = list[2];
            this.frequencyForm.location = list[3];
          }
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      });
    },
    // 文本消息下发
    textSubmitHandle () {
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      dataSend({
        deviceId: BigInt(this.phones[0].deviceId),
        fr: 'cd',
        ...JSON.parse(JSON.stringify(this.textForm))
      }).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.config-card {
  background-color: #ffffff;
  border-top: none;
}
.config-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.config-card-header-title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}
.config-line {
  margin: 10px 0;
  width: 100%;
  height: 1px;
  background: #ebeef5;
}
.config-card-body {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.config-card-form {
  width: 60%;
}
.config-card-btn {
  width: 60%;
  text-align: right;
}
</style>