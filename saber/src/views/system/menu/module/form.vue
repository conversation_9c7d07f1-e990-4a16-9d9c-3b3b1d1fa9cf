<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看菜单' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 菜单名称 -->
          <el-form-item
            :label="getLabel('name')"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              :disabled="isDetail"
              :placeholder="getPlaceholder('name')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 路由地址 -->
          <el-form-item
            :label="getLabel('path')"
            prop="path"
          >
            <el-input
              v-model.trim="form.path"
              :disabled="isDetail"
              :placeholder="getPlaceholder('path')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 上级菜单 -->
          <el-form-item
            :label="getLabel('parentId')"
            prop="parentName"
          >
            <xh-select
              ref="select"
              v-model="form.parentName"
              :disabled="isDetail || isTreeDisabled"
              clearable
              :placeholder="getPlaceholder('parentId')"
              @clear="handleParentClick"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  :data="menuData"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 菜单图标 -->
          <el-form-item
            :label="getLabel('source')"
            prop="source"
          >
            <el-input
              :value="form.source"
              :disabled="isDetail"
              clearable
              :placeholder="getPlaceholder('source')"
              @clear="form.source = null"
              @focus="dialogVisible = true"
            >
              <template slot="append">
                <i :class="form.source"/>
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 菜单编号 -->
          <el-form-item
            :label="getLabel('code')"
            prop="code"
          >
            <el-input
              v-model.trim="form.code"
              :disabled="isDetail"
              :placeholder="getPlaceholder('code')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 菜单类型 -->
          <el-form-item
            :label="getLabel('category')"
            prop="category"
          >
            <el-radio-group
              v-model="form.category"
              :disabled="isDetail"
              :placeholder="getPlaceholder('category')"
              @change="handleCategoryChange"
            >
              <el-radio :label="1">菜单</el-radio>
              <el-radio :label="2">按钮</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 菜单别名 -->
          <el-form-item
            :label="getLabel('alias')"
            prop="alias"
          >
            <el-input
              v-model.trim="form.alias"
              :disabled="isDetail"
              :placeholder="getPlaceholder('alias')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 菜单排序 -->
          <el-form-item
            :label="getLabel('sort')"
            prop="sort"
          >
            <el-input
              v-model.trim.number="form.sort"
              :disabled="isDetail"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('sort')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 菜单备注 -->
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model.trim="form.remark"
              type="textarea"
              :rows="2"
              :disabled="isDetail"
              :placeholder="getPlaceholder('remark')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
    <IconDialog
      ref="iconDialog"
      :dialog-visible.sync="dialogVisible"
      @handleIconClick="handleIconClick"
    />
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { getMenuTree } from '@/api/system/menuNew';
import IconDialog from './iconDialog.vue';

const defaultForm = {
  id: null,
  name: null,
  path: null,
  parentId: null,
  parentName: null,
  source: null,
  code: null,
  category: 1,
  alias: null,
  sort: null,
  isOpen: 1,
  remark: null
};
export default {
  components: { IconDialog },
  mixins: [form(defaultForm)],
  props: {
    isDetail: {
      type: Boolean,
      default: false
    },
    isTreeDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      rules: {
        name: { required: true, message: '请输入菜单名称', trigger: 'blur' }, // 菜单名称
        path: { required: true, message: '请输入路由地址', trigger: 'blur' }, // 路由地址
        source: { required: true, message: '请输入菜单图标', trigger: 'blur' }, // 菜单图标
        code: { required: true, message: '请输入菜单编号', trigger: 'blur' }, // 菜单编号
        category: { required: true, message: '请选择菜单类型', trigger: 'change' }, // 菜单类型
        alias: { required: true, message: '请输入菜单别名', trigger: 'blur' }, // 菜单别名
        sort: { required: true, message: '请输入菜单排序', trigger: 'blur' }, // 菜单排序
      },
      defaultProps: {
        children: 'children',
        label: 'title',
        value: 'id'
      },
      menuData: [],
      dialogVisible: false
    };
  },
  methods: {
    handleParentClick () {
      this.form.parentId = null;
    },
    handleIconClick (data) {
      this.dialogVisible = false;
      this.form.source = data;
      this.$refs?.form?.validateField('source');
    },
    handleNodeClick (node) {
      this.$set(this.form, 'parentName', node.title);
      this.$set(this.form, 'parentId', node.id);
      this.$refs.select.blur();
    },
    handleCategoryChange () {
      this.$refs?.form?.clearValidate('path');
      if (this.form.category === 1) {
        this.rules.path = { required: true, message: '请输入路由地址', trigger: 'blur' };
      } else {
        this.rules.path = {};
      }
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
      if (this.form.category === 1) {
        this.rules.path = { required: true, message: '请输入路由地址', trigger: 'blur' };
      } else {
        this.rules.path = {};
      }
      getMenuTree().then(res => {
        this.menuData = res.data;
        if (this.form.parentId && this.form.parentId !== '0') {
          this.$nextTick(()=>{
            this.$refs.tree.setCurrentKey(this.form.parentId);
            const node = this.$refs.tree.getNode(this.form.parentId);
            this.form.parentName = node?.data.title;
          });
        } else if (this.form.parentId === '0') {
          this.form.parentId = null;
          this.form.parentName = null;
        }
      });
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
      this.$emit('update:isTreeDisabled', false);
      this.rules.path = { required: true, message: '请输入路由地址', trigger: 'blur' };
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Menu', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Menu', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
