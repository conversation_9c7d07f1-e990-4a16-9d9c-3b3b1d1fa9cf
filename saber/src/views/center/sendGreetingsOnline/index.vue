<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :permission="permission"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          style="width: 100%; height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','TemplateIssue:edit','TemplateIssue:del', 'TemplateIssue:execute', 'TemplateIssue:copy', 'TemplateIssue:start']"
            label="操作"
            width="150"
            :show-overflow-tooltip="true"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="crud.toEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-popover
                :ref="`popover-del-${scope.$index}`"
                placement="top"
                width="250"
                style="margin: 0 8px"
              >
                <p>确定删除本条数据吗？</p>
                <div style="text-align: right; margin-top: 20px">
                  <el-button
                    size="mini"
                    type="text"
                    @click="scope._self.$refs[`popover-del-${scope.$index}`].doClose()"
                  >
                    取消
                  </el-button>
                  <el-button
                    :loading="crud.dataStatus[scope.row.id].delete === 2"
                    type="primary"
                    size="mini"
                    @click="handleDeleteClick(scope)"
                  >
                    确定
                  </el-button>
                </div>
                <el-button
                  slot="reference"
                  type="text"
                  size="mini"
                >
                  删除
                </el-button>
              </el-popover>
<!--              <el-popover-->
<!--                :ref="`popover-execute-${scope.$index}`"-->
<!--                placement="top"-->
<!--                width="250"-->
<!--                style="margin-right: 10px"-->
<!--              >-->
<!--                <p>确定立即执行本条数据吗？</p>-->
<!--                <div style="text-align: right; margin-top: 20px">-->
<!--                  <el-button-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    @click="scope._self.$refs[`popover-execute-${scope.$index}`].doClose()"-->
<!--                  >-->
<!--                    取消-->
<!--                  </el-button>-->
<!--                  <el-button-->
<!--                    type="primary"-->
<!--                    size="mini"-->
<!--                    @click="toExecute(scope)"-->
<!--                  >-->
<!--                    确定-->
<!--                  </el-button>-->
<!--                </div>-->
<!--                <el-button-->
<!--                  slot="reference"-->
<!--                  type="text"-->
<!--                  size="mini"-->
<!--                >-->
<!--                  立即执行-->
<!--                </el-button>-->
<!--              </el-popover>-->
              <el-popover
                :ref="`popover-copy-${scope.$index}`"
                placement="top"
                width="250"
              >
                <p>{{ scope.row.taskState === 1 ? '确定关闭本条数据吗？' : '确定启动本条数据吗？' }}</p>
                <div style="text-align: right; margin-top: 20px">
                  <el-button
                    size="mini"
                    type="text"
                    @click="scope._self.$refs[`popover-copy-${scope.$index}`].doClose()"
                  >
                    取消
                  </el-button>
                  <el-button
                    type="primary"
                    size="mini"
                    @click="toStart(scope)"
                  >
                    确定
                  </el-button>
                </div>
                <el-button
                  slot="reference"
                  type="text"
                  size="mini"
                >
                  {{ scope.row.taskState === 1 ? '关闭任务' : '启动任务' }}
                </el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('taskName')"
            prop="taskName"
            :label="getLabel('taskName')"
            show-overflow-tooltip
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('result')"
            prop="result"
            :label="getLabel('result')"
            show-overflow-tooltip
            min-width="200"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('executeTime')"
            prop="executeTime"
            :label="getLabel('executeTime')"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ scope.row.executeTime ? parseTimes1000(scope.row.executeTime) : '未执行' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('createTime')"
            prop="createTime"
            :label="getLabel('createTime')"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTimes1000(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('taskState')"
            prop="taskState"
            :label="getLabel('taskState')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ handleTaskState(scope.row.taskState) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
      <!--表单渲染-->
      <eForm :dict="dict"/>
    </div>
  </basic-container>
</template>

<script>
import crudSendGreetingsOnline from '@/api/center/sendGreetingsOnline';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import HeadCommon from '@/components/formHead/headCommon.vue';
import eForm from './module/form';
import getLabel from '@/utils/getLabel';

// crud交由presenter持有
const crud = CRUD({
  title: '上线问候语',
  crudMethod: { ...crudSendGreetingsOnline }
});

export default {
  name: 'SendGreetingsOnline',
  components: {
    HeadCommon,
    eForm,
    crudOperation,
    pagination
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [
    'instructType'
  ],
  data() {
    return {
      permission: {
        add: [
          'admin',
          'sendGreetingsOnline:add'
        ],
        edit: [
          'admin',
          'sendGreetingsOnline:edit'
        ],
        del: [
          'admin',
          'sendGreetingsOnline:del'
        ],
        execute: [
          'admin',
          'sendGreetingsOnline:execute'
        ],
        start: [
          'admin',
          'sendGreetingsOnline:start'
        ]
      },
      dialogFormVisible: false,
      dataForm: {
        taskResult: 1,
        taskName: ''
      },
      rules: {
        taskName: {
          required: true,
          message: '请输入任务名称',
          trigger: 'blur'
        }
      },
      taskData: null,
      headConfig: {
        initQuery: true,
        item: {
          3: {
            name: '任务名称',
            type: 'input',
            value: 'taskName'
          },
          4: {
            name: '开始日期',
            type: 'datetime',
            value: 'startTime',
          },
          5: {
            name: '结束日期',
            type: 'datetime',
            value: 'endTime',
          }
        },
        button: {}
      }
    };
  },
  methods: {
    handleTaskState(val) {
      let str = '';
      switch (val) {
      case 1:
        str = '开启';
        break;
      case 2:
        str = '关闭';
        break;
      case 3:
        str = '过期';
        break;
      case 4:
        str = '已完成';
        break;
      }
      return str;
    },
    toStart(scope) {
      let query = {
        taskId: scope.row.id,
        taskState: scope.row.taskState === 1 ? 2 : 1
      };
      crudSendGreetingsOnline.stateEdit(query).then((res) => {
        this.$message.success(`${scope.row.taskState === 1 ? '关闭任务' : '开启任务'}成功`);
        this.crud.refresh();
      });
      this.$nextTick(() => {
        scope._self.$refs[`popover-copy-${scope.$index}`].doClose();
      });
    },
    closed() {
      this.$refs['dataForm'].resetFields();
    },
    toExecute(scope) {
      let query = {
        taskId: scope.row.id
      };
      crudSendGreetingsOnline.execute(query).then((res) => {
        this.$message.success('执行成功');
        this.crud.refresh();
      });
      this.$nextTick(() => {
        scope._self.$refs[`popover-execute-${scope.$index}`].doClose();
      });
    },
    handleDeleteClick(scope) {
      this.crud.doDelete(scope.row);
      this.$nextTick(() => {
        scope._self.$refs[`popover-del-${scope.$index}`].doClose();
      });
    },
    /**
     * 跳转详情页
     * @param {String} data
     * @return {String}
     */
    toDetails(data) {
      this.$router.push({
        path: '/center/issueDetails',
        query: {
          id: data.id,
          isRouter: this.$route.fullPath
        }
      });
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('TemplateIssue', value);
    },
    parseTimes(time) {
      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
    },
    parseTimes1000(time) {
      return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.platformRecord {
  margin: 10px;
  border: 1px solid #e5e7e9;
  box-shadow: 1px 1px 5px #88888850;
  background: #ffffff;
}

::v-deep .el-table th {
  text-align: center;
}

::v-deep .el-table td {
  text-align: center;
}

.app-container ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1;
}
</style>
