<template>
  <div class="monitor-instruction-dialog">
    <el-form
      ref="form"
      size="small"
      label-width="120px"
    >
      <el-form-item
        :label="getLabel('flag')"
        prop="flag"
      >
        <xh-select
          v-model="flag"
          size="small"
          filterable
          clearable
          style="width: 370px;"
          :placeholder="getPlaceholder('flag')"
        >
          <el-option
            v-for="(item, index) in flagOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-form-item>
      <el-form-item
        :label="getLabel('phone')"
        prop="phone"
      >
        <el-input
          v-model="phone"
          :placeholder="getPlaceholder('phone')"
          style="width: 370px;"
        />
      </el-form-item>
      <span class="text-footer">
        <el-button
          size="small"
          type="primary"
          @click="handleMonitorClick"
        >
          开始监听
        </el-button>
      </span>
    </el-form>
  </div>
</template>
<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import {sendMonitorMsg} from '@/api/center/instruction.js';
export default {
  name: 'MonitorInstruction',
  components: {
  },
  data () {
    return {
      phone: '',
      flag: 0,
      flagOptions: [
        {
          value: 0,
          label: '普通通话'
        },
        {
          value: 1,
          label: '监听'
        }
      ],
      selectedCars: null,
      successObj: {}
    };
  },
  methods: {
    setPhotoCar (val) {
      this.selectedCars = val;
      console.log(val)
    },
    handleMonitorClick () {
      let selectedCars = this.selectedCars;
      console.log(selectedCars);
      // 后端要求点击监听后, 需要60s后才能再次点击, 期间不再调用接口, 直接返回成功
      if (this.successObj[selectedCars.deviceType + '-' + selectedCars.deviceId]) {
        this.$message({
          type: 'success',
          message: '操作成功'
        });
        return;
      }
      if (!selectedCars) {
        this.$message({
          type: 'error',
          message: '请选择终端！'
        });
        return;
      }
      if (!this.phone) {
        this.$message({
          type: 'error',
          message: '请输入电话号码！'
        });
        return;
      }
      let parme = {
        deviceType: selectedCars.deviceType,
        flag: this.flag,
        phone: this.phone,
        deviceId: BigInt(selectedCars.deviceId)
      };
      sendMonitorMsg(parme).then(res => {
        this.successObj[selectedCars.deviceType + '-' + selectedCars.deviceId] = 1;
        setTimeout(() => {
          this.successObj[selectedCars.deviceType + '-' + selectedCars.deviceId] = 0;
        }, 60 * 1000);
        this.$message({
          type: 'success',
          message: '操作成功'
        });
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('MonitorInstruction', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('MonitorInstruction', value);
    }
  }
};
</script>
<style lang="less" scoped>
  .monitor-instruction-dialog{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .text-footer{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
  }
</style>
