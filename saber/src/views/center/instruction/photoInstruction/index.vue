<template>
  <div class="text-instruction-dialog">
    <el-form
      ref="form"
      size="small"
      :inline="true"
      label-width="120px"
      label-position="right"
    >
      <el-form-item :label="getLabel('channel')">
        <xh-select
          v-model="channel"
          :placeholder="getPlaceholder('channel')"
          size="mini"
          class="column-width"
        >
          <el-option
            v-for="item in options"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-form-item>
      <el-form-item :label="getLabel('resolution')">
        <xh-select
          v-model="resolution"
          :placeholder="getPlaceholder('resolution')"
          size="mini"
          class="column-width"
        >
          <el-option
            v-for="item in resolutionOptions"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-form-item>
      <el-form-item :label="getLabel('picNum')">
        <el-input
          v-model="picNum"
          :placeholder="getPlaceholder('picNum')"
          size="mini"
          class="column-width"
          oninput="if(value>10)value=10;if(value<0)value=0;"
          onkeyup="value=value.replace(/[^0-9]/g,'')"
        >
          <template
            slot="append"
          >
            张
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="getLabel('interval')">
        <el-input
          v-model="interval"
          :placeholder="getPlaceholder('interval')"
          size="mini"
          class="column-width"
        >
          <template
            slot="append"
          >
            秒
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="getLabel('ratio')">
        <el-slider
          v-model="ratio"
          :step="1"
          input-size="small"
          :min="1"
          :max="10"
          class="column-width"
        />
      </el-form-item>
      <el-form-item :label="getLabel('saveFlag')">
        <el-radio-group
          v-model="saveFlag"
          size="small"
          class="column-width"
        >
          <el-radio
            :label="0"
            border
          >
            上传
          </el-radio>
          <el-radio
            :label="1"
            border
          >
            保存
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="getLabel('contrast')">
        <el-slider
          v-model="contrast"
          :step="1"
          input-size="small"
          :min="0"
          :max="127"
          class="column-width"
        />
      </el-form-item>
      <el-form-item :label="getLabel('light')">
        <el-slider
          v-model="light"
          :step="1"
          input-size="small"
          :min="0"
          :max="255"
          class="column-width"
        />
      </el-form-item>
      <el-form-item :label="getLabel('color')">
        <el-slider
          v-model="color"
          :step="1"
          input-size="small"
          :min="0"
          :max="255"
          class="column-width"
        />
      </el-form-item>
      <el-form-item :label="getLabel('saturation')">
        <el-slider
          v-model="saturation"
          :step="1"
          input-size="small"
          :min="0"
          :max="127"
          class="column-width"
        />
      </el-form-item>
    </el-form>
    <span class="text-footer">
      <el-button
        size="small"
        type="primary"
        @click="sendPhotoMsg"
      >
        立即拍照
      </el-button>
    </span>
  </div>
</template>
<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import {sendPhotoMsg} from '@/api/center/instruction.js';
export default {
  name: 'PhotoInstruction',
  components: {
  },
  data () {
    return {
      selectedCars: null,
      channel: null,
      resolution: 0x00,
      ratio: 5,
      light: 127,
      contrast: 64,
      saveFlag: 0,
      color: 127,
      saturation: 64,
      picNum: 1,
      interval: 0,
      options: [
        // {
        //   value: 1,
        //   label: '通道1'
        // },
        // {
        //   value: 2,
        //   label: '通道2'
        // },
        // {
        //   value: 3,
        //   label: '通道3'
        // },
        // {
        //   value: 4,
        //   label: '通道4'
        // }
      ],
      resolutionOptions: [
        {
          value: 0x00,
          label: '最低分辨率'
        },
        {
          value: 0x01,
          label: '320*240'
        },
        {
          value: 0x02,
          label: '640*480'
        },
        {
          value: 0x03,
          label: '800*600'
        },
        {
          value: 0x04,
          label: '1024*768'
        },
        {
          value: 0x05,
          label: '176*144 [Qcif]'
        },
        {
          value: 0x06,
          label: '352*288 [Cif]'
        },
        {
          value: 0x07,
          label: '704*288 [HALF DI]'
        },
        {
          value: 0x08,
          label: '704*576 [DI]'
        },
        {
          value: 0xff,
          label: '最高分辨率'
        }
      ],
      picUrl: ''
    };
  },
  methods: {
    setPhotoCar (val) {
      // this.selectedCars = val;
    },
    getSingleCarOriInfo (data) {
      if (data.children && data.children.length > 0) {
        let arr = data.children.map(item => {
          return {
            value: item.channel,
            label: item.name
          };
        });
        this.options = arr;
        this.channel = this.options[0].value;
      }
      this.selectedCars = data;
    },
    sendPhotoMsg () {
      let selectedCars = this.selectedCars;
      if (!selectedCars) {
        this.$message({
          type: 'error',
          message: '请选择车辆！'
        });
        return;
      }
      let parme = {
        deviceType: selectedCars.deviceType,
        channel: this.channel,
        picNum: parseInt(this.picNum),
        interval: parseInt(this.interval),
        saveFlag: this.saveFlag,
        resolution: this.resolution,
        ratio: this.ratio,
        light: this.light,
        contrast: this.contrast,
        saturation: this.saturation,
        color: this.color,
        deviceId: BigInt(selectedCars.deviceId)
      };
      sendPhotoMsg(parme).then(res => {
        // FIXME: 处理拍照获得的图片
        this.picUrl = res.data;
        this.$message({
          type: 'success',
          message: '操作成功'
        });
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('PhotoInstruction', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('PhotoInstruction', value);
    }
  }
};
</script>
<style lang="less" scoped>
@import '../../../../assets/less/variables.less';
  .text-instruction-dialog{
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
  }
  .column-width{
    width: @columnWidth;
  }
  .text-footer{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
  }
</style>
