<template>
  <div>
    <!-- 车组 -->
    <div class="xh-page-searchItem-content-auto">
      <DeptSingleSelect
        ref="deptId"
        v-model="query.deptId"
      />
    </div>
    <el-input
      v-model="query.licencePlate"
      size="small"
      :placeholder="getPlaceholder('licencePlate')"
      clearable
      style="width: 200px;"
      class="filter-item"
    />
    <div class="xh-page-searchItem-content-auto">
      <xh-select
        v-model="query.plateColor"
        clearable
        size="small"
        :placeholder="getPlaceholder('plateColor')"
      >
        <el-option
          v-for="item in dict.dict.vehicleColor"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </xh-select>
    </div>
    <div class="xh-page-searchItem-content-auto">
      <xh-select
        v-model="query.executeResult"
        clearable
        size="small"
        :placeholder="getPlaceholder('executeResult')"
        class="filter-item"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </xh-select>
    </div>
    <div class="xh-page-searchItem-content-auto">
      <xh-select
        v-model="query.commandExecuteResult"
        clearable
        size="small"
        :placeholder="getPlaceholder('commandExecuteResult')"
        class="filter-item"
      >
        <el-option
          v-for="item in resultOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </xh-select>
    </div>
    <!-- <div class="xh-page-searchItem-content-auto">
      <xh-select
        v-model="query.commandType"
        clearable
        size="small"
        :placeholder="getPlaceholder('commandType')"
        class="filter-item"
      >
        <el-option
          v-for="item in instructType"
          :key="item.id"
          :label="item.value"
          :value="item.value"
        />
      </xh-select>
    </div> -->
    <el-button
      class="filter-item"
      size="mini"
      type="primary"
      icon="el-icon-refresh-left"
      @click="clearClick"
    >
      清除
    </el-button>
    <el-button
      class="filter-item"
      size="mini"
      type="primary"
      icon="el-icon-search"
      @click="toQuery"
    >
      查 询
    </el-button>
    <div>
      <el-button
        :loading="downloadLoading"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-download"
        @click="doExport"
      >
        导出
      </el-button>
    </div>
  </div>
</template>

<script>
import getPlaceholder from '@/utils/getPlaceholder';
import DeptSingleSelect from '@/components/select/DeptSingleSelect/DeptSingleSelectNoDefault';
import { queryparamconfigtype } from '@/api/center/templateIssue';
export default {
  components: { DeptSingleSelect },
  props: {
    dict: {
      type: Object,
      required: true
    },
    downloadLoading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      typeOptions: [
        {
          value: 1,
          label: '下发成功'
        },
        {
          value: 2,
          label: '下发失败'
        },
        {
          value: 3,
          label: '未执行'
        }
      ],
      resultOptions: [
        {
          value: 1,
          label: '执行成功'
        },
        {
          value: 2,
          label: '执行失败'
        },
        {
          value: 3,
          label: '部分成功'
        }
      ],
      query: {
        deptId: undefined,
        licencePlate: undefined,
        plateColor: undefined,
        executeResult: undefined,
        commandType: undefined,
        commandExecuteResult: undefined
      },
      instructType: []
    };
  },
  mounted () {
    // this.getParameterData();
  },
  methods: {
    getParameterData () {
      queryparamconfigtype().then(res => {
        this.instructType = res.data.content;
      });
    },
    doExport () {
      this.$emit('doExport', this.query);
    },
    toQuery () {
      this.$emit('toQuery', this.query);
    },
    clearClick () {
      this.query = {
        licencePlate: undefined,
        plateColor: undefined,
        executeResult: undefined,
        commandType: undefined,
        commandExecuteResult: undefined
      };
      this.$refs.deptId.selectedDeptId = [];
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('TemplateIssue', value);
    }
  }
};
</script>
