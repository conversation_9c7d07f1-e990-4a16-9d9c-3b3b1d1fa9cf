<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :permission="permission"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
          width="170"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          style="width: 100%; height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','InstructTask:edit','InstructTask:del', 'InstructTask:execute', 'InstructTask:copy', 'InstructTask:start']"
            label="操作"
            :width="elTableColumnWidth"
            :show-overflow-tooltip="true"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-button
                v-permission="permission.edit"
                size="mini"
                type="text"
                @click="crud.toEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="toDetails(scope.row)"
              >
                详情
              </el-button>
              <el-popover
                :ref="`popover-del-${scope.$index}`"
                placement="top"
                width="250"
                style="margin: 0 8px"
              >
                <p>确定删除本条数据吗？</p>
                <div style="text-align: right; margin-top: 20px">
                  <el-button
                    size="mini"
                    type="text"
                    @click="scope._self.$refs[`popover-del-${scope.$index}`].doClose()"
                  >
                    取消
                  </el-button>
                  <el-button
                    :loading="crud.dataStatus[scope.row.id].delete === 2"
                    type="primary"
                    size="mini"
                    @click="handleDeleteClick(scope)"
                  >
                    确定
                  </el-button>
                </div>
                <el-button
                  slot="reference"
                  v-permission="permission.del"
                  type="text"
                  size="mini"
                >
                  删除
                </el-button>
              </el-popover>
              <el-popover
                :ref="`popover-execute-${scope.$index}`"
                placement="top"
                width="250"
                style="margin-right: 8px"
              >
                <p>确定立即执行本条数据吗？</p>
                <div style="text-align: right; margin-top: 20px">
                  <el-button
                    size="mini"
                    type="text"
                    @click="scope._self.$refs[`popover-execute-${scope.$index}`].doClose()"
                  >
                    取消
                  </el-button>
                  <el-button
                    type="primary"
                    size="mini"
                    @click="toExecute(scope)"
                  >
                    确定
                  </el-button>
                </div>
                <el-button
                  slot="reference"
                  v-permission="permission.execute"
                  type="text"
                  size="mini"
                >
                  立即执行
                </el-button>
              </el-popover>
              <el-button
                v-permission="permission.copy"
                size="mini"
                type="text"
                @click="handleCopy(scope)"
              >
                复制任务
              </el-button>
              <el-popover
                :ref="`popover-copy-${scope.$index}`"
                placement="top"
                width="250"
                style="margin: 0 8px"
              >
                <p>{{ scope.row.taskState === 1 ? '确定关闭本条数据吗？' : '确定启动本条数据吗？' }}</p>
                <div style="text-align: right; margin-top: 20px">
                  <el-button
                    size="mini"
                    type="text"
                    @click="scope._self.$refs[`popover-copy-${scope.$index}`].doClose()"
                  >
                    取消
                  </el-button>
                  <el-button
                    type="primary"
                    size="mini"
                    @click="toStart(scope)"
                  >
                    确定
                  </el-button>
                </div>
                <el-button
                  slot="reference"
                  v-permission="permission.start"
                  type="text"
                  size="mini"
                >
                  {{ scope.row.taskState === 1 ? '关闭任务' : '启动任务' }}
                </el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('taskName')"
            prop="taskName"
            :label="getLabel('taskName')"
            width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('executeMaxCount')"
            prop="executeMaxCount"
            :label="getLabel('executeMaxCount')"
            width="140"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('result')"
            prop="result"
            :label="getLabel('result')"
            show-overflow-tooltip
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('startTime')"
            prop="startTime"
            :label="getLabel('taskTime')"
            width="350"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTimes1000(scope.row.startTime) }} 至 {{ parseTimes1000(scope.row.endTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('executeTime')"
            prop="executeTime"
            :label="getLabel('executeTime')"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ scope.row.executeTime ? parseTimes1000(scope.row.executeTime) : '未执行' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('createTime')"
            prop="createTime"
            :label="getLabel('createTime')"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTimes1000(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('taskState')"
            prop="taskState"
            :label="getLabel('taskState')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ handleTaskState(scope.row.taskState) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm :dict="dict" />
      <el-dialog
        title="复制任务"
        :append-to-body="true"
        :visible.sync="dialogFormVisible"
        :close-on-click-modal="false"
        @closed="closed"
      >
        <el-form
          ref="dataForm"
          :model="dataForm"
          label-width="100px"
          :rules="rules"
        >
          <el-form-item
            label="任务名称"
            prop="taskName"
          >
            <el-input
              v-model="dataForm.taskName"
              placeholder="请输入任务名称"
            />
          </el-form-item>
          <el-form-item
            label="任务选择"
          >
            <el-select
              v-model="dataForm.taskResult"
              placeholder="请选择要复制的任务"
            >
              <el-option
                label="全部车辆"
                :value="1"
              />
              <el-option
                label="执行失败和未执行的车辆"
                :value="2"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div
          slot="footer"
          class="dialog-footer"
        >
          <el-button
            size="small"
            @click="dialogFormVisible = false"
          >
            取 消
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="toCopy('dataForm')"
          >
            确 定
          </el-button>
        </div>
      </el-dialog>
    </div>
  </basic-container>
</template>

<script>
import crudTemplateIssue from '@/api/center/instructTask';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import HeadCommon from '@/components/formHead/headCommon.vue';
import eForm from './module/form';
import getLabel from '@/utils/getLabel';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('InstructTask', 'uniName'),
  crudMethod: { ...crudTemplateIssue },
  queryOnPresenterCreated: false
});

export default {
  name: 'InstructTask',
  components: {
    eForm,
    crudOperation,
    pagination,
    HeadCommon
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: ['licenceColor', 'instructType', "bdmDeviceType"],
  data () {
    return {
      permission: {
        add: ['admin', 'InstructTask:add'],
        edit: ['admin', 'InstructTask:edit'],
        del: ['admin', 'InstructTask:del'],
        execute: ['admin', 'InstructTask:execute'],
        copy: ['admin', 'InstructTask:copy'],
        start: ['admin', 'InstructTask:start']
      },
      elTableColumnWidth: 300,
      dialogFormVisible: false,
      dataForm: {
        taskResult: 1,
        taskName: ''
      },
      rules: {
        taskName: { required: true, message: '请输入任务名称', trigger: 'blur' },
      },
      taskData: null,
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: "终端类型",
            type: "select",
            filterable: true,
            value: "deviceCategory",
            dictOptions: "bdmDeviceType",
          },
          2: {
            name: '开始日期',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束日期',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '任务名称',
            type: 'input',
            value: 'taskName'
          },
          5: {
            name: "监控对象",
            type: "input",
            value: "targetName",
          },
          6: {
            name: '所属机构',
            type: 'extra',
            value: 'deptId'
          }
        },
        button: {}
      },
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  methods: {
    handleTaskState(val) {
      let str = '';
      switch (val) {
      case 1:
        str = '开启';
        break;
      case 2:
        str = '关闭';
        break;
      case 3:
        str = '过期';
        break;
      case 4:
        str = '已完成';
        break;
      }
      return str;
    },
    toStart (scope) {
      let query = {
        taskId: scope.row.id,
        taskState: scope.row.taskState === 1 ? 2 : 1
      };
      crudTemplateIssue.stateEdit(query).then((res) => {
        this.$message.success(`${scope.row.taskState === 1 ? '关闭任务' : '开启任务'}成功`);
        this.crud.refresh();
      });
      this.$nextTick(() => {
        scope._self.$refs[`popover-copy-${scope.$index}`].doClose();
      });
    },
    handleCopy (scope) {
      this.dialogFormVisible = true;
      this.dataForm.taskResult = 1;
      this.dataForm.taskName = scope.row.taskName + '(复制任务)';
      this.taskData = scope.row;
    },
    closed() {
      this.$refs['dataForm'].resetFields();
    },
    toCopy (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let query = {
            taskId: this.taskData.id,
            taskResult: this.dataForm.taskResult,
            taskName: this.dataForm.taskName
          };
          crudTemplateIssue.copy(query).then((res) => {
            this.dialogFormVisible = false;
            this.$message.success('复制任务成功');
            this.crud.refresh();
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    toExecute (scope) {
      let query = {
        taskId: scope.row.id
      };
      crudTemplateIssue.execute(query).then((res) => {
        this.$message.success('执行成功');
        this.crud.refresh();
      });
      this.$nextTick(() => {
        scope._self.$refs[`popover-execute-${scope.$index}`].doClose();
      });
    },
    handleDeleteClick (scope) {
      this.crud.doDelete(scope.row);
      this.$nextTick(() => {
        scope._self.$refs[`popover-del-${scope.$index}`].doClose();
      });
    },
    /**
     * 跳转详情页
     * @param {String} data
     * @return {String}
     */
    toDetails (data) {
      sessionStorage.setItem('instructDetailId', data.id)
      this.$router.push({ path: '/center/instructDetails', query: {isRouter: this.$route.fullPath} });
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('InstructTask', value);
    },
    parseState (value) {
      return value === 0 ? '未执行' : value === 1 ? '执行失败' : '执行成功';
    },
    parseTimes (time) {
      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
    },
    parseTimes1000 (time) {
      return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
 .platformRecord{
  margin: 10px;
  border: 1px solid #e5e7e9;
  box-shadow: 1px 1px 5px #88888850;
  background: #fff;
 }
 ::v-deep.el-table th{
  text-align: center;
}
::v-deep.el-table td{
  text-align: center;
}
.app-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: #fcf0c1;
}
</style>
