<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="!isDetail ? crud.status.title : '查看' "
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="isDetail"
      label-width="100px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('name')"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              :placeholder="getPlaceholder('name')"
              :disabled="isDetail"
              @input="e => form.name = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('version')"
            prop="version"
          >
            <el-input
              v-model.trim="form.version"
              :placeholder="getPlaceholder('version')"
              :disabled="isDetail"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('category')"
            prop="category"
          >
            <single-select
              v-model="form.category"
              :options="dict.upgradeCategory"
              :placeholder="getPlaceholder('category', 'select')"
              clearable
              :disabled="isDetail"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('deviceModel')"
            prop="deviceModel"
          >
            <xh-select
              v-model="form.deviceModel"
              :placeholder="getPlaceholder('deviceModel')"
              clearable
              filterable
              :disabled="isDetail"
            >
              <el-option
                v-for="item in dict.bdmDeviceModel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('vendor')"
            prop="vendor"
          >
            <el-input
              v-model.trim="form.vendor"
              :placeholder="getPlaceholder('vendor')"
              :disabled="isDetail"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            :label="getLabel('fileUrl')"
            prop="fileUrl"
          >
            <el-upload
              ref="upload"
              :class="{'upload-file': isDetail}"
              action="/bdsPlatformApi/vdm-base-info/base/file/up"
              :on-remove="handleRemove"
              :headers="uploadHeaders"
              :on-success="handleSuccess"
              :on-exceed="handleLimit"
              :file-list="fileList"
              :before-upload="beforeUpload"
              :limit="1"
            >
              <el-button
                v-if="!isDetail"
                slot="trigger"
                size="small"
                type="primary"
              >
                选取文件
              </el-button>
            </el-upload>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model.trim="form.remark"
              type="textarea"
              :placeholder="getPlaceholder('remark')"
              :disabled="isDetail"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="!isDetail"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="!isDetail"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
import { getToken } from '@/util/auth';
import website from "@/config/website";
import md5 from "js-md5";
const defaultForm = {
  name: null,
  version: null,
  category: null,
  deviceModel: null,
  fileUrl: null,
  remark: null,
  md5Hash: null,
  vendor: null,
  size: null
};
export default {
  components: {
    SingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        name: {
          required: true,
          message: '请输入文件名',
          trigger: 'blur'
        },
        version: {
          required: true,
          message: '请输入版本名称',
          trigger: 'blur'
        },
        category: {
          required: true,
          message: '请选择软件类型',
          trigger: 'change'
        },
        deviceModel: {
          required: true,
          message: '请输入终端型号',
          trigger: 'blur'
        }
      },
      uploadHeaders: {}, // upload token
      fileList: []
    };
  },
  mounted () {
    if (window.localStorage.hasOwnProperty("cedi__Access-Token")) {
      let currentToken =window.localStorage.getItem("cedi__Access-Token");
      let currentTokenValue= JSON.parse(currentToken).value;
      this.uploadHeaders = {
        [website.ceTokenHeader]: '' + currentTokenValue
      };
    } else {
      this.uploadHeaders = {
        [website.tokenHeader]: 'bearer ' + getToken()
      };
    }
  },
  methods: {
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      if (!this.form.fileUrl) {
        this.$message.warning('请上传安装包文件');
        return false;
      }
    },
    handleRemove () {
      this.form.fileUrl = '';
      this.form.md5Hash = '';
    },
    async beforeUpload (file) {
      const buffer = await file.arrayBuffer();
      this.form.md5Hash = md5(new Uint8Array(buffer));
      this.form.size = file.size;
    },
    // 图片上传成功回调
    handleSuccess(file){
      if (file.code === 200 && file.data) {
        this.form.fileUrl = file.data;
      } else {
        this.$message.warning('上传失败');
      }
    },
    handleLimit() {
      this.$message.warning('只能上传1个文件');
    },
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU] () {
      if (this.form.category != null) this.form.category = this.form.category.toString();
      if (this.form.fileUrl) {
        this.fileList.push({
          name: this.form.fileUrl.substring(this.form.fileUrl.lastIndexOf('/') + 1),
          url: this.form.fileUrl
        });
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('UpgradePatch', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value, pre) {
      return getPlaceholder('UpgradePatch', value, pre);
    },
    // 监听关闭事件
    closed () {
      this.fileList = [];
      this.$emit('update:isDetail', false);
    }
  }
};
</script>
<style lang="less" scoped>
.upload-file {
  /deep/ .el-upload--text {
    display: none;
  }
}
</style>
