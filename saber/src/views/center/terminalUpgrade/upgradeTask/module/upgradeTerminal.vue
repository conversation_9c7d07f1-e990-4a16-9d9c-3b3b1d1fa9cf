<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :visible="dialogVisible"
    title="关联终端"
    append-to-body
    width="40%"
  >
    <el-row>
      <el-form
        ref="form"
        :label-width="labelWidth"
      >
        <div class="xh-header-content">
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12 xh-header-content-item">
            <el-form-item label="终端序列号">
              <el-input
                v-model="query.deviceUniqueId"
                clearable
                size="small"
                placeholder="请输入终端序列号"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-md-12 no-print xh-crud-search">
            <el-form-item>
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchClick"
              >查 询
              </el-button>
              <el-button
                class="filter-item clear-item"
                size="small"
                icon="el-icon-refresh-left"
                @click="clearClick"
              >重 置
              </el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-row>
    <!--表格渲染-->
    <u-table
      ref="table"
      v-loading="loading"
      use-virtual
      :data="tableData"
      :row-height="54"
      :cell-style="{'text-align':'center'}"
      :border="false"
      height="600px"
    >
      <u-table-column
        type="index"
        label="#"
        width="80"
      />
      <u-table-column
        label="所属机构"
        prop="deviceDeptName"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      />
      <u-table-column
        label="终端序列号"
        prop="deviceUniqueId"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      />
      <u-table-column
        label="升级状态"
        prop="upgradeState"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      >
        <template slot-scope="scope">
          <span>{{ stateObj[scope.row.upgradeState] }}</span>
        </template>
      </u-table-column>
      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </u-table>
    <el-pagination
      background
      layout="total, prev, pager, next, sizes"
      :current-page="query.current"
      :page-size="query.size"
      :page-sizes="[10, 20, 30, 40, 50, 100]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-dialog>
</template>
<script>
import { upgradeTerminalState } from '@/api/center/upgradeTask';
export default {
  props:{
    dialogVisible:{
      type: Boolean,
      default: false
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    taskId: {
      type: Number,
      default: 0
    },
    stateObj: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data(){
    return{
      labelWidth: '100px',
      query: {
        deviceUniqueId: '',
        current: 1,
        size: 10
      },
      tableData: [],
      loading: false,
      total: 0
    };
  },
  watch: {
    dialogVisible: {
      handler (newVal) {
        if (newVal) {
          this.getData();
        }
      }
    }
  },
  methods:{
    // 选择每页展示条数
    handleSizeChange (val) {
      this.query.size = val;
      this.getData();
    },
    // 点击分页按钮
    handleCurrentChange (val) {
      this.query.current = val;
      this.getData();
    },
    searchClick(){
      this.getData();
    },
    beforeClose(){
      this.$emit('update:dialogVisible', false);
      this.query = {
        deviceUniqueId: '',
        current: 1,
        size: 10
      };
      this.tableData = [];
      this.total = 0;
    },
    getData(){
      this.loading = true;
      const params = {
        ...JSON.parse(JSON.stringify(this.query)),
        taskId: this.taskId
      };
      upgradeTerminalState(params).then((res)=>{
        this.loading = false;
        this.tableData = res.data.content;
        this.total = res.data.total;
      }).catch((err)=>{
        this.loading = false;
      });
    },
    clearClick() {
      this.query = {
        deviceUniqueId: '',
        current: 1,
        size: 10
      };
      this.getData();
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    }
  }
};
</script>
<style lang="less" scoped>
.xh-crud-search{
    ::v-deep .el-form-item__content{
      margin-left: 0 !important;
    }
}
</style>
