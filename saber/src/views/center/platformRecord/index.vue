<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="86px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation :permission="permission" />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%; height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
              type="selection"
              width="50"
            />
          <el-table-column
            v-if="columns.visible('deviceCategory')"
            prop="deviceCategory"
            :label="getLabel('deviceCategory')"
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deviceUniqueId')"
            prop="deviceUniqueId"
            :label="getLabel('deviceUniqueId')"
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deviceNum')"
            prop="deviceNum"
            :label="getLabel('deviceNum')"
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('targetName')"
            prop="targetName"
            :label="getLabel('targetName')"
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.targetName || $utils.emptymap.targetName }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('type')"
            prop="type"
            :label="getLabel('type')"
            min-width="110"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('param')"
            prop="param"
            :label="getLabel('param')"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('updateTime')"
            prop="updateTime"
            :label="getLabel('updateTime')"
            width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTimes(scope.row.updateTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('resParam')"
            prop="resParam"
            :label="getLabel('resParam')"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ parseState(scope.row.resParam) }}</span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
    </div>
  </basic-container>
</template>

<script>
import crudPlatformRecord from "@/api/center/platformRecord.js";
import CRUD, { presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import getLabel from "@/utils/getLabel";
import HeadCommon from "@/components/formHead/headCommon.vue";
// crud交由presenter持有
const crud = CRUD({
  title: getLabel("PlatformRecord", "uniName"),
  crudMethod: { ...crudPlatformRecord },
  queryOnPresenterCreated: false
});

export default {
  name: "PlatformRecord",
  components: {
    HeadCommon,
    crudOperation,
    pagination,
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: ["licenceColor", "instructType", "bdmDeviceType___onlyChild"],
  data() {
    return {
      permission: {
        add: ['admin', 'platformrecord:add'],
        edit: ['admin', 'platformrecord:edit'],
        del: ['admin', 'platformrecord:del']
      },
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: "赋码编号",
            type: "input",
            value: "deviceNum",
          },
          2: {
            name: '开始日期',
            type: 'datetime',
            value: 'startTime',
            defaultFn: 'stDE'
          },
          3: {
            name: '结束日期',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: "终端类型",
            type: "select",
            filterable: true,
            value: "deviceCategory",
            dictOptions: "bdmDeviceType",
          },
          5: {
            name: "序列号",
            type: "input",
            value: "deviceUniqueId",
          },
          6: {
            name: "监控对象",
            type: "input",
            value: "targetName",
          },
          7: {
            name: "指令类型",
            type: "select",
            value: "type",
            dictOptions: "instructType",
          },
        },
        button: {
        }
      }
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling() {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    },
  },
  mounted() {},
  methods: {
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },

    //
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('PlatformRecord', value);
    },
    parseState (value) {
      return value ? '未响应' : '已响应';
    },
    parseTimes (time) {
      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
/deep/.el-table th {
  text-align: center;
}
/deep/.el-table td {
  text-align: center;
}
</style>
