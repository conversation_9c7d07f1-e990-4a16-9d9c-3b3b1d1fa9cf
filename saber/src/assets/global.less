/**
* 共用样式
*/

/** 颜色 */
@color_primary : #409EFF;
@color_text_primary : #303133;
@color_text_regular : #606266;
@color_text_secondary : #909399;
@color_text_placeholder : #C0C4CC;
@color_text_info:#f0f0f0;
@border_color_base : #DCDFE6;
@border_color_light : #E4E7ED;
@border_color_lighter : #EBEEF5;
@border_color_extra_light : #F2F6FC;

@car_move : #4bdaff;
@car_stop : #7c7c7c;
@car_warning : #d7a8ff;
@car_error : #fc0e22;
@car_unused : #fda245;

@component_bg : rgba(255, 255, 255, 0.65);

/** 尺寸 */
// 导航栏高度
@navHeight: 60px;

/** 动画 */
// 滑入滑出
.slide-fade-enter-active {
  transition: all .3s ease;
}
.slide-fade-leave-active {
  transition: all .1s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}
.slide-fade-enter, .slide-fade-leave-to{
  transform: translateX(-10px);
  opacity: 0;
}

.dialog-fade-enter-active {
  transition: all .3s ease;
}
.dialog-fade-leave-active {
  transition: all .1s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}
.dialog-fade-enter, .dialog-fade-leave-to{
  transform: translateY(-10px);
  opacity: 0;
}

/** 按钮样式 */

// 面板按钮
.planel_button{
  font-size: 10px;
  background: var(--gn-color);
  color: white;
  border-radius: 2px;
  border: none;
  padding: 6px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  user-select: none;
  transition-duration: 0.2s;
  align-self: center;
  white-space: nowrap;
  box-shadow: 0 0 6px var(--gn-color);
}
.planel_button:hover{
  background: #e8edf3;
  box-shadow: 0 0 1px var(--gn-color);
  color: #268aff;
  cursor:pointer;
}
.planel_button_video{
  font-size: 10px;
  background: @color_text_placeholder;
  color: white;
  border-radius: 2px;
  border: none;
  padding: 6px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  user-select: none;
  transition-duration: 0.2s;
  align-self: center;
  white-space: nowrap;
  box-shadow: 0 0 6px @color_text_placeholder;
}
.info_button{
  font-size: 10px;
  background: @color_text_info;
  color: black;
  border-radius: 2px;
  padding: 6px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  user-select: none;
  transition-duration: 0.2s;
  align-self: center;
  white-space: nowrap;
  border: 1px solid #ccc;
  box-shadow: 0 0 6px @color_text_info;
}
.info_button:hover{
  background: #e8edf3;
  box-shadow: 0 0 1px @color_text_info;
  color: #268aff;
  cursor:pointer;
}

/** 其他样式 */
// 车辆状态圈圈尺寸
@icon_car_size : 10px;
// 车辆行驶状态
.icon_car_move{
  width: @icon_car_size;
  height: @icon_car_size;
  background: @car_move;
  border-radius: @icon_car_size;
  display: inline-flex;
}
// 车辆停驶状态
.icon_car_stop{
  width: @icon_car_size;
  height: @icon_car_size;
  background: @car_stop;
  border-radius: @icon_car_size;
  display: inline-flex;
}
// 车辆维修状态
.icon_car_warning{
  width: @icon_car_size;
  height: @icon_car_size;
  background: @car_warning;
  border-radius: @icon_car_size;
  display: inline-flex;
}
// 车辆告警状态
.icon_car_error{
  width: @icon_car_size;
  height: @icon_car_size;
  background: @car_error;
  border-radius: @icon_car_size;
  display: inline-flex;
}
.icon_car_unused{
  width: @icon_car_size;
  height: @icon_car_size;
  background: @car_unused;
  border-radius: @icon_car_size;
  display: inline-flex;
}

/** 功能 */
.flex_box{
  display: flex;
  // max-width: 55vw;
}
.flex_find{
  // display: block;
  max-width: 50vw;
  // min-width: 60vw;
}
.blank_witch{
  width:15px;
}
.select_box{
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 10px 0 10px;
}

/** 页面业务公用样式 */
.work_content{
  flex: 1;
  display: flex;
  flex-direction: column;
}
.work_location{
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  margin: 10px 10px;
  background: #efefef;
}
.work_tools{
  // height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  margin: 0 10px;
  background: #efefef;
}
.work_tools_text{
  color: @color_text_regular;
  white-space: nowrap;
}
.work_table{
  // align-items: center;
  width: calc(100%);
  display: flex;
  flex-direction: column;
  flex: 1;
  flex-basis: auto;
  overflow: auto;
}
.work_eltable{
  flex: 1;
  flex-basis: auto;
  overflow-y: auto;
  display: flex;
  box-shadow: 1px 1px 6px #88888850;
}
.work_pagination{
  display: flex;
  // justify-content: center;
  margin-bottom: 10px;
  // flex-shrink: 0;
}
.text_required{
  color: red;
}
.work_table_header {
  color: #909399 !important;
  background: #f3f9ff !important;
}

// InputLocationSearch.vue 组件的样式，因为放在里面无法覆盖 el 的样式，所以放出来
.inputlocationsearch_autocomplete{
  li {
    line-height: normal;
    padding: 7px;
  }
}

.el-table .warning-row {
  background: rgba(245, 108, 108, 0.2) !important;
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner{
  background-color: var(--gn-color);
  border-color: var(--gn-color);
}

/** element ui 全局样式 */
.el-cascader-menu__wrap{
  height: 100%;
}

.xh_work_search_item_input{
  flex: 1;
}

.clickStyle{
  color: #3399ff;
  cursor:pointer;
}



/** 响应式布局 */
@breakpoints: {
  // Extra small screen / phone
  xs: 0;
  // // 超小屏幕（手机，小于 768px）
  sm: 576px;
  // // 小屏幕（平板，大于等于 768px）
  md: 768px;
  // 中等屏幕（桌面显示器，大于等于 992px）
  lg: 992px;
  // 大屏幕（大桌面显示器，大于等于 1200px）
  xl: 1200px;
}

//.head-container {
//  padding-bottom: 10px;
//  .filter-item {
//    display: inline-block;
//    vertical-align: middle;
//    margin: 0 2px 10px 0;
//    input {
//      height: 30.5px;
//      line-height: 30.5px;
//    }
//  }
//  .el-select__caret.el-input__icon.el-icon-arrow-up{
//    line-height: 30.5px;
//  }
//  .date-item {
//    display: inline-block;
//    vertical-align: middle;
//    margin-bottom: 10px;
//    height: 30.5px;
//    width: 223px;
//  }
//}
.error-notification{
  background-color: #fddde1;
  border-color:#ffcccd ;
  .el-notification__content {
    margin: 0;
  }
}
/* 修改 @riophae/vue-treeselect 选项样式*/
.vue-treeselect__menu-container {
  .vue-treeselect__label {
    color: #606266;
    font-weight: normal;
  }
  .vue-treeselect__option--disabled .vue-treeselect__label {
    color: rgba(0, 0, 0, 0.25);
  }
}
.tree-select__w-280 .vue-treeselect__menu {
  width: 280px;
}

.el-dialog__wrapper.avue-dialog.avue-crud__dialog {
  .el-dialog{
    .el-dialog__header{
      .avue-crud__dialog__menu{
        .el-dialog__close.el-icon-full-screen {
          display: none;
        }
      }
    }
  }
}

.avue-crud__dialog .avue--detail .el-form {
  .el-row {
    border-color: transparent;
    .el-col {
      border-color: transparent;
      .el-form-item--small.el-form-item {
        background-color: transparent;
        .el-form-item__label {
          padding-right: 0px;
        }
        .el-form-item__content {
          border-left: none;
          .el-input.is-disabled .el-input__inner,
          .el-range-editor.is-disabled,
          .el-range-editor.is-disabled input,
          .el-textarea.is-disabled .el-textarea__inner {
            padding-left: 15px;
            border: 1px solid #e4e7ed;
          }
        }
      }
    }
  }
}

// 国能提供的滚动条样式
/* 滚动条 start */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: #f6f6f6;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb {
  background: #cdcdcd;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb:hover {
  background: #747474;
}
::-webkit-scrollbar-corner {
  background: #f6f6f6;
}
/* 滚动条 end */