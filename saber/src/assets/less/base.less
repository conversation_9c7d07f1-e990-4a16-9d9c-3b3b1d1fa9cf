/* 初始化样式 */
html {
  font-size: 14px;
  width: 100%;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  line-height: 1;
}

p {
  margin: 0;
  padding: 0;
}

//ul,
//li {
//  margin: 0;
//  padding: 0;
//  list-style: none;
//}

a {
  text-decoration: none;
  cursor: pointer;
}

.clearfix {
  *zoom: 1;
  &::before,
  &::after {
    content: "";
    display: table;
  }
  &::after {
    clear: both;
    overflow: hidden;
  }
}

.fl {
  float: left;
}

.fr {
  float: right;
}
