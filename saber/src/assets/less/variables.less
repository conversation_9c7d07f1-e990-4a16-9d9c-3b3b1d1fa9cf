/* 公共样式变量 */
@xh-color-primary: #1890ff;
@xh-color-header: #16325c;
@xh-color-nav: #0f4f7d;
@xh-color-logo: #10294f;
@xh-button-shadow: 0 2px 25px 0 rgba(0, 0, 0, 0.04);

// base color
@blue:#324157;
@light-blue:#3A71A8;
@red:#C03639;
@pink: #E65D6E;
@green: #30B08F;
@tiffany: #4AB7BD;
@yellow:#FEC171;
@panGreen: #30B08F;

// sidebar
@menuText:#bfcbd9;
@menuActiveText:#409EFF;
@subMenuActiveText:#f4f4f5; // https://github.com/ElemeFE/element/issues/12951

@menuBg:#304156;
@menuHover:#263445;

@subMenuBg:#1f2d3d;
@subMenuHover:#001528;

// 颜色
@xhUIColorMain: #2398ff;// 主色
@xhUIColorOther: #006bb1;// 辅助色Other
@xhUIColorBrand: #1e4176;// 辅助色Brand
@xhUIColorSuccess: #56a80f;// 成功色
@xhUIColorWaring: #d68000;// warning
@xhUIColorDanger: #d43900;// 警告色
// 文字色 由深入浅
@xhTextColor1: #303133; // 文字色1
@xhTextColor2: #606266; // 文字色2
@xhTextColor3: #909399; // 文字色3
@xhTextColor4: #ffffff; // 文字色4
// 辅助色 由浅入深
@xhTextLineColor1: #e4e7ed; // 辅助色1
@xhTextLineColor2: #ebeef5; // 辅助色2
@xhTextLineColor3: #c0c4cc; // 辅助色3
@xhTextLineColor4: #a0a9b1; // 辅助色4
// 背景色 由深入浅
@xhBackgroundColor1: #f0f2f5; // 背景色1
@xhBackgroundColor2: #ffffff; // 背景色2

// 布局
@sideBarWidth: 200px;// 左侧导航栏
@sideBarWidthCollapse: 64px;// 左侧导航栏
@logoBarHeight: 42px;// 系统顶部栏
@logoBarIconSize: 28px;// 系统顶部栏图标的大小
@tagsViewHeight: 36px;// 标签栏
@tagsViewItemHeight: 32px;// 标签栏
@selectComponentContainerWidth: 300px;
@selectComponentContainerWidthPlus: 450px;
@videoSelectHeight: 40px;
@historyListHeight: 300px;
@instructionHeight: 68px;
@columnWidth: 250px;

// 字体大小
@xhFontSizeTitle1: 32px;// 标题一 大页面、文章标题
@xhFontSizeTitle2: 20px;// 标题二 登录页标题
@xhFontSizeTitle3: 18px;// 标题三 首页标题
@xhFontSizeTitleBase: 16px;// 小标题 模块、列表、表格内标题
@xhFontSizeContent1: 14px;// 正文
@xhFontSizeContent2: 13px;// 正文表单预设内容、弹窗正文
@xhFontSizeContent3: 12px;// 说明文字、链接、备注、或1024尺寸正文的大小

@xhLineHigh1: 32px;// 正文行高
@xhLineHigh2: 30px;// 正文表单预设内容、弹窗正文
@xhLineHigh3: 28px;// 说明文字、链接、备注、或1024尺寸正文的大小
@xhLineHigh4: 15px;// 车辆面板行高

// 字体类型
@xhFontFamily: YaHei,Arial,PingFang SC, Helvetica;

// 布局间距
@xhSpacingBase: 4px; // 间距；列表中图标和文字水平间距
@xhSpacingMiddle: 16px; // 中等间距（垂直、水平）；列表中图标和文字中等间距
@xhSpacingLarge: 24px; // 大间距（垂直、水平）；
@xhSpacingSuperLarge: 32px; // 大间距（垂直、水平）；图标之间的间距

// 边界圆滑
@xhBorderRadiusBase: 4px;

// 动画特效
@xhTransitionFast: all linear 100ms;
@xhTransitionNormal: all linear 500ms;
@xhTransitionSlow: all linear 1000ms;

// 输入框的长度
@xhSearchInputWidth: 200px;
@xhBoxShadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 系统新布局
@newLogoBarHeight: 64px;// 系统顶部栏
@newTagsViewHeight: 40px;// 标签栏
@newBottomBlankHeight: 0px; // 底部空白
@newCardPaddingHeight: 0px; // 表格Card填充
