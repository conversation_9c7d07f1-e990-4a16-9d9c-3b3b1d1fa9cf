@import 'variables';

.xh-tree {
  @bgColor: #FFFFFF;
  @iconColor: #1f1f1f;
  @nodeBgColor: #DCEFF5;
  @iconBgColor: #D4E8FF;
  .el-tree-node {
    padding-left: 12px;
    &.is-current {
      & > .el-tree-node__content {
        background-color: @iconBgColor;
      }
    }
  }
  .tree-node:focus > .el-tree-node__content{
    background-color: @nodeBgColor;
  }
  .el-tree-node__content {
    color: #343434;
    font-size: 14px;
    .el-tree-node__expand-icon {
      padding: 1px;
      border: 1px solid @iconColor;
      margin-right: 4px;
      background-color: @bgColor;
      &.expanded {
        // transform: none;
        transform: rotate(0deg);
      }
    }
    .el-icon-caret-right {
      &::before {
        content: '\e6d9';
        color: @iconColor;
        font-weight: bold;
        font-size: 12px;
        transform:scale(.9);
        display: inline-block;
      }
      &.expanded::before {
        content: '\e6d8';
      }
    }
  }
  // 只有子节点有选择框 并去掉展开收起图标
  .is-leaf.el-tree-node__expand-icon {
    display: none;
  }
  .is-leaf + .el-checkbox {
    &.is-checked {
      .el-checkbox__inner {
        background-color: #409EFF;
        border-color: #409EFF;
      }
    }
    .el-checkbox__inner {
      display: inline-block;
      border: 1px solid #d9d9d9;
      width: 16px;
      height: 16px;
      border-radius: 0;
      background-color: @bgColor;
      &::after {
        border: 2px solid @bgColor;
        border-left: 0;
        border-top: 0;
        height: 8px;
        left: 5px;
        top: 2px;
      }
    }
  }
  .el-checkbox .el-checkbox__inner {
    display: none;
  }

}

.easy-tree {
  @bgColor: #FFFFFF;
  @iconColor: #1f1f1f;
  @nodeBgColor: #DCEFF5;
  @iconBgColor: #D4E8FF;
  .el-tree-node {
    padding-left: 12px;
    &.is-current {
      & > .el-tree-node__content {
        background-color: @iconBgColor;
      }
    }
  }
  .tree-node:focus > .el-tree-node__content{
    background-color: @nodeBgColor;
  }
  .el-tree-node__content {
    color: #343434;
    font-size: 14px;
    .el-tree-node__expand-icon {
      padding: 1px;
      border: 1px solid @iconColor;
      margin-right: 4px;
      background-color: @bgColor;
      &.expanded {
        // transform: none;
        transform: rotate(0deg);
      }
    }
    .el-icon-caret-right {
      &::before {
        content: '\e6d9';
        color: @iconColor;
        font-weight: bold;
        font-size: 10px !important;
        transform:scale(.9);
        display: inline-block;
      }
      &.expanded::before {
        content: '\e6d8';
      }
    }
  }
  // 只有子节点有选择框 并去掉展开收起图标
  .is-leaf.el-tree-node__expand-icon {
    display: none;
  }
  .is-leaf + .el-checkbox {
    &.is-checked {
      .el-checkbox__inner {
        background-color: #409EFF;
        border-color: #409EFF;
      }
      .el-checkbox__inner::after {
        position: absolute;
        top: 1px;
        left: 4px;
      }
    }
  }
  .el-checkbox .el-checkbox__inner {
    display: inline-block;
    border: 1px solid #d9d9d9;
    width: 16px;
    height: 16px;
    border-radius: 0;
    background-color: @bgColor;
    &::after {
      border: 2px solid @bgColor;
      border-left: 0;
      border-top: 0;
      height: 8px;
      left: 5px;
      top: 2px;
    }
  }

}