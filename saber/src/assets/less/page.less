@import 'variables';

.xh-page-searchItem-label{
  margin-right: @xhSpacingBase;
  display: inline-block;
  margin-bottom: @xhSpacingBase;
}
.xh-page-searchItem-content{
  margin-right: @xhSpacingBase;
  display: inline-block;
  margin-bottom: @xhSpacingBase;
  width: 156px;
}
.xh-page-searchItem-content-auto{
  margin-right: @xhSpacingBase;
  display: inline-block;
  margin-bottom: @xhSpacingBase;
  width: auto;
}
.xh-container{
  // height: calc(100vh - @newLogoBarHeight - @newTagsViewHeight - @newBottomBlankHeight - @newCardPaddingHeight);
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.xh-crud-table-container {
  flex: 1;
  padding: 15px 0 10px;
  // border: 1px #E1E5E8 solid;
  overflow: hidden;
}
.xh-header-content {
  height: auto;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  // border-top: 1px solid rgb(255, 255, 255);
  // border-bottom: 2px solid rgb(255, 255, 255);
  // margin-top: 4px;
  .el-col {
    margin-bottom: 0;
  }
  &-item {
    // padding-left: 5px;
    // padding-right: 5px;
    margin-bottom: 0 !important;
  }
  .el-form-item {
    margin-bottom: 0 !important;
  }
  .el-row, .xh-crud-search {
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .xh-crud-search {
    margin-bottom: 0;
    .el-form-item {
      margin-bottom: 0;
    }
  }

  .xh-search-btn {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
  }
  .xh-search-btn-near {
    display: flex;
    justify-content: flex-start;
    padding-left: 10px;
  }
  .el-form-item__label {
    font-weight: 500;
    color: #3C3C3C;
  }
  .el-form-item {
    margin-bottom: 6px;
  }
  .el-button + .el-button {
    margin-left: 8px;
  }
  .el-button--default {
    background-color: #f9f9f9;
    color: #686868;
  }
}
/* 处理独立header组件的curd页面*/
.head-container  {
  & > .xh-custom-header {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    border: 1px solid #DEDEDE;
    box-shadow: 2px 2px 3px #E6E6E6;
    border-radius: 5px;
    padding: 19px 0;

    .el-col {
      margin-bottom: 0;
      display: flex;
      height: 32px;
      box-sizing: border-box;
      & > span {
        width: 80px;
        min-width: 80px;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: flex-end;
        color: #3c3c3c;
        font-size: 14px;
        font-weight: 500;
        padding-right: 12px;
      }
      & > div {
        flex: 1;
        & > input {
          width: 100%;
        }
      }
    }
    .xh-page-searchItem-content,
    .xh-page-searchItem-content-auto {
      margin-bottom: 10px;
    }

    .xh-search-btn {
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      padding-right: 20px;
      .slot-btn {
        display: flex;
        align-items: flex-start;
      }

      .el-button {
        height: 32px;
      }
    }
  }
}


.more-btn {
  // padding: 9px 8px !important;
  .el-icon-d-arrow-right,
  .el-icon-d-arrow-left{
    transform: rotateZ(90deg);
  }
}
.el-table {
  .el-table__empty-block {
    width: 100% !important;
  }
  .el-empty__description {
    margin-top: -8px;
    p {
      color: #343434;
    }
  }
}
.app-base-main {
  background-color: #fff;
}
.dynamic-num {
  display: flex;
  align-items: center;
}
.strong-text {
  font-size: 14px;
  font-weight: bold;
}
.table-alarmLevel-default{
  background-color: #ecf5ff !important;
  border: 1px solid #409eff !important;
  color: #409eff !important;
}
.table-alarmLevel-success{
  background-color: #f0f9eb !important;
  border: 1px solid #67c23a !important;
  color: #67c23a !important;
}
.table-alarmLevel-info{
  background-color: #f4f4f5 !important;
  border: 1px solid #e9e9eb !important;
  color: #909399 !important;
}
.table-alarmLevel-warning{
  background-color: #fdf6ec !important;
  border: 1px solid #f7b742 !important;
  color: #f7b742 !important;
}
.table-alarmLevel-danger{
  background-color: #fef0f0 !important;
  border: 1px solid #f56c6c !important;
  color: #f56c6c !important;
}
.table-alarmLevel-severity{
  background-color: #f4f4f4 !important;
  border: 1px solid #B25A05 !important;
  color: #B25A05 !important;
}
.table-alarmLevel-critical{
  background-color: #C69898 !important;
  border: 1px solid #710303 !important;
  color: #710303 !important;
}
// 高德搜索插件样式
.amap-sug-result {
  display: none;
}