/* 公共mixins 用mixin开头，区分普通类 */
.mixin-ellipsis(@width: auto) {
  width: @width;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}

.mixin-ellipsis-line(@line: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
}

// 底线
.mixin-under-line(@width: 100%){
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: @width;
  border-bottom: 1px solid #E6E6E6;
  transform: scaleY(0.5);
  transform-origin: 0 0;
}

// 顶线
.mixin-top-line(@width: 100%){
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  width: @width;
  border-top: 1px solid #E6E6E6;
  transform: scaleY(0.5);
  transform-origin: 0 0;
}
