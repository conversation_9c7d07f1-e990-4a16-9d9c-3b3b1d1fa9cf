@import 'variables';

// 无遮罩对话框
.el-dialog__wrapper.xh-workspace-no-modal-dialog{
  pointer-events: none;
}
.xh-workspace-no-modal-dialog .el-dialog{
  pointer-events: all;
}

.xh-dialog-inline.el-dialog{
  width: 650px;
}
.el-dialog{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0 !important;
  .el-dialog__header{
    padding: 22px 32px;
    border-bottom: 1px solid #E1E5E8;
    // background-image: linear-gradient(to right, #114FB6, #2682E0);
    .el-dialog__title {
      font-size: 16px;
      color: #242424;
      display: flex;
      align-items: center;
      letter-spacing: 3px;

      // &::before {
      //   content: '';
      //   width: 8px;
      //   height: 16px;
      //   background-color: #242424;
      //   margin: 0 8px;
      //   display: inline-block;
      // }
    }
  }
  .el-dialog__headerbtn {
    top: 22px;
    right: 32px;
    font-size: 22px;
    .el-icon-close {
      color: #CBCBCB;
      font-weight: bold;
    }
  }
  .el-dialog__footer{
    border-top: 1px solid #E1E5E8;
    padding: 22px 18px;

    .dialog-footer {
      display: flex;
      justify-content: center;

      .el-button {
        min-width: 64px;
        height: 32px;
      }
      .el-button + .el-button {
        margin-left: 16px;
      }
    }
  }
}
