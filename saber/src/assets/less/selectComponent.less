@import 'variables';

// 主容器
.xh-select-component-container{
  width: @selectComponentContainerWidth;
  // height: calc(100vh - 2 * @logoBarHeight - 4 * @xhSpacingBase);
  height: calc(100vh - @newLogoBarHeight - @newTagsViewHeight - @newBottomBlankHeight);
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  transition: @xhTransitionFast;
}
// 视频页需要一个头部高度@videoSelectHeight
.xh-select-component-video-container{
  width: @selectComponentContainerWidth;
  height: calc(100vh - 2 * @logoBarHeight - 2 * @xhSpacingBase - @videoSelectHeight);
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  transition: @xhTransitionFast;
}
//指令页上部增加了导航栏，需要一个头部高度@instructionHeight
.xh-select-instruction-container{
  width: @selectComponentContainerWidth;
  height: calc(100vh - 2 * @logoBarHeight - 2 * @xhSpacingBase - @instructionHeight);
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  transition: @xhTransitionFast;
}
.xh-dropDown-invisible{
  .xh-select-component-container{
    width: 300px;
  }
}

// 标题栏
.xh-select-component-header{
  width: 100%;
  height: @tagsViewHeight;
  background-color: @xhBackgroundColor2;
  position: relative;
}
.xh-select-component-header-title{
  font-size: @xhFontSizeTitle2;
  float: left;
  margin-left: @xhSpacingBase;
  line-height: @tagsViewHeight;
}
.xh-select-component-header-refreshBtn{
  font-size: @xhFontSizeTitle2;
  float: left;
  line-height: @tagsViewHeight;
  height: @tagsViewHeight;
  width: @tagsViewHeight;
  transition: @xhTransitionSlow;
  cursor: pointer;
  text-align: center;
}
// .xh-select-component-header-refreshBtn:hover{
//   transform: rotate(360deg);
// }
.xh-minus-button{
  font-size: @xhFontSizeTitle2;
  height: @tagsViewHeight;
  width: @tagsViewHeight;
  transition: @xhTransitionSlow;
  cursor: pointer;
  text-align: center;
}
.xh-minus-button:hover{
  transform: rotate(360deg);
}
.xh-dropDown-invisible{
  .xh-select-component-header{
    background-color: @xhUIColorMain;
    color: @xhTextColor4;
    width: auto;
  }
}

// 切换按钮-绝对定位悬浮
.xh-select-component-toggle-button{
  position: absolute;
  right: 0;
  // top: 22px;
  top: 0;
  height: @tagsViewHeight;
  width: @tagsViewHeight;
  background-color: @xhUIColorMain;
  color: @xhTextColor4;
  border-radius: @xhBorderRadiusBase;
  line-height: @tagsViewHeight;
  cursor: pointer;
  transition: @xhTransitionNormal;
  text-align: center;
}
// 切换按钮-绝对定位悬浮
.xh-select-component-toggle-video-button{
  float: right;
  height: @tagsViewHeight;
  width: @tagsViewHeight;
  background-color: @xhUIColorMain;
  color: @xhTextColor4;
  border-radius: @xhBorderRadiusBase;
  line-height: @tagsViewHeight;
  cursor: pointer;
  transition: @xhTransitionNormal;
  text-align: center;
}
.xh-select-component-toggle-button:hover{
  border-radius: 50%;
}
.xh-dropDown-invisible{
  .xh-select-component-toggle-button{
    transform: rotate(180deg);
    border-radius: 50%;
  }
}

// el-collapse-item中的插槽标题
.collapse-item-title-slot{
  font-size: @xhFontSizeTitleBase;
  padding: @xhSpacingBase;
}
.xh-select-component-collapse-item{
  // background-color: red; // 不清楚有什么用
  .el-collapse-item__header{
    height: @tagsViewHeight;
  }
}

// 条件过滤的条件
.xh-select-component-filter-item-key {
  font-size: @xhFontSizeTitleBase;
  float: left;
  margin-left: @xhSpacingBase;
  clear:both
}
.xh-select-component-filter-item-value {
  width: 150px !important;
  float: left;
  margin-left: @xhSpacingBase;
}
